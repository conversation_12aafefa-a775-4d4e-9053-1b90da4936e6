; php.ini

;
; Increase PHP memory limit
;
memory_limit = 1G

;
; enable resulting html compression
;
zlib.output_compression = on

;
; Increase realpath cache size
;
realpath_cache_size = 10M

;
; Increase realpath cache ttl
;
realpath_cache_ttl = 7200

;
; Multi store support
;
auto_prepend_file = /app/magento-vars.php

;
; Increase max input variables value
;
max_input_vars = 10000

;
; Setup the session garbage collector
;
session.gc_probability = 1

;
; Setup opcache configuration
;
opcache.validate_timestamps = 0
opcache.blacklist_filename="${MAGENTO_CLOUD_APP_DIR}/op-exclude.txt"
opcache.max_accelerated_files=16229
opcache.consistency_checks=0
