<?php

declare(strict_types=1);

/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Improved Layered Navigation Base for Magento 2
 */

namespace Amasty\Shopby\Test\Unit\Model\Layer;

use Amasty\Shopby\Model\Layer\FilterList;
use Amasty\Shopby\Model\Source\FilterPlacedBlock;
use Amasty\Shopby\Test\Unit\Traits;
use Amasty\ShopbyBase\Model\FilterSetting\FilterResolver;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class FilterListSortingTest extends TestCase
{
    use Traits\ReflectionTrait;

    /**
     * @var FilterList|MockObject
     */
    private $filterList;

    /**
     * @var FilterResolver|MockObject
     */
    private $filterResolver;

    /**
     * @covers       FilterList::sortingByPosition
     * @dataProvider sortingByPositionProvider
     */
    public function testSortingByPosition($itemsToSort, $resultItems, $place)
    {
        $this->setProperty($this->filterList, 'currentPlace', $place, FilterList::class);

        usort($itemsToSort, [$this->filterList, 'sortingByPosition']);

        self::assertSame($itemsToSort, $resultItems);
    }

    public function sortingByPositionProvider()
    {
        $item1 = $this->createFilter(1, FilterPlacedBlock::POSITION_SIDEBAR, 6, 6);
        $item2top5 = $this->createFilter(33, FilterPlacedBlock::POSITION_BOTH, 2, 5);
        $item3 = $this->createFilter(3, FilterPlacedBlock::POSITION_TOP, 0, 0);
        $item4top2 = $this->createFilter(0, FilterPlacedBlock::POSITION_BOTH, 4, 2);
        $item5top4 = $this->createFilter(1, FilterPlacedBlock::POSITION_BOTH, 4, 4);

        return [
            'Sidebar Sorting' => [
                [$item4top2, $item5top4, $item1, $item2top5, $item3],
                [$item1, $item2top5, $item3, $item4top2, $item5top4],
                \Amasty\Shopby\Model\Layer\FilterList::PLACE_SIDEBAR
            ],
            'Top Sorting' => [
                [$item4top2, $item5top4, $item1, $item2top5, $item3],
                [$item1, $item4top2, $item3, $item5top4, $item2top5],
                \Amasty\Shopby\Model\Layer\FilterList::PLACE_TOP
            ]
        ];
    }

    private function createFilter(int $position, int $blockPlace = 0, int $sidePosition = 0, int $topPosition = 0)
    {
        $filter = $this->getMockBuilder(\Amasty\Shopby\Model\Layer\Filter\OnSale::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getPosition'])
            ->addMethods(['getSetting'])
            ->getMock();
        $filter->method('getPosition')->willReturn($position);

        $settings = $this->createMock(\Amasty\ShopbyBase\Model\FilterSetting::class);
        $settings->method('getTopPosition')->willReturn($topPosition);
        $settings->method('getSidePosition')->willReturn($sidePosition);
        $settings->method('getBlockPosition')->willReturn($blockPlace);

        $filter->method('getSetting')->willReturn($settings);

        return $filter;
    }

    protected function setUp(): void
    {
        $this->filterResolver = $this->createMock(FilterResolver::class);
        $this->filterResolver->method('resolveByFilter')
            ->willReturnCallback(
                function ($filter) {
                    return $filter->getSetting();
                }
            );

        $this->filterList = $this->getMockBuilder(FilterList::class)
            ->disableOriginalConstructor()
            ->addMethods([])
            ->getMock();

        $this->setProperty($this->filterList, 'filterResolver', $this->filterResolver, FilterList::class);
    }
}
