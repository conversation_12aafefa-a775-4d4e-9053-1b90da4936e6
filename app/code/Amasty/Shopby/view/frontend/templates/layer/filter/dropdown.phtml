<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Improved Layered Navigation Base for Magento 2
 */
/** @var \Amasty\Shopby\Model\Layer\Filter\Item $filterItem */
/** @var \Amasty\ShopbyBase\Model\FilterSetting $filterSetting */
/** @var \Amasty\Shopby\Block\Navigation\FilterRenderer $block */
/** @var \Magento\Framework\Escaper $escaper */

$filterCode = $escaper->escapeHtml($filterSetting->getAttributeCode());
?>

<div class="items am-filter-items-<?= $escaper->escapeHtml($filterCode) ?> am-dropdown">
    <form data-amshopby-filter="<?= $escaper->escapeHtml($filterCode) ?>"
          data-amshopby-filter-request-var="<?= $escaper->escapeHtml($block->getFilter()->getRequestVar()) ?>">
    <?php if (count($filterItems) > 0): ?>
        <select
            <?= /* @noEscape */ $block->isMultiselect($filterSetting) ? 'multiple="on"' : '' ?>
            class="am-shopby-filter-<?= $escaper->escapeHtml($filterCode) ?> am-select"
            name="amshopby[<?= /* @noEscape */ $block->getFilter()->getRequestVar();?>][]"
        >
            <?php if (!$block->isMultiselect($filterSetting)): ?>
                <option value=""
                        data-url="<?= /* @noEscape */ $block->getClearUrl()
                            ? $escaper->escapeUrl($block->getClearUrl())
                            : '#' ?>"
                >
                    <?= $escaper->escapeHtml(__('Select Option ...')) ?>
                </option>
            <?php endif; ?>
            <?php foreach ($filterItems as $filterItem):
                $disabled = ($filterItem->getCount() == 0) ? ' disabled' : '';
                $selected = ($block->checkedFilter($filterItem)) ? ' selected' : '';
                $url = $filterItem->getUrl();
                $label = $filterItem->getOptionLabel();

                if ($block->isShowProductQuantities($filterSetting->getShowProductQuantities())) {
                    $label .= ' (' . $filterItem->getCount() . ')';
                }
                ?>
                <option value="<?= $escaper->escapeHtml($filterItem->getValueString()) ?>"
                        data-url="<?= $escaper->escapeUrl($url) ?>"
                        <?= /* @noEscape */ $disabled . $selected ?>
                >
                    <?= /* @noEscape */ strip_tags($label) ?>
                </option>
            <?php endforeach; ?>
            <script type="text/x-magento-init">
                {
                    ".am-shopby-filter-<?= /* @noEscape */ $filterCode ?>": {
                        "amShopbyFilterDropdown": {
                            "collectFilters": <?= /* @noEscape */ $block->collectFilters() ?>,
                            "isMultiselect": <?= /* @noEscape */ (int) $block->isMultiselect($filterSetting)?>
                        }
                    }
                }
            </script>
        </select>
    </form>
    <?php endif; ?>
</div>
<?php if ($filterSetting->getAddFromToWidget() && isset($fromToConfig) && is_array($fromToConfig)): ?>
    <?= /* @noEscape */ $block->getFromToWidget('dropdown') ?>
<?php endif; ?>
