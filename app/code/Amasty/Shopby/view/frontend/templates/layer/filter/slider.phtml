<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Improved Layered Navigation Base for Magento 2
 */
/**
 * @var \Amasty\ShopbyBase\Model\FilterSetting $filterSetting
 * @var \Amasty\Shopby\Block\Navigation\FilterRenderer $block
 * @var \Magento\Framework\Escaper $escaper
 */

$postfix = uniqid();
$filterCode = $filterSetting->getAttributeCode();
$max = ceil($fromToConfig['max']);
$min = floor($fromToConfig['min']);
$sliderStyle = $block->getSliderStyle();
$sliderColor = $block->getSliderColor();
$hideDigitsAfterDot = $filterSetting->getHideZeros();
?>

<div class="amshopby-slider-wrapper <?= $escaper->escapeHtmlAttr($sliderStyle) ?>">
    <div class="items am-filter-items-<?= $escaper->escapeHtmlAttr($filterCode) ?>">
        <form data-amshopby-filter="<?= $escaper->escapeHtmlAttr($filterCode) ?>"
              data-amshopby-filter-request-var="<?= $escaper->escapeHtmlAttr($block->getFilter()->getRequestVar()) ?>">
            <div id="am-shopby-filter-<?= $escaper->escapeHtmlAttr($filterCode . '_' . $postfix) ?>"
                 class="amshopby-slider-container amshopby_currency_rate <?= $escaper->escapeHtmlAttr($sliderStyle) ?>"
                 data-am-js="slider-container"
                 data-min="<?= $escaper->escapeHtmlAttr($min) ?>"
                 data-max="<?= $escaper->escapeHtmlAttr($max) ?>"
                 data-rate="<?= /* @noEscape */ $fromToConfig['curRate'] ?>"
                 data-mage-init='{"Amasty_Shopby/js/components/amShopbySliderInit": {
                    "priceSliderOptions": {
                        "style": "<?= $escaper->escapeHtml($sliderStyle) ?>",
                        "colors": {"main": "<?= $escaper->escapeHtml($sliderColor) ?>"},
                        "collectFilters": <?= /* @noEscape */ $block->collectFilters() ?>,
                        "template": "<?= $escaper->escapeHtml($fromToConfig['template']) ?>",
                        "currencySymbol": "<?= $escaper->escapeHtml($fromToConfig['currencySymbol']) ?>",
                        "currencyPosition": "<?= $escaper->escapeHtml($fromToConfig['currencyPosition']) ?>",
                        "step": <?= /* @noEscape */ $escaper->escapeHtml(floatval($fromToConfig['step'])) ?>,
                        "from": "<?= /* @noEscape */ $fromToConfig['from'] ?>",
                        "to": "<?= /* @noEscape */ $fromToConfig['to'] ?>",
                        "deltaFrom": "<?= /* @noEscape */ $fromToConfig['deltaFrom'] ?>",
                        "deltaTo": "<?= /* @noEscape */ $fromToConfig['deltaTo'] ?>",
                        "curRate": "<?= /* @noEscape */ $fromToConfig['curRate'] ?>",
                        "min": <?= (float)$min ?>,
                        "max": <?= (float)$max ?>,
                        "url": "<?= /* @noEscape */ $block->getSliderUrlTemplate() ?>",
                        "code": "<?= $escaper->escapeHtml($filterSetting->getAttributeModel()->getAttributeCode()) ?>",
                        "hideDisplay": <?= /* @noEscape */ $filterSetting->getAddFromToWidget() ? "1" : '0' ?>,
                        "hideDigitsAfterDot": <?= /* @noEscape */ $hideDigitsAfterDot ? '1' : '0' ?>
                        },
                     "filterCode": "<?= $escaper->escapeHtmlAttr($filterCode) ?>"
                    }}'>
                <input data-amshopby-slider-id="value"
                       type=hidden
                       data-digits-after-dot="<?= /* @noEscape */ $hideDigitsAfterDot ? '1' : '0' ?>"
                       name="amshopby[<?= $escaper->escapeHtmlAttr($block->getFilter()->getRequestVar())?>][]"/>
                <div data-amshopby-slider-id="slider" class="am-slider"></div>
                <div data-amshopby-slider-id="display"
                     class="amshopby-slider-display"
                     data-am-js="slider-display"></div>
            </div>
        </form>
    </div>

    <?php if ($filterSetting->getAddFromToWidget()): ?>
        <?= /* @noEscape */ $block->getFromToWidget('slider') ?>
    <?php endif; ?>
</div>
