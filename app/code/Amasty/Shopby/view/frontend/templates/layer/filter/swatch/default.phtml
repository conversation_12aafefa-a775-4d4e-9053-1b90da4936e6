<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Improved Layered Navigation Base for Magento 2
 */
/** @var $block SwatchRenderer| State */
/** @var \Magento\Framework\Escaper $escaper */

use Amasty\Shopby\Block\Navigation\State;
use Amasty\Shopby\Block\Navigation\SwatchRenderer;
use Amasty\Shopby\Model\Source\DisplayMode;
use Magento\Swatches\Model\Swatch;
?>
<?php
$swatchData = $block->getSwatchData();
$isSwatchBlock = $block instanceof \Amasty\Shopby\Block\Navigation\State\Swatch;
$filterSetting = $block->getFilterSetting();
$filterCode = $filterSetting ? $escaper->escapeHtml($filterSetting->getAttributeCode()) : '';
$extraClass = $filterSetting ? 'am-filter-items-' . $filterCode : '';
$attributeCode = $escaper->escapeHtml($swatchData['attribute_code']);
$attributeFilter = $block->getFilter() ? $block->getFilter()->getRequestVar() : '';
$displayMode = $filterSetting ? $filterSetting->getDisplayMode() : false;
$displayMode = $displayMode ?: $block->getDisplayModeByAttributeCode($attributeCode);
$showImagesLabel = $displayMode == DisplayMode::MODE_IMAGES_LABELS;
$relNofollow = $filterSetting ? $filterSetting->isAddNofollow() : false;
$showProductQuantities = $filterSetting ? $filterSetting->getShowProductQuantities() : 0;
?>

<?php if ($filterSetting && $showImagesLabel && $filterSetting->isShowSearchBox(count($swatchData['options']))): ?>
    <?= /* @noEscape */ $block->getSearchForm() ?>
<?php endif; ?>
    <form class="am-shopby-form" data-amshopby-filter="<?= /* @noEscape */ $attributeFilter ?>"
        <?php if ($filterSetting && $block->getEnableOverflowScroll($filterSetting) > 0): ?>
            style="max-height:<?= /* @noEscape */ $block->getOverflowScrollValue($filterSetting) ?>px;"
        <?php endif; ?>>
        <div class="<?= /* @noEscape */ $extraClass ?> swatch-attribute swatch-layered
            <?= /* @noEscape */ $attributeCode ?> <?= $showImagesLabel ? 'am-swatch-options' : ''; ?>"
            data-attribute-code="<?= /* @noEscape */ $attributeCode ?>"
            data-attribute-id="<?= /* @noEscape */ $swatchData['attribute_id'] ?>"
            data-am-js="swatch-options">
            <?php foreach ($swatchData['options'] as $option => $label): ?>
            <div class="am-swatch-wrapper item swatch-option-link-layered
                <?= (array_key_exists(SwatchRenderer::VAR_COUNT, $label)
                && !$label[SwatchRenderer::VAR_COUNT]) ? '-empty-value' : '' ?>">
                <?php
                    $filterItem = $label[SwatchRenderer::VAR_FILTER_ITEM] ?? null;
                    $selected = $filterItem ? $block->isFilterItemSelected($filterItem) : false;
                    $extraSwatchClass = $selected ? 'selected' : '';
                ?>
                <?php if ($isSwatchBlock): ?>
                    <span class="swatch-option-link-layered am-swatch-link">
                <?php else: ?>
                    <?php if ($filterSetting && $block->isMultiselect($filterSetting)):
                        $inputType = "checkbox";
                    elseif ($filterSetting && !in_array($filterCode, ['rating', 'stock'])):
                        $inputType = "radio";
                    endif;?>
                    <?php if (isset($inputType)): ?>
                        <input  class="input -hidden"
                                name="amshopby[<?= /* @noEscape */ $block->getFilter()->getRequestVar() ?>][]"
                                aria-label="<?= $escaper->escapeHtmlAttr($label['label']) ?>"
                                value="<?= /* @noEscape */ $option ?>"
                                type="<?= /* @noEscape */ $inputType ?>"
                            <?= /* @noEscape */ $selected ? 'checked' : ''; ?>
                        />
                    <?php endif; ?>

                    <a href="<?= $escaper->escapeUrl($label['link']) ?>"
                        <?= $relNofollow ? ' rel="nofollow"' : '' ?>
                        aria-label="<?= $escaper->escapeHtmlAttr($label['label']) ?>"
                       data-label="<?= $escaper->escapeHtmlAttr($label['label']) ?>"
                       class="am-swatch-link">
                <?php endif; ?>

                    <?php if (isset($swatchData['swatches'][$option]['type'])): ?>
                        <?php switch ($swatchData['swatches'][$option]['type']):
                            case Swatch::SWATCH_TYPE_EMPTY:
                                $class = '';
                                $optionType = 3;
                                $swatchThumbPath = '';
                                $swatchTooltipValue = '';
                                $style = '';
                                $value = '';
                                break;
                            case Swatch::SWATCH_TYPE_VISUAL_IMAGE:
                                $class = 'image';
                                $optionType = 2;
                                $swatchTooltipValue = '';
                                $swatchThumbPath = $block->getSwatchPath(
                                    'swatch_thumb',
                                    $swatchData['swatches'][$option]['value']
                                );
                                $swatchImagePath = $block->getSwatchPath(
                                    'swatch_image',
                                    $swatchData['swatches'][$option]['value']
                                );
                                $style = "background-image: url(" . $swatchImagePath . ")";
                                $value = '';
                                break;
                            case SwatchRenderer::SWATCH_TYPE_OPTION_IMAGE:
                                $class = 'image';
                                $optionType = 2;
                                $swatchThumbPath = $swatchData['swatches'][$option]['value'];
                                $swatchTooltipValue = '';
                                $style = "background-image: url(" . $swatchData['swatches'][$option]['value'] . ")";
                                $value = '';
                                break;
                            case Swatch::SWATCH_TYPE_VISUAL_COLOR:
                                $class = 'color';
                                $optionType = 1;
                                $swatchThumbPath = '';
                                $swatchTooltipValue = $swatchData['swatches'][$option]['value'];
                                $style = "background-color: " . $swatchData['swatches'][$option]['value'] . ";";
                                $value = '';
                                break;
                            case Swatch::SWATCH_TYPE_TEXTUAL:
                            default:
                                $class = 'text';
                                $optionType = 0;
                                $swatchThumbPath = '';
                                $swatchTooltipValue = '';
                                $style = '';
                                $value = $swatchData['swatches'][$option]['value'];
                                break;
                        endswitch; ?>
                        <div class="<?= /* @noEscape */ $extraSwatchClass; ?> swatch-option
                            <?= /* @noEscape */ $label['custom_style'] ?> <?= /* @noEscape */ $class ?> "
                             data-am-js="swatch-item"
                             option-type="<?= /* @noEscape */ $optionType; ?>"
                             option-id="<?= /* @noEscape */ $option ?>"
                             option-label="<?= $escaper->escapeHtmlAttr($label['label']) ?>"
                             option-tooltip-thumb="<?= /* @noEscape */ $swatchThumbPath ?>"
                             option-tooltip-value="<?= /* @noEscape */ $swatchTooltipValue ?>"
                             data-option-type="<?= /* @noEscape */ $optionType; ?>"
                             data-option-id="<?= /* @noEscape */ $option ?>"
                             data-option-label="<?= $escaper->escapeHtmlAttr($label['label']) ?>"
                             data-option-tooltip-thumb="<?= /* @noEscape */ $swatchThumbPath ?>"
                             data-option-tooltip-value="<?= /* @noEscape */ $swatchTooltipValue ?>"
                             style="<?= /* @noEscape */ $style ?>">
                            <?= /* @noEscape */ $value ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($showImagesLabel): ?>
                        <div class="am-shopby-swatch-label">
                            <span class="label"><?= $escaper->escapeHtmlAttr($label['label']) ?></span>
                            <?php if ($filterSetting && $block->isShowProductQuantities($showProductQuantities)): ?>
                                <span class="count"><?= /* @noEscape */ $label[SwatchRenderer::VAR_COUNT] ?></span>
                                <span class="filter-count-label">
                                <?php $title = ($label[SwatchRenderer::VAR_COUNT] == 1) ? __('item') : __('items') ?>
                                <?= /* @noEscape */ $escaper->escapeHtml($title) ?>
                            </span>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php if ($isSwatchBlock): ?>
                    </span>
                <?php else: ?>
                    </a>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        </div>
    </form>

    <script type="text/x-magento-init">
    {
        "[data-am-js='swatch-item']": {
            "amShopbySwatchTooltip": {}
        }
    }
    </script>
<?php if ($filterSetting): ?>
    <script type="text/x-magento-init">
    {
        ".<?= /* @noEscape */ $extraClass; ?>": {
            "amShopbyFilterSwatch": {
                "collectFilters": <?= /* @noEscape */ $block->collectFilters();?>,
                "clearUrl": "<?= $escaper->escapeUrl($block->getClearUrl()); ?>"
            }
        }
    }
    </script>
<?php endif; ?>
