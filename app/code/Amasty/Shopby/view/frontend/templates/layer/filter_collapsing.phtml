<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Improved Layered Navigation Base for Magento 2
 */
/**
 * @var \Amasty\Shopby\Block\Navigation\FilterCollapsing $block
 * @var \Magento\Framework\Escaper $escaper
 */
?>

<script type="text/x-magento-init">
    {
        ".sidebar #narrow-by-list": {
            "accordion": {
                "multipleCollapsible": true,
                "active": [<?= $escaper->escapeHtml(implode(",", $block->getFiltersExpanded()))?>]
            }
        }
    }
</script>
