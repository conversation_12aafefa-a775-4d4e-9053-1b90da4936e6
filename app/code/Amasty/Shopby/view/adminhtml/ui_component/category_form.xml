<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Improved Layered Navigation Base for Magento 2
 */-->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="display_settings">
        <field name="am_exclude_from_filter">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Exclude from Category Filter</item>
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="dataScope" xsi:type="string">am_exclude_from_filter</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="sortOrder" xsi:type="string">85</item>
                </item>
            </argument>
        </field>
    </fieldset>
    <fieldset name="design">
        <field name="page_layout" component="Amasty_Shopby/js/form/element/ui-layout-promotion-select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="showCheckbox" xsi:type="boolean">false</item>
                    <item name="multiple" xsi:type="boolean">false</item>
                    <item name="disableLabel" xsi:type="boolean">true</item>
                    <item name="selectedPlaceholders" xsi:type="array">
                        <item name="defaultPlaceholder" xsi:type="string" translate="true">No layout updates</item>
                    </item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>
