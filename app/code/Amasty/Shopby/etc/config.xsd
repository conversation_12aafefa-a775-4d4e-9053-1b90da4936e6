<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="config" type="configType"/>
    <xs:complexType name="layoutType">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute type="xs:string" name="id"/>
                <xs:attribute type="xs:string" name="label"/>
                <xs:attribute type="xs:string" name="type"/>
                <xs:attribute type="xs:string" name="depends" use="optional"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="configType">
        <xs:sequence>
            <xs:element type="layoutType" name="layout" maxOccurs="unbounded" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
