<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Improved Layered Navigation Base for Magento 2
 */-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="amshopby" translate="label" type="text" sortOrder="91312" showInDefault="1" showInWebsite="1" showInStore="1">
            <resource>Amasty_Shopby::config</resource>
            <class>separator-top</class>
            <label>Improved Layered Navigation</label>
            <tab>amasty</tab>

            <group id="general" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="ajax_enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable AJAX</label>
                    <comment>For the ‘By button click’ mode AJAX keeps working no matter which option is selected</comment>
                    <tooltip>Enabling AJAX allows your store to update filter results dynamically, so when a customer uses filtering on the frontend, selecting filters will not cause a page reload.</tooltip>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="ajax_scroll_up" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Scroll to Top after AJAX Load</label>
                    <comment>With this setting, you can enable an option of scrolling to the top after filter applying by AJAX. Select 'No' to not enable.</comment>
                    <source_model>Amasty\Shopby\Model\Source\ScrollToTop</source_model>
                    <depends>
                        <field id="ajax_enabled">1</field>
                    </depends>
                </field>
                <field id="enable_overflow_scroll" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Add Vertical Scrolling to Filter Block After</label>
                    <comment>Specify maximal filter size (in pixels) that will be displayed without vertical scrolling. In case a filter is higher than the indicated size, the scrolling will appear. To disable set 0.</comment>
                    <validate>validate-zero-or-greater</validate>
                </field>
                <field id="enable_sticky_sidebar_desktop" translate="label comment" type="select" sortOrder="45" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Sticky Sidebar for Desktop</label>
                    <comment>Note: filters in top block will be hidden.</comment>
                    <tooltip>Activate the Sticky Sidebar to keep the filter block visible while users scroll down the page on desktop devices. Enabling this will hide any filters located in the top block, ensuring the sidebar remains fixed and accessible.</tooltip>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="submit_filters_on_desktop" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Submit Filters on Desktop</label>
                    <source_model>Amasty\Shopby\Model\Source\SubmitMode</source_model>
                </field>
                <field id="submit_filters_on_mobile" translate="label" type="select" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Submit Filters on Mobile</label>
                    <source_model>Amasty\Shopby\Model\Source\SubmitMode</source_model>
                </field>
                <field id="keep_single_choice_visible" translate="label comment" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Leave the Single-Select Filter Visible after Selection</label>
                    <comment>Disable the setting to hide the filter with just one option when the value is selected.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="hide_filters_with_one_option" translate="label comment" type="select" sortOrder="75"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Hide Filters with One Available Option</label>
                    <comment>Applies only to category filters and filters based on EAV attributes.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="unfolded_options_state" translate="label comment" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Number of Unfolded Options in State</label>
                    <comment>Specify the number of unfolded options. To see other options, a customer should click the 'More' button.</comment>
                    <validate>validate-zero-or-greater</validate>
                </field>
                <field id="exclude_out_of_stock" translate="label comment" type="select" sortOrder="90" showInDefault="1">
                    <label>Exclude 'Out of Stock' Configurable Options from Navigation</label>
                    <comment>Parent configurable products won’t be displayed in the results when filtered by an out of stock option. This setting will exclude such products from search results as well.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <frontend_model>Amasty\Shopby\Block\Adminhtml\System\Config\ExcludeOutOfStock</frontend_model>
                    <backend_model>\Amasty\Shopby\Model\Config\Backend\InvalidateIndex</backend_model>
                </field>
            </group>
            <group id="slider" translate="label" type="select" sortOrder="17" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Slider Settings</label>
                <field id="slider_color" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Slider Color</label>
                    <comment>Default Slider color - #ff5502</comment>
                    <frontend_model>Amasty\Shopby\Block\Adminhtml\System\Config\Field\SliderColor</frontend_model>
                </field>
                <field id="slider_style" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Slider Style</label>
                    <frontend_model>Amasty\Shopby\Block\Adminhtml\System\Config\Field\SliderStyle</frontend_model>
                </field>
            </group>
            <group id="heading" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Add Title, Description and CMS Blocks of the Selected Filters</label>
                <comment><![CDATA[Title, Description and CMS blocks of the applied filters will be added to the category and brand pages.]]></comment>
                <field id="apply_to" translate="label comment" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label><![CDATA[Add the title & description of the selected filters]]></label>
                    <source_model>Amasty\Shopby\Model\Source\Attribute\Extended</source_model>
                    <frontend_model>Amasty\Shopby\Block\System\Multiselect</frontend_model>
                    <can_be_empty>1</can_be_empty>
                    <comment><![CDATA[The title & description of the applied filters will be added to the category and brand pages]]></comment>
                </field>
                <field id="add_title" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Add Filter Title</label>
                    <source_model>Amasty\Shopby\Model\Source\FilterDataPosition\Title</source_model>
                </field>
                <field id="title_separator" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label><![CDATA[Separate Category Name & Title with]]></label>
                </field>
                <field id="add_description" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Add Filter Description</label>
                    <source_model>Amasty\Shopby\Model\Source\FilterDataPosition\Description</source_model>
                </field>
                <field id="replace_image" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Replace Category Image</label>
                    <comment>When enabled, a category image will be replaced if a filter option has a custom image uploaded.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="replace_cms_block" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Replace Category CMS Block</label>
                    <comment>When enabled, a category CMS block will be replaced if a filter option has a custom Top CMS Block defined.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="children_categories" translate="label comment" type="select" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Children Categories Block</label>
                <comment>These settings activate the block with subcategories on top of the selected category pages.</comment>

                <field id="display_mode" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Mode</label>
                    <source_model>Amasty\Shopby\Model\Source\ChildrenCategoriesBlock\DisplayMode</source_model>
                </field>
                <field id="image_size" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Category's Thumbnail Image Size</label>
                    <validate>validate-greater-than-zero</validate>
                    <depends>
                        <field id="display_mode">1</field>
                    </depends>
                </field>
                <field id="show_labels" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Image Labels</label>
                    <comment>Enable the setting to display the titles of the subcategories.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="display_mode">1</field>
                    </depends>
                </field>
                <field id="slider_enabled" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Slider</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="display_mode" separator=",">1,2</field>
                    </depends>
                </field>
                <field id="items_per_slide" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Items per Slide</label>
                    <depends>
                        <field id="display_mode" separator=",">1,2</field>
                        <field id="slider_enabled">1</field>
                    </depends>
                </field>
                <field id="infinity_loop" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Infinity Loop</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>
                        <![CDATA[Enable continuous loop mode. Because of the nature of how the loop mode works (it will rearrange slides),
                        the total number of slides must be >= slidesPerView*2; otherwise, this mode will not function properly.]]>
                    </comment>
                    <depends>
                        <field id="display_mode" separator=",">1,2</field>
                        <field id="slider_enabled">1</field>
                    </depends>
                </field>
                <field id="categories" translate="label" type="multiselect" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1" >
                    <label>Categories</label>
                    <source_model>Amasty\Base\Model\Source\ActiveWithEmptyOptionCategory</source_model>
                    <depends>
                        <field id="display_mode" separator=",">1,2</field>
                    </depends>
                </field>
            </group>
            <group id="meta" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Category Meta Tags</label>
                <comment><![CDATA[Please open Stores -> Attributes -> Product -> [open attribute] -> Properties ->
[open attribute option settings] in order to define Meta-Title, Meta-Description and Meta-Keywords for your filter options.]]></comment>
                <field id="apply_to" translate="label comment" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label><![CDATA[Add the Meta-data of the selected filters]]></label>
                    <source_model>Amasty\Shopby\Model\Source\Attribute\Extended</source_model>
                    <frontend_model>Amasty\Shopby\Block\System\Multiselect</frontend_model>
                    <can_be_empty>1</can_be_empty>
                    <comment>The Meta-data of the applied filters will be added to the category and brand pages</comment>
                </field>
                <field id="add_title" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Add Filter Title to Meta-Title</label>
                    <source_model>Amasty\Shopby\Model\Source\FilterDataPosition\MetaTitle</source_model>
                </field>
                <field id="title_separator" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Title Tag Separator</label>
                </field>
                <field id="add_description" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Add Filter Description</label>
                    <source_model>Amasty\Shopby\Model\Source\FilterDataPosition\MetaDescription</source_model>
                </field>
                <field id="description_separator" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Description Tag Separator</label>
                </field>
                <field id="add_keywords" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Add Filter Title to Meta-Keywords</label>
                    <source_model>Amasty\Shopby\Model\Source\FilterDataPosition\MetaKeyWords</source_model>
                </field>
            </group>
            <group id="category_filter" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                <label><![CDATA["Category" Filter]]></label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="position" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position</label>
                    <comment>Specify sorting order in the sidebar navigation block.</comment>
                    <validate>validate-zero-or-greater</validate>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="button_category_filter" translate="label" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Category Filter</label>
                    <frontend_model>Amasty\Shopby\Block\Adminhtml\Config\Form\Field\CategoryFilter</frontend_model>
                </field>
            </group>
            <group id="stock_filter" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                <label><![CDATA["Stock" Filter]]></label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[If set to ‘Yes’, please make sure that displaying of out of stock products is configured accordingly
                    (Stores -> Configuration -> Catalog -> Inventory -> Stock Options -> Display Out of Stock Products -> Yes).
                    Otherwise, stock filter won’t appear on storefront.]]></comment>
                </field>
                <field id="use_salable_qty" translate="label comment" type="select" sortOrder="15" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Consider Product Salable Quantity in the Filter</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled the Salable Quantity will be taken into account and displayed on the front-end
                        in the "In Stock" filter. Is only compatible with simple products.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <if_module_enabled>Magento_Inventory</if_module_enabled>
                </field>
                <field id="block_position" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show in the Block</label>
                    <source_model>Amasty\Shopby\Model\Source\FilterPlacedBlock</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="top_position" translate="label comment" type="text" sortOrder="23" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position in Top</label>
                    <comment>Specify sorting order in the top navigation block.</comment>
                    <validate>validate-number</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position">2</field>
                    </depends>
                </field>
                <field id="side_position" translate="label comment" type="text" sortOrder="26" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position in Sidebar</label>
                    <comment>Specify sorting order in the sidebar navigation block.</comment>
                    <validate>validate-number</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position">2</field>
                    </depends>
                </field>
                <field id="position" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position</label>
                    <validate>validate-zero-or-greater</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position" negative="1">2</field>
                    </depends>
                </field>
                <field id="label" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Label</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="tooltip" translate="label comment" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tooltip</label>
                    <comment>Specify tooltip text that will be displayed on a mouse hover for the Stock filter.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="is_expanded" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Expand</label>
                    <source_model>Amasty\Shopby\Model\Source\Expand</source_model>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position" separator=",">0,2</field>
                    </depends>
                    <comment>Allows to expand filter automatically right after a page is loaded. Set 'Expand for desktop only' to keep filter minimized on mobile. Keep 'Auto' to work based on the custom theme functionality.</comment>
                </field>
            </group>
            <group id="rating_filter" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                <label><![CDATA["Rating" Filter]]></label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="block_position" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show in the Block</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <source_model>Amasty\Shopby\Model\Source\FilterPlacedBlock</source_model>
                </field>
                <field id="top_position" translate="label comment" type="text" sortOrder="23" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position in Top</label>
                    <comment>Specify sorting order in the top navigation block.</comment>
                    <validate>validate-number</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position">2</field>
                    </depends>
                </field>
                <field id="side_position" translate="label comment" type="text" sortOrder="26" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position in Sidebar</label>
                    <comment>Specify sorting order in the sidebar navigation block.</comment>
                    <validate>validate-number</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position">2</field>
                    </depends>
                </field>
                <field id="position" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position</label>
                    <validate>validate-zero-or-greater</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position" negative="1">2</field>
                    </depends>
                </field>
                <field id="label" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Label</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="tooltip" translate="label comment" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tooltip</label>
                    <comment>Specify tooltip text that will be displayed on a mouse hover for the Rating filter.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="is_expanded" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Expand</label>
                    <source_model>Amasty\Shopby\Model\Source\Expand</source_model>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position" separator=",">0,2</field>
                    </depends>
                    <comment>Allows to expand filter automatically right after a page is loaded. Set 'Expand for desktop only' to keep filter minimized on mobile. Keep 'Auto' to work based on the custom theme functionality.</comment>
                </field>
            </group>
            <group id="am_is_new_filter" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                <label><![CDATA["New" Filter]]></label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="block_position" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show in the Block</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <source_model>Amasty\Shopby\Model\Source\FilterPlacedBlock</source_model>
                </field>
                <field id="top_position" translate="label comment" type="text" sortOrder="23" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position in Top</label>
                    <comment>Specify sorting order in the top navigation block.</comment>
                    <validate>validate-number</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position">2</field>
                    </depends>
                </field>
                <field id="side_position" translate="label comment" type="text" sortOrder="26" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position in Sidebar</label>
                    <comment>Specify sorting order in the sidebar navigation block.</comment>
                    <validate>validate-number</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position">2</field>
                    </depends>
                </field>
                <field id="position" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position</label>
                    <validate>validate-zero-or-greater</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position" negative="1">2</field>
                    </depends>
                </field>
                <field id="label" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Label</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="tooltip" translate="label comment" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tooltip</label>
                    <comment>Specify tooltip text that will be displayed on a mouse hover for the New filter.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="is_expanded" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Expand</label>
                    <source_model>Amasty\Shopby\Model\Source\Expand</source_model>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position" separator=",">0,2</field>
                    </depends>
                    <comment>Allows to expand filter automatically right after a page is loaded. Set 'Expand for desktop only' to keep filter minimized on mobile. Keep 'Auto' to work based on the custom theme functionality.</comment>
                </field>
            </group>
            <group id="am_on_sale_filter" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                <label><![CDATA["On Sale" Filter]]></label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="block_position" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show in the Block</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <source_model>Amasty\Shopby\Model\Source\FilterPlacedBlock</source_model>
                </field>
                <field id="top_position" translate="label comment" type="text" sortOrder="23" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position in Top</label>
                    <comment>Specify sorting order in the top navigation block.</comment>
                    <validate>validate-number</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position">2</field>
                    </depends>
                </field>
                <field id="side_position" translate="label comment" type="text" sortOrder="26" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position in Sidebar</label>
                    <comment>Specify sorting order in the sidebar navigation block.</comment>
                    <validate>validate-number</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position">2</field>
                    </depends>
                </field>
                <field id="position" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position</label>
                    <validate>validate-zero-or-greater</validate>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position" negative="1">2</field>
                    </depends>
                </field>
                <field id="label" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Label</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="tooltip" translate="label comment" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tooltip</label>
                    <comment>Specify tooltip text that will be displayed on a mouse hover for the On Sale filter.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="is_expanded" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Expand</label>
                    <source_model>Amasty\Shopby\Model\Source\Expand</source_model>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="block_position" separator=",">0,2</field>
                    </depends>
                    <comment>Allows to expand filter automatically right after a page is loaded. Set 'Expand for desktop only' to keep filter minimized on mobile. Keep 'Auto' to work based on the custom theme functionality.</comment>
                </field>
            </group>
            <group id="tooltips" translate="label" type="select" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Tooltips</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="image" translate="label" type="image" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tooltip Image</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <backend_model>Amasty\Shopby\Model\Config\Backend\Image\Tooltip</backend_model>
                </field>
            </group>
        </section>
        <section id="amshopby_root" translate="label" type="text" sortOrder="91313" showInDefault="1" showInWebsite="1" showInStore="1">
            <resource>Amasty_ShopbyBase::config</resource>
            <class>separator-top</class>
            <label>Improved Layered Navigation: All Products</label>
            <tab>amasty</tab>
            <group id="general" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable All Products Page</label>
                    <comment>When enabled, the All Products page is activated, containing all store items and layered navigation filters.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="url" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>All Products Page URL</label>
                </field>
            </group>
        </section>

        <section id="web">
            <group id="default_layouts">
                <field id="default_category_layout" type="Amasty\Shopby\Model\Config\Backend\Element\DisableableSelect" />
            </group>
        </section>
    </system>
</config>
