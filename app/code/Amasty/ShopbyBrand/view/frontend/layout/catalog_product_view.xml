<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop by Brand for Magento 2
 */-->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content.aside">
            <block class="Amasty\ShopbyBrand\Block\Catalog\Product\ProductList\MoreFrom"
                   name="product.info.amshopby.morefrom"
                   ifconfig="amshopby_brand/more_from_brand/enable"
                   template="Amasty_ShopbyBrand::product/list/morefrom/items.phtml">
                <arguments>
                    <argument name="type" xsi:type="string">morefrom</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
