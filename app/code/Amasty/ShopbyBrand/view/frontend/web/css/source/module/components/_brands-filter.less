//
//  Shop by Brand Search
//  ______________________________________________

//
//  Imports
//  ----------------------------------------------

@import '../../module/_mixins';

//
//  Variables
//  ----------------------------------------------

@ambrands-filter__active__color: #006bb4;
@ambrands-filter__active__text-color: #006bb4;
@ambrands-filter__hover__color: #fff;
@ambrands-filter__pressed__color: #dff1ff;
@ambrands-filter__disabled__color: #ececec;
@ambrands-filter__disabled__text-color: @color-gray34;

@ambrands-filter-input__width: 460px;
@ambrands-filter-input__height: 40px;
@ambrands-filter-input__line-height: 20px;
@ambrands-filter-input__border-radius: 6px;
@ambrands-filter-input__active__color: #006bb4;
@ambrands-filter-input__text-color: #333;

@ambrands-filter-button__border-radius: 6px;
@ambrands-filter-button__font-size: 14px;
@ambrands-filter-button__size: 34px;
@ambrands-filter-button__text-color: #333;

@ambrands-filter-icon__size: 20px;
@ambrands-filter-icon__search: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0wIDcuNjkyYTcuNjkyIDcuNjkyIDAgMTAxNS4zODUgMEE3LjY5MiA3LjY5MiAwIDAwMCA3LjY5MnptMS41MzggMGE2LjE1NCA2LjE1NCAwIDExMTIuMzA4IDAgNi4xNTQgNi4xNTQgMCAwMS0xMi4zMDggMHoiIGZpbGw9IiNDN0M3QzciLz48cGF0aCBkPSJNMTkuMjMyIDIwYS43Ni43NiAwIDAxLS41NDQtLjIyNmwtNi42MzYtNi42NDZhLjc3Ljc3IDAgMTExLjA3Ny0xLjA5N2w2LjY0NyA2LjY0NmMuMy4zLjMuNzg3IDAgMS4wODdhLjc2Ljc2IDAgMDEtLjU0NC4yMzZ6IiBmaWxsPSIjQzdDN0M3Ii8+PC9zdmc+) center no-repeat;
@ambrands-filter-icon__clear: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00LjIzIDQuMjNhLjc4My43ODMgMCAwMTEuMTA2IDBMMTAgOC44OTJsNC42NjQtNC42NjRhLjc4My43ODMgMCAwMTEuMTA3IDEuMTA3TDExLjEwNyAxMGw0LjY2NCA0LjY2NGEuNzgzLjc4MyAwIDAxLTEuMTA3IDEuMTA3TDEwIDExLjEwNyA1LjMzNiAxNS43N2EuNzgzLjc4MyAwIDAxLTEuMTA3LTEuMTA3TDguODkzIDEwIDQuMjMgNS4zMzZhLjc4My43ODMgMCAwMTAtMS4xMDd6IiBmaWxsPSIjMzMzIi8+PC9zdmc+) center no-repeat;

//
//  Common
//  ----------------------------------------------

& when (@media-common = true) {
    .ambrands-letters-filter {
        display: flex;
        flex-wrap: wrap;
    }

    .ambrands-letters-filter .ambrands-letter {
        & {
            align-items: center;
            background: @color-white;
            border: 1px solid @color-gray80;
            border-radius: @ambrands-filter-button__border-radius;
            box-sizing: border-box;
            color: @ambrands-filter-button__text-color;
            cursor: pointer;
            display: flex;
            font-size: @ambrands-filter-button__font-size;
            font-weight: 400;
            justify-content: center;
            line-height: 1.2;
            margin: 0 12px 12px 0;
            min-height: @ambrands-filter-button__size;
            min-width: @ambrands-filter-button__size;
            padding: 6px;
            text-align: center;
        }

        &:hover {
            background: @ambrands-filter__hover__color;
            border-color: @ambrands-filter__active__color;
            text-decoration: none;
        }

        &.-disabled {
            background: @ambrands-filter__disabled__color;
            border-color: @ambrands-filter__disabled__color;
            box-shadow: none;
            color: @ambrands-filter__disabled__text-color;
            pointer-events: none;
        }

        &.hide {
            display: none;
        }

        &.-active {
            background: @ambrands-filter__pressed__color;
            border-color: @ambrands-filter__active__color;
            color: @ambrands-filter__active__text-color;
        }

        &.-letter-all {
            padding: 6px 11px;
        }
    }

    .ambrands-filters-block {
        display: inline-block;
        margin: 20px 0 53px;
        width: 100%;
    }

    .ambrands-search-wrapper {
        margin-bottom: 30px;
        max-width: @ambrands-filter-input__width;
    }

    .ambrands-search-block {
        & {
            display: block;
            position: relative;
        }

        &:before {
            background: @ambrands-filter-icon__search;
            content: '';
            display: flex;
            height: @ambrands-filter-icon__size;
            left: 15px;
            min-width: @ambrands-filter-icon__size;
            pointer-events: none;
            position: absolute;
            top: ~'calc(50% - @{ambrands-filter-icon__size} / 2)';
            width: @ambrands-filter-icon__size;
        }

        .ambrands-clear {
            background: @ambrands-filter-icon__clear;
            border: 0;
            border-radius: 0 @ambrands-filter-input__border-radius @ambrands-filter-input__border-radius 0;
            cursor: pointer;
            display: block;
            height: @ambrands-filter-input__height;
            padding: 10px;
            position: absolute;
            right: 0;
            top: 0;
            width: @ambrands-filter-input__height;
        }

        .ambrands-clear:not(.-active) {
            display: none;
        }
    }

    .ambrands-search-block .ambrands-input {
        & {
            .ambrands-input-placeholder();

            background: @color-white;
            border: 1px solid @color-gray80;
            border-radius: @ambrands-filter-input__border-radius;
            box-sizing: border-box;
            color: @ambrands-filter-input__text-color;
            display: block;
            font-size: 14px;
            font-weight: 400;
            height: @ambrands-filter-input__height;
            line-height: @ambrands-filter-input__line-height;
            margin: 0 0 10px;
            max-width: 100%;
            padding: ((@ambrands-filter-input__height - @ambrands-filter-input__line-height) / 2) @ambrands-filter-input__height;
        }

        &:hover,
        &:focus {
            border-color: @ambrands-filter-input__active__color;
        }

        ._keyfocus &:focus,
        &:not([disabled]):focus {
            box-shadow: none;
        }
    }

    .ambrands-livesearch-block {
        & {
            background-color: @color-white;
            border: 1px solid transparent;
            border-radius: 6px;
            box-shadow: 0 4px 8px rgba(190, 199, 219, .21), 0 6px 25px rgba(190, 199, 219, .28);
            box-sizing: border-box;
            display: block;
            max-height: 250px;
            max-width: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            position: absolute;
            width: @ambrands-filter-input__width;
            z-index: 99;
        }

        &:not(.-active) {
            display: none;
        }

        > .ambrands-item {
            & {
                box-sizing: border-box;
                color: @primary__color__dark;
                display: block;
                float: left;
                padding: 5px 10px;
                width: 100%;
            }

            &:hover {
                background-color: @color-gray91;
                text-decoration: none;
            }

            &.active {
                background-color: @color-gray91;
            }
        }
    }

    //  Safari mobile fix
    @supports (-webkit-touch-callout: none) {
        .ambrands-search-block .ambrands-input {
            ._keyfocus &:not(:focus),
            &:not(:focus) {
                border-top-width: .5px;
            }
        }
    }
    //  END Safari mobile fix
}

//
//  Desktop
//  ----------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .ambrands-letters-filter .ambrands-letter {
        margin: 0 7px 7px 0;
    }
}
