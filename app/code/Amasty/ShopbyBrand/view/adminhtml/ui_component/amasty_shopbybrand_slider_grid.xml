<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop by Brand for Magento 2
 */-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">amasty_shopbybrand_slider_grid.amasty_shopby_brand_grid_data_source</item>
            <item name="deps" xsi:type="string">amasty_shopbybrand_slider_grid.amasty_shopby_brand_grid_data_source</item>
        </item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
            </item>
        </item>
        <item name="spinner" xsi:type="string">amasty_shopbybrand_slider_columns</item>
    </argument>
    <dataSource name="amasty_shopby_brand_grid_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Amasty\ShopbyBrand\Ui\DataProvider\Listing\DataProvider</argument>
            <argument name="name" xsi:type="string">amasty_shopby_brand_grid_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">option_setting_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">option_setting_id</item>
                    </item>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="sticky" xsi:type="boolean">true</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                </item>
            </argument>
            <filterSelect name="virtual_store_id">
                <argument name="optionsProvider" xsi:type="configurableObject">
                    <argument name="class" xsi:type="string">Magento\Cms\Ui\Component\Listing\Column\Cms\Options</argument>
                </argument>
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="provider" xsi:type="string">${ $.parentName }</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">componentType = column, index = ${ $.index }:visible</item>
                        </item>
                        <item name="dataScope" xsi:type="string">virtual_store_id</item>
                        <item name="label" xsi:type="string" translate="true">Store View</item>
                        <item name="captionValue" xsi:type="string">0</item>
                    </item>
                </argument>
            </filterSelect>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_columns.ids</item>
                    <item name="indexField" xsi:type="string">option_setting_id</item>
                </item>
            </argument>
            <action name="add">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">add</item>
                        <item name="label" xsi:type="string" translate="true">Add to the slider</item>
                        <item name="url" xsi:type="url" path="amasty_shopbybrand/slider/massAction">
                            <param name="value">1</param>
                        </item>
                    </item>
                </argument>
            </action>
            <action name="remove">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">remove</item>
                        <item name="label" xsi:type="string" translate="true">Remove from the slider</item>
                        <item name="url" xsi:type="url" path="amasty_shopbybrand/slider/massAction">
                            <param name="value">0</param>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
        <paging name="listing_paging">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_grid.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.paging</item>
                    </item>
                    <item name="selectProvider" xsi:type="string">amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_columns.ids</item>
                </item>
            </argument>
        </paging>
    </listingToolbar>
    <columns name="amasty_shopbybrand_slider_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="storageConfig" xsi:type="array">
                    <item name="provider" xsi:type="string">amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_grid.listing_top.bookmarks</item>
                    <item name="namespace" xsi:type="string">current</item>
                </item>
                <item name="childDefaults" xsi:type="array">
                    <item name="fieldAction" xsi:type="array">
                        <item name="provider" xsi:type="string">amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_columns.actions</item>
                        <item name="target" xsi:type="string">applyAction</item>
                        <item name="params" xsi:type="array">
                            <item name="0" xsi:type="string">edit</item>
                            <item name="1" xsi:type="string">${ $.$data.rowIndex }</item>
                        </item>
                    </item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">amasty_shopbybrand_slider_grid.amasty_shopbybrand_slider_grid.listing_top.bookmarks</item>
                        <item name="root" xsi:type="string">columns.${ $.index }</item>
                        <item name="namespace" xsi:type="string">current.${ $.storageConfig.root}</item>
                    </item>
                </item>
            </item>
        </argument>
        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">55</item>
                    <item name="indexField" xsi:type="string">option_setting_id</item>
                    <item name="sortOrder" xsi:type="number">5</item>
                </item>
            </argument>
        </selectionsColumn>

        <column name="title">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Title</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </column>

        <column name="scope" class="Amasty\ShopbyBrand\Ui\Component\Listing\Columns\Scope">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="bodyTmpl" xsi:type="string">ui/grid/cells/html</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="label" xsi:type="string" translate="true">Store View</item>
                    <item name="dataType" xsi:type="string">multiselect</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                </item>
            </argument>
        </column>

        <column name="brand_code" class="Amasty\ShopbyBrand\Ui\Component\Listing\Columns\BrandAttribute">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="bodyTmpl" xsi:type="string">ui/grid/cells/html</item>
                    <item name="label" xsi:type="string" translate="true">Brand Attribute</item>
                    <item name="fieldAction" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                </item>
            </argument>
        </column>

        <column name="slider_image" class="Amasty\ShopbyBrand\Ui\Component\Listing\Columns\SliderImage">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/thumbnail</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="altField" xsi:type="string">name</item>
                    <item name="has_preview" xsi:type="string">1</item>
                    <item name="label" xsi:type="string" translate="true">Slider Image</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                </item>
            </argument>
        </column>

        <column name="is_show_in_slider">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magento\Config\Model\Config\Source\Yesno</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="label" xsi:type="string" translate="true">Show in Slider</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                </item>
            </argument>
        </column>

        <column name="slider_position">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Position in Slider</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                </item>
            </argument>
        </column>

        <column name="url_alias">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">URL alias</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                </item>
            </argument>
        </column>
        <!-- hidden columns -->
        <column name="image" class="Amasty\ShopbyBrand\Ui\Component\Listing\Columns\Image">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/thumbnail</item>
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="altField" xsi:type="string">name</item>
                    <item name="has_preview" xsi:type="string">1</item>
                    <item name="label" xsi:type="string" translate="true">Image</item>
                </item>
            </argument>
        </column>
        <column name="meta_title">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Meta Title</item>
                </item>
            </argument>
        </column>
        <column name="meta_description">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Meta Description</item>
                </item>
            </argument>
        </column>
        <column name="meta_keywords">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Meta Keywords</item>
                </item>
            </argument>
        </column>
        <!-- hidden columns end-->

        <column name="description">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Description</item>
                    <item name="sortOrder" xsi:type="number">80</item>
                </item>
            </argument>
        </column>

        <actionsColumn name="actions" class="Amasty\ShopbyBrand\Ui\Component\Listing\Column\Actions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">option_setting_id</item>
                    <item name="sortOrder" xsi:type="number">100</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>
