<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop by Brand for Magento 2
 */-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Amasty\ShopbyBrand\Model\Brand\BrandDataInterface" type="Amasty\ShopbyBrand\Model\Brand\BrandData"/>
    <type name="Amasty\ShopbyBase\Model\FilterSetting\AttributeConfig">
        <arguments>
            <argument name="attributeProviders" xsi:type="array">
                <item name="amasty_shopby_brand_attriubte_list_provider" xsi:type="object">Amasty\ShopbyBrand\Model\FilterSetting\AttributeListProvider</item>
            </argument>
        </arguments>
    </type>
    <type name="Amasty\Xsearch\Block\Search\Brand">
        <plugin name="Amasty_ShopbyBrand::addBrands" type="Amasty\ShopbyBrand\Plugin\Xsearch\Block\Search\Brand" />
    </type>

    <type name="Amasty\ShopbyBase\Model\UrlBuilder">
        <arguments>
            <argument name="urlAdapters" xsi:type="array">
                <item name="brand" xsi:type="array">
                    <item name="adapter" xsi:type="object">Amasty\ShopbyBrand\Model\UrlBuilder\Adapter</item>
                    <item name="sort_order" xsi:type="string">10</item>
                </item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Sitemap\Model\ItemProvider\Composite">
        <arguments>
            <argument name="itemProviders" xsi:type="array">
                <item name="amBrand" xsi:type="object">Amasty\ShopbyBrand\Model\Sitemap\ItemProvider\Brand</item>
            </argument>
        </arguments>
    </type>

    <!-- Integration with Amasty_XmlSitemap  -->
    <type name="Amasty\XmlSitemap\Model\Sitemap\SourceProvider">
        <arguments>
            <argument name="sources" xsi:type="array">
                <item name="amasty_shopbybrand" xsi:type="string">Amasty\ShopbyBrand\Model\XmlSitemap\Source\Brand</item>
            </argument>
        </arguments>
    </type>

    <type name="Amasty\ShopbyBrand\Model\XmlSitemap\Source\Brand">
        <arguments>
            <argument name="data" xsi:type="array">
                <item name="language_code_provider" xsi:type="object">Amasty\XmlSitemap\Model\Sitemap\Hreflang\LanguageCodeProvider</item>
            </argument>
        </arguments>
    </type>
    <!-- End integration with Amasty_XmlSitemap  -->

    <type name="Amasty\ShopbyBrand\Model\ProductCount">
        <arguments>
            <argument name="collectionFactory" xsi:type="object">Magento\CatalogSearch\Model\ResourceModel\Fulltext\CollectionFactory</argument>
        </arguments>
    </type>

    <!-- Integration with Amasty_ShopbyBase  -->
    <type name="Amasty\ShopbyBase\Model\Integration\ShopbyBrand\GetConfigProvider">
        <arguments>
            <argument name="data" xsi:type="array">
                <item name="object" xsi:type="object">Amasty\ShopbyBrand\Model\ConfigProvider</item>
            </argument>
        </arguments>
    </type>
    <!-- End integration with Amasty_ShopbyBase  -->

    <type name="Magento\Elasticsearch\SearchAdapter\Query\Builder\Sort">
        <arguments>
            <argument name="skippedFields" xsi:type="array">
                <item name="null" xsi:type="string"/>
            </argument>
        </arguments>
    </type>
    <virtualType name="AmastyWrapper\SharedCatalog\State" type="Amasty\Base\Model\Di\Wrapper">
        <arguments>
            <argument name="name" xsi:type="string">Magento\SharedCatalog\Model\State</argument>
            <argument name="isShared" xsi:type="boolean">true</argument>
            <argument name="isProxy" xsi:type="boolean">true</argument>
        </arguments>
    </virtualType>
    <type name="Amasty\ShopbyBrand\Model\Brand\ListDataProvider\LoadItems">
        <arguments>
            <argument name="state" xsi:type="object">AmastyWrapper\SharedCatalog\State</argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Search\Request\Config\FilesystemReader">
        <plugin name="Amasty_ShopbyBrand::AddBrandSearchRequest"
                type="Amasty\ShopbyBrand\Plugin\Framework\Search\Request\Config\FilesystemReader\AddBrandSearchRequest"
                sortOrder="9999"/>
    </type>

    <type name="Amasty\ShopbySeo\Model\SeoOptionsModifier\UniqueBuilder">
        <plugin name="Amasty_ShopbyBrand::CheckUniqueWithBrandAliases"
                type="Amasty\ShopbyBrand\Plugin\ShopbySeo\Model\SeoOptionsModifier\UniqueBuilder\CheckUniqueWithBrandAliases"/>
    </type>
</config>
