<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop by Brand for Magento 2
 */-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Amasty\ElasticSearch\Model\Search\GetRequestQuery\GetAggregations">
        <plugin name="Amasty_ShopbyBrand::SetBrandBucketSize"
                type="Amasty\ShopbyBrand\Plugin\ElasticSearch\Model\Search\GetRequestQuery\GetAggregations\SetBrandBucketSize" />
    </type>
    <type name="Magento\Elasticsearch\SearchAdapter\Query\Builder\Aggregation">
        <plugin name="Amasty_ShopbyBrand::SetBrandBucketSize"
                type="Amasty\ShopbyBrand\Plugin\Elasticsearch\SearchAdapter\Query\Builder\Aggregation\SetBrandBucketSize" />
    </type>
</config>
