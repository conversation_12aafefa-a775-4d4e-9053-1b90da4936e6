parameters:
    checkExplicitMixedMissingReturn: true
    checkPhpDocMissingReturn: true
    scanDirectories:
        - %rootDir%/../../../dev/tests/static/framework/tests/unit/testsuite/Magento
        - %rootDir%/../../../dev/tests/integration/framework/tests/unit/testsuite/Magento
        - %rootDir%/../../../dev/tests/api-functional/_files/Magento
    bootstrapFiles:
        - %rootDir%/../../../dev/tests/static/framework/autoload.php
        - %rootDir%/../../../dev/tests/integration/framework/autoload.php
        - %rootDir%/../../../dev/tests/api-functional/framework/autoload.php
        - %rootDir%/../../../dev/tests/setup-integration/framework/autoload.php
        - %rootDir%/../../../dev/tests/static/framework/Magento/PhpStan/autoload.php
    ignoreErrors:
    # Optional module deps
        -
            message: '#^Parameter.*has invalid type Amasty\\ElasticSearch\\.*\.#'
            paths:
                - Plugin/ElasticSearch/*
        -
            message: '#^Parameter.*has invalid type Amasty\\ShopbySeo\\.*\.#'
            paths:
                - Plugin/ShopbySeo/*

services:
    -
        class: Magento\PhpStan\Reflection\Php\DataObjectClassReflectionExtension
        tags: {phpstan.broker.methodsClassReflectionExtension: {priority: 100}}

    errorFormatter.filtered:
        class: Magento\PhpStan\Formatters\FilteredErrorFormatter
        arguments:
            tableErrorFormatter: @errorFormatter.table
