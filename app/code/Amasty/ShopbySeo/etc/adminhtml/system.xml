<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop by Seo for Magento 2 (System)
 */-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="amasty_shopby_seo" translate="label" type="text" sortOrder="91316" showInDefault="1" showInWebsite="1"
                 showInStore="1">
            <label>Improved Layered Navigation: SEO</label>
            <tab>amasty</tab>
            <resource>Amasty_ShopbySeo::amasty_shopby_seo</resource>
            <group id="url" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>SEO URLs</label>
                <field id="mode" translate="label" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <comment><![CDATA[When enabled, store URLs are optimized for search engines, e.g. URL will be 'category/autumn.html' instead of 'category.html?season=114'.]]></comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="is_generate_seo_default" translate="label" type="select" sortOrder="11" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>SEO-friendly URLs for Attributes (Default Value)</label>
                    <comment><![CDATA[Enable to generate SEO-friendly URLs for attributes in bulk. Go to Stores - Attributes - Product - {attribute_name} - Improved Layered Navigation - SEO - Generate SEO URL setting to apply per-attribute fine-tuning.]]></comment>
                    <source_model>Amasty\ShopbySeo\Model\Source\SeoByDefault</source_model>
                    <depends>
                        <field id="mode">1</field>
                    </depends>
                </field>
                <field id="attribute_name" translate="label commment" type="select" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Include Attribute Code</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <backend_model>Amasty\ShopbySeo\Model\Config\Backend\InvalidateSeoCache</backend_model>
                    <comment><![CDATA[Select ‘Yes' to add attribute code to the URL scheme, e.g. ‘black’ -> 'color_black’. Attribute Code alias can be adjusted per store view on an attribute edit page (Improved Layered Navigation tab).]]></comment>
                    <depends>
                        <field id="mode">1</field>
                    </depends>
                </field>
<!--                field from Amasty_ShopByRedirect-->
<!--                <field id="redirect_to_seo" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">-->
                <field id="filter_word" translate="label commment" type="text" sortOrder="17" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Filter Key</label>
                    <comment><![CDATA[It allows to add filter key to the URL scheme, e.g. black-xl-activity -> shopby/black-xl-activity]]></comment>
                    <depends>
                        <field id="mode">1</field>
                    </depends>
                </field>
                <field id="add_suffix_shopby" translate="label comment" type="select" sortOrder="20" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Add Suffix to the Brand Pages and All-products Pages</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>
                        <![CDATA[To configure the 'Category URL Suffix' setting please go to Configuration - Catalog - Catalog - Search Engine Optimization - Category URL Suffix. FYI on the category pages the suffix is added regardless of this setting.]]>
                    </comment>
                </field>
                <field id="special_char" translate="label" type="select" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Replace Special Characters With</label>
                    <source_model>Amasty\ShopbySeo\Model\Source\SpecialChar</source_model>
                    <depends>
                        <field id="mode">1</field>
                    </depends>
                </field>
                <field id="option_separator" translate="label comment" type="select" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Separate Attribute Options With</label>
                    <source_model>Amasty\ShopbySeo\Model\Source\OptionSeparator</source_model>
                    <comment>
                        <![CDATA[For example, if a customer selects 3 options for the attribute `color`, the URL will look like http://example.com/red-green-blue.html.]]>
                    </comment>
                    <depends>
                        <field id="mode">1</field>
                    </depends>
                </field>
                <field id="separator_checker" sortOrder="68" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label/>
                    <frontend_model>Amasty\ShopbySeo\Block\System\Config\SeparatorChecker</frontend_model>
                </field>
                <field id="ignored_urls" translate="label comment" type="textarea" sortOrder="40"
                       showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Ignore URLs with Pass Info Request</label>
                    <comment>URLs with a Path Info that starts with the specified value will be excluded from SEO processing by the module.
                        For example, in the URL https://faq-and-product-questions-m2.magento-demo.amasty.com/knowledge-base/do-you-provide-the-same-day-delivery-to-new-york/, to exclude Knowledge Base URLs from processing, enter 'knowledge-base' in the setting.
                        Specify each on a new line.</comment>
                    <depends>
                        <field id="mode">1</field>
                    </depends>
                </field>
            </group>
            <group id="robots" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Robots Tag Control</label>
                <field id="control_robots" translate="label" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Allow the Module to Modify Robots Meta Tag</label>
                    <comment>When enabled, the module manages indexation according to SEO settings for each attribute. If you disable the option, search engines will index all the store content. Use "No" for compatibility with other SEO extensions.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="noindex_multiple" translate="label comment" type="select" sortOrder="20" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Noindex when Multiple Attribute Filters Applied</label>
                    <comment>Enable this option to set the robots to noindex values for multiple filters.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="rel_nofollow" translate="label" type="select" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Automatically Add rel="nofollow" to Filter Links when Required</label>
                    <comment>When enabled, rel=“nofollow” is automatically added to the filter links and “nofollow” robot tag is set. This setting helps exclude filter links from search engine indexation if necessary. Please refer to filter-specific settings to exclude filters.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="noindex_paginated" translate="label comment" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Noindex for Paginated Pages</label>
                    <comment>Enable this option to set the robots to noindex for paginated pages.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
            <group id="canonical" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Canonical URL</label>
                <frontend_model>\Amasty\ShopbySeo\Block\Adminhtml\System\Config\Canonical</frontend_model>
                <field id="category" translate="label comment" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Category Pages</label>
                    <comment>Set the structure of canonical urls for category pages.</comment>
                    <source_model>Amasty\ShopbySeo\Model\Source\Canonical\Category</source_model>
                </field>
                <field id="brand" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Brand Pages</label>
                    <source_model>Amasty\ShopbySeo\Model\Source\Canonical\Brand</source_model>
                </field>
                <field id="root" translate="label" type="select" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>"all-product" Page</label>
                    <source_model>Amasty\ShopbySeo\Model\Source\Canonical\Root</source_model>
                </field>
                <field id="add_canonical_for_noindex" translate="label comment" type="select" sortOrder="40"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Add Canonical URL for Noindex Pages</label>
                    <comment>If enabled, canonical URLs will be added to the NOINDEX pages.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <frontend_model>Amasty_ShopByCanonicalManagement\AddCanonicalForNoindexPromo</frontend_model>
                </field>
            </group>
            <group id="other" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Pagination Settings</label>
                <field id="prev_next" translate="label comment" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Use Prev/Next Tags for Improved Navigation Pages</label>
                    <comment><![CDATA[Set 'Yes' to use rel=“next” and rel=“prev” tags and point Google where the next or the previous pages are. Please click <a target="blank" href="https://webmasters.googleblog.com/2011/09/pagination-with-relnext-and-relprev.html">here</a> to learn more details.]]></comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>

                <field id="page_meta_title" translate="label comment" type="select" sortOrder="20" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Add Page Number to Meta Title</label>
                    <comment>Adds the Page Number at the end of the Meta Title, e.g. 'Apparel | Page 5'</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>

                <field id="page_meta_descriprion" translate="label comment" type="select" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Add Page Number to Meta Description</label>
                    <comment>Adds the Page Number at the end of the Meta Description, e.g. 'Apparel Description | Page 5'</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
