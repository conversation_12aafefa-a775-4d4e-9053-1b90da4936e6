<div class="admin__field"
     visible="visible"
     css="$data.additionalClasses"
     attr="'data-index': index">
    <div class="admin__field-label" visible="$data.labelVisible" data-bind="css: { 'ampromo-field-label-container': !!$data.isPromo }">
        <label if="$data.label && !$data.isPromo"  attr="for: uid">
            <span translate="label" attr="'data-config-scope': $data.scopeLabel"></span>
        </label>
        <!-- ko if: $data.label && !!$data.isPromo -->
            <!-- ko template: { name: 'Amasty_Base/form/promo-label', data: $data } --><!-- /ko -->
        <!-- /ko -->
    </div>
    <div class="admin__field-control"
         css="'_with-tooltip': $data.tooltip, '_with-reset': $data.showFallbackReset && $data.isDifferedFromDefault"
         afterRender="!!$data.isPromo && $data.disabled(true)"
    >
        <render args="elementTmpl" ifnot="hasAddons()"></render>

        <div class="admin__control-addon" if="hasAddons()">
            <render args="elementTmpl"></render>

            <label class="admin__addon-prefix" if="$data.addbefore" attr="for: uid">
                <span text="addbefore"></span>
            </label>
            <label class="admin__addon-suffix" if="$data.addafter" attr="for: uid">
                <span text="addafter"></span>
            </label>
        </div>

        <render args="tooltipTpl" if="$data.tooltip"></render>

        <render args="fallbackResetTpl" if="$data.showFallbackReset && $data.isDifferedFromDefault"></render>

        <label class="admin__field-error" if="error" attr="for: uid" text="error"></label>
        <div class="admin__field-note" if="$data.notice" attr="id: noticeId">
            <span translate="notice"></span>
        </div>

        <!-- ko if: $data.additionalInfo -->
            <!-- ko with: {additionalInfoUnsanitizedHtml: $data.additionalInfo} -->
                <div class="admin__additional-info" html="additionalInfoUnsanitizedHtml"></div>
            <!-- /ko -->
        <!-- /ko -->

        <render args="$data.service.template" if="$data.hasService()"></render>
    </div>
</div>
