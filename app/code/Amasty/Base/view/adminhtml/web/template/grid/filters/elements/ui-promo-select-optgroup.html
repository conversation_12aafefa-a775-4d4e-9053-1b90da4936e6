<ul class="admin__action-multiselect-menu-inner"
    data-bind="
    visible: $data.root.showLevels($data.current),
    attr: {
        'data-level': $data.current.level++
    }">
    <!-- ko if: $data.current.visible() || $data.current.isVisited  -->
    <!-- ko foreach: { data: $data.current.optgroup, as: 'option'}  -->
    <li class="admin__action-multiselect-menu-inner-item"
        data-bind="css: { _parent: $data.optgroup }">
        <div class="action-menu-item"
             data-bind="
                css: {
                    _selected: $parent.root.isSelected(option.value),
                    _hover: $parent.root.isHovered(option, $element),
                    _expended: $parent.root.getLevelVisibility($data) || $data.visible,
                    _unclickable: $parent.root.isLabelDecoration($data),
                    _last: $parent.root.addLastElement($data),
                    '_with-checkbox': $parent.root.showCheckbox,
                    '_is-am-promo': option.isPromo
                },
                click: function(data, event){
                    $parent.root.toggleOptionSelected($data, $index(), event);
                },
                clickBubble: false

            ">
            <!-- ko if: $data.optgroup && $parent.root.showOpenLevelsActionIcon-->
            <div class="admin__action-multiselect-dropdown"
                 data-bind="
                        click: function(data, event){
                            $parent.root.openChildLevel($data, $element, event);
                        },
                        clickBubble: false
                     "></div>
            <!-- /ko-->

            <!--ko if: $parent.root.showCheckbox-->
            <input
                    class="admin__control-checkbox"
                    type="checkbox"
                    tabindex="-1"
                    data-bind="attr: { 'checked': $parent.root.isSelected(option.value) }">
            <!--/ko-->

            <div class="ampromo-icon-wrap"
                 if="option.isPromo && !option.promoIcon"
                 data-bind="html: $parent.root.promoConfig.promoIcon"
            ></div>
            <img class="ampromo-icon"
                 if="option.isPromo && !!option.promoIcon"
                 data-bind="
                    attr: { src: option.promoIcon },
                    css: 'ampromo-icon-' + option.value
                 "
            />
            <label
                    class="admin__action-multiselect-label"
                    data-bind="text: option.label">
            </label>

            <button class="ampromo-badge"
                    if="option.isPromo"
                    data-bind="
                        text: option.promoBadgeText ?? $parent.root.promoConfig.badgeText,
                        style: {
                            color: option.promoBadgeColor ?? $parent.root.promoConfig.badgeColor,
                            backgroundColor: option.promoBadgeBgColor ?? $parent.root.promoConfig.badgeBgColor
                        },
                        click: !!option.promoLink ? () => { window.open(option.promoLink, '_blank') } : null
                    "
            ></button>
        </div>
        <!-- ko if: $data.optgroup -->
        <!-- ko template: {name: $parent.root.optgroupTmpl, data: {root: $parent.root, current: $data}} -->
        <!-- /ko -->
        <!-- /ko-->
    </li>
    <!-- /ko -->
    <!-- /ko -->
</ul>
