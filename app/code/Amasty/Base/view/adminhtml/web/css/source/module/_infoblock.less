//
//  Amasty Base Info-block
//  ______________________________________________

//
//  Variables
//  _____________________________________________

@ambase-button__margin: 25px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .ambase-buttons-container {
        & {
            align-items: center;
            display: flex;
            margin-top: @ambase-button__margin;
        }

        .ambase-button {
            background: @ambase__infoblock-button__color;
            border-radius: 4px;
            color: @color-white;
            font-weight: 600;
            letter-spacing: 1.25px;
            margin-right: @ambase-button__margin;
            padding: 13px 27px;
            text-decoration: none;
            text-transform: uppercase;
            transition: opacity .3s ease-in-out;
        }

        .ambase-button:hover {
            opacity: .8;
        }
    }

    .amasty-info-block {
        .module-version.last-version {
            color: @ambase__infoblock-module-last-version__color;
        }

        span.message {
            display: inline-block;
        }

        .message.success {
            background-color: @ambase__infoblock-message-success__background;
        }
    }
}
