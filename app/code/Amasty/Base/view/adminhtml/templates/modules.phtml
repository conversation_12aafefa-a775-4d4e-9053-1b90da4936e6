<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Magento 2 Base Package
 */
/** @var \Amasty\Base\Block\Adminhtml\Extensions $block */
/** @var \Magento\Framework\Escaper $escaper */
?>

<!-- ko scope: 'ambase_extensions' -->
    <!-- ko template: getTemplate() --><!-- /ko-->
<!-- /ko-->

<script type="text/x-magento-init">
{
"*": {
    "Magento_Ui/js/core/app": {
            "components": {
                "ambase_extensions": {
                    "component": "Amasty_Base/js/extensions",
                    "modulesData": <?= /* @noEscape */ $block->getModulesDataJson() ?>,
                    "isOriginMarketplace": "<?= /* @noEscape */ $block->isOriginMarketplace() ?>",
                    "seoParams": "<?= $escaper->escapeHtml($block->getSeoparams()) ?>"
                }
            }
        }
    }
}
</script>

<?php if ($block->isNeedCheckLicense()): ?>
<!-- ko scope: 'ambase_extensions_additional' -->
<!-- ko template: getTemplate() --><!-- /ko-->
<!-- /ko-->

<script type="text/x-magento-init">
    {
    "*": {
        "Magento_Ui/js/core/app": {
                "components": {
                    "ambase_extensions_additional": {
                        "component": "Amasty_Base/js/extensions/additional-info",
                        "contentEndpoint": "<?= $escaper->escapeJs($escaper->escapeUrl($block->getUrl('ambase/extensions/additionalinfo'))) ?>",
                        "loaderIcon": "<?= $escaper->escapeJs($escaper->escapeUrl($block->getViewFileUrl('images/loader-2.gif'))) ?>"
                }
            }
        }
    }
}
</script>
<?php endif; ?>

