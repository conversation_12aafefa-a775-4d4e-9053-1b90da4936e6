<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Magento 2 Base Package
 */-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="amasty_base">
        <job name="ambase_feeds_refresh" instance="Amasty\Base\Cron\RefreshFeedData" method="execute">
            <config_path>amasty_base/cron/feeds_refresh</config_path>
        </job>
        <job name="amasty_base_daily_send_system_info" instance="Amasty\Base\Cron\DailySendSystemInfo" method="execute">
            <config_path>amasty_base/cron/daily_send_system_info</config_path>
        </job>
        <job name="amasty_base_instance_registration" instance="Amasty\Base\Cron\InstanceRegistration" method="execute">
            <config_path>amasty_base/cron/instance_registration</config_path>
        </job>
    </group>
</config>
