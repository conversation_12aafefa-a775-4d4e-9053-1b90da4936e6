<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Magento 2 Base Package
 */-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Amasty\Base\Model\Feed\Response\FeedResponseInterface" type="Amasty\Base\Model\Feed\Response\FeedResponse"/>
    <preference for="Magento\Framework\Filesystem\DriverInterface" type="Magento\Framework\Filesystem\Driver\File" />
    <preference for="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Info\CacheInfoInterface"
                type="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Info\CacheInfo" />
    <preference for="Amasty\Base\Api\Data\InstanceDataInterface" type="Amasty\Base\Model\InstanceData\InstanceData"/>
    <preference for="Amasty\Base\Api\InstanceDataRepositoryInterface" type="Amasty\Base\Model\InstanceData\Repository"/>
    <preference for="Amasty\Base\Api\Data\ScheduleInterface" type="Amasty\Base\Model\Schedule\Schedule"/>
    <preference for="Amasty\Base\Api\ScheduleRepositoryInterface" type="Amasty\Base\Model\Schedule\Repository"/>
    <preference for="Amasty\Base\Api\Data\InstanceHashInterface" type="Amasty\Base\Model\InstanceHash\InstanceHash"/>
    <preference for="Amasty\Base\Api\InstanceHashRepositoryInterface" type="Amasty\Base\Model\InstanceHash\Repository"/>

    <type name="Amasty\Base\Model\SysInfo\Command\LicenceService\ProcessLicenseValidationResponse">
        <arguments>
            <argument name="dateTime" xsi:type="object">Magento\Framework\Stdlib\DateTime\DateTime\Proxy</argument>
        </arguments>
    </type>

    <type name="Amasty\Base\Model\Response\OctetResponseInterfaceFactory">
        <arguments>
            <argument name="responseFactoryAssociationMap" xsi:type="array">
                <item name="file" xsi:type="object">Amasty\Base\Model\Response\File\FileOctetResponseFactory</item>
                <item name="url" xsi:type="object">Amasty\Base\Model\Response\File\FileUrlOctetResponseFactory</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\View\TemplateEngine\Php">
        <plugin name="Amasty_Base::AddEscaperToPhpRenderer" type="Amasty\Base\Plugin\Framework\View\TemplateEngine\Php" sortOrder="10"/>
    </type>

    <!--Add possibility for apply patches before DS start-->
    <virtualType name="Magento\Framework\Setup\Patch\DeclarativeSchemaApplyBeforeReader" type="Magento\Framework\Setup\Patch\PatchReader">
        <arguments>
            <argument name="type" xsi:type="string">declarativeSchemaApplyBefore</argument>
        </arguments>
    </virtualType>

    <virtualType name="Amasty\Base\Setup\Patch\PatchApplier" type="Magento\Framework\Setup\Patch\PatchApplier">
        <arguments>
            <argument name="dataPatchReader" xsi:type="object">Magento\Framework\Setup\Patch\DeclarativeSchemaApplyBeforeReader</argument>
            <argument name="schemaPatchReader" xsi:type="object">Magento\Framework\Setup\Patch\DeclarativeSchemaApplyBeforeReader</argument>
        </arguments>
    </virtualType>

    <type name="Amasty\Base\Plugin\Setup\Model\DeclarationInstaller\ApplyPatchesBeforeDeclarativeSchema">
        <arguments>
            <argument name="patchApplier" xsi:type="object">Amasty\Base\Setup\Patch\PatchApplier</argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Setup\Declaration\Schema\OperationsExecutor">
        <plugin name="Amasty_Base::execute-patches-before-schema-apply"
                type="Amasty\Base\Plugin\Setup\Model\DeclarationInstaller\ApplyPatchesBeforeDeclarativeSchema"/>
    </type>
    <!--Add possibility for apply patches before DS end-->

    <virtualType name="Amasty\Base\Model\LicenceService\Response\Entity\Config\RegisteredInstance"
                 type="Amasty\Base\Utils\Http\Response\Entity\Config">
        <arguments>
            <argument name="data" xsi:type="array">
                <item name="class_name" xsi:type="string">Amasty\Base\Model\LicenceService\Response\Data\RegisteredInstance</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Amasty\Base\Utils\Http\Response\Entity\ConfigPool">
        <arguments>
            <argument name="configs" xsi:type="array">
                <item name="/api/v1/instance/registration" xsi:type="object">
                    Amasty\Base\Model\LicenceService\Response\Entity\Config\RegisteredInstance
                </item>
            </argument>
        </arguments>
    </type>

    <type name="Amasty\Base\Model\SysInfo\Provider\CollectorPool">
        <arguments>
            <argument name="collectors" xsi:type="array">
                <item name="licenceService" xsi:type="array">
                    <item name="modules" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\LicenceService\Module</item>
                    <item name="domains" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\LicenceService\Domain</item>
                    <item name="platform" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\LicenceService\Platform</item>
                    <item name="customer_instance_key" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\LicenceService\CustomerInstanceKey</item>
                    <item name="is_production" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\LicenceService\IsProduction</item>
                </item>
                <item name="pingRequest" xsi:type="array">
                    <item name="customer_instance_key" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\LicenceService\CustomerInstanceKey</item>
                    <item name="is_production" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\LicenceService\IsProduction</item>
                </item>
                <item name="sysInfoService" xsi:type="array">
                    <item name="module" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\Module</item>
                    <item name="config" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\Config</item>
                    <item name="system" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\System</item>
                </item>
                <item name="cacheInfoService" xsi:type="array">
                    <item name="page" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Page</item>
                    <item name="opcache" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Opcache</item>
                    <item name="redis" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis</item>
                </item>
                <item name="additionalInfoRequest" xsi:type="array">
                    <item name="magento_edition" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\AdditionalInfoRequest\MagentoEdition</item>
                    <item name="base_version" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\AdditionalInfoRequest\BaseVersion</item>
                </item>
            </argument>
        </arguments>
    </type>
    <!-- Cache Info Configuration Start -->
    <virtualType name="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypeDefaultConfig" type="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypeConfig">
        <arguments>
            <argument name="path" xsi:type="string">cache/frontend/default/backend</argument>
            <argument name="values" xsi:type="array">
                <item name="0" xsi:type="string">Magento\Framework\Cache\Backend\Redis</item>
                <item name="1" xsi:type="string">Cm_Cache_Backend_Redis</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypePageCacheConfig" type="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypeConfig">
        <arguments>
            <argument name="path" xsi:type="string">cache/frontend/page_cache/backend</argument>
            <argument name="values" xsi:type="array">
                <item name="0" xsi:type="string">Magento\Framework\Cache\Backend\Redis</item>
                <item name="1" xsi:type="string">Cm_Cache_Backend_Redis</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypeSessionConfig" type="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypeConfig">
        <arguments>
            <argument name="path" xsi:type="string">session/save</argument>
            <argument name="values" xsi:type="array">
                <item name="0" xsi:type="string">redis</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypesResolver">
        <arguments>
            <argument name="redisTypeConfigPool" xsi:type="array">
                <item name="default" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypeDefaultConfig</item>
                <item name="pageCache" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypePageCacheConfig</item>
                <item name="session" xsi:type="object">Amasty\Base\Model\SysInfo\Provider\Collector\CacheService\Redis\RedisTypeSessionConfig</item>
            </argument>
        </arguments>
    </type>
    <!-- Cache Info Configuration End -->

    <type name="Magento\Framework\Config\FileResolverByModule">
        <plugin name="AmBase::FileResolverByModule"
                type="Amasty\Base\Plugin\Framework\Setup\Declaration\Schema\FileSystem\XmlReader\RestrictDropOperationsPlugin"/>
    </type>

    <!-- CLI Configuration Start -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="AmBase::sendSysInfo" xsi:type="object">Amasty\Base\Console\Command\SendSysInfo</item>
                <item name="AmBase::registerLicenceKey" xsi:type="object">Amasty\Base\Console\Command\RegisterLicenceKey</item>
            </argument>
        </arguments>
    </type>
    <type name="Amasty\Base\Console\Command\SendSysInfo">
        <arguments>
            <argument name="sendSysInfo" xsi:type="object">Amasty\Base\Model\SysInfo\Command\LicenceService\SendSysInfo\Proxy</argument>
        </arguments>
    </type>
    <type name="Amasty\Base\Console\Command\RegisterLicenceKey">
        <arguments>
            <argument name="registerLicenceKey" xsi:type="object">Amasty\Base\Model\SysInfo\Command\LicenceService\RegisterLicenceKey\Proxy</argument>
        </arguments>
    </type>
    <!-- CLI Configuration End -->
    <type name="Magento\Framework\Mview\View\Subscription">
        <plugin name="Amasty_Base::FixInstall" sortOrder="10"
                type="Amasty\Base\Plugin\Framework\Mview\View\Subscription\FixInstall"/>
    </type>
    <type name="Magento\Indexer\Model\Indexer">
        <plugin name="Amasty_Base::skipException" sortOrder="10"
                type="Amasty\Base\Plugin\Indexer\Model\Indexer\SkipException"/>
    </type>
    <virtualType name="Amasty\Base\Model\Source\ActiveWithEmptyOptionCategory" type="Amasty\Base\Model\Source\Category">
        <arguments>
            <argument name="caption" xsi:type="string" translatable="true">All Categories</argument>
            <argument name="filters" xsi:type="array">
                <item name="is_active" xsi:type="string">1</item>
                <item name="level" xsi:type="array">
                    <item name="gt" xsi:type="string">0</item>
                </item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Amasty\Base\Model\MagentoVersion">
        <arguments>
            <argument name="mageOsVersionMap" xsi:type="array">
                <item name="1.0.0" xsi:type="string">2.4.6</item>
                <item name="1.0.1" xsi:type="string">2.4.6</item>
                <item name="1.0.2" xsi:type="string">2.4.6</item>
                <item name="1.0.3" xsi:type="string">2.4.7</item>
                <item name="1.0.4" xsi:type="string">2.4.7</item>
                <item name="1.0.5" xsi:type="string">2.4.7</item>
            </argument>
        </arguments>
    </type>
</config>
