<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Magento 2 Base Package
 */-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="amasty_base_add_information_content">
        <observer name="amasty_base_add_information_content" instance="Amasty\Base\Observer\GenerateInformationTab"/>
    </event>

    <event name="controller_action_predispatch">
        <observer name="amasty_base_admin_notification"
                  instance="Amasty\Base\Observer\PreDispatchAdminActionController" />
    </event>

    <event name="admin_system_config_save">
        <observer name="amasty_base_config_save" instance="Amasty\Base\Observer\SaveConfig"/>
    </event>

    <event name="admin_system_config_changed_section_amasty_products">
        <observer name="Amasty_Base::sendVerifyRequest" instance="Amasty\Base\Observer\Adminhtml\SendVerifyRequest"/>
    </event>
</config>
