<select class="admin__control-select" data-bind="
    attr: {
        name: inputName,
        id: uid,
        disabled: disabled,
        'aria-describedby': getDescriptionId(),
        'aria-required': required,
        'aria-invalid': error() ? true : 'false',
        placeholder: placeholder
    },
    hasFocus: focused,
    optgroup: options,
    value: value,
    optionsCaption: caption,
    optionsValue: 'value',
    optionsText: 'label',
    optionsAfterRender: function(option, item) {
        if (item && item.disabled) {
            ko.applyBindingsToNode(option, {attr: {disabled: true}}, item);
        }
    }"
></select>
