<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Amasty\MegaMenuLite\Block\Adminhtml\Builder\Content $block */
?>

<ul class="ammenu-items-tree" data-ammenu-js="items-tree">
<?php foreach ($block->getItems() as $item): ?>
    <?php $contentItem = $block->getContentItem($item) ?>
    <?php if ($contentItem !== null): ?>
        <li class="ammenu-item" id="<?= $block->escapeHtml($item->getId()) ?>">
            <?= $block->escapeHtml($contentItem->getName()) ?>
        </li>
    <?php endif; ?>
<?php endforeach; ?>
</ul>
<script type="text/x-magento-init">
    {
        "[data-ammenu-js='items-tree']": {
            "Amasty_MegaMenuLite/js/builder/sortable": {
                "moveUrl": "<?= $escaper->escapeUrl($block->getMoveUrl()) ?>"
            }
        }
    }
</script>
