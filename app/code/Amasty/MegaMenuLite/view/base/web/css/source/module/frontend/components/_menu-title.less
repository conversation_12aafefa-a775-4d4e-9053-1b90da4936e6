//
//  <PERSON><PERSON><PERSON>u Title Component
//  ______________________________________________

//
//  Common
//  ----------------------------------------------

& when (@media-common = true) {
    .ammenu-menu-title {
        & {
            .ammenu-flex(none, center);

            padding: @ammenu__indent__xl;
        }

        .ammenu-title {
            font-size: 2.6rem;
            font-weight: 300;
            margin: 0;
        }

        .ammenu-button.-close {
            margin: 0 0 0 auto;
        }
    }
}
