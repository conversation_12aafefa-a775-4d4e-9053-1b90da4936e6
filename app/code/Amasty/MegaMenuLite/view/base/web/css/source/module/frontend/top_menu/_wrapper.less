//
//  Top Menu Component styles
//  ______________________________________________

//
//  Common
//  ----------------------------------------------

& when (@media-common = true) {
    .ammenu-nav-sections.-topmenu {
        & {
            .ammenu-transition(.1);
        }

        &.-sticky {
            & {
                min-height: 58px;
            }

            .ammenu-main-container {
                &:extend(.ammenu-sticky);
            }

            .ammenu-items.-root {
                & {
                    margin: 0 auto;
                    max-width: @ammenu-menu__max-width;
                }

                > .ammenu-item > .ammenu-link {
                    padding: 23.5px 14px;
                }
            }

            .ammenu-label {
                white-space: nowrap;
            }
        }

        .ammenu-main-container {
            .ammenu-transition(.1);

            box-sizing: border-box;
            margin: auto;
            padding: 0 @ammenu__indent__xl;
        }

        .ammenu-items {
            align-items: center;
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            margin: 0;
            padding: 0;
            position: relative;
        }

        .ammenu-items.-root > .ammenu-item {
            & {
                box-sizing: border-box;
                display: inline-block;
                font-size: 1.4rem;
                font-weight: 700;
                margin: 0;
                transition: .5s;
            }

            &:hover + .ammenu-submenu-block {
                display: flex;
            }

            &:hover > .ammenu-link {
                text-decoration: none;
            }

            > .ammenu-link {
                font-size: 1.4rem;
                padding: 14.5px 14px;
                position: relative;
            }

            > .ammenu-link .ammenu-label {
                bottom: 100%;
                left: 0;
                margin: 0 0 4px;
                position: absolute;
            }
        }
    }

    .ammenu-nav-sections.-topmenu:not(.-hamburger) .ammenu-main-container .ammenu-items.-root {
        margin: 0 auto;
        max-width: @ammenu-menu__max-width;
    }
}
