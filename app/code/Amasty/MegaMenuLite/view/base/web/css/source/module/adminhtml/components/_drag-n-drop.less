//
//  Drag-n-Drop component
//  _________________________

//
//  Common
//  --------------------------

& when (@media-common = true) {
    .ammenu-items-tree {
        & {
            .ammenu-flex(none, center, wrap);

            font-size: 2rem;
            list-style: none;
        }

        .ammenu-item {
            & {
                background: #fff;
                border: 1px dashed #ccc;
                border-radius: 4px;
                box-sizing: border-box;
                cursor: pointer;
                flex-basis: 200px;
                margin-bottom: @ammenu__indent__xl;
             //   padding: 35px @ammenu__indent__l * 2;
                white-space: nowrap;
            }

            &:hover {
                border-color: #a0a0a0;
                box-shadow: 0 2px 4px rgba(32, 77, 112, .16);
            }

            &:not(:last-child) {
                margin-right: @ammenu__indent__xl;
            }
        }
    }
}
