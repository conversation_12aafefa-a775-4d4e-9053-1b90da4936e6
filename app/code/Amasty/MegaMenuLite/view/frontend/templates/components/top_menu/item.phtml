<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */
/**
 * @var \Amasty\MegaMenuLite\Block\Container $block
 * @var \Magento\Framework\Escaper $escaper
 * @var \Amasty\MegaMenuLite\ViewModel\Store\Menu $viewModel
 */

$viewModel = $block->getViewModel();
$key = $block->getData('key');
$item = $block->getData('item');
$itemId = $item['id'];
$isIconsAvailable = $block->getData('isIconsAvailable');
?>

<li class="ammenu-item category-item"
    role="menuitem"
    data-bind="
        scope: 'ammenu_topmenu_item_<?= (int) $key ?>',
        mageInit: {
            'Magento_Ui/js/core/app': {
                components: {
                    'ammenu_topmenu_item_<?= (int) $key ?>': {
                        component: 'Amasty_MegaMenuLite/js/top_menu/item',
                        elemIndex: <?= (int) $key ?>,
                        id: '<?= /* @noEscape */ $escaper->escapeHtml($itemId) ?>'
                    }
                }
            }
        }">

    <?= $block->getChildBlock('ammenu.top.menu.link')->setData('item', $item)->setData('isIconsAvailable', $isIconsAvailable)->toHtml() ?>

    <!-- ko scope: 'index = ammenu_submenu_wrapper' -->
        <!-- ko template: {
            name: getTemplate(),
            data: {
                item: $parent.item,
                event: {
                    mouseleave: function () {
                        $parent.item.isActive(false);
                    },
                    mouseenter: function () {
                        $parent.item.isActive(true);
                    }
                },
                isSidebar: false
            }
        }-->
        <!-- /ko -->
    <!-- /ko -->
</li>
