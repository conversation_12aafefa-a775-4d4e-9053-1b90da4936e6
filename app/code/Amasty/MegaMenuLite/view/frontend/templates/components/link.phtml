<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */
/**
 * @var \Magento\Theme\Block\Html\Header\Logo $block
 * @var \Magento\Framework\Escaper $escaper
 */

$item = $block->getData('item');
$isIconsAvailable = $block->getData('isIconsAvailable');
?>

<a if="item.isVisible"
   class="ammenu-link"
   href="<?= $escaper->escapeHtmlAttr($item['url'] ?: 'javascript:void(0)') ?>"
   style="font-weight: <?= $escaper->escapeHtmlAttr($item['desktop_font'] ?? '') ?>"
   data-bind="
        event: item.isInteractive ? {
            mouseenter: onMouseenter,
            keypress: onMouseenter,
            mouseleave: onMouseleave
        } : false,
        style: {
            'color': item.color() ? item.color() : '',
            'background': item.backgroundColor() ? item.backgroundColor() : ''
        },
        attr: {
            tabindex: item.isInteractive ? '0' : '-1',
            'data-item-id': item.id
        },
        css: {
            '-current': item.current,
            '-disabled': !item.isInteractive
        }">
    <?php if ($isIconsAvailable): ?>
        <?= /* @noEscape */ $block->getChildBlock('ammenu.top.menu.icon')->setData('item', $item)->toHtml() ?>
    <?php endif; ?>

    <p class="ammenu-text-block">
        <span class="ammenu-text"><?= $escaper->escapeHtml($item['name']) ?></span>
        <?php if (isset($item['label'])): ?>
            <span class="ammenu-label"
                  style="
                      background: <?= $escaper->escapeHtmlAttr($item['label']['label_background_color'] ?? '') ?>;
                      color: <?= $escaper->escapeHtmlAttr($item['label']['label_text_color'] ?? '') ?>;">
                <?= $escaper->escapeHtml($item['label']['label'] ?? '') ?>
            </span>
        <?php endif; ?>
   </p>
</a>
