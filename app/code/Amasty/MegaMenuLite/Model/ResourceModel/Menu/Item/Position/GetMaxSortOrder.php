<?php

declare(strict_types=1);

/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */

namespace Amasty\MegaMenuLite\Model\ResourceModel\Menu\Item\Position;

use Amasty\MegaMenuLite\Model\ResourceModel\CategoryCollection;
use Amasty\MegaMenuLite\Model\ResourceModel\Menu\Item\Position;
use Magento\Catalog\Api\Data\CategoryInterface;
use Magento\Framework\App\ResourceConnection;

class GetMaxSortOrder
{
    private const DEFAULT_SORT_ORDER = 0;

    /**
     * @var array
     */
    private $maxSortOrder = [];

    /**
     * @var ResourceConnection
     */
    private $resource;

    public function __construct(
        ResourceConnection $resource
    ) {
        $this->resource = $resource;
    }

    public function execute(int $storeId): int
    {
        if (!isset($this->maxSortOrder[$storeId])) {
            $sortOrderSelect = $this->resource->getConnection()->select()
                ->from(
                    $this->resource->getTableName(Position::TABLE),
                    'MAX(sort_order)'
                );
            $positionSelect = $this->resource->getConnection()->select()
                ->from(
                    $this->resource->getTableName('catalog_category_entity'),
                    'MAX(position)'
                )
                ->where(CategoryInterface::KEY_LEVEL . '=' . CategoryCollection::MENU_LEVEL);

            $sortOrder = $this->resource->getConnection()->fetchOne($sortOrderSelect);
            $position = $this->resource->getConnection()->fetchOne($positionSelect);

            $this->maxSortOrder[$storeId] = max($sortOrder, $position);
        }

        return $this->maxSortOrder[$storeId] === null
            ? self::DEFAULT_SORT_ORDER : (int)++$this->maxSortOrder[$storeId];
    }
}
