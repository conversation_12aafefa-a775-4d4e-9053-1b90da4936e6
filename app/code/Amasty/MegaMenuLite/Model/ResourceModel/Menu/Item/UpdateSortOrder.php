<?php

declare(strict_types=1);

/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */

namespace Amasty\MegaMenuLite\Model\ResourceModel\Menu\Item;

use Magento\Framework\App\ResourceConnection;

class UpdateSortOrder
{
    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    public function __construct(
        ResourceConnection $resourceConnection
    ) {
        $this->resourceConnection = $resourceConnection;
    }

    public function execute(array $items): void
    {
        if (!empty($items)) {
            $this->resourceConnection->getConnection()->insertOnDuplicate(
                $this->resourceConnection->getTableName(Position::TABLE),
                $items,
                [Position::POSITION]
            );
        }
    }
}
