<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="amasty_menu_link" resource="default" engine="innodb" comment="Amasty Mega Menu Link Table">
        <column xsi:type="int" name="entity_id" padding="11" unsigned="false" nullable="false" identity="true" comment="Amasty Menu Link ID"/>
        <column xsi:type="int" name="parent_id" padding="11" unsigned="true" nullable="true" identity="false" default="0" comment="Amasty Menu Item Parent Id"/>
        <column xsi:type="varchar" name="path" nullable="true" length="255" default="0/" comment="Amasty Menu Item Path"/>
        <column xsi:type="int" name="level" padding="11" unsigned="true" nullable="true" identity="false" default="0" comment="Amasty Menu Item Level"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>

        <index referenceId="AMASTY_MENU_LINK_ENTITY_ID" indexType="btree">
            <column name="entity_id"/>
        </index>
    </table>

    <table name="amasty_menu_item_content" resource="default" engine="innodb" comment="Amasty Mega Menu Link Table">
        <column xsi:type="int" name="id" padding="11" unsigned="false" nullable="false" identity="true" comment="Amasty Menu Item Auto ID"/>
        <column xsi:type="int" name="entity_id" padding="11" unsigned="false" nullable="false" identity="false" comment="Amasty Menu Item ID"/>
        <column xsi:type="varchar" name="type" nullable="false" length="20" comment="Amasty Menu Item Type (category or amasty link)"/>
        <column xsi:type="int" name="store_id" padding="11" unsigned="false" nullable="false" identity="false" comment="Store ID"/>
        <column xsi:type="varchar" name="name" nullable="true" length="255" comment="Amasty Menu Item Name"/>
        <column xsi:type="varchar" name="link" nullable="true" length="255" comment="Link"/>
        <column xsi:type="int" name="link_type" padding="11" unsigned="false" nullable="true" identity="false"
                comment="Link type"/>
        <column xsi:type="smallint" name="status" nullable="true" default="0" padding="6" unsigned="false" identity="false"/>
        <column xsi:type="varchar" name="label" nullable="true" length="255" comment="Amasty Menu Label"/>
        <column xsi:type="varchar" name="label_text_color" nullable="true" length="255" comment="Amasty Menu Label Color"/>
        <column xsi:type="varchar" name="label_background_color" nullable="true" length="255" comment="Amasty Menu Label Color"/>
        <column xsi:type="text" name="use_default" nullable="true" comment="Use Default Fields"/>
        <column xsi:type="text" name="customer_group_ids" nullable="true" comment="Customer group IDs"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>

        <constraint xsi:type="unique" referenceId="AMASTY_MENU_ITEM_CONTENT_ENTITY_ID_STORE_ID_TYPE">
            <column name="entity_id"/>
            <column name="store_id"/>
            <column name="type"/>
        </constraint>
    </table>

    <table name="amasty_menu_item_order" resource="default" engine="innodb" comment="amasty_menu_item_order">
        <column xsi:type="int" name="id" padding="11" unsigned="false" nullable="false" identity="true" comment="Amasty Menu Order Item Auto ID"/>
        <column xsi:type="int" name="root_category_id" padding="11" unsigned="false" nullable="false" identity="false" comment="Root Category" disabled="true"/>
        <column xsi:type="varchar" name="type" nullable="false" length="20" comment="Amasty Menu Item Type (category or amasty link)"/>
        <column xsi:type="int" name="sort_order" padding="11" unsigned="false" nullable="true" identity="false" default="99999" comment="Amasty Menu Item Sort Order"/>
        <column xsi:type="int" name="entity_id" padding="11" unsigned="false" nullable="false" identity="false" comment="Amasty Menu Item ID"/>
        <column xsi:type="int" name="store_view" padding="11" unsigned="true" nullable="true" identity="false" comment="Store View"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>

        <constraint xsi:type="unique" referenceId="AMASTY_MENU_ITEM_ORDER_ROOT_CATEGORY_ID_TYPE_ENTITY_ID" disabled="true">
            <column name="root_category_id"/>
            <column name="type"/>
            <column name="entity_id"/>
        </constraint>

        <constraint xsi:type="unique" referenceId="AMASTY_MENU_ITEM_ORDER_TYPE_ENTITY_ID_STORE_VIEW">
            <column name="type"/>
            <column name="entity_id"/>
            <column name="store_view"/>
        </constraint>
    </table>
</schema>
