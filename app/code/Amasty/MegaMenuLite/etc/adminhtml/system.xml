<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="ammegamenu" translate="label" type="text" sortOrder="130507" showInDefault="1" showInWebsite="1" showInStore="1">
            <resource>Amasty_MegaMenuLite::config</resource>
            <class>separator-top</class>
            <label>Mega Menu Lite</label>
            <tab>amasty</tab>
            <group id="amasty_information" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Information</label>
                <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Information</frontend_model>
            </group>

            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>

                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>

                <field id="hamburger_enabled" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Hamburger Menu For Categories on Desktop</label>
                    <comment>For mobile version hamburger menu is used by default.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>

                <field id="mobile_menu_title" translate="label" type="text" showInDefault="1" sortOrder="40" showInWebsite="1" showInStore="1">
                    <label>Mobile Menu Title</label>
                    <comment>Mobile Menu Title length is restricted to 17 characters.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <validate>validate-no-html-tags validate-length maximum-length-17</validate>
                </field>

                <field id="mobile_menu_width" translate="label" type="text" showInDefault="1" sortOrder="80" showInWebsite="1" showInStore="1">
                    <label>Mobile Menu Width(px)</label>
                    <comment>Show mobile menu from target screen size</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <validate>validate-number validate-zero-or-greater validate-length maximum-length-4 minimum-length-2</validate>
                </field>
            </group>

            <group id="color" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Color Settings</label>
                <depends>
                    <field id="ammegamenu/general/enabled">1</field>
                </depends>

                <field id="color_template" translate="label comment" type="select" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Color Scheme Template</label>
                    <comment>If 'Blank' is selected, plugin will not add color styles dynamically. In that case please define them using Magento LESS files.</comment>
                    <source_model>Amasty\MegaMenuLite\Model\OptionSource\ColorTemplate</source_model>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\ColorTemplate</frontend_model>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/color_template.png">]]>
                    </tooltip>
                </field>

                <field id="main_menu_background" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Main Menu Background Color</label>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/main_menu_background.png">]]>
                    </tooltip>
                </field>

                <field id="main_menu_background_hover" translate="label,comment" type="text" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Main Menu Background Hover Color</label>
                    <comment>Active background color for items in desktop menu type</comment>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/main_menu_background_hover.png">]]>
                    </tooltip>
                </field>

                <field id="main_menu_text" translate="label" type="text" sortOrder="4" showInDefault="3" showInWebsite="1" showInStore="1">
                    <label>Main Menu Text Color</label>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/main_menu_text.png">]]>
                    </tooltip>
                </field>

                <field id="main_menu_text_hover" translate="label,comment" type="text" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Main Menu Text Hover Color</label>
                    <comment>Leave empty if no highlighting for active menu items is required.</comment>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/main_menu_text_hover.png">]]>
                    </tooltip>
                </field>

                <field id="submenu_background_color" translate="label" type="text" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Submenu Background Color</label>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/submenu_background_color.png">]]>
                    </tooltip>
                </field>

                <field id="submenu_background_image" translate="label" type="image" sortOrder="6" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Submenu Background Image</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <upload_dir config="system/filesystem/media" scope_info="1">amasty/megamenu/submenu_background_image</upload_dir>
                    <base_url type="media" scope_info="1">amasty/megamenu/submenu_background_image</base_url>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/submenu_background_image.png">]]>
                    </tooltip>
                </field>

                <field id="submenu_text" translate="label" type="text" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Submenu Text Color</label>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/submenu_text.png">]]>
                    </tooltip>
                </field>

                <field id="submenu_text_hover" translate="label" type="text" sortOrder="8" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Submenu Text Hover Color</label>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/submenu_text_hover.png">]]>
                    </tooltip>
                </field>

                <field id="current_category_color" translate="label" type="text" sortOrder="9" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Current Category Text Color</label>
                    <comment>Highlight the category on the page you are currently on</comment>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/current_category_color.png">]]>
                    </tooltip>
                </field>

                <field id="toggle_icon_color" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Close and Toggle Buttons Color</label>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/toggle_icon_color.png">]]>
                    </tooltip>
                </field>

                <field id="hamburger_icon_color" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Hamburger Button Color</label>
                    <validate>color validate-no-html-tags</validate>
                    <frontend_model>Amasty\MegaMenuLite\Block\Adminhtml\System\Config\Field\Color</frontend_model>
                    <depends>
                        <field id="color_template" negative="1">blank</field>
                    </depends>
                    <tooltip>
                        <![CDATA[<img src="Amasty_MegaMenuLite::images/toggle_icon_color.png">]]>
                    </tooltip>
                </field>
            </group>
        </section>
    </system>
</config>
