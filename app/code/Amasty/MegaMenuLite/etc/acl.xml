<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Amasty_MegaMenuLite::config" title="Amasty Mega Menu" sortOrder="130507"/>
                        </resource>
                    </resource>
                </resource>
                <resource id="Magento_Backend::content">
                    <resource id="Amasty_MegaMenu::menu" title="Amasty Mega Menu" translate="title" sortOrder="10">
                        <resource id="Amasty_MegaMenu::menu_links" title="Custom Menu Items" translate="title" sortOrder="10"/>
                        <resource id="Amasty_MegaMenu::menu_builder" title="Menu Builder" translate="title" sortOrder="20"/>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
