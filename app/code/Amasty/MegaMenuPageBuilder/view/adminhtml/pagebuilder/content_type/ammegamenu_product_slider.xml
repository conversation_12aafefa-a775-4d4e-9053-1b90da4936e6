<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Amasty Mega Menu PageBuilder for Magento 2 (System)
 */-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_PageBuilder:etc/content_type.xsd">
    <type name="ammegamenu_product_slider"
          label="Product Slider"
          component="Magento_PageBuilder/js/content-type"
          preview_component="Magento_PageBuilder/js/content-type/products/preview"
          form="ammegamenu_product_slider_form"
          menu_section="add_content"
          icon="icon-pagebuilder-products"
          sortOrder="100"
          translate="label">
        <children default_policy="deny"/>
        <appearances>
            <appearance default="true"
                        name="grid"
                        preview_template="Magento_PageBuilder/content-type/products/grid/preview"
                        master_template="Magento_PageBuilder/content-type/products/grid/master"
                        reader="Magento_PageBuilder/js/master-format/read/configurable">
                <elements>
                    <element name="main">
                        <style name="text_align" source="text_align"/>
                        <style converter="Magento_PageBuilder/js/converter/style/border-style" name="border" source="border_style"/>
                        <style converter="Magento_PageBuilder/js/converter/style/color" name="border_color" source="border_color"/>
                        <style converter="Magento_PageBuilder/js/converter/style/border-width" name="border_width" source="border_width"/>
                        <style converter="Magento_PageBuilder/js/converter/style/remove-px" name="border_radius" source="border_radius"/>
                        <style name="display" source="display" converter="Magento_PageBuilder/js/converter/style/display" preview_converter="Magento_PageBuilder/js/converter/style/preview/display"/>
                        <style name="margins" storage_key="margins_and_padding" reader="Magento_PageBuilder/js/property/margins" converter="Magento_PageBuilder/js/converter/style/margins"/>
                        <style name="padding" storage_key="margins_and_padding" reader="Magento_PageBuilder/js/property/paddings" converter="Magento_PageBuilder/js/converter/style/paddings"/>
                        <attribute source="data-content-type" name="name"/>
                        <attribute source="data-appearance" name="appearance"/>
                        <html name="html" preview_converter="Magento_PageBuilder/js/converter/attribute/preview/store-id"/>
                        <css name="css_classes"/>
                    </element>
                </elements>
                <converters>
                    <converter component="Amasty_MegaMenuPageBuilder/js/content-type/products/mass-converter/widget-directive" name="widget_directive">
                        <config>
                            <item name="html_variable" value="html"/>
                        </config>
                    </converter>
                </converters>
            </appearance>
        </appearances>
    </type>
</config>
