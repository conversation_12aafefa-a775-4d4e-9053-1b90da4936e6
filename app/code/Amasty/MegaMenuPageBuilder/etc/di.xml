<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Amasty Mega Menu PageBuilder for Magento 2 (System)
 */-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\PageBuilder\Model\Stage\RendererPool">
        <arguments>
            <argument name="renderers" xsi:type="array">
                <item name="ammega_menu_widget" xsi:type="object">Amasty\MegaMenuPageBuilder\Model\Renderer\Widget</item>
                <item name="ammegamenu_product_slider" xsi:type="object">Magento\PageBuilder\Model\Stage\Renderer\WidgetDirective</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponentFactory">
        <plugin name="Amasty_MegaMenuPageBuilder::remove_field" type="Amasty\MegaMenuPageBuilder\Plugin\Framework\View\Element\UiComponentFactory" />
    </type>
    <type name="Amasty\MegaMenu\Block\Product\ProductsSlider">
        <arguments>
            <argument name="productCollectionFactory" xsi:type="object">pageBuilderProductCollectionFactory</argument>
        </arguments>
    </type>
</config>
