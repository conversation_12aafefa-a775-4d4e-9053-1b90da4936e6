<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Infinite Scroll for Magento 2
 */-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- leave empty to compatibility wiht old versions -->

    <virtualType name="AjaxScrollProductCounterConfig" type="Amasty\Base\Block\Adminhtml\System\Config\Form\Field\Promo\PromoField">
        <arguments>
            <argument name="moduleName" xsi:type="string">Amasty_AjaxScrollSubscriptionFunctionality</argument>
            <argument name="promoConfig" xsi:type="array">
                <item name="promoLink" xsi:type="string"><![CDATA[https://amasty.com/amcustomer/account/products/?utm_source=extension&utm_medium=backend&utm_campaign=subscribe_scroll]]></item>
                <item name="comment" xsi:type="string">
                    <![CDATA[The functionality is available as part of an active product subscription or support subscription.
                    To upgrade and obtain functionality please follow the
                    <a href="https://amasty.com/amcustomer/account/products/?utm_source=extension&utm_medium=backend&utm_campaign=subscribe_scroll" target="_blank">link</a>.
                    Than you can find the 'amasty/module-ajax-scroll-subscription-functionality'
                    package for installation in composer suggest.]]>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
