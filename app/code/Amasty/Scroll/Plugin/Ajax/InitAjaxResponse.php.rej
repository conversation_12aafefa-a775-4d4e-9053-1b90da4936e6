--- vendor/amasty/ajax-scroll/Plugin/Ajax/InitAjaxResponse.php
+++ vendor/amasty/ajax-scroll/Plugin/Ajax/InitAjaxResponse.php
@@ -20,9 +20,7 @@ class InitAjaxResponse extends AjaxAbstract
      */
     public function afterExecute($controller, $page = null)
     {
-        if (!$this->isAjax() || !$page instanceof Page) {
-            return $page;
-        }
+        return $page;
 
         $this->setPage($page);
         $responseData = $this->getAjaxResponseData();
