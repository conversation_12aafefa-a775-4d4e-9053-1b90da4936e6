<?php

declare(strict_types=1);

/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Infinite Scroll for Magento 2
 */

namespace Amasty\Scroll\Model;

use Amasty\Base\Model\ConfigProviderAbstract;

class ConfigProvider extends ConfigP<PERSON>iderAbstract
{
    public const PRODUCT_CONTAINER_GROUP = 'advanced/product_container_group';

    /**
     * @var string
     */
    protected $pathPrefix = 'amasty_scroll/';

    public function getProductContainerGroup(?int $storeId = null): string
    {
        $originSelectors = (string)$this->getValue(self::PRODUCT_CONTAINER_GROUP, $storeId);

        //compatibility with Amasty_PromoBanners
        $selectors = ($originSelectors === null) ? ['.products.wrapper'] : explode(',', $originSelectors);
        foreach ($selectors as &$selector) {
            $selector = rtrim($selector);
            $selector .= ':not(.amasty-banners)';
        }

        return implode(',', $selectors);
    }
}
