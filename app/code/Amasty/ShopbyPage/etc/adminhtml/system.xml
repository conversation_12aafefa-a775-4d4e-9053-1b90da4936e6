<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop by Page for Magento 2 (System)
 */-->
<!--
/**
 * Copyright © 2015 Amasty. All rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="amshopby_page" translate="label" type="text" sortOrder="91315" showInDefault="1" showInWebsite="1" showInStore="1">
            <resource>Amasty_ShopbyPage::config</resource>
            <class>separator-top</class>
            <label>Improved Layered Navigation: Pages</label>
            <tab>amasty</tab>
            <group id="general" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="page_match_strict" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Use Strict Pages Matching</label>
                    <comment><![CDATA['Pages' feature modifies filtered results meta tags and in-page content in accordance with the selected filters. If a strict mode is enabled any additional filter, like price or weight will cancel these changes]]></comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
