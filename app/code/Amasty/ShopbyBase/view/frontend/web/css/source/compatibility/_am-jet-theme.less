//
//  Amasty JetTheme compatibility
//  _____________________________________________

//
//  Imports
//  ---------------------------------------------

@import '../module/_mixins';

//
//  Variables
//  ---------------------------------------------

@amshopbybase__swatch__size__m: 25px;
@amshopbybase__swatch__size__l: 40px;

@ambrands-slider-arrow__color: @color-green;
@ambrands-slider-arrow__hover__color: #a2d331;
@ambrands-slider-arrow__active__color: #67871d;
@ambrands-slider-arrow__disabled__color: #d9ecac;
@ambrands-slider-title__color: @color-black;
@ambrands-slider-bullet__color: @color-gray-light;
@ambrands-slider-bullet__active__color: @color-green;

//
//  Common
//  ---------------------------------------------

& when (@media-common = true) {
    .filter-options [class*='am-filter-items-'] {
        & {
            padding: 0 4px;
        }

        a[class*='am-filter-item-'] {
            display: inline-block;
        }

        a.am-swatch-link {
            overflow: inherit;
        }

        .item {
            &:not(.swatch-option-link-layered) {
                .am-flex(none, center);
            }

            input[type='radio'],
            input[type='checkbox'] {
                .am-visually-hidden-reset;

                margin: 0 5px 0 0;
                position: relative;
                top: -1px;
            }
        }
    }

    .am-shopby-form {
        .swatch-option {
            min-height: @amshopbybase__swatch__size__m;
            min-width: @amshopbybase__swatch__size__m;
        }

        .swatch-option.color:hover:before {
            border-color: @color-black;
        }

        .swatch-attribute .swatch-option:not(.text) {
            & {
                border: 0;
                border-radius: 50%;
                box-shadow: inset 1px 2px 3px rgba(0, 0, 0, .16);
                box-sizing: border-box;
                height: @amshopbybase__swatch__size__m;
                min-height: @amshopbybase__swatch__size__m;
                min-width: @amshopbybase__swatch__size__m;
                padding: 0;
                position: relative;
                width: @amshopbybase__swatch__size__m;
            }

            &:before {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: inherit;
                content: '';
                display: inline-block;
                height: @amshopbybase__swatch__size__m;
                min-width: auto;
                opacity: 0;
                transform: scale(1.15);
                transition: all .4s ease-in-out;
                width: @amshopbybase__swatch__size__m;
                z-index: 10;
            }

            &:hover:before,
            &.selected:before {
                border-color: @color-black;
                opacity: 1;
            }

            &.selected {
                background-color: #fff;
            }
        }

        .swatch-option.color:before {
            height: @amshopbybase__swatch__size__m;
            width: @amshopbybase__swatch__size__m;
        }

        .swatch-option:not(.color) {
            min-height: @amshopbybase__swatch__size__l;
            min-width: @amshopbybase__swatch__size__l;
        }
    }

    .filter-options .items.am-filter-items-rating [class*='am-filter-item-'] {
        &, > .label {
            .am-flex(none, center);
        }

        > .count {
            margin-left: 5px;
        }
    }

    .block.amshopby-morefrom .block-content .product-items {
        & {
            &:extend(.amtheme-products-secondary.-in-widget all);

            max-width: inherit;
        }

        .action.tocart {
            .lib-button-primary();
            .lib-button-s();

            border-radius: 0;
            width: 100%;
        }
    }

    .ambrand-index-index,
    .amshopby-index-index {
        .products.wrapper ~ .toolbar {
            &:extend(.catalog-category-view .products.wrapper ~ .toolbar all);
        }

        .top-toolbar > .toolbar {
            &:extend(.catalog-category-view .top-toolbar > .toolbar all);
        }

        .toolbar-products {
            &:extend(.catalog-category-view .toolbar-products all);
        }
    }

    .amshopby-filter-current {
        .amshopby-filter-value {
            font-weight: @font-weight__bold;
        }

        .amshopby-item.item {
            background: transparent;
        }
    }

    .swatch-layered .swatch-option-link-layered .am-swatch-link {
        & {
            align-items: center;
        }

        .swatch-option.color:first-child {
            margin-left: 0;
        }

        .swatch-option + .am-shopby-swatch-label {
            margin-left: 10px;
            max-width: inherit;
        }
    }

    .amtheme-productinfo-wrap .block.amshopby-morefrom {
        & {
            margin-top: 60px;
        }

        .block-title > strong {
            font-weight: @font-weight__regular;
        }
    }

    .filter-options-content {
        .am-filter-price {
            font-size: 1.4rem;
            text-align: center;
        }

        .am-swatch-wrapper .swatch-option.image,
        .swatch-attribute.size .swatch-option.selected,
        .swatch-attribute.manufacturer .swatch-option.selected {
            background-size: auto;
        }

        .swatch-attribute.color .swatch-option {
            .am-flex(none, center);

            font-size: 1.2rem;
            min-width: @amshopbybase__swatch__size__m;
            width: auto;
        }
    }

    .range.am-fromto-widget .am-filter-go {
        &:extend(.action);

        height: 4rem;
        padding: 5px 15px;
        width: auto;
    }

    .amshopby-filter-current .am-swatch-wrapper.item.swatch-option-link-layered {
        & {
            background: transparent;
            margin: 0;
            padding: 0;
        }

        > .am-swatch-link {
            .am-flex(none, center);

            margin: 4px;
        }

        .swatch-option {
            width: auto;
        }

        .swatch-option.image {
            background-size: 100%;
        }

        .swatch-option + .am-shopby-swatch-label {
            line-height: 1;
        }
    }

    .amshopby-filter-current {
        .swatch-attribute {
            .am-flex();

            margin: 0;
        }

        .swatch-option.text {
            font-weight: @font-weight__regular;
            line-height: 1;
            min-height: @amshopbybase__swatch__size__m;
            min-width: @amshopbybase__swatch__size__m;
            padding: 4px;
            text-transform: none;
        }

        .amshopby-filter-value,
        .amshopby-filter-name {
            line-height: 1.2;
        }
    }

    .amslider .swiper-slide .ambrands-label {
        font-weight: 500;
    }
}

//
//  Mobile
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .filter-options-content .am-shopby-form {
        .swatch-option {
            min-height: @amshopbybase__swatch__size__l;
            min-width: @amshopbybase__swatch__size__l;
        }

        .swatch-attribute .swatch-option:not(.text) {
            & {
                height: @amshopbybase__swatch__size__l;
                min-height: @amshopbybase__swatch__size__l;
                min-width: @amshopbybase__swatch__size__l;
                width: @amshopbybase__swatch__size__l;
            }

            &:before {
                height: @amshopbybase__swatch__size__l;
                width: @amshopbybase__swatch__size__l;
            }
        }

        .swatch-option.color:before {
            height: @amshopbybase__swatch__size__l;
            width: @amshopbybase__swatch__size__l;
        }
    }

    .filter-options-content .swatch-attribute.color .swatch-option {
        min-width: @amshopbybase__swatch__size__l;
    }

    .swatch-layered[class*='am-filter-items-attr'] .swatch-option-link-layered {
        margin: 0 10px 10px 0 !important; // Override Magento important directive
    }
}

//
//  Desktop
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .amasty-jet-theme {
        .block.amshopby-morefrom .block-content .product-items {
            &:extend(.amtheme-products-secondary.-in-widget all);

            margin: 0 -@indent__s;
        }

        .amasty-catalog-topnav .filter-options-title {
            & {
                .am-flex();
            }

            &:after {
                margin: 0 0 0 5px;
                position: static;
                transform: none;
            }
        }

        .amasty-catalog-topnav .filter-options-item.-active .filter-options-title:after {
            transform: rotateZ(180deg);
        }

        .navigation .ambrands-link {
            &:extend(.navigation .category-item > a all);
        }

        &.ambrand-index-index,
        &.amshopby-index-index {
            .top-toolbar > .toolbar.toolbar-products {
                align-items: center;
                flex-direction: row;
                margin-top: 0;
            }
        }
    }

    .ambrand-index-index,
    .amshopby-index-index,
    .catalog-category-view {
        .top-toolbar > .toolbar {
            margin-top: 50px;
        }
    }

    .ambrand-index-index .products.wrapper ~ .toolbar .limiter,
    .amshopby-index-index .products.wrapper ~ .toolbar .limiter {
        &:extend(.catalog-category-view .products.wrapper ~ .toolbar .limiter);
    }

    .swatch-layered[class*='am-filter-items-attr'] .swatch-option-link-layered {
        margin: 0 5px 10px 0 !important; // Override Magento important directive
    }

    .am-shopby-form .swatch-attribute .swatch-option:not(.text):before {
        transform: scale(1.24);
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .amasty-jet-theme .ambrands-list-popup {
        & {
            z-index: 70;
        }

        .ambrands-list-container {
            .am-flex();
        }
    }

    .amasty-jet-theme .block.amshopby-morefrom .block-content .product-items {
        &:extend(.amtheme-products-secondary.-in-widget all);
    }
}
