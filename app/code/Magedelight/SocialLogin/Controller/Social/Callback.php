<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Controller\Social;

use Hybridauth\Exception\Exception as ExceptionFactory;
use Hybridauth\Exception\InvalidArgumentException;
use Hybridauth\Exception\UnexpectedValueException;
use Hybridauth\Hybridauth;
use Hybridauth\HttpClient;
use Hybridauth\Storage\Session as SessionFactory;

use Exception;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Controller\Result\Raw;
use Magento\Framework\Controller\Result\RawFactory;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\AuthenticationException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Framework\Stdlib\Cookie\FailureToSendException;
use Magento\Framework\Stdlib\Cookie\PhpCookieManager;
use Magento\Store\Api\Data\StoreInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\SocialLogin\Helper\SocialMedia as SocialHelper;
use Magedelight\SocialLogin\Model\SocialMedia as Social;
use Psr\Log\LoggerInterface;
use Magedelight\SocialLogin\Model\SocialProvider\Apple;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;

class Callback extends AbstractSocial
{
    /** @var LoggerInterface */
    protected $logger;

    /** @var Session */
    protected $session;

    /** @var StoreManagerInterface */
    protected $storeManager;

    /** @var SocialHelper */
    protected $apiHelper;

    /** @var Social */
    protected $apiObject;

    /** @var RawFactory */
    protected $resultRawFactory;

    /** @var $cookieMetadataManager */
    protected $cookieMetadataManager;

    /** @var $cookieMetadataFactory */
    protected $cookieMetadataFactory;

    /** @var RedirectFactory */
    protected $resultPageFactory;

    /** @var Apple */
    private $appleProvider;

    /** @var ScopeConfigInterface */
    protected $scopeConfig;
    
    /** @var TransportBuilder */
    protected $transportBuilder;

    /** @var StateInterface */
    protected $inlineTranslation;
    /**
     * Callback constructor.
     * @param Context $context
     * @param StoreManagerInterface $storeManager
     * @param SocialHelper $apiHelper
     * @param Social $apiObject
     * @param Session $customerSession
     * @param RawFactory $resultRawFactory
     * @param LoggerInterface $logger
     * @param RedirectFactory $resultPageFactory
     * @param Apple $appleProvider
     */
    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        SocialHelper $apiHelper,
        Social $apiObject,
        Session $customerSession,
        RawFactory $resultRawFactory,
        LoggerInterface $logger,
        RedirectFactory $resultPageFactory,
        Apple $appleProvider,
        ScopeConfigInterface $scopeConfig,
        TransportBuilder $transportBuilder,
        StateInterface $inlineTranslation
    ) {
        $this->storeManager = $storeManager;
        $this->apiHelper = $apiHelper;
        $this->apiObject = $apiObject;
        $this->session = $customerSession;
        $this->resultRawFactory = $resultRawFactory;
        $this->resultPageFactory = $resultPageFactory;
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
        $this->transportBuilder = $transportBuilder;
        $this->inlineTranslation = $inlineTranslation;
        parent::__construct(
            $context,
            $storeManager,
            $apiHelper,
            $apiObject,
            $customerSession,
            $resultRawFactory
        );
        $this->appleProvider = $appleProvider;
    }

    /**
     * @inheritdoc
     * @throws LocalizedException
     * @throws LocalizedException
     * @throws UnexpectedValueException
     * @throws InvalidArgumentException
     * @throws UnexpectedValueException
     */
    public function execute()
    {
        $post = $this->getRequest()->getPostValue();

        if ($this->session->isLoggedIn()) {
            $type = $this->session->getData('type', true);
            if (isset($post['type'])) {
                $type = $post['type'];
            }

            if (!$type) {
                $this->_forward('noroute');
                return;
            }

            try {
                $userProfile = $this->apiObject->getUserProfile($type);
                if (!$userProfile->identifier) {
                    return $this->emailRedirect($type);
                }
            } catch (\ExceptionFactory $e) {
                $_msg = $e->getMessage() . ' Callback';
                $this->setBodyResponse($_msg);
                return;
            } catch (InvalidArgumentException $e) {
                throw new InvalidArgumentException(__($e->getMessage()));
            } catch (UnexpectedValueException $e) {
                throw new UnexpectedValueException(__($e->getMessage()));
            } catch (LocalizedException $e) {
                throw new LocalizedException(__($e->getMessage()));
            }

            $customer = $this->apiObject->getCustomerBySocial($userProfile->identifier, $type);
            $post = $this->getRequest()->getPostValue();
            if (isset($post['custom'])) {
                $userProfile->email=$post['email'];
                $userProfile->identifier=$post['identifier'];
            }

            if ($type=='apple') {
                if (!$customer->getId()) {
                    if (!$userProfile->email) {
                        $resultPage = $this->resultPageFactory->create();
                        $url = $this->storeManager->getStore()->getUrl(
                            'customer/account/login',
                            [
                                '_current' => true,
                                'identifier' => $userProfile->identifier,
                                'type' => $type
                            ]
                        );
                        return $resultPage->setUrl($url);
                    }
                }
            }
            if (!$customer->getId()) {
                if (!$userProfile->email) {
                    $this->session->setUserProfile($userProfile);
                    return $this->_appendJs(
                        sprintf("<script>window.close();window.opener.fakeEmailCallback('%s');</script>", $type)
                    );
                }
                $this->logger->info("Customer not exists new customer will be created");
                $customer = $this->createCustomerProcess($userProfile, $type);
                if ($customer && $customer->getId()) {
                    $this->sendWellcomeEmail($customer);
                    $this->sendSetPasswordEmail($customer);
                }
            }
            $this->refresh($customer);

            return $this->_customAppendJs();
        }

        $type = $this->session->getData('social_type', true);
        if (isset($post['type'])) {
            $type = $post['type'];
        }

        if (isset($post['state'])) {
            $type = "apple";
        }

        if (!$type) {
            $this->_forward('noroute');
            return;
        }
        try {
            if ($type != 'apple') {
                $userProfile = $this->apiObject->getUserProfile($type);
            } else {
                if (isset($post['code'])) {
                    $this->session->setData('apple_code', $post['code']);
                }
                $userProfile = $this->appleProvider->getUserProfile($type, $post);
            }
            if (!$userProfile->identifier) {
                return $this->emailRedirect($type);
            }
        } catch (\ExceptionFactory $e) {
            $_msg = $e->getMessage() . ' Callback';
            $this->setBodyResponse($_msg);
            return;
        }
        $customer = $this->apiObject->getCustomerBySocial($userProfile->identifier, $type);
        $post = $this->getRequest()->getPostValue();
        if (isset($post['custom'])) {
            $userProfile->email=$post['email'];
            $userProfile->identifier=$post['identifier'];
        }

        if ($type == 'apple') {
            if (!$customer->getId()) {
                if (!$userProfile->email) {
                    $resultPage = $this->resultPageFactory->create();
                    $url = $this->storeManager->getStore()->getUrl(
                        'customer/account/login',
                        [
                            '_current' => true,
                            'identifier' => $userProfile->identifier,
                            'type' => $type
                        ]
                    );
                    return $resultPage->setUrl($url);
                }
            }
        }
        if (!$customer->getId()) {
            if ($type!='apple') {
                if (!$userProfile->email) {
                    $this->session->setUserProfile($userProfile);
                    return $this->_appendJs(
                        sprintf("<script>window.close();window.opener.fakeEmailCallback('%s');</script>", $type)
                    );
                }
            }
            $this->logger->info("Customer not exists new customer will be created");
            $customer = $this->createCustomerProcess($userProfile, $type);
            if ($customer && $customer->getId()) {
                $this->sendWellcomeEmail($customer);
                $this->sendSetPasswordEmail($customer);
            }
        }
        $this->refresh($customer);

        return $this->_appendJs();
    }

    protected function sendWellcomeEmail($customer, $emailType = 'welcome')
    {
        try {
            $this->inlineTranslation->suspend();
            $templateIdentifier = 'customer_create_account_email_template'; // default template
            $senderName = $this->scopeConfig->getValue('trans_email/ident_general/name', \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
            $senderEmail = $this->scopeConfig->getValue('trans_email/ident_general/email', \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
            $sender = ['name' => $senderName, 'email' => $senderEmail];
            $sentToEmail = $customer->getEmail();
            $sentToName = $customer->getFirstname() . ' ' . $customer->getLastname();
            $transport = $this->transportBuilder
                ->setTemplateIdentifier($templateIdentifier)
                ->setTemplateOptions([
                    'area' => 'frontend',
                    'store' => $this->storeManager->getStore()->getId()
                ])
                ->setTemplateVars(['customer' => $customer])
                ->setFromByScope($sender)
                ->addTo($sentToEmail, $sentToName)
                ->getTransport();
            $transport->sendMessage();
            $this->inlineTranslation->resume();
            $this->logger->info("Email ($emailType) sent to " . $sentToEmail);
        } catch (\Exception $e) {
            $this->logger->critical('Error sending email (' . $emailType . '): ' . $e->getMessage());
        }
    }

    protected function sendSetPasswordEmail($customer)
    {
        try {
            $this->inlineTranslation->suspend();
            $senderName = $this->scopeConfig->getValue('trans_email/ident_general/name', \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
            $senderEmail = $this->scopeConfig->getValue('trans_email/ident_general/email', \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
            $sender = [
                'name' => $senderName,
                'email' => $senderEmail,
            ];
            $sentToEmail = $customer->getEmail();
            $sentToName = $customer->getFirstname() . ' ' . $customer->getLastname();
            $transport = $this->transportBuilder
                ->setTemplateIdentifier('customer_password_forgot_email_template')
                ->setTemplateOptions([
                    'area' => 'frontend',
                    'store' => $this->storeManager->getStore()->getId()
                ])
                ->setTemplateVars(['customer' => $customer])
                ->setFromByScope($sender)
                ->addTo($sentToEmail, $sentToName)
                ->getTransport();
            $transport->sendMessage();
            $this->inlineTranslation->resume();
            $this->logger->info("Password set email sent to " . $sentToEmail);
        } catch (\Exception $e) {
            $this->logger->critical('Error sending password set email: ' . $e->getMessage());
        }
    }

}
