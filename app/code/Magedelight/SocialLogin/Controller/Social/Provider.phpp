<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Controller\Social;

use Exception;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Controller\Result\Raw;
use Magento\Framework\Controller\Result\RawFactory;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Framework\Stdlib\Cookie\FailureToSendException;
use Magento\Framework\Stdlib\Cookie\PhpCookieManager;
use Magento\Store\Api\Data\StoreInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\SocialLogin\Helper\SocialMedia as SocialHelper;
use Magedelight\SocialLogin\Model\SocialMedia as Social;
use Psr\Log\LoggerInterface;
/**
 * Class Login
 * @package Magedelight\SocialLogin\Controller\Social
 */
class Provider extends AbstractSocial
{
    protected $logger;

    /**
     * @type Session
     */
    protected $session;

    /**
     * @type StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @type SocialHelper
     */
    protected $apiHelper;



    /**
     * @type
     */
    protected $cookieMetadataManager;

    /**
     * @type
     */
    protected $cookieMetadataFactory;

    /**
     * @var RawFactory
     */
    protected $resultRawFactory;

    /**
     * Login constructor.
     *
     * @param Context $context
     * @param StoreManagerInterface $storeManager
     * @param SocialHelper $apiHelper
     * @param Social $apiObject
     * @param Session $customerSession
     * @param RawFactory $resultRawFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        SocialHelper $apiHelper,
        Social $apiObject,
        Session $customerSession,
        RawFactory $resultRawFactory,
        LoggerInterface $logger
    )
    {
        $this->storeManager = $storeManager;
        $this->apiHelper = $apiHelper;
        $this->apiObject = $apiObject;
        $this->session = $customerSession;
        $this->resultRawFactory = $resultRawFactory;
        $this->logger = $logger;

        parent::__construct(
            $context,
            $storeManager,
            $apiHelper,
            $apiObject,
            $customerSession,
            $resultRawFactory
        );
    }

    public function execute(){

        if ($this->session->isLoggedIn()) {
            $this->_redirect('customer/account');
            return;
        }

        $type = $this->getRequest()->getParam('type');

        if (!$type) {
            $this->_forward('noroute');
            return;
        }

        $this->session->setData('type', $type);

        if ($isCheckout = $this->getRequest()->getParam('is_checkout')) {
            $this->session->setData('social_in_checkout', $isCheckout);
        }

        if ($isCheckout = $this->getRequest()->getParam('is_manage')) {
            $this->session->setData('social_in_manage', $isCheckout);
        }

        try {

            $this->apiObject->getCustomUserProfile($type);
        } catch (\Exception $e) {
            $this->setBodyResponse($e->getMessage());
            return;
        }
    }
}
