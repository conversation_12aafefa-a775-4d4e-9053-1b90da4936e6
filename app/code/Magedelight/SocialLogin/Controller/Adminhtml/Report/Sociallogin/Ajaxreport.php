<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Controller\Adminhtml\Report\SocialLogin;

class Ajaxreport extends \Magento\Backend\App\Action
{
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    protected $resultJsonFactory;

    /**
     * @var \Magedelight\SocialLogin\Model\Report
     */
    protected $reportModel;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory
     * @param \Magedelight\SocialLogin\Model\Report $reportModel
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory,
        \Magedelight\SocialLogin\Model\Report $reportModel
    ) {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->reportModel = $reportModel;
    }

    /**
     * Execute ajax report
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        $storeId = $this->getRequest()->getParam('store_id');
        $reportType = $this->getRequest()->getParam('report_type');
        $from = $this->getRequest()->getParam('from');
        $to = $this->getRequest()->getParam('to');
        $group = $this->getRequest()->getParam('group');

        $reportData = $this->reportModel->getReportFromDateRange([
            'storeId'   => $storeId,
            'reportType' => $reportType,
            'from'       => $from,
            'to'         => $to,
            'group'      => $group
        ]);

        $result = $this->resultJsonFactory->create();

        $result->setData([
            'from' => $this->getRequest()->getParam('from'),
            'to' => $this->getRequest()->getParam('to'),
            'reportData' => $reportData
        ]);

        return $result;
    }
}
