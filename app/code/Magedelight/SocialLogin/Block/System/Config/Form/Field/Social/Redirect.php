<?php

/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Block\System\Config\Form\Field\Social;

use Magento\Framework\Data\Form\Element\AbstractElement;
use Magento\Config\Block\System\Config\Form\Field as FormField;
use Magedelight\SocialLogin\Helper\Data as Helper;
use Magento\Backend\Block\Template\Context;

/**
 * Backend system config datetime field renderer.
 */
class Redirect extends FormField
{
    /**
     * @var Helper
     */
    public $helper;

    /**
     * Construct
     *
     * @param Context $context
     * @param Helper $helper
     */
    public function __construct(
        Context $context,
        Helper $helper
    ) {
        $this->helper = $helper;
        parent::__construct($context);
    }

    /**
     * GetElementHtml
     *
     * @param AbstractElement $element
     *
     * @return string
     */
    // @codingStandardsIgnoreStart
    protected function _getElementHtml(AbstractElement $element)
    {
        $html_id = $element->getHtmlId();
        $redirectUrl = $this->helper->getCustomAuthUrl();
        //$redirectUrl = str_replace('index.php/', '', $redirectUrl);
        $html = '<input style="opacity:1;" readonly id="'.$html_id.'" '
                . 'class="input-text admin__control-text" '
                . 'value="'.$redirectUrl.'" onclick="this.select()" '
                . 'type="text">';

        return $html;
    }
    // @codingStandardsIgnoreEnd
}
