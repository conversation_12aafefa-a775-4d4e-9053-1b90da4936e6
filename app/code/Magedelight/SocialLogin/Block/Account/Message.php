<?php

/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Block\Account;

class Message extends \Magento\Framework\View\Element\Messages
{

    /**
     * Prepare Layout
     *
     * @return Message
     */
    public function _prepareLayout()
    {
        $this->addMessages($this->messageManager->getMessages(true));
        return parent::_prepareLayout();
    }
}
