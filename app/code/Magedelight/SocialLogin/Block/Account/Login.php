<?php

/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Block\Account;

class Login extends \Magento\Customer\Block\Form\Login
{
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $requestInterface;

    /**
     * Login constructor.
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Customer\Model\Url $customerUrl
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Customer\Model\Url $customerUrl,
        array $data = []
    ) {
        $this->requestInterface = $context->getRequest();
        parent::__construct($context, $customerSession, $customerUrl, $data);
    }

    /**
     * Prepare layout
     *
     * @return $this|Login
     */
    public function _prepareLayout()
    {
        $routeName      = $this->requestInterface->getRouteName();
        $controllerName = $this->requestInterface->getControllerName();
        $actionName     = $this->requestInterface->getActionName();
        if ($routeName == 'customer' && $controllerName == 'account' && $actionName == 'login') {
            $this->pageConfig->getTitle()->set(__('Customer Login'));
        }
        return $this;
    }
}
