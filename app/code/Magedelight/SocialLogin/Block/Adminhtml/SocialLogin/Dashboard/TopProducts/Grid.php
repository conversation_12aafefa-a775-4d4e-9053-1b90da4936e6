<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Block\Adminhtml\SocialLogin\Dashboard\TopProducts;

use Magedelight\SocialLogin\Model\ResourceModel\Social;

class Grid extends \Magento\Backend\Block\Dashboard\Grid
{
    /**
     * @var Social\CollectionFactory
     */
    protected $collectionFactory;

    /**
     * Construct
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Backend\Helper\Data $backendHelper
     * @param Social\CollectionFactory $collectionFactory
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Backend\Helper\Data $backendHelper,
        Social\CollectionFactory $collectionFactory,
        array $data = []
    ) {
        $this->collectionFactory = $collectionFactory;
        parent::__construct($context, $backendHelper, $data);
    }

    /**
     * Construct
     *
     * @return void
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('lastSubscriptionsGrid');
    }

    /**
     * PrepareCollection
     *
     * @return $this
     */
    protected function _prepareCollection()
    {
        $collection = $this->getGridCollection();

        $this->_eventManager->dispatch(
            'md_subscribenowpro_dashboard_top_subscription_products_grid_collection',
            [
                'collection' => &$collection
            ]
        );

        $this->addStoreFilterToCollection($collection);
        $this->setCollection($collection);
        return parent::_prepareCollection();
    }
    /**
     * GetGridCollection
     *
     * @return $this
     */
    public function getGridCollection()
    {
        $collection = $this->collectionFactory->create();
        $collection->getSelect()
            ->reset(\Magento\Framework\DB\Select::COLUMNS)
            ->columns([
                'MAX(id) AS social_id',
                'social_id',
                'COUNT(social_id) AS no_of_social',
                'MIN(user_email) AS user_email',
                "MIN(type) AS type",

            ])
            ->group('social_id')
            ->order([
                'no_of_social DESC',
                'social_id ASC'
            ]);

        //$collection->setOrder('no_of_subscription', 'DESC');

        return $collection;
    }

    /**
     * Prepares page sizes for dashboard grid with last 5 subscriptions
     *
     * @return void
     */
    protected function _preparePage()
    {
        $this->getCollection()->setPageSize($this->getParam($this->getVarNameLimit(), $this->_defaultLimit));
    }

    /**
     * PrepareColumns
     *
     * @return $this
     */
    protected function _prepareColumns()
    {
        $this->addColumn(
            'user_email',
            [
                'header' => __('User Email'),
                'sortable' => false,
                'index' => 'user_email'
            ]
        );

        $this->addColumn(
            'type',
            [
                'header' => __('Provider'),
                'sortable' => false,
                'index' => 'type'
            ]
        );

        $this->addColumn(
            'subscription_count',
            [
                'header' => __('Subscriptions'),
                'sortable' => false,
                'index' => 'no_of_social'
            ]
        );

        $this->setFilterVisibility(false);
        $this->setPagerVisibility(false);

        return parent::_prepareColumns();
    }

    /**
     * @inheritdoc
     */
    public function getRowUrl($row)
    {
        //return $this->getUrl('catalog/product/edit', ['id' => $row->getProductId()]);
        return $this->getUrl('mdsocial/report_sociallogin/dashboard');
    }
    /**
     * AddStoreFilterToCollection
     *
     * @param Collection $collection
     */
    public function addStoreFilterToCollection($collection)
    {
        if ($this->getRequest()->getParam('store')) {
            $collection->addFieldToFilter('store_id', $this->getRequest()->getParam('store'));
        } elseif ($this->getRequest()->getParam('website')) {
            $storeIds = $this->_storeManager->getWebsite($this->getRequest()->getParam('website'))->getStoreIds();
            $collection->addFieldToFilter('store_id', ['in' => $storeIds]);
        } elseif ($this->getRequest()->getParam('group')) {
            $storeIds = $this->_storeManager->getGroup($this->getRequest()->getParam('group'))->getStoreIds();
            $collection->addFieldToFilter('store_id', ['in' => $storeIds]);
        }
    }
}
