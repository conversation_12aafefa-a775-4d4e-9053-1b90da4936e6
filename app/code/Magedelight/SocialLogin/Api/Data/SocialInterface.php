<?php declare(strict_types=1);

/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>abs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Api\Data;

/**
 * Blog post interface.
 * @api
 * @since 1.0.0
 */
interface SocialInterface
{
    public const ID = 'id';
    public const SOCIAL_ID = 'social_id';
    public const USER_EMAIL = 'user_email';
    public const CUSTOMER_ID = 'customer_id';
    public const TYPE = 'type';
    public const STATUS = 'status';

    /**
     * Getid
     *
     * @return int
     */
    public function getId();

    /**
     * Setid
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

    /**
     * GetSocialId
     *
     * @return string
     */
    public function getSocialId();

    /**
     * SetSocialId
     *
     * @param string $social_id
     * @return $this
     */
    public function setSocialId($social_id);

    /**
     * GetUserEmail
     *
     * @return string
     */
    public function getUserEmail();

    /**
     * SetUserEmail
     *
     * @param string $user_email
     * @return $this
     */
    public function setUserEmail($user_email);

    /**
     * GetCustomerId
     *
     * @return string
     */
    public function getCustomerId();

    /**
     * SetCustomerId
     *
     * @param string $customer_id
     * @return $this
     */
    public function setCustomerId($customer_id);

    /**
     * GetType
     *
     * @return string
     */
    public function getType();

    /**
     * SetType
     *
     * @param string $type
     * @return $this
     */
    public function setType($type);

    /**
     * GetStatus
     *
     * @return string
     */
    public function getStatus();

    /**
     * SetStatus
     *
     * @param string $status
     * @return $this
     */
    public function setStatus($status);
}
