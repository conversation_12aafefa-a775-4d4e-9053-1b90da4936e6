<?php declare(strict_types=1);
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>s. All Rights reserved.
 */
namespace Magedelight\SocialLogin\Api;

use Magedelight\SocialLogin\Api\Data\SocialInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Blog post CRUD interface.
 * @api
 * @since 1.0.0
 */
interface SocialRepositoryInterface
{
    /**
     * GetById
     *
     * @param int $id
     * @return SocialInterface
     * @throws LocalizedException
     */
    public function getById(int $id): SocialInterface;

    /**
     * Save
     *
     * @param SocialInterface $post
     * @return SocialInterface
     * @throws LocalizedException
     */
    public function save(SocialInterface $post): SocialInterface;

    /**
     * DeleteById
     *
     * @param int $id
     * @return bool
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function deleteById(int $id): bool;
}
