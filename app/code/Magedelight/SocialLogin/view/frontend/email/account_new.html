<!--
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->

<!--@subject {{trans "Welcome to %store_name" store_name=$store.getFrontendName()}} @-->
<!--@vars {
"var store.getFrontendName()":"Store Name",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])":"Password Reset URL",
"var this.getUrl($store, 'customer/account/')":"Customer Account URL",
"var customer.email":"Customer Email",
"var customer.name":"Customer Name"
} @-->

{{template config_path="design/email/header_template"}}

<p class="greeting">{{trans "%name," name=$customer.name}}</p>
<p>{{trans "Welcome to %store_name." store_name=$store.getFrontendName()}}</p>
<p>
    {{trans
        'To sign in to our site, use these credentials during checkout or on the <a href="%customer_url">My Account</a> page:'

        customer_url=$this.getUrl($store,'customer/account/',[_nosid:1])
    |raw}}
</p>
<ul>
    <li><strong>{{trans "Email:"}}</strong> {{var customer.email}}</li>
    <li><strong>{{trans "Password:"}}</strong> <em>{{trans "Your email id is your temporary password"}}</em></li>
</ul>
<p>
    {{trans
        'Please Click <a href="%reset_url">here</a> to reset your password.'

        reset_url="$this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])"
    |raw}}
</p>
<p>{{trans "When you sign in to your account, you will be able to:"}}</p>
<ul>
    <li>{{trans "Proceed through checkout faster"}}</li>
    <li>{{trans "Check the status of orders"}}</li>
    <li>{{trans "View past orders"}}</li>
    <li>{{trans "Store alternative addresses (for shipping to multiple family members and friends)"}}</li>
</ul>

{{template config_path="design/email/footer_template"}}
