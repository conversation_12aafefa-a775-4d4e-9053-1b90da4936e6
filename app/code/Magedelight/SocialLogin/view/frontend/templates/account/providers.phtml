<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

/** @var Sociallogin $block */

use Magedelight\SocialLogin\Block\Account\Sociallogin;
use Magedelight\SocialLogin\Helper\Data;

$sociallogins = $block->getSortedLoginList();
$social_count = count($sociallogins);
$isloggedin = $block->isloggedin();
$socialLoginIsEnabled = $block->getEnable();
$buttonDesignTypeValue = $block->getButtonDesignTypeValue();

?>
<style>
    #md-login-social::after{
        display: none;
    }
</style>
<?php if ($social_count > 0 && $socialLoginIsEnabled == 1): ?>
    <div id="md-login-social" style="display: block;">
        <table class="md-login-social" id="myList">
            <thead>
            <tr>
                <th class="col">&nbsp;</th>
                <th class="col">&nbsp;</th>
                <th class="col actions">&nbsp;</th>
            </tr>
            </thead>
            <tbody>
            <?php $count = 1; ?>
            <?php foreach ($sociallogins as $loginvalue => $socialsite): ?>
                <?php
                $fontawesome = $block->getFontawesome($loginvalue);
                $buttonbgcolor = $block->getButtonBgColor($loginvalue);
                $buttonfontcolor = $block->getButtonFontColor($loginvalue);
                $iconcolor = $block->getIconColor($loginvalue);
                $iconbgcolor = $block->getIconBgColor($loginvalue);
                ?>

                <?php if ($block->isActive($loginvalue)): ?>
                    <?php $fontawesome = $block->getFontawesome($loginvalue); ?>
                    <tr>
                        <td>
                            <?php // @codingStandardsIgnoreStart ?>
                            <button type="button"
                                    class="<?= /* @noEscape */
                                    $loginvalue ?> <?= /* @noEscape */
                                    __($buttonDesignTypeValue); ?>"
                                style="color:<?= /* @noEscape */ '#' . $buttonfontcolor ?>; pointer-events: none; background: <?= /* @noEscape */ '#' . $buttonbgcolor; ?>">
                                <i class="fa <?= /* @noEscape */ $fontawesome; ?>"
                                    aria-hidden="true"
                                    style="color:<?= /* @noEscape */ '#' . $iconcolor ?>; background: <?= /* @noEscape */ '#' . $iconbgcolor; ?>"></i>
                            </button>
                            <?php // @codingStandardsIgnoreEnd ?>
                            <span>
                                <?php
                                $title = '';
                                if ($loginvalue == 'windowslive') {
                                    $title = 'Microsoft';
                                } elseif ($loginvalue == 'linkedin') {
                                     $title = 'LinkedIn';
                                } else {
                                    $title = $loginvalue;
                                }
                                ?>
                                <span><?= /* @noEscape */ __(ucfirst($title)) ?></span>
                            </span>
                        </td>
                        <td>
                            <div>
                                <?php $_display_name = $socialsite['display_name']??''; ?>
                                <?php $_id = $socialsite['id']??''; ?>
                                <?php if ($_display_name): ?>
                                    <strong><?=/* @noEscape */ __('Connected as %1', $_display_name); ?></strong>
                                <?php else: ?>
                                    <em><?= /* @noEscape */__('Not connected'); ?></em>
                                <?php endif; ?>
                            </div>
                            <div><?=/* @noEscape */ __('%1', $socialsite['created_at']??''); ?></div>
                        </td>
                        <td>
                            <?php if ($_display_name): ?>
                            <button class="action md_social_btn btn_social_md <?=  /* @noEscape */
                                     $loginvalue ?> <?= /* @noEscape */__($buttonDesignTypeValue); ?>"
                                    data-url="<?= /* @noEscape */ $block->getDisconnectLoginUrl($loginvalue, $_id) ?>"
                                    title="<?= /* @noEscape */ __('Login By ' . ucfirst($loginvalue)) ?>"
                                    data-text="<?= /* @noEscape */ __('Login By ' . ucfirst($loginvalue)) ?>"
                                    id="<?= /* @noEscape */ $loginvalue . '_btn' ?>"
                                    type="button"
                                    data-mage-init='{
                                    "socialProvider": {
                                    "url": "<?= /* @noEscape */ $block->getDisconnectLoginUrl($loginvalue, $_id) ?>",
                                    "label": "<?= /* @noEscape */ __('Login By ' . ucfirst($loginvalue)) ?>"
                                    }
                                    }'
                            >
                            <span>
                                <span><?= /* @noEscape */ __('Disconnect') ?></span>
                            </span>
                            </button>
                            <?php else: ?>
                            <button class="action md_social_btn btn_social_md <?=  /* @noEscape */
                                     $loginvalue ?> <?= /* @noEscape */__($buttonDesignTypeValue); ?>"
                                    data-url="<?= /* @noEscape */ $block->getConnectLoginUrl($loginvalue) ?>"
                                    title="<?= /* @noEscape */ __('Login By ' . ucfirst($loginvalue)) ?>"
                                    data-text="<?= /* @noEscape */ __('Login By ' . ucfirst($loginvalue)) ?>"
                                    id="<?= /* @noEscape */ $loginvalue . '_btn' ?>"
                                    type="button"
                                    data-mage-init='{
                                    "socialProvider": {
                                    "url": "<?= /* @noEscape */ $block->getConnectLoginUrl($loginvalue) ?>",
                                    "label": "<?= /* @noEscape */ __('Login By ' . ucfirst($loginvalue)) ?>"
                                    }
                                    }'
                            >
                            <span>
                                <span><?=  /* @noEscape */__('Connect') ?></span>
                            </span>
                            </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endif; ?>
                <?php $count++;?>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <script >
        require(['jquery','Magedelight_SocialLogin/js/popup','mage/translate'], function (jQuery, Popup) {
            //Popup function for social login buttons
            jQuery('.btn_social_md').each(function (index) {
                jQuery(this).on('click', function (e) {
                    var mdSocialPopup = new Popup();
                    mdSocialPopup.openPopup(
                        jQuery(this).attr('data-url'),
                        jQuery(this).attr('data-text'), 'height=500,width=300'
                    );
                });
            });
        });
    </script>

<!-- <?php else: ?>

    <style type="text/css">
        .md-social-popup #md_popup #md-login-form {
            width: 100% !important;
            border-right: 0 !important;
        }
    </style>

<?php endif; ?> -->
