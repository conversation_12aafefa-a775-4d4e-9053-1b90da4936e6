/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>s. All Rights reserved.
 */
define([
    "jquery",
    'mage/translate',
    'underscore',
    'Magento_Ui/js/modal/modal',
], function ($,$t, _,modal) {
    'use strict';

    $.widget('mage.mdLoginPopup', {
        mdoptions: {
            fields: {
                popupContainer: '[data-md-js="md-login-container"]',
                login:'a[href*="customer/account/login"]',
                createAccount:'a[href*="customer/account/create"]',
                mdTabContainer:'[data-md-js="md-tab-container"]',
                mdTabWrapper:'[data-md-js="md-tabs-wrapper"]',
                popupForgotLink:'#md-login-content .action.remind',
                mdTabWrapperForgot:'[data-md-js="md-tabs-wrapper-forgot"]',
                mdTabLink:'.md-link',
                moreButton:'.md-more-less',
                form:'[data-md-js="md-login-container"] .form',
                passwordInputType:'password',
                textInputType:'text',
                showPassword :'.md-popup #show-password',
                passwordSelector:'.md-popup #pass'
            }
        },

        _create: function () {
            this.initObservable();
        },

        initObservable: function () {

            var self = this;

            /* login links*/
            $(self.mdoptions.fields.login).prop('href', '#').on('click', function (event) {
                self.openPopupModal(0);
                event.preventDefault();
                $(".login-header").show();
                $(".register-header").hide();
                return false;
            });

            /* create account links*/
            $(self.mdoptions.fields.createAccount).prop('href', '#').on('click', function (event) {
                self.openPopupModal(1);
                $(self.mdoptions.fields.loginText).hide();
                $(self.mdoptions.fields.createAccountText).show();
                $(".register-header").show();
                $(".login-header").hide();
                event.preventDefault();
                return false;
            });

            /* forgot links*/
            $(self.mdoptions.fields.popupForgotLink).unbind('click').on('click', function (event) {
                self.toggleTabWrappers();
                event.preventDefault();
                return false;
            });

            /*popup tab links*/
            $(self.mdoptions.fields.mdTabLink).on('click',function (event) {
                if ($(this).attr('tabindex')==0) {
                    $(".login-header").show();
                    $(".register-header").hide();
                } else {
                    $(".register-header").show();
                    $(".login-header").hide();
                }
            });

            /* button more*/
            $(self.mdoptions.fields.moreButton).unbind('click').on('click', function (event) {
                $(this).toggleClass("minus");
                $('.md-all-button-popup').toggle(function () {
                    $('.md-more-less').text(jQuery(this).is(':visible')?jQuery.mage.__("less") : jQuery.mage.__("more"));
                });
            });


            /* popup form submit event */
            $(self.mdoptions.fields.form).unbind('submit').on('submit', function (event) {
                var form = $(this);
                if (form.valid()) {
                    form.find('button.action').prop('disabled', true);
                    self.submitFormWithAjax(form);
                }
                event.preventDefault();
                return false;
            });

            /* login hide/show password */
            $(document).on("click", self.mdoptions.fields.showPassword, function (event) {
                if ($(this).is(':checked')) {
                    self.showPassword(true);
                } else {
                    self.showPassword(false);
                }
            });
        },

        openPopupModal: function (activeTabIndex) {

            $(this.mdoptions.fields.mdTabWrapperForgot).hide();
            $(this.mdoptions.fields.mdTabWrapper).show();

            var mdoptions = {
                type: 'popup',
                responsive: true,
                innerScroll: true,
                buttons: false,
                modalClass: 'md-popup'
            };

            $(this.mdoptions.fields.popupContainer).modal(mdoptions).modal('openModal');
            $('.modal-inner-wrap').addClass('md-social-popup');
            $('.modal-popup').addClass('social-popup');
            
            if ($('html').hasClass('nav-open')) {
                $('.navigation > .ui-menu').menu('toggle');
            }
            $('.modal-inner-wrap.md-social-popup').addClass($('#md-login-popup').attr('data-position'));
            
            $(this.mdoptions.fields.mdTabContainer).tabs('activate', activeTabIndex);
            $('#send2').prop('disabled', false);
        },

        toggleTabWrappers: function () {
            $(this.mdoptions.fields.mdTabWrapper).toggle();
            $(this.mdoptions.fields.mdTabWrapperForgot).toggle();
        },

        submitFormWithAjax: function (form) {
            var self = this;
            $.ajax({
                url: form.attr('action'),
                data: form.serialize(),
                type: 'post',
                dataType: 'html',
                showLoader: true,
                success: function (response) {
                    $('.md-error span').text('');
                    $('.md-success span').text('');
                    var cookieMessages = $.cookieStorage.get('mage-messages');
                    $.cookieStorage.set('mage-messages', '');
                    if (cookieMessages.length) {
                        var flag = true;
                        $(cookieMessages).each(function (index, message) {
                            if (message.type === 'error') {
                                flag = false;
                            }
                        });

                        if (!flag) {
                            form.find('button.action').prop('disabled', false);
                            $('.md-error span').append($.parseHTML(cookieMessages[0].text));
                            return;
                        }
                    }

                    if (cookieMessages.length) {
                        $('.md-success').html(cookieMessages[0].text);
                    } else {
                        if (form.hasClass('form-login')) {
                            if (response.indexOf('customer/account/logout') !== -1) {
                                $('.md-success span').text(($t('You have successfully logged in.')));
                            }
                        } else if (form.hasClass('form-create-account')) {
                            $('.md-success span').text(($t('Thank you for registering with us.')));
                        }
                    }
                    setTimeout(function () {
                        window.location.reload(true);
                    }, 3000);
                },
                error: function () {
                    $('.md-error').html();
                    $('.md-error').html($t('Sorry, an unspecified error occurred. Please try again.'));
                    setTimeout(function () {
                        window.location.reload(true);
                    }, 3000);
                }
            });
        },

        /**
         * Show/Hide password
         * @private
         */
        showPassword: function (isChecked) {
            var self = this;
            $(self.mdoptions.fields.passwordSelector).attr(
                'type',
                isChecked ? self.mdoptions.fields.textInputType : self.mdoptions.fields.passwordInputType
            );
        }
    });

    return $.mage.mdLoginPopup;
});
