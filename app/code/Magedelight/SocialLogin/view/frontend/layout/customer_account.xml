<?xml version="1.0"?>
<!--
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_account_navigation">
            <block class="Magento\Customer\Block\Account\SortLinkInterface" ifconfig="sociallogin/general/enable" name="customer-account-navigation-social-media-providers-link">
                <arguments>
                    <argument name="path" xsi:type="string">mdsocial/manage</argument>
                    <argument name="label" xsi:type="string" translate="true">Manage Social Sign in</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
