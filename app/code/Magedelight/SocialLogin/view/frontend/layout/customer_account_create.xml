<?xml version="1.0"?>
<!--
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
   <body>
    <referenceContainer name="content">
            <block class="Magedelight\SocialLogin\Block\Account\Sociallogin" ifconfig="sociallogin/general/enable" name="md.sociallogin.buttons" as="md_socialbuttons" template="Magedelight_SocialLogin::account/buttons.phtml" />
    </referenceContainer>
    <move element="md.sociallogin.buttons" destination="content" before="customer_form_register" />
  </body>
</page>
