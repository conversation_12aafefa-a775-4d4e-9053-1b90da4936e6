/**
 * Magedelight
 * Copyright (C) 2023 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_SocialLogin
 * @copyright Copyright (c) 2023 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */

@import 'module/pages/_dashboard.less';

@magedelightsociallogin-icons-admin__font-name-path: '@{baseDir}Magedelight_SocialLogin/fonts/print/icon';
@magedelightsociallogin-icons-admin__font-name : 'sociallogin';
.lib-font-face(
    @family-name:@magedelightsociallogin-icons-admin__font-name,
    @font-path: @magedelightsociallogin-icons-admin__font-name-path,
    @font-weight: normal,
    @font-style: normal
);


#chart_print_div .icon-md-print {
    margin-right: 60px;
}

.icon-md-print, .icon-md-print:hover {
    color: #4c4c4b;
    text-decoration: none;
}

.icon-md-print:before {
    font-family: @magedelightsociallogin-icons-admin__font-name;
    content: "\e954";
    font-size: 2.4rem;

}

.md_deeplink_available {
    color: #41362f;
    text-decoration: none;
}

.md_deeplink_available:hover {
    color: #eb5202;
    text-decoration: none;
}

.md_deeplink_available:after {
    -webkit-font-smoothing: antialiased;
    font-family: 'Admin Icons';
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    speak: none;

    content: '\e644';
    font-size: 0.8rem;
    vertical-align: super;
}
