<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Model;

use Exception;
use Magedelight\SocialLogin\Helper\SocialMedia as Social;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Api\Data\CustomerInterfaceFactory;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\EmailNotificationInterface;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\AlreadyExistsException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Framework\Math\Random;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\Context;
use Magento\Framework\Model\ResourceModel\AbstractResource;
use Magento\Framework\Registry;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Store\Model\StoreManagerInterface;
use Magento\User\Model\User;
use Psr\Log\LoggerInterface;
use Magento\Customer\Model\Session;

/**
 * Class Social
 *
 */
class SocialMedia extends AbstractModel
{
    public const STATUS_PROCESS = 'processing';
    public const STATUS_LOGIN = 'logging';
    public const STATUS_CONNECT = 'connected';
    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;
    /**
     * @var CustomerFactory
     */
    protected $customerFactory;
    /**
     * @var CustomerInterfaceFactory
     */
    protected $customerDataFactory;
    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var $apiName
     */
    protected $apiName;
    /**
     * @var User
     */
    protected $_userModel;
    /**
     * @var DateTime
     */
    protected $_dateTime;
    /**
     * @var LoggerInterface
     */
    protected $logger;
    /**
     * @var Social
     */
    protected $apiHelper;

    /** @var Session  */
    protected $_customerSession;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * SocialMedia constructor.
     * @param Context $context
     * @param Registry $registry
     * @param CustomerFactory $customerFactory
     * @param CustomerInterfaceFactory $customerDataFactory
     * @param CustomerRepositoryInterface $customerRepository
     * @param StoreManagerInterface $storeManager
     * @param Social $apiHelper
     * @param User $userModel
     * @param DateTime $dateTime
     * @param LoggerInterface $logger
     * @param Session $customerSession
     * @param ManagerInterface $messageManager
     * @param AbstractResource|null $resource
     * @param AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        CustomerFactory $customerFactory,
        CustomerInterfaceFactory $customerDataFactory,
        CustomerRepositoryInterface $customerRepository,
        StoreManagerInterface $storeManager,
        Social $apiHelper,
        User $userModel,
        DateTime $dateTime,
        LoggerInterface $logger,
        Session $customerSession,
        ManagerInterface $messageManager,
        ?AbstractResource $resource = null,
        ?AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        $this->customerFactory = $customerFactory;
        $this->customerRepository = $customerRepository;
        $this->customerDataFactory = $customerDataFactory;
        $this->storeManager = $storeManager;
        $this->apiHelper = $apiHelper;
        $this->_userModel = $userModel;
        $this->_dateTime = $dateTime;
        $this->logger = $logger;
        $this->_customerSession = $customerSession;
        $this->messageManager = $messageManager;
        parent::__construct(
            $context,
            $registry,
            $resource,
            $resourceCollection,
            $data
        );
    }

    /**
     * Define resource model
     */
    protected function _construct()
    {
        $this->_init(ResourceModel\Social::class);
    }

    /**
     * Get Customer by social
     *
     * @param identify $identify
     * @param type $type
     * @return Customer
     */
    public function getCustomerBySocial($identify, $type)
    {
        $customer = $this->customerFactory->create();
        $socialCustomer = $this->getCollection()
            ->addFieldToFilter('social_id', $identify)
            ->addFieldToFilter('type', $type)
            ->addFieldToFilter('status', ['null' => 'true'])
            ->getFirstItem();
        $errorMsg = 'Email you are trying to login is not registered with us so you can not link it.
        Please use social media account with your registered email only.';
        if (($customerId = $this->_customerSession->getCustomerId())) {
            $customer_logged = $this->customerFactory->create()->load($this->_customerSession->getCustomerId());
            $customer_data = $customer_logged->getData();
            if ($customer_data['email'] == $socialCustomer->getUserEmail()) {
                if ($socialCustomer && $socialCustomer->getId()) {
                    $customer->load($socialCustomer->getCustomerId());
                    return $customer;
                }
            } else {
                if ($socialCustomer && $socialCustomer->getId()) {
                    $this->messageManager->addErrorMessage(__($errorMsg));
                    $customer->load($this->_customerSession->getCustomerId());
                    return $customer;
                } else {
                    $customer->load($this->_customerSession->getCustomerId());
                    return $customer;
                }
            }
        }

        if ($socialCustomer && $socialCustomer->getId()) {
            $customer->load($socialCustomer->getCustomerId());
        }
        return $customer;
    }

    /**
     * Get Customer By email
     *
     * @param email $email
     * @param websiteId $websiteId
     *
     * @return Customer
     * @throws LocalizedException
     */
    public function getCustomerByEmail($email, $websiteId = null)
    {
        /**
         * @var Customer $customer
         */
        $customer = $this->customerFactory->create();
        $customer->setWebsiteId($websiteId ?: $this->storeManager->getWebsite()->getId());
        $customer->loadByEmail($email);
        return $customer;
    }

    /**
     * Create Customer Social
     *
     * @param data $data
     * @param Store $store
     *
     * @return mixed
     * @throws Exception
     */
    public function createCustomerSocial($data, $store)
    {
        /**
         * @var CustomerInterface $customer
         */

        $customer = $this->customerDataFactory->create();
        $customer->setFirstname($data['firstname'])
            ->setLastname($data['lastname'])
            ->setEmail($data['email'])
            ->setStoreId($store->getId())
            ->setWebsiteId($store->getWebsiteId())
            ->setCreatedIn($store->getName());
        try {
            if ($data['password'] !== null) {
                $customer = $this->customerRepository->save($customer, $data['password']);
                $this->getEmailNotification()->newAccount(
                    $customer,
                    EmailNotificationInterface::NEW_ACCOUNT_EMAIL_REGISTERED
                );
            } else {
                // If customer exists existing hash will be used by Repository
                $customer = $this->customerRepository->save($customer);
                $objectManager = ObjectManager::getInstance();
                $mathRandom = $objectManager->get(Random::class);
                $newPasswordToken = $mathRandom->getUniqueHash();
                $accountManagement = $objectManager->get(AccountManagementInterface::class);
                $accountManagement->changeResetPasswordLinkToken($customer, $newPasswordToken);
            }
            if ($this->apiHelper->canSendPassword($store)) {
                $this->getEmailNotification()->newAccount(
                    $customer,
                    EmailNotificationInterface::NEW_ACCOUNT_EMAIL_REGISTERED_NO_PASSWORD
                );
            }
            $this->setAuthorCustomer($data, $customer->getId(), $data['type']);
        } catch (AlreadyExistsException $e) {
            throw new InputMismatchException(
                __('A customer with the same email already exists in an associated website.')
            );
        } catch (Exception $e) {
            if ($customer->getId()) {
                $this->_registry->register('isSecureArea', true, true);
                $this->customerRepository->deleteById($customer->getId());
            }
            throw $e;
        }
        /**
         * @var Customer $customer
         */
        $customer = $this->customerFactory->create()->load($customer->getId());
        return $customer;
    }

    /**
     * Get email notification
     *
     * @return EmailNotificationInterface
     */
    private function getEmailNotification()
    {
        return ObjectManager::getInstance()->get(EmailNotificationInterface::class);
    }

    /**
     * Set Author Customer
     *
     * @param Userdata $userdata
     * @param customerId $customerId
     * @param type $type
     *
     * @return $this
     * @throws Exception
     */
    public function setAuthorCustomer($userdata, $customerId, $type)
    {

        $modelData = $this->load($userdata["email"],'user_email');
        $this->setData(
            [
                'id' => ($modelData) ? $modelData->getId() : NULL,
                'social_id' => $userdata['identifier'],
                'customer_id' => $customerId,
                'type' => $type,
                'user_email' => $userdata['email'],
                'display_name' => $userdata['displayname'],
                //'is_send_password_email' => $this->apiHelper->canSendPassword(),
                'social_created_at' => $this->_dateTime->date()
            ]
        )
            ->setId(null)->save();
        return $this;
    }
    /**
     * Get User Profile
     *
     * @param apiName $apiName
     * @return \Hybridauth\User\Profile
     * @throws LocalizedException
     * @throws \Hybridauth\Exception\InvalidArgumentException
     * @throws \Hybridauth\Exception\UnexpectedValueException
     */
    public function getUserProfile($apiName)
    {
        $config = $this->apiHelper->getAuthConfig($apiName);

        // Fix Twitch provider renaming if needed
        if ($apiName === "twitch" && isset($config['providers']['Twitch'])) {
            $config['providers']['TwitchTV'] = $config['providers']['Twitch'];
            unset($config['providers']['Twitch']);
            $apiName = "TwitchTV"; // Use the updated provider name
        }

        $hybridauth = new \Hybridauth\Hybridauth($config);
        try {
            $adapter = $hybridauth->authenticate($apiName);
            $userProfile = $adapter->getUserProfile();
            $this->logger->info(json_encode($userProfile));
        } catch (Exception $e) {
            $hybridauth->disconnectAllAdapters();
            $hybridauth = new \Hybridauth\Hybridauth($config);
            $adapter = $hybridauth->authenticate($apiName);
            $userProfile = $adapter->getUserProfile();
        }
        return $userProfile;
    }

    /**
     * Get User BySocial
     *
     * @param identify $identify
     * @param type $type
     *
     * @return User
     */
    public function getUserBySocial($identify, $type)
    {
        $user = $this->_userModel;
        $socialCustomer = $this->getCollection()
            ->addFieldToFilter('social_id', $identify)
            ->addFieldToFilter('type', $type)->addFieldToFilter('user_id', ['notnull' => true])
            ->getFirstItem();
        if ($socialCustomer && $socialCustomer->getId()) {
            $user->load($socialCustomer->getUserId());
        }
        return $user;
    }

    /**
     * Get User
     *
     * @param type $type
     * @param identifier $identifier
     *
     * @return DataObject
     */
    public function getUser($type, $identifier)
    {
        // @codingStandardsIgnoreStart
        return $this->getCollection()
            ->addFieldToSelect('user_id')
            ->addFieldToSelect('social_customer_id')
            ->addFieldToFilter('type', $type)
            ->addFieldToFilter('social_id', base64_decode($identifier))
            ->addFieldToFilter('status', self::STATUS_LOGIN)
            ->getFirstItem();
        // @codingStandardsIgnoreEnd
    }

    /**
     * Update Auth Customer
     *
     * @param socialCustomerId $socialCustomerId
     * @param identifier $identifier
     *
     * @return $this
     * @throws Exception
     */
    public function updateAuthCustomer($socialCustomerId, $identifier)
    {
        $social = $this->load($socialCustomerId);
        $social->addData(
            [
                'social_id' => $identifier,
                'status' => self::STATUS_CONNECT
            ]
        );
        $social->save();
        return $this;
    }

    /**
     * Update Status
     *
     * @param socialCustomerId $socialCustomerId
     * @param status $status
     *
     * @return $this
     * @throws Exception
     */
    public function updateStatus($socialCustomerId, $status)
    {
        $social = $this->load($socialCustomerId);
        $social->addData(['status' => $status])->save();
        return $this;
    }
}
