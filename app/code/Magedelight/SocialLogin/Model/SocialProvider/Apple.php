<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
namespace Magedelight\SocialLogin\Model\SocialProvider;

use Exception;
use Hybridauth\Exception\InvalidApplicationCredentialsException;
use Hybridauth\Exception\UnexpectedValueException;

use Hybridauth\Data;
use Hybridauth\User;

use Magento\Framework\App\Request\Http;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Escaper;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Registry;
use Magento\Framework\Serialize\SerializerInterface;

use Firebase\JWT\JWT;
use Magento\Framework\Model\Context;
use Magento\Framework\Model\ResourceModel\AbstractResource;
use Magento\Customer\Model\Session;
use Magedelight\SocialLogin\Helper\SocialMedia as HelperData;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Driver\File;

class Apple extends AbstractModel
{
    public const SCOPE = 'name email';
    public const APPLE_ID_URL = 'https://appleid.apple.com';
    public const API_BASE_URL = 'https://appleid.apple.com/auth/';
    public const AUTHORIZE_URL = 'https://appleid.apple.com/auth/authorize';
    public const ACCESS_TOKEN_URL = 'https://appleid.apple.com/auth/token';
    public const API_DOCUMENTATION = 'https://developer.apple.com/documentation/sign_in_with_apple';

    /** @var Http */
    protected $request;

    /** @var Session */
    protected $session;

    /** @var HelperData */
    private $helperData;

    /** @var SerializerInterface */
    private $serialize;

    /** @var Curl */
    private $curl;

    /**
     * @var Filesystem
     */
    protected $_filesystem;
    /**
     * @var Escaper
     */
    private $escaper;
    /**
     * @var File
     */
    private $file;

    /**
     * Apple constructor.
     * @param Context $context
     * @param Filesystem $filesystem
     * @param Registry $registry
     * @param Http $request
     * @param Session $customerSession
     * @param HelperData $helperData
     * @param SerializerInterface $serialize
     * @param Curl $curl
     * @param Escaper $escaper
     * @param File $file
     * @param AbstractResource|null $resource
     * @param AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        Filesystem $filesystem,
        Registry $registry,
        Http $request,
        Session $customerSession,
        HelperData $helperData,
        SerializerInterface $serialize,
        Curl $curl,
        Escaper $escaper,
        File $file,
        ?AbstractResource $resource = null,
        ?AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        $this->request = $request;
        $this->_filesystem = $filesystem;
        $this->session = $customerSession;
        $this->helperData = $helperData;
        $this->serialize = $serialize;
        $this->curl = $curl;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
        $this->escaper = $escaper;
        $this->file = $file;
    }

    /**
     * GetUserProfile
     *
     * @param mixed $type
     * @param mixed $post
     * @return User\Profile
     * @throws LocalizedException
     * @throws UnexpectedValueException
     * @throws Exception
     */
    public function getUserProfile($type, $post)
    {
        if (isset($post['code']) || $post['custom']) {
            $client_id = trim($this->helperData->getConfigValue("sociallogin/{$type}/consumerid"));
            $teamId = trim($this->helperData->getConfigValue("sociallogin/{$type}/team_id"));
            $redirect_uri = $this->helperData->getCallbackUrl();
            $keyId = trim($this->helperData->getConfigValue("sociallogin/{$type}/key_id"));
            $keyContent = trim($this->helperData->getConfigValue("sociallogin/{$type}/key_content"));
            $key_file = trim($this->helperData->getConfigValue("sociallogin/{$type}/key_file"));

            $client_secret = $this->getSecret($client_id, $teamId, $keyId, $keyContent, $key_file);

            if ($this->request->getParam('error')) {
                throw new Exception(
                    __(
                        'Authorization server returned an error: %1',
                        $this->escaper->escapeHtml($this->request->getParam('error'))
                    )
                );
            }

            if (isset($post['code'])) {
                $code = $post['code'];
            } else {
                $code = $this->session->getData('apple_code', true);
            }

            $response = $this->getHttp(self::ACCESS_TOKEN_URL, [
                'grant_type' => 'authorization_code',
                'code' => $code,
                'redirect_uri' => $redirect_uri,
                'client_id' => $client_id,
                'client_secret' => $client_secret,
            ]);

            if (isset($response['id_token'])) {
                $this->session->setData('id_token', $response['id_token']);
                $idToken = $response['id_token'];
            } else {
                $idToken = $this->session->getData('id_token', true);
            }

            $claims = explode('.', $idToken ?? '')[1];

            // @codingStandardsIgnoreStart
            $claims = $this->serialize->unserialize(base64_decode($claims));
            // @codingStandardsIgnoreEnd

            $data = new Data\Collection($claims);

            if (!$data->exists('sub')) {
                throw new UnexpectedValueException('Missing token payload.');
            }

            $userProfile = new User\Profile();
            $userProfile->identifier = $data->get('sub');
            $userProfile->email = $data->get('email');

            $iduser = $this->request->getParam('user');
            if (!empty($iduser)) {
                $objUser = $this->serialize->unserialize($iduser);
                $user = new Data\Collection($objUser);
                if (!$user->isEmpty()) {
                    $name = $user->get('name');
//                    $userProfile->firstName = $name->firstName;
//                    $userProfile->lastName = $name->lastName;
                    // Check if $name is an array
                    if (is_array($name)) {
                        // Assuming $name is an associative array with 'firstName' and 'lastName' keys
                        $userProfile->firstName = isset($name['firstName']) ? $name['firstName'] : '';
                        $userProfile->lastName = isset($name['lastName']) ? $name['lastName'] : '';
                    } elseif (is_object($name)) {
                        // Assuming $name is an object with 'firstName' and 'lastName' properties
                        $userProfile->firstName = isset($name->firstName) ? $name->firstName : '';
                        $userProfile->lastName = isset($name->lastName) ? $name->lastName : '';
                    } else {
                        // Handle unexpected type of $name here, if needed
                        $userProfile->firstName = '';
                        $userProfile->lastName = '';
                    }
                    $userProfile->displayName = join(' ', [$userProfile->firstName, $userProfile->lastName]);
                }
            }
        }
        return $userProfile;
    }

    /**
     * GetHttp
     *
     * @param mixed $url
     * @param bool $params
     * @return array|bool|float|int|string|null
     */
    protected function getHttp($url, $params = false)
    {
        $this->curl->setOption(CURLOPT_RETURNTRANSFER, true);
        if ($params) {
            $headers = ["Accept"=>"application/json","User-Agent"=>"curl"];
            $this->curl->setHeaders($headers);
            $this->curl->post($url, http_build_query($params));
            /*curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'User-Agent: curl', # Apple requires a user agent header at the token endpoint
            ]);*/
        }
        $response = $this->curl->getBody();
        /*$response = curl_exec($ch);*/
        return $this->serialize->unserialize($response);
    }

    /**
     * GetSecret
     *
     * @param mixed $client_id
     * @param mixed $teamId
     * @param mixed $keyId
     * @param mixed $keyContent
     * @param mixed $key_file
     * @return string
     */
    private function getSecret($client_id, $teamId, $keyId, $keyContent, $key_file)
    {
        // Generate the JWT token
        $jwtPayload = [
            'iss' => $teamId,
            'iat' => time(),
            'exp' => time() + 3600,
            'aud' => self::APPLE_ID_URL,
            'sub' => $client_id,
        ];

        $mediaPath = $this->_filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath();
        $privateKeyPath = trim($mediaPath.$key_file);
        if (isset($keyContent)) {
            return JWT::encode($jwtPayload, $keyContent, 'ES256', $keyId);
        } else {
            return JWT::encode($jwtPayload, $this->file->fileGetContents($privateKeyPath), 'ES256', $keyId);
        }
    }
}
