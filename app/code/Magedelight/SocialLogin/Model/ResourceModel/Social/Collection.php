<?php

/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Model\ResourceModel\Social;

use Magedelight\SocialLogin\Model\Social;
use Magedelight\SocialLogin\Model\ResourceModel\Social as SocialResource;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    /**
     * Define model & resource model.
     */
    public function _construct()
    {
        $this->_init(Social::class, SocialResource::class);
    }
}
