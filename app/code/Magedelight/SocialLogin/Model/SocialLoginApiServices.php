<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Model;

use Magedelight\SocialLogin\Api\SocialLoginApiServicesInterface;
use Magedelight\SocialLogin\Helper\Data as HelperData;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Customer;
use Magento\Framework\Exception\AuthenticationException;
use Magento\Framework\Math\Random;
use Magento\Framework\Webapi\Rest\Request;
use Magento\Integration\Model\Oauth\Token\RequestThrottler;
use Magento\Framework\Event\ManagerInterface;
use Magento\Integration\Model\Oauth\TokenFactory as TokenModelFactory;
use Magento\Customer\Model\AccountManagement as CustomerAccountManagement;
use Magento\Customer\Model\CustomerFactory;
use Magedelight\SocialLogin\Api\Data\SocialInterface;
use Magedelight\SocialLogin\Api\SocialRepositoryInterface;
use Magedelight\SocialLogin\Model\ResourceModel\Social\CollectionFactory as SocialMediaCollectionFactory;

class SocialLoginApiServices implements SocialLoginApiServicesInterface
{
    /**
     * Token Model
     *
     * @var TokenModelFactory
     */
    private $tokenModelFactory;

    /**
     * @var RequestThrottler
     */
    private $requestThrottler;

    /**
     * @var Magento\Framework\Event\ManagerInterface
     */
    private $eventManager;

    /**
     * @var Magento\Customer\Model\CustomerFactory
     */
    private $customerFactory;

    /**
     * @var Magedelight\SocialLogin\Helper\Data
     */
    private $helperData;

    /**
     * @var Request
     */
    protected $request;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var CustomerAccountManagement
     */
    protected $customerAccountManagement;

    /**
     * @var Random
     */
    protected $mathRandom;

    /**
     * @var Customer|Customer
     */
    protected $customer;

    /**
     * @var Social
     */
    protected $socialModel;

    /**
     * @var SocialRepository
     */
    protected $socialRepository;

    /**
     * @var SocialMediaCollectionFactory
     */
    protected $collectionFactory;

    /**
     * SocialLoginApiServices constructor
     *
     * @param TokenModelFactory $tokenModelFactory
     * @param CustomerRepositoryInterface $customerRepository
     * @param CustomerAccountManagement $customerAccountManagement
     * @param CustomerFactory $customerFactory
     * @param HelperData $helperData
     * @param Random $mathRandom
     * @param SocialInterface $socialModel
     * @param SocialRepositoryInterface $socialRepository
     * @param Customer $customers
     * @param Request $request
     * @param SocialMediaCollectionFactory $collectionFactory
     * @param ManagerInterface|null $eventManager
     */
    public function __construct(
        TokenModelFactory $tokenModelFactory,
        CustomerRepositoryInterface $customerRepository,
        CustomerAccountManagement $customerAccountManagement,
        CustomerFactory $customerFactory,
        HelperData $helperData,
        Random $mathRandom,
        SocialInterface $socialModel,
        SocialRepositoryInterface $socialRepository,
        Customer $customers,
        Request $request,
        SocialMediaCollectionFactory $collectionFactory,
        ?ManagerInterface $eventManager = null
    ) {
        $this->tokenModelFactory = $tokenModelFactory;
        $this->customerRepository = $customerRepository;
        $this->customerAccountManagement = $customerAccountManagement;
        $this->customerFactory = $customerFactory;
        $this->helperData = $helperData;
        $this->mathRandom = $mathRandom;
        $this->customer = $customers;
        $this->request = $request;
        $this->eventManager = $eventManager ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->get(ManagerInterface::class);
        $this->socialModel = $socialModel;
        $this->socialRepository = $socialRepository;
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * AuthenticateCustomerWithSocialLogin
     *
     * @param input $input
     * @return string
     */
    public function authenticateCustomerWithSocialLogin($input)
    {
        $socialId = isset($input['socialId']) ? $input['socialId'] : '';
        $socialLoginType = isset($input['socialLoginType']) ? $input['socialLoginType'] : '';
        $firstName = isset($input['firstname']) ? $input['firstname'] : '';
        $lastName = isset($input['lastname']) ? $input['lastname'] : '';
        $input['flag'] = 0;
        if (!isset($input['email'])) {
            $input['flag'] = 1;
            $input['email'] = $socialId.'@'.$socialLoginType.".com";
        }

        $username = isset($input['email']) ? $input['email'] : '';
        try {
            $customerCollection = $this->customer->getCollection()
                ->addAttributeToFilter("email", ["eq" => $username]);
            $customerCollectionCount = $customerCollection->getSize();
            if ($customerCollectionCount == "0") {
                // Create Customer & Set Customer Into Social Login Table
                $customerDataObject = $this->createNewCustomerWithSocialLogin($input);
            } else {
                // Get Customer First Object
                $customerDataObject = $customerCollection->getFirstItem();
                $existedCustomerId = (int)$customerDataObject->getData('entity_id');

                $socialCollection = $this->collectionFactory->create()
                    ->addFieldToFilter('user_email', $username);

                if (($socialCollection->getSize() > 0)) {
                    $socialCollection = $socialCollection->getFirstItem();
                    $displayName = explode(' ', $socialCollection['display_name']);
                    if ($socialCollection['user_email'] !== $username ||
                        $socialCollection['social_id'] !== $socialId ||
                        $socialCollection['type'] !== $socialLoginType ||
                        $displayName[0] !== $firstName ||
                        $displayName[1] !== $lastName) {
                        return 'Authentication failed!';
                    }
                } else {
                    $name = $input['firstname'] . ' ' . $input['lastname'];
                    $this->socialModel->setData('social_id', $socialId);
                    $this->socialModel->setData('user_email', $username);
                    $this->socialModel->setData('type', $socialLoginType);
                    $this->socialModel->setData('customer_id', $existedCustomerId);
                    $this->socialModel->setData('display_name', $name);
                    $this->socialRepository->save($this->socialModel);
                }
            }
            $username = $customerDataObject->getEmail();
            $this->getRequestThrottler()->throttle($username, RequestThrottler::USER_TYPE_CUSTOMER);
        } catch (\Exception $e) {
            $this->helperData->printLog($e->getMessage() . __FILE__);
            $this->getRequestThrottler()->logAuthenticationFailure($username, RequestThrottler::USER_TYPE_CUSTOMER);
            throw new AuthenticationException(
                __(
                    'The account sign-in was incorrect or your account is disabled temporarily. '
                    . 'Please wait and try again later.'
                )
            );
        }
        $this->eventManager->dispatch('customer_login', ['customer' => $customerDataObject]);
        $this->getRequestThrottler()->resetAuthenticationFailuresCount($username, RequestThrottler::USER_TYPE_CUSTOMER);
        return $this->tokenModelFactory->create()->createCustomerToken($customerDataObject->getId())->getToken();
    }

    /**
     * Get request throttler instance
     *
     * @return RequestThrottler
     * @deprecated 100.0.4
     * @see Magento\Integration\Model\Oauth\Token\RequestThrottler
     */
    private function getRequestThrottler()
    {
        if (!$this->requestThrottler instanceof RequestThrottler) {
            return \Magento\Framework\App\ObjectManager::getInstance()->get(RequestThrottler::class);
        }
        return $this->requestThrottler;
    }

    /**
     * CreateNewCustomerWithSocialLogin
     *
     * @param customerData $customerData
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function createNewCustomerWithSocialLogin($customerData)
    {
        $flag = $customerData['flag'];
        $email = $customerData['email'];

        /* Generate Password Hash */
        $hash = $this->customerAccountManagement->getPasswordHash($customerData['email']);

        /* Create Customer */
        try {
            $customer = $this->customerFactory->create()
                ->setFirstname($customerData['firstname'])
                ->setLastname($customerData['lastname'])
                ->setEmail($email)
                ->setPassword($hash)
                ->save();
        } catch (\Exception $e) {
            $this->helperData->printLog($e->getMessage() . __FILE__);
            throw new AuthenticationException(__($e->getMessage()));
        }

        /* Sending Email To Customer */
        try {
            $newCustomerId = $customer->getId();
            $name = $customer->getName();
            if (!$flag && $this->helperData->getWelcomePermission()) {
                $newLinkToken = $this->mathRandom->getUniqueHash();
                $customerRepo = $this->customerRepository->getById($newCustomerId);

                $this->customerAccountManagement->changeResetPasswordLinkToken($customerRepo, $newLinkToken);

                $customeremailData['name'] = trim($customerData['firstname'].' '.$customerData['lastname']);
                $customeremailData['email'] = trim($customerData['email']);
                $customeremailData['id'] = $newCustomerId;
                $customeremailData['rp_token'] = $newLinkToken;
                $this->helperData->sendWelcomeEmail($customeremailData);
            }
        } catch (\Exception $e) {
            $this->helperData->printLog($e->getMessage() . __FILE__);
            throw new AuthenticationException(__($e->getMessage()));
        }

        /* Store data into magedelight_sociallogin table */
        try {
            $this->socialModel->setData('social_id',$customerData['socialId']);
            $this->socialModel->setData('user_email',$customerData['email']);
            $this->socialModel->setData('type',$customerData['socialLoginType']);
            $this->socialModel->setData('customer_id',$newCustomerId);
            $this->socialModel->setData('display_name',$name);
            $this->socialRepository->save($this->socialModel);
        } catch (\Exception $e) {
            $this->helperData->printLog($e->getMessage() . __FILE__);
            throw new AuthenticationException(__($e->getMessage()));
        }
        return $customer;
    }

    /**
     * AuthenticateCustomerWithSocialLoginWithApi
     *
     * @return mixed|string
     * @throws AuthenticationException
     */
    public function authenticateCustomerWithSocialLoginWithApi()
    {
        $bodyParams = $this->request->getBodyParams();
        return $this->authenticateCustomerWithSocialLogin($bodyParams);
    }
}
