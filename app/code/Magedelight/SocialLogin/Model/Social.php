<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Model;

use Magedelight\SocialLogin\Api\Data\SocialInterface;

use Magento\Framework\Model\AbstractModel;

class Social extends AbstractModel implements SocialInterface
{
    /**
     * Define resource model.
     */
    public function _construct()
    {
        $this->_init(\Magedelight\SocialLogin\Model\ResourceModel\Social::class);
    }

    /**
     * Get social id
     *
     * @return array|mixed|string|null
     */
    public function getSocialId()
    {
        return $this->getData(self::SOCIAL_ID);
    }

    /**
     * SetSocialId
     *
     * @param social_id $social_id
     * @return Social
     */
    public function setSocialId($social_id)
    {
        return $this->setData(self::SOCIAL_ID, $social_id);
    }

    /**
     * Get user email
     *
     * @return array|mixed|string|null
     */
    public function getUserEmail()
    {
        return $this->getData(self::USER_EMAIL);
    }

    /**
     * SetUserEmail
     *
     * @param user_email $user_email
     * @return Social
     */
    public function setUserEmail($user_email)
    {
        return $this->setData(self::USER_EMAIL, $user_email);
    }

    /**
     * Get customer id
     *
     * @return array|mixed|string|null
     */
    public function getCustomerId()
    {
        return $this->getData(self::CUSTOMER_ID);
    }

    /**
     * SetCustomerId
     *
     * @param customer_id $customer_id
     * @return Social
     */
    public function setCustomerId($customer_id)
    {
        return $this->setData(self::CUSTOMER_ID);
    }

    /**
     * Get type
     *
     * @return array|mixed|string|null
     */
    public function getType()
    {
        return $this->getData(self::TYPE);
    }

    /**
     * SetType
     *
     * @param type $type
     * @return Social
     */
    public function setType($type)
    {
        return $this->setData(self::TYPE);
    }

    /**
     * Get Status
     *
     * @return array|mixed|string|null
     */
    public function getStatus()
    {
        return $this->getData(self::STATUS);
    }

    /**
     * SetStatus
     *
     * @param status $status
     * @return Social
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS);
    }
}
