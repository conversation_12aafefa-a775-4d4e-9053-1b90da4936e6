<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
namespace Magedelight\SocialLogin\Model\Config\Source;

class DisplayPosition implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * Return options array.
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
                ['value' => 'top', 'label' => __('Top')],
                ['value' => 'bottom', 'label' => __('Bottom')],
                ['value' => 'left', 'label' => __('Left')],
                ['value' => 'right', 'label' => __('Right')],
        ];
    }
}
