<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Helper;

use Magento\Framework\Exception\LocalizedException;
use Magento\Store\Model\ScopeInterface;
use Magedelight\SocialLogin\Helper\DataMedia as HelperData;

/**
 * Class Social
 *
 */
class SocialMedia extends HelperData
{
    /**
     * GetSocialTypes
     *
     * @return array
     */
    public function getSocialTypes()
    {
        $socialTypes = $this->getSocialTypesArray();
        uksort(
            $socialTypes,
            function ($a, $b) {
                $sortA = $this->getConfigValue("sociallogin/{$a}/sort_order") ?: 0;
                $sortB = $this->getConfigValue("sociallogin/{$b}/sort_order") ?: 0;
                if ($sortA === $sortB) {
                    return 0;
                }
                return ($sortA < $sortB) ? -1 : 1;
            }
        );
        return $socialTypes;
    }

    /**
     * GetSocialConfig
     *
     * @param type $type
     * @return array|mixed
     */
    public function getSocialConfig($type)
    {
        $apiData = [
            'Google' => ['scope' => 'profile email']
        ];
        if ($type && array_key_exists($type, $apiData)) {
            return $apiData[$type];
        }
        return [];
    }

    /**
     * GetAuthenticateParams
     *
     * @param type $type
     * @return array|null
     */
    public function getAuthenticateParams($type)
    {
        return null;
    }

    /**
     * IsEnabled
     *
     * @param type $type
     *
     * @return mixed
     */
    public function isEnabled($type)
    {
        return $this->getConfigValue("sociallogin/{$type}/enable");
    }

    /**
     * IsSignInAsAdmin
     *
     * @param storeId $storeId
     *
     * @return array|mixed
     */
    public function isSignInAsAdmin($storeId = null)
    {
        return $this->getConfigValue("sociallogin/{$this->_type}/admin", $storeId);
    }

    /**
     * GetAuthConfig
     *
     * @param type $type
     * @return array
     * @throws LocalizedException
     */
    public function getAuthConfig($type): array
    {
        return [
            'callback' => $this->getCallbackUrl(),
            'providers' => $this->getProviderData($type),
            'debug_mode' => true,
            'debug_file' => BP . '/var/log/social.log'
        ];
    }

    /**
     * GetProviderData
     *
     * @param type $type
     * @return array|array[]
     */
    public function getProviderData($type): array
    {
        $data = [];
        $label = ucfirst($type);

        if ($isEnable = $this->getConfigValue("sociallogin/{$type}/enable")) {
            if ($type != 'apple') {
                $keys = [
                    'id' => trim($this->getConfigValue("sociallogin/{$type}/consumerid")),
                    'key' => trim($this->getConfigValue("sociallogin/{$type}/consumerid")),
                    'secret' => trim($this->getConfigValue("sociallogin/{$type}/consumersecret"))
                ];
            } else {
                $keys = [
                    'id' => trim($this->getConfigValue("sociallogin/{$type}/consumerid")),
                    'team_id' => trim($this->getConfigValue("sociallogin/{$type}/team_id")),
                    'key_id' => trim($this->getConfigValue("sociallogin/{$type}/key_id")),
                    'key_content' => trim($this->getConfigValue("sociallogin/{$type}/key_content"))
                ];
            }

            $config = [
                'enabled' => $isEnable,
                'keys' => $keys
            ];

            $data = [
                $label => $config
            ];
        }

        return $data;
    }

    /**
     * GetCallbackUrl
     *
     * @return string
     * @throws LocalizedException
     */
    public function getCallbackUrl()
    {
        $storeId = $this->getScopeId();

        return $this->_getUrl(
            'mdsocial/social/callback',
            [
                '_nosid' => true,
                '_scope' => $storeId,
                '_secure' => true
            ]
        );
    }

    /**
     * GetScopeId
     *
     * @return int
     * @throws LocalizedException
     */
    protected function getScopeId()
    {
        $scope = $this->_request->getParam(ScopeInterface::SCOPE_STORE) ?: $this->storeManager->getStore()->getId();
        if ($website = $this->_request->getParam(ScopeInterface::SCOPE_WEBSITE)) {
            $scope = $this->storeManager->getWebsite($website)->getDefaultStore()->getId();
        }
        return $scope;
    }

    /**
     * GetSocialTypesArray
     *
     * @return array
     */
    public function getSocialTypesArray()
    {
        return [
            'facebook' => 'Facebook',
            'google' => 'Google',
            'twitter' => 'Twitter',
            'windowslive' => 'windowslive',
            'amazon' => 'Amazon',
            'linkedin' => 'LinkedIn',
            'github' => 'Github',
            'live' => 'Live',
            'slack'=>'Slack',
            'pinterest'=>'Pinterest',
            'twitch'=>'Twitch'
        ];
    }
}
