<?xml version="1.0"?>
 <!--
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
 -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\Controller\Account\LoginPost">
        <plugin name="magedelight_socialLogin_loginpostplugin" type="Magedelight\SocialLogin\Plugin\LoginPostPlugin" sortOrder="1" disabled="false" />
    </type>

    <type name="Magento\Customer\ViewModel\LoginButton">
        <plugin disabled="true" sortOrder="1" name="recaptcha_disable_login_button"
                type="Magento\ReCaptchaCustomer\Plugin\Customer\DisableLoginButton"/>
    </type>
    <type name="Magento\Customer\ViewModel\CreateAccountButton">
        <plugin disabled="true" sortOrder="1" name="recaptcha_disable_create_account_button"
                type="Magento\ReCaptchaCustomer\Plugin\Customer\DisableCreateAccountButton"/>
    </type>
    <type name="Magento\Customer\ViewModel\ForgotPasswordButton">
        <plugin disabled="true" sortOrder="1" name="recaptcha_disable_forgot_password_button"
                type="Magento\ReCaptchaCustomer\Plugin\Customer\DisableForgotPasswordButton"/>
    </type>
</config>
