<?xml version="1.0"?>
 <!--
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */
 -->

<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">

    <!-- Login with Social Media -->
    <route method="POST" url="/V1/authenticateCustomerWithSocialLoginWithApi">
        <service class="Magedelight\SocialLogin\Api\SocialLoginApiServicesInterface" method="authenticateCustomerWithSocialLoginWithApi"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
</routes>
