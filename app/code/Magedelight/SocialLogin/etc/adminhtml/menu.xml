<?xml version="1.0"?>
<!--
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>

        <add id="Magedelight_SocialLogin::sociallogin_root"
            title="Social Media Login"
            module="Magedelight_SocialLogin"
            sortOrder="50"
            resource="Magedelight_SocialLogin::root"
            toolTip="magedelight_base" />

        <add id="Magedelight_SocialLogin::sociallogin_root_commonlyvisible"
            title="Social Media Login"
            module="Magedelight_SocialLogin"
            sortOrder="200"
            parent="Magedelight_Base::md_modules"
            resource="Magedelight_SocialLogin::root" />

        <add id="Magedelight_SocialLogin::sociallogin_dashboard"
             title="Dashbaord"
             module="Magedelight_SocialLogin"
             sortOrder="100"
             action="mdsocial/report_sociallogin/dashboard"
             resource="Magedelight_SocialLogin::report_sociallogin_dashboard"
             parent="Magedelight_SocialLogin::sociallogin_root" />

        <add id="Magedelight_SocialLogin::sociallogin_reports"
             title="Social Login Reports"
             module="Magedelight_SocialLogin"
             sortOrder="100"
             action="mdsocial/report_sociallogin/sociallogin"
             resource="Magedelight_SocialLogin::report_sociallogin"
             parent="Magedelight_SocialLogin::sociallogin_root" />

        <add id="Magedelight_SocialLogin::settings"
            title="Configuration"
            module="Magedelight_SocialLogin"
            sortOrder="200"
            action="adminhtml/system_config/edit/section/sociallogin"
            parent="Magedelight_SocialLogin::sociallogin_root"
            resource="Magedelight_SocialLogin::settings" />

        <add id="Magedelight_SocialLogin::useful_links"
            title="Useful Links"
            module="Magedelight_SocialLogin"
            sortOrder="999"
            parent="Magedelight_SocialLogin::sociallogin_root"
            resource="Magedelight_SocialLogin::root" />

        <add id="Magedelight_SocialLogin::documentation"
            title="Documentation"
            module="Magedelight_SocialLogin"
            sortOrder="10"
            target="_blank"
            parent="Magedelight_SocialLogin::useful_links"
            resource="Magedelight_SocialLogin::root" />
    </menu>
</config>
