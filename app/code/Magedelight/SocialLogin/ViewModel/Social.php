<?php declare(strict_types=1);
/**
 * Magedelight
 * Copyright (C) 2023 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_SocialLogin
 * @copyright Copyright (c) 2023 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */
namespace Magedelight\SocialLogin\ViewModel;

use Magedelight\SocialLogin\Api\Data\SocialInterface;
use Magedelight\SocialLogin\Api\SocialRepositoryInterface;
use Magedelight\SocialLogin\Model\ResourceModel\Social\Collection;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Social implements ArgumentInterface
{
    /**
     * @var Collection
     */
    private Collection $collection;

    /**
     * @var SocialRepositoryInterface
     */
    private SocialRepositoryInterface $socialRepository;

    /**
     * @var RequestInterface
     */
    private RequestInterface $request;
    /**
     * Construct
     *
     * @param Collection $collection
     * @param SocialRepositoryInterface $socialRepository
     * @param RequestInterface $request
     */
    public function __construct(
        Collection $collection,
        SocialRepositoryInterface $socialRepository,
        RequestInterface $request
    ) {
        $this->collection = $collection;
        $this->socialRepository = $socialRepository;
        $this->request = $request;
    }
    /**
     * GetList
     */
    public function getList(): array
    {
        return $this->collection->getItems();
    }
    /**
     * GetCustomerSocial
     *
     * @param customerId $customerId
     */
    public function getCustomerSocial($customerId)
    {
        return $this->collection->addFieldToFilter('customer_id', ['in' => [$customerId]]);
    }
    /**
     * GetCount
     */
    public function getCount(): int
    {
        return $this->collection->count();
    }
    /**
     * GetDetail
     */
    public function getDetail(): SocialInterface
    {
        $id = (int) $this->request->getParam('id');
        return $this->socialRepository->getById($id);
    }
}
