<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
namespace Magedelight\SocialLogin\Plugin\Magento\Backend\Model\Menu;

class Item
{
    /**
     * AfterGetUrl
     *
     * @param subject $subject
     * @param result $result
     * @return result|string
     */
    public function afterGetUrl($subject, $result)
    {
        $menuId = $subject->getId();

        if ($menuId == 'Magedelight_SocialLogin::documentation') {
            $result = 'http://docs.magedelight.com/display/MAG/Social+Media+Login+-+Magento+2';
        }

        return $result;
    }
}
