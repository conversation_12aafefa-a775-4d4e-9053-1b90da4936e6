<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magedelight\SMSProfile\Helper\Data;
use Magento\Customer\Helper\Address;
use Magento\Contact\Helper\Data as contactHelper;

class Modelhelper implements ArgumentInterface
{
    protected $helper;
    protected $addressHelper;
    protected $contactHelper;


    public function __construct(
        Data $helperData,
        Address $addressHelper,
        contactHelper $contactHelper
    ) {
          $this->helper = $helperData;
          $this->addressHelper = $addressHelper;
          $this->contactHelper = $contactHelper;
    }

    public function getHelperData()
    {
        return $this->helper;
    }

    public function getAddressHelper()
    {
        return $this->addressHelper;
    }

    public function getContactHelper()
    {
        return $this->contactHelper;
    }
}
