<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model;

use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magento\Checkout\Model\ConfigProviderInterface;
use Magedelight\SMSProfile\Helper\Data as HelperData;

class SmsprofileConfigProvider implements ConfigProviderInterface
{

    /** @var HelperData */
    private $datahelper;

    /** @var \Magento\Customer\Model\Session */
    private $session;
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;

    /**
     * Constructor
     * @param HelperData $dataHelper
     * @param \Magento\Customer\Model\Session $session
     * @param ConfigHelper $configHelper
     */
    public function __construct(
        HelperData $dataHelper,
        \Magento\Customer\Model\Session $session,
        ConfigHelper $configHelper
    ) {
        $this->datahelper = $dataHelper;
        $this->session = $session;
        $this->configHelper = $configHelper;
    }

    public function getConfig()
    {
        $config = [];
        $config['customer_country_enabled']=0;
        if ($this->configHelper->isModuleEnable()) {
            $config['otplogin'] = $this->configHelper->getLoginWith();
            $config['enable_on_checkoutpage'] = ($this->datahelper->getSmsProfileOnCheckoutPage() && $this->datahelper->getGuestCheckoutLogin())?$this->datahelper->getSmsProfileOnCheckoutPage():0;
            if ($this->configHelper->isCountryCodeRequire()) {
                $config['customer_country_enabled'] = $this->configHelper->isCountryCodeRequire();
                $config['only_countries'] = $this->configHelper->getAvailableCountries() ?: [];
                $config['preferred_countries'] = [$this->configHelper->getDefaultCustomerCountry()];
            } else {
                $config['mobile_default_validation'] = $this->configHelper->getMobileLength();
            }
            if ($this->configHelper->getPhoneNote()) {
                $config['otpnote'] = $this->configHelper->getPhoneNote();
            }
            $config['auto_verify_otp'] = $this->configHelper->canAutoVerify();
            $config['resend_limit'] = $this->configHelper->getResendLimit();
            $config['resend_limit_time'] = $this->configHelper->getResendTime();
            $config['resend_link_disable'] = ($this->configHelper->isResendEnable())?0:1;
            $config['otpcod'] = (bool) $this->configHelper->isOtpRequiredForCod();
            $config['otpresend_limit'] = $this->configHelper->getResendLimit();
        } else {
            $config['otplogin'] = 'login_pwd';
            $config['enable_on_checkoutpage'] = 0;
            $config['otpcod']=0;
        }
        $config['otp-widget'] = [
            'otpLength' => $this->configHelper->getOtpLength(),
            'otpFormat' => $this->configHelper->getOtpFormat(),
            'isIntl' => $this->configHelper->isCountryCodeRequire(),
            'mobileLength' => $this->configHelper->getMobileLength(),
            'resendEnable' => $this->configHelper->isResendEnable(),
            'resendAfter' => $this->configHelper->getResendTime(),
            'resendLimit' => $this->configHelper->getResendLimit(),
            'autoVerify' => $this->configHelper->canAutoVerify(),
            'enableRecaptcha' => $this->configHelper->isRecaptchaEnable(),
            'recaptchaForms' => $this->configHelper->getRecaptchaForms(),
            'sitekey' => $this->configHelper->getRecaptchaSiteKey()
        ];
        $config['smsotp'] = $config;

        return $config;
    }
}
