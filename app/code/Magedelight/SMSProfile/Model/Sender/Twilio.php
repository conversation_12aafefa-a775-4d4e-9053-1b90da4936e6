<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\Sender;

class Twilio implements \Magedelight\SMSProfile\Model\SenderInterface
{
    public const VENDOR = 'Twilio';
    /**
     * @inheritDoc
     */
    public function send($contact, $message, $type)
    {
        return true;
    }

    /**
     * Return SMS vendor Name
     *
     * @return string
     */
    public function getVendor()
    {
        return self::VENDOR;
    }
}
