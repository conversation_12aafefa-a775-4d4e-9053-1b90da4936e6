<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class SendOtpVia implements OptionSourceInterface
{
    public const SMS = 'sms';
    public const EMAIL = 'email';
    public const BOTH = 'both';

    /**
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            ['value' => self::SMS, 'label' => __('SMS')],
            ['value' => self::EMAIL, 'label' => __('Email')],
            ['value' => self::BOTH, 'label' => __('Both')]
        ];
    }
}
