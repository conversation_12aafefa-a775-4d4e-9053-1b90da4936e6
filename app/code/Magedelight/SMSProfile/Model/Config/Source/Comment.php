<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\Config\Source;

class Comment implements \Magento\Config\Model\Config\CommentInterface
{
    public $_storeManager;

    public function __construct(
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        $this->_storeManager=$storeManager;
    }

    /**
     * Retrieve element comment by element value
     * @param string $elementValue
     * @return string
     */
    public function getCommentText($elementValue)
    {
        //do some calculations here
        $storeurl = $this->_storeManager->getStore()
           ->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_LINK);
        return __('If your service provider sends delivery report through the webhook for the message which are processed and they include: delivered, failed, rejected,etc .And above 4 fields are not mandatory in this case Please specify below url in your service provider\'s account <br/>'.$storeurl.'smsnotification/pushurl/index');
    }
}
