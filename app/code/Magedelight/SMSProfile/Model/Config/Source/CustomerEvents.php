<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\Config\Source;

class CustomerEvents implements \Magento\Framework\Option\ArrayInterface
{
    public function toOptionArray()
    {
        return [
            ['value' => 'customer_neworder', 'label' => __('Order Place')],
            ['value' => 'customer_contact', 'label' => __('Contact')],
            ['value' => 'customer_order_cancel', 'label' => __('Admin Order Cancel')],
            ['value' => 'customer_invoice', 'label' => __('Admin Invoice Order')],
            ['value' => 'customer_creditmemo', 'label' => __('Admin Creditmemo Order')],
            ['value' => 'customer_shipment', 'label' => __('Admin Shipment Order')],
            ['value' => 'customer_shipment_tracking', 'label' => __('Admin Shipment Tracking')],
        ];
    }
}
