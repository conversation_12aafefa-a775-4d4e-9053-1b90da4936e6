<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class SMSProfileCountryCode implements OptionSourceInterface
{
    public function toOptionArray()
    {
        return [
            ['value' => '+1','label' => __('USA/Canada (+1)')],
            ['value' => '+213','label' => __('Algeria (+213)')],
            ['value' => '+44','label' => __('UK (+44)')],
            ['value' => '+376','label' => __('Andorra (+376)')],
            ['value' => '+244','label' => __('Angola (+244)')],
            ['value' => '+1264','label' => __('Ang<PERSON><PERSON> (+1264)')],
            ['value' => '+1268','label' => __('Antigua & Barbuda (+1268)')],
            ['value' => '+54','label' => __('Argentina (+54)')],
            ['value' => '+374','label' => __('Armenia (+374)')],
            ['value' => '+297','label' => __('Aruba (+297)')],
            ['value' => '+61','label' => __('Australia (+61)')],
            ['value' => '+43','label' => __('Austria (+43)')],
            ['value' => '+994','label' => __('Azerbaijan (+994)')],
            ['value' => '+1242','label' => __('Bahamas (+1242)')],
            ['value' => '+973','label' => __('Bahrain (+973)')],
            ['value' => '+880','label' => __('Bangladesh (+880)')],
            ['value' => '+1246','label' => __('Barbados (+1246)')],
            ['value' => '+375','label' => __('Belarus (+375)')],
            ['value' => '+32','label' => __('Belgium (+32)')],
            ['value' => '+501','label' => __('Belize (+501)')],
            ['value' => '+229','label' => __('Benin (+229)')],
            ['value' => '+1441','label' => __('Bermuda (+1441)')],
            ['value' => '+975','label' => __('Bhutan (+975)')],
            ['value' => '+591','label' => __('Bolivia (+591)')],
            ['value' => '+387','label' => __('Bosnia Herzegovina (+387)')],
            ['value' => '+267','label' => __('Botswana (+267)')],
            ['value' => '+55','label' => __('Brazil (+55)')],
            ['value' => '+673','label' => __('Brunei (+673)')],
            ['value' => '+359','label' => __('Bulgaria (+359)')],
            ['value' => '+226','label' => __('Burkina Faso (+226)')],
            ['value' => '+257','label' => __('Burundi (+257)')],
            ['value' => '+855','label' => __('Cambodia (+855)')],
            ['value' => '+237','label' => __('Cameroon (+237)')],
            ['value' => '+238','label' => __('Cape Verde Islands (+238)')],
            ['value' => '+1345','label' => __('Cayman Islands (+1345)')],
            ['value' => '+236','label' => __('Central African Republic (+236)')],
            ['value' => '+56','label' => __('Chile (+56)')],
            ['value' => '+86','label' => __('China (+86)')],
            ['value' => '+57','label' => __('Colombia (+57)')],
            ['value' => '+269','label' => __('Comoros (+269)')],
            ['value' => '+242','label' => __('Congo (+242)')],
            ['value' => '+682','label' => __('Cook Islands (+682)')],
            ['value' => '+506','label' => __('Costa Rica (+506)')],
            ['value' => '+385','label' => __('Croatia (+385)')],
            ['value' => '+53','label' => __('Cuba (+53)')],
            ['value' => '+90392','label' => __('Cyprus North (+90392)')],
            ['value' => '+357','label' => __('Cyprus South (+357)')],
            ['value' => '+42','label' => __('Czech Republic (+42)')],
            ['value' => '+45','label' => __('Denmark (+45)')],
            ['value' => '+253','label' => __('Djibouti (+253)')],
            ['value' => '+1809','label' => __('Dominica (+1809)')],
            ['value' => '+1809','label' => __('Dominican Republic (+1809)')],
            ['value' => '+593','label' => __('Ecuador (+593)')],
            ['value' => '+20','label' => __('Egypt (+20)')],
            ['value' => '+503','label' => __('El Salvador (+503)')],
            ['value' => '+240','label' => __('Equatorial Guinea (+240)')],
            ['value' => '+291','label' => __('Eritrea (+291)')],
            ['value' => '+372','label' => __('Estonia (+372)')],
            ['value' => '+251','label' => __('Ethiopia (+251)')],
            ['value' => '+500','label' => __('Falkland Islands (+500)')],
            ['value' => '+298','label' => __('Faroe Islands (+298)')],
            ['value' => '+679','label' => __('Fiji (+679)')],
            ['value' => '+358','label' => __('Finland (+358)')],
            ['value' => '+33','label' => __('France (+33)')],
            ['value' => '+594','label' => __('French Guiana (+594)')],
            ['value' => '+689','label' => __('French Polynesia (+689)')],
            ['value' => '+241','label' => __('Gabon (+241)')],
            ['value' => '+220','label' => __('Gambia (+220)')],
            ['value' => '+7880','label' => __('Georgia (+7880)')],
            ['value' => '+49','label' => __('Germany (+49)')],
            ['value' => '+233','label' => __('Ghana (+233)')],
            ['value' => '+350','label' => __('Gibraltar (+350)')],
            ['value' => '+30','label' => __('Greece (+30)')],
            ['value' => '+299','label' => __('Greenland (+299)')],
            ['value' => '+1473','label' => __('Grenada (+1473)')],
            ['value' => '+590','label' => __('Guadeloupe (+590)')],
            ['value' => '+671','label' => __('Guam (+671)')],
            ['value' => '+502','label' => __('Guatemala (+502)')],
            ['value' => '+224','label' => __('Guinea (+224)')],
            ['value' => '+245','label' => __('Guinea - Bissau (+245)')],
            ['value' => '+592','label' => __('Guyana (+592)')],
            ['value' => '+509','label' => __('Haiti (+509)')],
            ['value' => '+504','label' => __('Honduras (+504)')],
            ['value' => '+852','label' => __('Hong Kong (+852)')],
            ['value' => '+36','label' => __('Hungary (+36)')],
            ['value' => '+354','label' => __('Iceland (+354)')],
            ['value' => '+91','label' => __('India (+91)')],
            ['value' => '+62','label' => __('Indonesia (+62)')],
            ['value' => '+98','label' => __('Iran (+98)')],
            ['value' => '+964','label' => __('Iraq (+964)')],
            ['value' => '+353','label' => __('Ireland (+353)')],
            ['value' => '+972','label' => __('Israel (+972)')],
            ['value' => '+39','label' => __('Italy (+39)')],
            ['value' => '+1876','label' => __('Jamaica (+1876)')],
            ['value' => '+81','label' => __('Japan (+81)')],
            ['value' => '+962','label' => __('Jordan (+962)')],
            ['value' => '+7','label' => __('Kazakhstan (+7)')],
            ['value' => '+254','label' => __('Kenya (+254)')],
            ['value' => '+686','label' => __('Kiribati (+686)')],
            ['value' => '+850','label' => __('Korea North (+850)')],
            ['value' => '+82','label' => __('Korea South (+82)')],
            ['value' => '+965','label' => __('Kuwait (+965)')],
            ['value' => '+996','label' => __('Kyrgyzstan (+996)')],
            ['value' => '+856','label' => __('Laos (+856)')],
            ['value' => '+371','label' => __('Latvia (+371)')],
            ['value' => '+961','label' => __('Lebanon (+961)')],
            ['value' => '+266','label' => __('Lesotho (+266)')],
            ['value' => '+231','label' => __('Liberia (+231)')],
            ['value' => '+218','label' => __('Libya (+218)')],
            ['value' => '+417','label' => __('Liechtenstein (+417)')],
            ['value' => '+370','label' => __('Lithuania (+370)')],
            ['value' => '+352','label' => __('Luxembourg (+352)')],
            ['value' => '+853','label' => __('Macao (+853)')],
            ['value' => '+389','label' => __('Macedonia (+389)')],
            ['value' => '+261','label' => __('Madagascar (+261)')],
            ['value' => '+265','label' => __('Malawi (+265)')],
            ['value' => '+60','label' => __('Malaysia (+60)')],
            ['value' => '+960','label' => __('Maldives (+960)')],
            ['value' => '+223','label' => __('Mali (+223)')],
            ['value' => '+356','label' => __('Malta (+356)')],
            ['value' => '+692','label' => __('Marshall Islands (+692)')],
            ['value' => '+596','label' => __('Martinique (+596)')],
            ['value' => '+222','label' => __('Mauritania (+222)')],
            ['value' => '+269','label' => __('Mayotte (+269)')],
            ['value' => '+52','label' => __('Mexico (+52)')],
            ['value' => '+691','label' => __('Micronesia (+691)')],
            ['value' => '+373','label' => __('Moldova (+373)')],
            ['value' => '+377','label' => __('Monaco (+377)')],
            ['value' => '+976','label' => __('Mongolia (+976)')],
            ['value' => '+1664','label' => __('Montserrat (+1664)')],
            ['value' => '+212','label' => __('Morocco (+212)')],
            ['value' => '+258','label' => __('Mozambique (+258)')],
            ['value' => '+95','label' => __('Myanmar (+95)')],
            ['value' => '+264','label' => __('Namibia (+264)')],
            ['value' => '+674','label' => __('Nauru (+674)')],
            ['value' => '+977','label' => __('Nepal (+977)')],
            ['value' => '+31','label' => __('Netherlands (+31)')],
            ['value' => '+687','label' => __('New Caledonia (+687)')],
            ['value' => '+64','label' => __('New Zealand (+64)')],
            ['value' => '+505','label' => __('Nicaragua (+505)')],
            ['value' => '+227','label' => __('Niger (+227)')],
            ['value' => '+234','label' => __('Nigeria (+234)')],
            ['value' => '+683','label' => __('Niue (+683)')],
            ['value' => '+672','label' => __('Norfolk Islands (+672)')],
            ['value' => '+670','label' => __('Northern Marianas (+670)')],
            ['value' => '+47','label' => __('Norway (+47)')],
            ['value' => '+968','label' => __('Oman (+968)')],
            ['value' => '+680','label' => __('Palau (+680)')],
            ['value' => '+507','label' => __('Panama (+507)')],
            ['value' => '+675','label' => __('Papua New Guinea (+675)')],
            ['value' => '+595','label' => __('Paraguay (+595)')],
            ['value' => '+51','label' => __('Peru (+51)')],
            ['value' => '+63','label' => __('Philippines (+63)')],
            ['value' => '+48','label' => __('Poland (+48)')],
            ['value' => '+351','label' => __('Portugal (+351)')],
            ['value' => '+1787','label' => __('Puerto Rico (+1787)')],
            ['value' => '+974','label' => __('Qatar (+974)')],
            ['value' => '+262','label' => __('Reunion (+262)')],
            ['value' => '+40','label' => __('Romania (+40)')],
            ['value' => '+7','label' => __('Russia (+7)')],
            ['value' => '+250','label' => __('Rwanda (+250)')],
            ['value' => '+378','label' => __('San Marino (+378)')],
            ['value' => '+239','label' => __('Sao Tome & Principe (+239)')],
            ['value' => '+966','label' => __('Saudi Arabia (+966)')],
            ['value' => '+221','label' => __('Senegal (+221)')],
            ['value' => '+381','label' => __('Serbia (+381)')],
            ['value' => '+248','label' => __('Seychelles (+248)')],
            ['value' => '+232','label' => __('Sierra Leone (+232)')],
            ['value' => '+65','label' => __('Singapore (+65)')],
            ['value' => '+421','label' => __('Slovak Republic (+421)')],
            ['value' => '+386','label' => __('Slovenia (+386)')],
            ['value' => '+677','label' => __('Solomon Islands (+677)')],
            ['value' => '+252','label' => __('Somalia (+252)')],
            ['value' => '+27','label' => __('South Africa (+27)')],
            ['value' => '+34','label' => __('Spain (+34)')],
            ['value' => '+94','label' => __('Sri Lanka (+94)')],
            ['value' => '+290','label' => __('St. Helena (+290)')],
            ['value' => '+1869','label' => __('St. Kitts (+1869)')],
            ['value' => '+1758','label' => __('St. Lucia (+1758)')],
            ['value' => '+249','label' => __('Sudan (+249)')],
            ['value' => '+597','label' => __('Suriname (+597)')],
            ['value' => '+268','label' => __('Swaziland (+268)')],
            ['value' => '+46','label' => __('Sweden (+46)')],
            ['value' => '+41','label' => __('Switzerland (+41)')],
            ['value' => '+963','label' => __('Syria (+963)')],
            ['value' => '+886','label' => __('Taiwan (+886)')],
            ['value' => '+7','label' => __('Tajikstan (+7)')],
            ['value' => '+66','label' => __('Thailand (+66)')],
            ['value' => '+228','label' => __('Togo (+228)')],
            ['value' => '+676','label' => __('Tonga (+676)')],
            ['value' => '+1868','label' => __('Trinidad & Tobago (+1868)')],
            ['value' => '+216','label' => __('Tunisia (+216)')],
            ['value' => '+90','label' => __('Turkey (+90)')],
            ['value' => '+7','label' => __('Turkmenistan (+7)')],
            ['value' => '+993','label' => __('Turkmenistan (+993)')],
            ['value' => '+1649','label' => __('Turks & Caicos Islands (+1649)')],
            ['value' => '+688','label' => __('Tuvalu (+688)')],
            ['value' => '+256','label' => __('Uganda (+256)')],
            ['value' => '+380','label' => __('Ukraine (+380)')],
            ['value' => '+971','label' => __('United Arab Emirates (+971)')],
            ['value' => '+598','label' => __('Uruguay (+598)')],
            ['value' => '+7','label' => __('Uzbekistan (+7)')],
            ['value' => '+678','label' => __('Vanuatu (+678)')],
            ['value' => '+379','label' => __('Vatican City (+379)')],
            ['value' => '+58','label' => __('Venezuela (+58)')],
            ['value' => '+84','label' => __('Vietnam (+84)')],
            ['value' => '+84','label' => __('Virgin Islands - British (+1284)')],
            ['value' => '+84','label' => __('Virgin Islands - US (+1340)')],
            ['value' => '+681','label' => __('Wallis & Futuna (+681)')],
            ['value' => '+969','label' => __('Yemen (North)(+969)')],
            ['value' => '+967','label' => __('Yemen (South)(+967)')],
            ['value' => '+260','label' => __('Zambia (+260)')],
            ['value' => '+263','label' => __('Zimbabwe (+263)')],
        ];
    }
}
