<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\Config\Source;

class CustomerNumber implements \Magento\Framework\Option\ArrayInterface
{
    public function toOptionArray()
    {
        return [
            ['value' => 'shipping_add_no', 'label' => __('Shipping Address Number')],
            ['value' => 'billing_add_no', 'label' => __('Billing Address Number')],
            ['value' => 'both', 'label' => __('Both')],
        ];
    }
}
