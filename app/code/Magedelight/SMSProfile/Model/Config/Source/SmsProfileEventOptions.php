<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\Config\Source;

use Magedelight\SMSProfile\Model\TransactionType;
use Magento\Framework\Data\OptionSourceInterface;

class SmsProfileEventOptions implements OptionSourceInterface
{
    public function toOptionArray()
    {
        return [
            ['value' => TransactionType::SIGN_UP, 'label' => __('Send Otp At Customer Signup Event')],
            ['value' => TransactionType::LOGIN, 'label' => __('Send Otp At Customer Login Event')],
            ['value' => TransactionType::EDIT, 'label' => __('Send Otp At Customer Account Update Event')],
            ['value' => TransactionType::COD_OTP, 'label' => __('Send Otp For COD Payment Method During Checkout')],
            ['value' => TransactionType::FORGOT, 'label' => __('Send Otp For Forgot Password Event')],
        ];
    }
}
