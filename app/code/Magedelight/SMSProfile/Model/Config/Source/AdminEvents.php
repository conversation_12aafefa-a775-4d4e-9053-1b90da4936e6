<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\Config\Source;

class AdminEvents implements \Magento\Framework\Option\ArrayInterface
{
    public function toOptionArray()
    {
        return [
            ['value' => 'admin_new_customer', 'label' => __('New Registration')],
            ['value' => 'admin_new_order', 'label' => __('New Order')],
            ['value' => 'admin_customer_contact', 'label' => __('Customer Contact')],
        ];
    }
}
