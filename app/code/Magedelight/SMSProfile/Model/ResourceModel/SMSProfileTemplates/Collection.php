<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\ResourceModel\SMSProfileTemplates;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    protected $_idFieldName = 'entity_id';
    protected $_eventPrefix = 'smsprofiletemplates_collection';
    protected $_eventObject = 'smsprofiletemplates_collection';

    protected function _construct()
    {
        $this->_init(
            '\Magedelight\SMSProfile\Model\SMSProfileTemplates',
            '\Magedelight\SMSProfile\Model\ResourceModel\SMSProfileTemplates'
        );
    }

    protected function _initSelect()
    {
        parent::_initSelect();
    }
}
