<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\ResourceModel\SMSMailTemplates;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    protected $_idFieldName = 'entity_id';
    protected $_eventPrefix = 'smsmailtemplates_collection';
    protected $_eventObject = 'smsmailtemplates_collection';

    protected function _construct()
    {
        $this->_init(
            '\Magedelight\SMSProfile\Model\SMSMailTemplates',
            '\Magedelight\SMSProfile\Model\ResourceModel\SMSMailTemplates'
        );
    }

    protected function _initSelect()
    {
        parent::_initSelect();
    }
}
