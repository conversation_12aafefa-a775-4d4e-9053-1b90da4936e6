<?php

namespace Magedelight\SMSProfile\Model;

interface SenderInterface
{
    public const VENDOR = '';

    /**
     * Email and SMS sender interface
     *
     * @param string $contact
     * @param string $message
     * @param string $type
     * @return boolean|\Magento\Framework\Exception\LocalizedException
     */
    public function send($contact, $message, $type);

    /**
     * Return SMS vendor Name
     *
     * @return string
     */
    public function getVendor();
}
