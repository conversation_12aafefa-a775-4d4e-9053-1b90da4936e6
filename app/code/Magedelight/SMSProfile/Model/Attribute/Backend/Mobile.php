<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
namespace Magedelight\SMSProfile\Model\Attribute\Backend;

use Magento\Catalog\Model\Product;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Module\Manager;

class Mobile extends \Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend
{
    /**
     * @var Manager
     */
    private Manager $moduleManager;

    /**
     * Mobile constructor.
     * @param Manager $moduleManager
     */
    public function __construct(
        Manager $moduleManager
    ) {
        $this->moduleManager = $moduleManager;
    }

    /**
     * Generate and set unique SKU to product
     *
     * @param Product $object
     * @return void
     */
    protected function checkUniquePhone($object)
    {
        $attribute = $this->getAttribute();
        $entity = $attribute->getEntity();
        $increment = null;
        while (!$entity->checkAttributeUniqueValue($attribute, $object)) {
            throw new NoSuchEntityException(__('Account with mobile number already exist.'));
        }
    }

    /**
     * Make SKU unique before save
     *
     * @param Product $object
     * @return $this
     */
    public function beforeSave($object)
    {
        if ($this->moduleManager->isOutputEnabled('Magedelight_SMSProfile')) {
            $this->checkUniquePhone($object);
        }
        return parent::beforeSave($object);
    }
}
