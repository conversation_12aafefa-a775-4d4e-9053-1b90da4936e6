<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

declare(strict_types=1);

namespace Magedelight\SMSProfile\Model\Resolver;

use Magento\Framework\Exception\AuthenticationException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthenticationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magedelight\SMSProfile\Api\SMSProfieApiServicesInterface;
use Magento\Quote\Api\Data\PaymentInterfaceFactory;
use Magento\Quote\Model\MaskedQuoteIdToQuoteIdInterface;

/**
 * Customers Token resolver, used for GraphQL request processing.
 */
class SavePaymentInformationAndPlaceOrderWithOtpForCustomer implements ResolverInterface
{
    /**
     * @var SMSProfieApiServicesInterface
     */
    private $smsProfieApiServices;

    /**
     * @var PaymentInterfaceFactory
     */
    protected $paymentInterfaceFactory;

    /**
     * @var MaskedQuoteIdToQuoteIdInterface
     */
    private $maskedQuoteIdToQuoteId;

    /**
     * SavePaymentInformationAndPlaceOrderWithOtpForCustomer constructor.
     * @param SMSProfieApiServicesInterface $smsProfieApiServices
     * @param PaymentInterfaceFactory $paymentInterfaceFactory
     * @param MaskedQuoteIdToQuoteIdInterface $maskedQuoteIdToQuoteId
     */
    public function __construct(
        SMSProfieApiServicesInterface $smsProfieApiServices,
        PaymentInterfaceFactory $paymentInterfaceFactory,
        MaskedQuoteIdToQuoteIdInterface $maskedQuoteIdToQuoteId
    ) {
        $this->paymentInterfaceFactory = $paymentInterfaceFactory;
        $this->smsProfieApiServices = $smsProfieApiServices;
        $this->maskedQuoteIdToQuoteId = $maskedQuoteIdToQuoteId;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args = null
    ) {
        if (!isset($args['cartId']) || empty($args['cartId'])) {
            throw new GraphQlInputException(__('Specify the "cartId" value.'));
        }

        if (!isset($args['mobile']) || empty($args['mobile'])) {
            throw new GraphQlInputException(__('Specify the "mobile" value.'));
        }

        if (!isset($args['otp']) || empty($args['otp'])) {
            throw new GraphQlInputException(__('Specify the "otp" value.'));
        }

        if (!isset($args['paymentInformation']) || empty($args['paymentInformation'])) {
            throw new GraphQlInputException(__('Specify the "paymentMethod" value.'));
        }

        if (!isset($args['paymentInformation']['paymentMethod']) ||
            empty($args['paymentInformation']['paymentMethod'])) {
            throw new GraphQlInputException(__('Specify the "paymentMethod" value.'));
        }

        try {
            $paymentMethodInput = $args['paymentInformation']['paymentMethod'];
            $paymentMethod = $this->paymentInterfaceFactory->create([ 'data' => $paymentMethodInput ]);
            $cartId = $this->maskedQuoteIdToQuoteId->execute($args['cartId']);
            $response = $this->smsProfieApiServices->savePaymentInformationAndPlaceOrderWithOtpForUser(
                $cartId,
                $args['mobile'],
                $args['otp'],
                $paymentMethod
            );
            return ["message" => "Payment Information Successfully Saved", "orderId" => $response];
        } catch (AuthenticationException $e) {
            throw new GraphQlAuthenticationException(__($e->getMessage()), $e);
        }
    }
}
