<?php


namespace Magedelight\SMSProfile\Observer;

use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magento\Captcha\Model\ResourceModel\Log;
use Magento\Captcha\Model\ResourceModel\LogFactory;
use Magento\Customer\Model\Customer;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;

class ResetCaptcha implements ObserverInterface
{
    /**
     * @var LogFactory
     */
    public LogFactory $resLogFactory;
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;

    /**
     * @param LogFactory $resLogFactory
     * @param ConfigHelper $configHelper
     */
    public function __construct(
        LogFactory $resLogFactory,
        ConfigHelper $configHelper
    ) {
        $this->resLogFactory = $resLogFactory;
        $this->configHelper = $configHelper;
    }

    /**
     * Reset Attempts For Frontend
     *
     * @param Observer $observer
     * @return Log|void
     * @throws LocalizedException
     */
    public function execute(Observer $observer)
    {
        if ($this->configHelper->isModuleEnable()) {
            /** @var Customer $model */
            $model = $observer->getModel();

            return $this->resLogFactory->create()->deleteUserAttempts($model->getCustomerMobile());
        }
    }
}
