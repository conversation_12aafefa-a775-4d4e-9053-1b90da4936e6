<?php


namespace Magedelight\SMSProfile\Observer;

use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class AddCustomHandle implements ObserverInterface
{
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;

    /**
     * AddCustomHandle constructor.
     * @param ConfigHelper $configHelper
     */
    public function __construct(ConfigHelper $configHelper)
    {
        $this->configHelper = $configHelper;
    }
    /**
     * Observer for layout_load_before
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        if (!$this->configHelper->isModuleEnable()) {
            return $this;
        }

        if ($observer->getEvent()->getFullActionName() == 'customer_account_login') {
            $observer->getEvent()->getLayout()->getUpdate()->addHandle('update_login');
        }

        if ($observer->getEvent()->getFullActionName() == 'customer_account_forgotpassword') {
            $observer->getEvent()->getLayout()->getUpdate()->addHandle('update_forgotpassword');
        }

        if ($observer->getEvent()->getFullActionName() == 'customer_account_create') {
            $observer->getEvent()->getLayout()->getUpdate()->addHandle('update_create');
        }

        if (!in_array(
            $observer->getEvent()->getFullActionName(),
            [
                'customer_account_login',
                'customer_account_forgotpassword',
                'customer_account_create',
                'customer_account_edit',
            ]
        ) && $this->configHelper->isPopupEnabled()) {
            $observer->getEvent()->getLayout()->getUpdate()->addHandle('update_default');
        }
    }
}
