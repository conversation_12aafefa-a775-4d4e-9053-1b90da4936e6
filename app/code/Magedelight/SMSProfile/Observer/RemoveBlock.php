<?php

/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */


namespace Magedelight\SMSProfile\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magedelight\SMSProfile\Helper\Data as HelperData;

class RemoveBlock implements ObserverInterface
{


    /**  @var HelperData */
    private $datahelper;

    /**
     * Constructor
     *
     * @param HelperData $dataHelper
     */
    public function __construct(
        HelperData $dataHelper
    ) {
        $this->datahelper = $dataHelper;
    }

    /**
     * Execute
     *
     * @param Observer $observer
     */
    public function execute(Observer $observer)
    {
        /** @var \Magento\Framework\View\Layout $layout */
        $layout = $observer->getLayout();
        if ($this->datahelper->getModuleStatus() && $this->datahelper->loginPopupEnable()) {
            $block = $layout->getBlock('top.links');
            if ($block) {
                $layout->unsetElement('register-link');
            }
        }
    }
}
