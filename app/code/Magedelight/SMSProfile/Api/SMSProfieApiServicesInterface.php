<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Api;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * @api
 */
interface SMSProfieApiServicesInterface
{
    /**
     * Send Otp To Customer.
     *
     * @param int $resend
     * @param int $storeId
     * @param string $mobile
     * @param string $eventType
     * @return string OTP created
     * @throws NoSuchEntityException
     */
    public function sendOtpToCustomer($resend, $storeId, $mobile, $eventType);

    /**
     * Customer Login with OTP only.
     *
     * @param string $mobile
     * @param string $otp
     * @param int $websiteId
     * @return string
     * @throws NoSuchEntityException
     */
    public function createCustomerTokenWithOtp($mobile, $otp, $websiteId);

    /**
     * Customer Login with OTP With Password .
     *
     * @param string $mobile
     * @param string $otp
     * @param string $password
     * @param int $websiteId
     * @return string
     * @throws NoSuchEntityException
     */
    public function createCustomerTokenWithOtpPassword($mobile, $otp, $password, $websiteId);

    /**
     * Create customer account with otp. Perform necessary business operations like sending email.
     *
     * @param CustomerInterface $customer
     * @param int $mobile
     * @param string $countryCode
     * @param string $otp
     * @param string $password
     * @param string $redirectUrl
     * @return CustomerInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function createAccountWithOtp(
        CustomerInterface $customer,
        $mobile,
        $countryCode,
        $otp,
        $password = null,
        $redirectUrl = ''
    );

    /**
     * Update customer account with otp. Perform necessary business operations.
     *
     * @param CustomerInterface $customer
     * @param array $inputData
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function updateMobileWithOtp(
        CustomerInterface $customer,
        $inputData
    );

    /**
     * Send an email to the customer with a password reset link.
     *
     * @param string $mobile
     * @param string $otp
     * @param string $template
     * @param int $websiteId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function initiatePasswordResetWithOTP($mobile, $otp, $template, $websiteId);

    /**
     * Set payment information and place order for a specified cart With OTP.
     *
     * @param string $cartId
     * @param string $email
     * @param int $mobile
     * @param string $otp
     * @param \Magento\Quote\Api\Data\PaymentInterface $paymentMethod
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @return int Order ID.
     */
    public function savePaymentInformationAndPlaceOrderWithOtp(
        $cartId,
        $email,
        $mobile,
        $otp,
        \Magento\Quote\Api\Data\PaymentInterface $paymentMethod
    );

    /**
     * Set payment information and place order for a specified cart with OTP for user.
     *
     * @param int $cartId
     * @param int $mobile
     * @param string $otp
     * @param \Magento\Quote\Api\Data\PaymentInterface $paymentMethod
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @return int Order ID.
     */
    public function savePaymentInformationAndPlaceOrderWithOtpForUser(
        $cartId,
        $mobile,
        $otp,
        \Magento\Quote\Api\Data\PaymentInterface $paymentMethod
    );
}
