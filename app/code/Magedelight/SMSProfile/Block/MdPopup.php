<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Block;

class MdPopup extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;

    /**
     * @var \Magedelight\SMSProfile\Helper\Data
     */
    private $dataHelper;

    /**
     * @var \Magento\Framework\Json\EncoderInterface
     */
    private $jsonEncoder;

    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magedelight\SMSProfile\Helper\Data $dataHelper,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Framework\Json\EncoderInterface $jsonEncoder,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->dataHelper = $dataHelper;
        $this->customerSession = $customerSession;
        $this->jsonEncoder = $jsonEncoder;
    }

    /**
     * @return bool
     */
    public function isModuleEnabled()
    {
        return $this->dataHelper->getModuleStatus()
            && !$this->customerSession->isLoggedIn();
    }

    /**
     * @return bool
     */
    public function isPopupEnabled()
    {
        return $this->dataHelper->loginPopupEnable();
    }
}
