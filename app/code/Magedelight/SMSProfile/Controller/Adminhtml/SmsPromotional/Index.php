<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Controller\Adminhtml\SmsPromotional;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\View\Result\PageFactory as ResultPageFactory;

class Index extends Action
{

    /**
    * @var string
    */
    const ACTION_RESOURCE = 'Magedelight_SMSProfile::smspromotional';

     /**
      * @var ResultPageFactory
      */
    private $resultPageFactory;

    /**
     * @param Context $context
     * @param ResultPageFactory $resultPageFactory
     */
    public function __construct(
        Context $context,
        ResultPageFactory $resultPageFactory
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
    }

    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed(self::ACTION_RESOURCE);
    }

    /**
     * SmsLog grid for AJAX request
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Magedelight_SMSProfile::smsprofile');
        $resultPage->addBreadcrumb(__('Magedelight'), __('Magedelight'));
        $resultPage->addBreadcrumb(__('SmsPromotional'), __('SmsPromotional'));
        $resultPage->getConfig()->getTitle()->prepend((__('SMS Promotional')));
        return $resultPage;
    }
}
