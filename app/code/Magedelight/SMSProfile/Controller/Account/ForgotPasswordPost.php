<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Controller\Account;

use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magedelight\SMSProfile\Helper\OtpHelper;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Encryption\Encryptor;
use Magento\Framework\Message\ManagerInterface;

/**
 * ForgotPasswordPost controller
 * @codingStandardsIgnoreFile
 */
class ForgotPasswordPost implements HttpPostActionInterface
{
    /**
     * @var RedirectFactory
     */
    private RedirectFactory $resultRedirectFactory;
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;
    /**
     * @var ManagerInterface
     */
    private ManagerInterface $messageManager;
    /**
     * @var Encryptor
     */
    private Encryptor $encryptor;
    /**
     * @var Session
     */
    private Session $session;
    /**
     * @var CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * @var OtpHelper
     */
    private OtpHelper $otpHelper;
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;

    /**
     * ForgotPasswordPost constructor.
     * @param RedirectFactory $resultRedirectFactory
     * @param RequestInterface $request
     * @param ManagerInterface $messageManager
     * @param Encryptor $encryptor
     * @param Session $session
     * @param CustomerRepositoryInterface $customerRepository
     * @param OtpHelper $otpHelper
     * @param ConfigHelper $configHelper
     */
    public function __construct(
        RedirectFactory $resultRedirectFactory,
        RequestInterface $request,
        ManagerInterface $messageManager,
        Encryptor $encryptor,
        Session $session,
        CustomerRepositoryInterface $customerRepository,
        OtpHelper $otpHelper,
        ConfigHelper $configHelper
    )
    {

        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->request = $request;
        $this->messageManager = $messageManager;
        $this->encryptor = $encryptor;
        $this->session = $session;
        $this->customerRepository = $customerRepository;
        $this->otpHelper = $otpHelper;
        $this->configHelper = $configHelper;
    }

    /**
     * @return \Magento\Framework\Controller\Result\Redirect
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();

        $password = $this->request->getParam('password');
        $passwordConfirmation = $this->request->getParam('password_confirmation');
        if ($password != $passwordConfirmation) {
            $this->messageManager->addErrorMessage(__("Password and confirm password are not same."));
            return $resultRedirect->setPath('customer/account/forgotpassword');
        }

        $contact = $this->request->getParam('customer_mobile');
        $dialCode = $this->request->getParam('countryreg');
        $otp = (string)$this->request->getParam('otp');
        $type = $this->otpHelper->getContactType($contact);
        if ($this->configHelper->isCountryCodeRequire()) {
            if ($type == OtpHelper::CONTACT_TYPE_PHONE) {
                $contact = $dialCode.$contact;
            }
        }

        try {
            $otpInstance = $this->otpHelper->verifyOtp($contact, $otp);
            $customer = $this->customerRepository->getById($otpInstance->getCustomerId());
            $passwordHash =$this->encryptor->getHash($password, true);
            $this->customerRepository->save($customer, $passwordHash);
            $this->messageManager->addSuccessMessage(
                __('You have successfully reset password.')
            );
            return $resultRedirect->setPath('customer/account/login');
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__($e->getMessage()));
            return $resultRedirect->setPath('customer/account/forgotpassword');
        }
    }
}
