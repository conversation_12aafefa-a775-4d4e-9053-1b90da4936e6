<?php

namespace Magedelight\SMSProfile\Controller\Account;

use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magedelight\SMSProfile\Helper\OtpHelper;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Account\Redirect as AccountRedirect;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Framework\Stdlib\Cookie\PhpCookieManager;

class LoginPost implements HttpPostActionInterface
{
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;
    /**
     * @var RedirectFactory
     */
    private RedirectFactory $resultRedirectFactory;
    /**
     * @var OtpHelper
     */
    private OtpHelper $otpHelper;
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;
    /**
     * @var ManagerInterface
     */
    private ManagerInterface $messageManager;
    /**
     * @var Session
     */
    private Session $session;
    /**
     * @var CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * @var AccountRedirect
     */
    private AccountRedirect $accountRedirect;
    /**
     * @var ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var PhpCookieManager
     */
    private PhpCookieManager $cookieMetadataManager;
    /**
     * @var CookieMetadataFactory
     */
    private CookieMetadataFactory $cookieMetadataFactory;

    /**
     * LoginPost constructor.
     * @param RequestInterface $request
     * @param RedirectFactory $resultRedirectFactory
     * @param OtpHelper $otpHelper
     * @param ConfigHelper $configHelper
     * @param ManagerInterface $messageManager
     * @param Session $session
     * @param CustomerRepositoryInterface $customerRepository
     * @param AccountRedirect $accountRedirect
     * @param ScopeConfigInterface $scopeConfig
     * @param PhpCookieManager $cookieMetadataManager
     * @param CookieMetadataFactory $cookieMetadataFactory
     */
    public function __construct(
        RequestInterface $request,
        RedirectFactory $resultRedirectFactory,
        OtpHelper $otpHelper,
        ConfigHelper $configHelper,
        ManagerInterface $messageManager,
        Session $session,
        CustomerRepositoryInterface $customerRepository,
        AccountRedirect $accountRedirect,
        ScopeConfigInterface $scopeConfig,
        PhpCookieManager $cookieMetadataManager,
        CookieMetadataFactory $cookieMetadataFactory
    ) {
        $this->request = $request;
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->otpHelper = $otpHelper;
        $this->configHelper = $configHelper;
        $this->messageManager = $messageManager;
        $this->session = $session;
        $this->customerRepository = $customerRepository;
        $this->accountRedirect = $accountRedirect;
        $this->scopeConfig = $scopeConfig;
        $this->cookieMetadataManager = $cookieMetadataManager;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
    }
    /**
     * @inheritDoc
     */
    public function execute()
    {
        if ($this->session->isLoggedIn()) {
            /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
            $resultRedirect = $this->resultRedirectFactory->create();
            $resultRedirect->setPath('customer/account');
            return $resultRedirect;
        }

        if ($this->request->isPost()) {
            $contact = $this->request->getParam('customer_mobile') ?: false;
            $dialCode = $this->request->getParam('countryreg') ?: false;
            $otp = (string)$this->request->getParam('otp');
            $type = $this->otpHelper->getContactType($contact);
            if ($this->configHelper->isCountryCodeRequire()) {
                if ($type == OtpHelper::CONTACT_TYPE_PHONE) {
                    $contact = $dialCode.$contact;
                }
            }
            try {
                $otpInstance = $this->otpHelper->verifyOtp($contact, $otp);
                // if otp verify then login.
                $customer = $this->customerRepository->getById($otpInstance->getCustomerId());
                $this->session->setCustomerDataAsLoggedIn($customer);
                if ($this->cookieMetadataManager->getCookie('mage-cache-sessid')) {
                    $metadata = $this->cookieMetadataFactory->createCookieMetadata();
                    $metadata->setPath('/');
                    $this->cookieMetadataManager->deleteCookie('mage-cache-sessid', $metadata);
                }
                $redirectUrl = $this->accountRedirect->getRedirectCookie();
                if (!$this->scopeConfig->getValue('customer/startup/redirect_dashboard') && $redirectUrl) {
                    $this->accountRedirect->clearRedirectCookie();
                    $resultRedirect = $this->resultRedirectFactory->create();
                    $resultRedirect->setPath($redirectUrl);
                    return $resultRedirect;
                }
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage(__($e->getMessage()));
            }
        }
        return $this->accountRedirect->getRedirect();
    }
}
