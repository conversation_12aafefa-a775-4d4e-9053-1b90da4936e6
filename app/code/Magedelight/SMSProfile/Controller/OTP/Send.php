<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Controller\OTP;

use Exception;
use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magedelight\SMSProfile\Helper\OtpHelper;
use Magedelight\SMSProfile\Model\Config\Source\SendOtpVia;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem\Driver\Http;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Serialize\Serializer\Json as JsonSerializer;

class Send implements HttpPostActionInterface
{
    private const CAPTCHA_VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";
    public const CUSTOMER_SHOULD_NOT_EXIST = [
        \Magedelight\SMSProfile\Model\TransactionType::SIGN_UP,
        \Magedelight\SMSProfile\Model\TransactionType::EDIT,
    ];
    public const CUSTOMER_SHOULD_EXIST = [
        \Magedelight\SMSProfile\Model\TransactionType::LOGIN,
        \Magedelight\SMSProfile\Model\TransactionType::FORGOT,
        'login'
    ];
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;
    /**
     * @var CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * @var JsonFactory
     */
    private JsonFactory $jsonFactory;
    /**
     * @var ManagerInterface
     */
    private ManagerInterface $messageManager;
    /**
     * @var JsonSerializer
     */
    private JsonSerializer $serializer;
    /**
     * @var Http
     */
    private Http $http;
    /**
     * @var OtpHelper
     */
    private OtpHelper $otpHelper;
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;
    /**
     * @var Curl
     */
    private Curl $curl;

    /**
     * Send constructor.
     * @param RequestInterface $request
     * @param CustomerRepositoryInterface $customerRepository
     * @param JsonFactory $jsonFactory
     * @param ManagerInterface $messageManager
     * @param JsonSerializer $json
     * @param Http $http
     * @param OtpHelper $otpHelper
     * @param ConfigHelper $configHelper
     * @param Curl $curl
     */
    public function __construct(
        RequestInterface $request,
        CustomerRepositoryInterface $customerRepository,
        JsonFactory $jsonFactory,
        ManagerInterface $messageManager,
        JsonSerializer $json,
        Http $http,
        OtpHelper $otpHelper,
        ConfigHelper $configHelper,
        Curl $curl
    ) {
        $this->request = $request;
        $this->customerRepository = $customerRepository;
        $this->jsonFactory = $jsonFactory;
        $this->messageManager = $messageManager;
        $this->serializer = $json;
        $this->http = $http;
        $this->otpHelper = $otpHelper;
        $this->configHelper = $configHelper;
        $this->curl = $curl;
    }

    /**
     * Execute function
     *
     * @return ResponseInterface|Json|ResultInterface
     */
    public function execute()
    {
        $result = $this->jsonFactory->create();
        $formAction = $this->request->getParam('eventType');
        $email = $this->request->getParam('email') ?: false;
        $mobile = $this->request->getParam('mobile') ?: false;
        $dialCode = $this->request->getParam('countrycode') ?: false;
        //remove dial code from mobile
        $mobile = ($mobile && $dialCode) ? str_replace($dialCode, '', $mobile) : $mobile;

        // require to null dialCode if not enable.
        if (!$this->configHelper->isCountryCodeRequire()) {
            $dialCode = null;
        }
        $contact = $email ?: ($dialCode.$mobile);
        $contactType = $this->otpHelper->getContactType($contact);
        $customer = false;
        $customerId = false;

        if ($this->configHelper->isRecaptchaEnable()) {
            if (in_array(
                $this->configHelper->eventToRecaptcha($formAction),
                $this->configHelper->getRecaptchaForms()
            )) {
                $responseData = $this->verifyRecaptcha($this->request->getParam('captchaResponse'));
                if (!$responseData['success']) {
                    $result->setData(['Success' => __('Robot verification failed, please try again.')]);
                    return $result;
                }
            }
        }

        try {
            if (in_array($formAction, self::CUSTOMER_SHOULD_EXIST)) {
                if ($contactType == OtpHelper::CONTACT_TYPE_EMAIL) {
                    $customer = $this->customerRepository->get($contact);
                } else {
                    $customerId = $this->otpHelper->getCustomerIdByPhone(
                        $mobile,
                        $dialCode
                    );
                }
            }
            if (in_array($formAction, self::CUSTOMER_SHOULD_NOT_EXIST)) {
                try {
                    $customerId = $this->otpHelper->getCustomerIdByPhone(
                        $mobile,
                        $dialCode
                    );
                } catch (Exception $e) {
                    $customerId = false;
                    // Customer does not exist, proceed
                }
                if ($customerId) {
                    throw new LocalizedException(__('Account with this number already exist.'));
                }
            }
        } catch (NoSuchEntityException $e) {
            $result->setData(['Success' => __("No customer found with provided details.")]);
            return $result;
        } catch (Exception $e) {
            $result->setData(['Success' => __($e->getMessage())]);
            return $result;
        }
        if ($customerId && !$customer) {
            $customer = $this->customerRepository->getById($customerId);
        }
        if ($customer) {
            $this->otpHelper->setCustomer($customer);
        }

        try {
            $otpInstance = $this->otpHelper->sendOtp($contact, $formAction);
            if ($this->configHelper->isSandbox()) {
                $this->messageManager->addWarningMessage(
                    __("Your OTP is %1. Turn off test mode to hide your OTP here and it will be delivered to either email or SMS.", $otpInstance->getOtp())
                );
            }

            $otpMessage = __('Kindly check your email or Mobile SMS for OTP.');

            if ($this->configHelper->getSendOtpVia() == SendOtpVia::EMAIL) {
                $otpMessage= __('Kindly check your registered email for OTP.');
            } elseif ($this->configHelper->getSendOtpVia() == SendOtpVia::SMS) {
                $otpMessage= __('Kindly check your mobile SMS App for OTP.');
            }
            $result->setData([
                'Success' => __('success'),
                'resend_link_count' => $otpInstance->getAttempts(),
                'otp_message' => $otpMessage
            ]);
        } catch (Exception $e) {
            $result->setData(['Success' => __($e->getMessage())]);
        }

        return $result;
    }

    public function verifyRecaptcha($res)
    {
        $this->curl->addHeader("Content-Type", "application/x-www-form-urlencoded");
        $this->curl->post(
            self::CAPTCHA_VERIFY_URL,
            [
                "secret" => $this->configHelper->getRecaptchaSecretKey(),
                "response" => $res
            ]
        );
        $res = $this->curl->getBody();
        return $this->serializer->unserialize($res);
    }
}
