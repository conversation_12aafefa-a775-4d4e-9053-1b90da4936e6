<?xml version="1.0"?>
 <!--
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
 -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- override block -->
    <preference for="Magento\Customer\Block\Account\Dashboard\Info" type="Magedelight\SMSProfile\Block\Account\Dashboard\Info" />

    <type name="Magento\Checkout\Model\CompositeConfigProvider">
        <arguments>
            <argument name="configProviders" xsi:type="array">
                <item name="smsprofile_configprovider" xsi:type="object">Magedelight\SMSProfile\Model\SmsprofileConfigProvider</item>
            </argument>
        </arguments>
    </type>

    <!-- plugin for  customer Before save -->
    <type name="Magento\Customer\Api\CustomerRepositoryInterface">
        <plugin name="SMSNotification_customer_before_save" type="Magedelight\SMSProfile\Plugin\CustomerRepository" />
    </type>
    <!-- plugin for  magento EE -->
    <type name="Magento\CustomAttributeManagement\Block\Form">
        <plugin name="unset-mobile-attr-ee"
                type="Magedelight\SMSProfile\Plugin\UnsetMobileAttr" sortOrder="10"/>
    </type>
    <type name="Magento\Customer\Block\Form\Login">
        <plugin name="change-email-type"
                type="Magedelight\SMSProfile\Plugin\ChangeEmail" sortOrder="10"/>
    </type>
    <type name="Magento\Customer\Block\Account\Forgotpassword">
        <plugin name="modify-forgot-url"
                type="Magedelight\SMSProfile\Plugin\ForgotUrl" sortOrder="10"/>
    </type>
    <type name="Magento\Customer\Block\Account\AuthenticationPopup">
        <plugin sortOrder="100" name="inject_recaptcha_in_login_popup"
                type="Magedelight\SMSProfile\Plugin\RecaptchaForLoginPopup"/>
    </type>
</config>
