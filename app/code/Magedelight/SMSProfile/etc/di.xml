<?xml version="1.0"?>
<!--
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- SMSProfileLog -->
    <virtualType name="SMSProfileLogGirdFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="SMSProfileLogGirdDataProvider" type="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Magedelight\SMSProfile\Model\ResourceModel\SMSProfileLog\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">SMSProfileLogGirdFilterPool</argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="smsprofilelog_listing_data_source" xsi:type="string">Magedelight\SMSProfile\Model\ResourceModel\SMSProfileLog\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <type name="Magedelight\SMSProfile\Model\ResourceModel\SMSProfileLog\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_smsprofilelog</argument>
            <argument name="eventPrefix" xsi:type="string">smsprofilelog_collection</argument>
            <argument name="eventObject" xsi:type="string">smsprofilelog_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\SMSProfile\Model\ResourceModel\SMSProfileLog</argument>
        </arguments>
    </type>

    <!-- SMSProfileTemplates  -->
    <virtualType name="SMSProfileTemplatesGirdFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="SMSProfileTemplatesGirdDataProvider" type="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Magedelight\SMSProfile\Model\ResourceModel\SMSProfileTemplates\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">SMSProfileTemplatesGirdFilterPool</argument>
        </arguments>
    </virtualType>


    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="smsprofiletemplates_listing_data_source" xsi:type="string">Magedelight\SMSProfile\Model\ResourceModel\SMSProfileTemplates\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <type name="Magedelight\SMSProfile\Model\ResourceModel\SMSProfileTemplates\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_smsprofiletemplates</argument>
            <argument name="eventPrefix" xsi:type="string">smsprofiletemplates_collection</argument>
            <argument name="eventObject" xsi:type="string">smsprofiletemplates_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\SMSProfile\Model\ResourceModel\SMSProfileTemplates</argument>
        </arguments>
    </type>


    <!-- code for service contract -->
    <preference for="Magedelight\SMSProfile\Api\SMSProfileTemplatesRepositoryInterface" type="Magedelight\SMSProfile\Model\SMSProfileTemplatesRepository"/>

    <preference for="Magedelight\SMSProfile\Api\Data\SMSProfileTemplatesInterface" type="Magedelight\SMSProfile\Model\ResourceModel\SMSProfileTemplates"/>

    <preference for="Magedelight\SMSProfile\Api\SMSProfileOtpAttemptRepositoryInterface" type="Magedelight\SMSProfile\Model\SMSProfileOtpAttemptRepository"/>

    <preference for="Magedelight\SMSProfile\Api\Data\SMSProfileOtpAttemptInterface" type="Magedelight\SMSProfile\Model\ResourceModel\SMSProfileOtpAttempt"/>


    <type name="Magento\Checkout\Block\Checkout\LayoutProcessor">
        <!--  <plugin name="SMSNotification_checkout_layoutprocessor" disabled="true"/> -->
        <plugin name="SmsProfile_checkout_layoutprocessor" type="Magedelight\SMSProfile\Plugin\SMSProfileLayoutProcessor" sortOrder="105"/>
    </type>

    <!-- add console command to store previous store customer's phone in eav attribute -->
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="profilesavephone" xsi:type="object">Magedelight\SMSProfile\Console\ProfileSavePhone</item>
            </argument>
        </arguments>
    </type>

    <!-- override model for allow login by mobile number -->
    <type name="Magento\Customer\Model\AccountManagement">
        <plugin name="SmsProfile_checkout_emailvalidate" type="Magedelight\SMSProfile\Plugin\CustomerEmailValidatePlugin" sortOrder="105"/>
    </type>

    <!-- code for webapi -->
    <preference for="Magedelight\SMSProfile\Api\SMSProfieApiServicesInterface" type="Magedelight\SMSProfile\Model\SMSProfieApiServices"/>
    <!-- code for webapi -->

    <!-- SMS Notification -->

    <!-- SMSLog -->
    <virtualType name="SMSLogGirdFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="SMSLogGirdDataProvider" type="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Magedelight\SMSProfile\Model\ResourceModel\SMSLog\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">SMSLogGirdFilterPool</argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="smslog_listing_data_source" xsi:type="string">Magedelight\SMSProfile\Model\ResourceModel\SMSLog\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <type name="Magedelight\SMSProfile\Model\ResourceModel\SMSLog\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_smslog</argument>
            <argument name="eventPrefix" xsi:type="string">smslog_collection</argument>
            <argument name="eventObject" xsi:type="string">smslog_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\SMSProfile\Model\ResourceModel\SMSLog</argument>
        </arguments>
    </type>

    <!-- SMSTemplates  Grid -->
    <virtualType name="SMSTemplatesGirdFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="SMSTemplatesGirdDataProvider" type="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Magedelight\SMSProfile\Model\ResourceModel\SMSTemplates\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">SMSTemplatesGirdFilterPool</argument>
        </arguments>
    </virtualType>


    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="smstemplates_listing_data_source" xsi:type="string">Magedelight\SMSProfile\Model\ResourceModel\SMSTemplates\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <type name="Magedelight\SMSProfile\Model\ResourceModel\SMSTemplates\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_smstemplates</argument>
            <argument name="eventPrefix" xsi:type="string">smstemplates_collection</argument>
            <argument name="eventObject" xsi:type="string">smstemplates_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\SMSProfile\Model\ResourceModel\SMSTemplates</argument>
        </arguments>
    </type>

    <!-- code for service contract -->
    <preference for="Magedelight\SMSProfile\Api\SMSTemplatesRepositoryInterface" type="Magedelight\SMSProfile\Model\SMSTemplatesRepository"/>

    <preference for="Magedelight\SMSProfile\Api\Data\SMSTemplatesInterface" type="Magedelight\SMSProfile\Model\ResourceModel\SMSTemplates"/>

    <preference for="Magedelight\SMSProfile\Api\SMSMailTemplatesRepositoryInterface" type="Magedelight\SMSProfile\Model\SMSMailTemplatesRepository"/>

    <preference for="Magedelight\SMSProfile\Api\Data\SMSMailTemplatesInterface" type="Magedelight\SMSProfile\Model\ResourceModel\SMSMailTemplates"/>
    <!-- Plugin -->
    <type name="Magento\Framework\Mail\Template\TransportBuilder">
        <plugin name="sms_mail_plugin"
                type="Magedelight\SMSProfile\Plugin\TransportBuilder" sortOrder="10"/>
    </type>
    <!-- Plugin -->
    <!-- plugin for  Order After Save -->
    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="OrderAfterSaveSMS" type="Magedelight\SMSProfile\Plugin\SmsOrderAfterSave" sortOrder="20" disabled="false" />
    </type>

    <!-- plugin for  contact After Post -->
    <type  name="Magento\Contact\Controller\Index\CreatePost">
        <plugin name="ContactSMS" sortOrder="1"  disabled="false"
                type="Magedelight\SMSProfile\Plugin\ContactPostPlugin"/>
    </type>
    <type  name="Magento\Customer\Controller\Account\CreatePost">
        <!-- plugin for  customer create account Before Post -->
        <plugin name="CheckOTPConfirmation" sortOrder="1"  disabled="false"
                type="Magedelight\SMSProfile\Plugin\CreateCustomerPlugin"/>
        <!-- plugin for  customer create account After Post -->
        <plugin name="RegisterationSMS" sortOrder="1"  disabled="false"
                type="Magedelight\SMSProfile\Plugin\CreatePostPlugin"/>
    </type>
    <!-- <type name="Magento\Checkout\Block\Checkout\LayoutProcessor">
          <plugin name="SMSNotification_checkout_layoutprocessor" type="Magedelight\SMSProfile\Plugin\LayoutProcessor" sortOrder="100"/>
    </type> -->
    <!-- plugin for  customer edit account Before Post -->
    <type name="Magento\Customer\Controller\Account\EditPost">
        <plugin name="MobileVerifyBeforeCustomerSave" type="Magedelight\SMSProfile\Plugin\EditPostPlugin" />
    </type>

    <type name="Magento\Customer\Api\AccountManagementInterface">
        <plugin name="switch-mobile-with-email"
                type="Magedelight\SMSProfile\Plugin\Login\SwitchMobile" sortOrder="10"/>
    </type>
    <type name="Magento\Quote\Model\QuoteManagement">
        <plugin name="check-cod-otp"
                type="Magedelight\SMSProfile\Plugin\CheckCOD" sortOrder="10"/>
    </type>
    <type name="Magento\Quote\Model\Quote\Payment">
        <plugin name="set-extension-attribute"
                type="Magedelight\SMSProfile\Plugin\SetExtensionAttribute" sortOrder="10"/>
    </type>
</config>
