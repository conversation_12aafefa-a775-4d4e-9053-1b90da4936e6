<?xml version="1.0" encoding="UTF-8"  ?>
<!--
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
     <event name="customer_register_success">
        <observer name="sMSHandleCustomerSaveAfter" instance="Magedelight\SMSProfile\Observer\CustomerRegisterObserver" />
     </event>
    <!-- <event name="layout_generate_blocks_after">
        <observer name="magedelight_smsprofile_remove_block" instance="Magedelight\SMSProfile\Observer\RemoveBlock" />
    </event> -->
</config>
