<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Helper;

class NotificationConfig extends ConfigHelper
{
    public const XML_PATH_CUSTOMER_EVENTS = 'magedelightsmsprofile/customerSms/customer_events';
    public const XML_PATH_SMSLOG_ENABLE = 'magedelightsmsprofile/alllog/smslog/enable';
    public const XML_PATH_SMSLOG_CRON_ENABLE = 'magedelightsmsprofile/alllog/smslog/cron_enable';
    public const XML_PATH_SELECT_CUSTOMER_NO = 'magedelightsmsprofile/customerSms/customer_no';
    public const XML_PATH_ADMIN_EVENTS = 'magedelightsmsprofile/adminnotity/adminSms/admin_events';
    public const XML_PATH_NOTIFYADMIN_ENABLE = 'magedelightsmsprofile/adminnotity/adminSms/notifyadmin';

    /**
     * Get which number to use from order to send notification
     *
     * @return string
     */
    public function getCustomerNotificationNumber()
    {
        return $this->getConfig(self::XML_PATH_SELECT_CUSTOMER_NO);
    }

    /**
     * Get events on which customer will notify.
     *
     * @return false|string[]
     */
    public function getCustomerEvents()
    {
        $events = (string) $this->getConfig(self::XML_PATH_ADMIN_EVENTS);

        $events = explode(",", $events ?: "");
        return $events;
    }

    /**
     * Get events on which admin will notify.
     *
     * @return false|string[]
     */
    public function getAdminEvents()
    {
        $events = (string) $this->getConfig(self::XML_PATH_CUSTOMER_EVENTS);

        $events = explode(",", $events ?: "");
        return $events;
    }

    /**
     * Is Admin notification enable.
     *
     * @return bool
     */
    public function isAdminNotify()
    {
        return (bool) $this->getConfig(self::XML_PATH_NOTIFYADMIN_ENABLE);
    }
}
