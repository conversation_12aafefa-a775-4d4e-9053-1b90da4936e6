<?php


namespace Magedelight\SMSProfile\Helper;

use Magedelight\SMSProfile\Model\Config\Source\RecaptchaForms;
use Magedelight\SMSProfile\Model\Config\Source\Registration\OtpSetting;
use Magedelight\SMSProfile\Model\TransactionType;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Store\Model\ScopeInterface;

class ConfigHelper
{
    public const XML_PATH_ENABLE = 'magedelightsmsprofile/general/enable';
    public const XML_PATH_MOBILE_LOGIN_POPUP_ENABLE = 'magedelightsmsprofile/form_setting/enable_login_popup';
    public const XML_PATH_OTP_RESEND_LIMIT_ENABLE = 'magedelightsmsprofile/otpsetting/otpresend_enable';
    public const XML_PATH_OTP_EXPIRY = 'magedelightsmsprofile/otpsetting/otp_expiry';
    public const XML_PATH_CUSTOMER_COUNTRY = 'magedelightsmsprofile/general/customer_country';
    public const XML_PATH_EMAIL_DOMAIN = 'magedelightsmsprofile/form_setting/email_domain';
    public const XML_PATH_OTP_LENGTH = 'magedelightsmsprofile/otpsetting/otp_length';
    public const XML_PATH_OTP_FORMAT = 'magedelightsmsprofile/otpsetting/otp_format';
    public const XML_PATH_RESEND_TIME = 'magedelightsmsprofile/otpsetting/resend_time';
    public const XML_PATH_OTP_RESEND_LIMIT = 'magedelightsmsprofile/otpsetting/otpresend_limit';
    public const XML_PATH_SEND_OTP_VIA = 'magedelightsmsprofile/communication/send_otp_via';
    public const XML_PATH_MO_LENGTH = 'magedelightsmsprofile/general/default_mo_length';
    public const XML_PATH_DEFAULT_CUSTOMER_COUNTRY = 'magedelightsmsprofile/general/default_customer_country';
    public const XML_PATH_AVAILABLE_COUNTRIES = 'magedelightsmsprofile/general/available_customer_country';
    public const XML_PATH_AUTO_VERITY = 'magedelightsmsprofile/otpsetting/auto_generate_enable';
    public const XML_PATH_RECAPTCHA_STATUS = 'magedelightsmsprofile/otpsetting/recaptcha_settings/enable';
    public const XML_PATH_RECAPTCHA_SITEKEY = 'magedelightsmsprofile/otpsetting/recaptcha_settings/recaptcha_sitekey';
    public const XML_PATH_RECAPTCHA_SECRETKEY = 'magedelightsmsprofile/otpsetting/recaptcha_settings/recaptcha_secret';
    public const XML_PATH_RECAPTCHA_FORMS = 'magedelightsmsprofile/otpsetting/recaptcha_settings/recaptcha_forms';
    public const XML_PATH_SANDBOX = 'magedelightsmsprofile/alllog/sandbox';
    public const XML_PATH_LOGIN_WITH = 'magedelightsmsprofile/form_setting/otp_login';
    public const XML_PATH_PHONE_NOTICE = 'magedelightsmsprofile/general/phone_notice';
    public const XML_PATH_PHONE_REGISTRATION = 'magedelightsmsprofile/form_setting/required_phone';
    public const XML_PATH_OTP_REGISTRATION = 'magedelightsmsprofile/form_setting/verify_mobile';
    public const XML_PATH_EMAILREQUIRED_SIGNUP = 'magedelightsmsprofile/form_setting/required_email';
    public const XML_PATH_OTP_COD = 'magedelightsmsprofile/checkout/otp_cod';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;
    /**
     * @var Json
     */
    protected Json $serializer;

    /**
     * ConfigHelper constructor.
     * @param ScopeConfigInterface $scopeConfig
     * @param Json $serializer
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        Json $serializer
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->serializer = $serializer;
    }

    /**
     * Retrieve config value by path.
     *
     * @param string $path
     * @return mixed
     */
    protected function getConfig($path)
    {
        return $this->scopeConfig->getValue($path, ScopeInterface::SCOPE_STORE);
    }

    /**
     * Is module enable
     *
     * @return bool
     */
    public function isModuleEnable()
    {
        return $this->getConfig(self::XML_PATH_ENABLE);
    }

    /**
     * Is popup enable
     *
     * @return bool
     */
    public function isPopupEnabled()
    {
        return $this->getConfig(self::XML_PATH_MOBILE_LOGIN_POPUP_ENABLE);
    }

    /**
     * Get login options
     *
     * @return bool
     */
    public function getLoginWith()
    {
        return $this->getConfig(self::XML_PATH_LOGIN_WITH);
    }

    /**
     * Get Otp Expiry in minutes
     *
     * @return int
     */
    public function getOTPExpiry(): int
    {
        return (int) $this->getConfig(self::XML_PATH_OTP_EXPIRY);
    }

    /**
     * Get phone note
     *
     * @return string
     */
    public function getPhoneNote()
    {
        return (string) $this->getConfig(self::XML_PATH_PHONE_NOTICE);
    }

    /**
     * Check if country code is require
     *
     * @return bool
     */
    public function isCountryCodeRequire(): bool
    {
        return (bool) $this->getConfig(self::XML_PATH_CUSTOMER_COUNTRY);
    }

    /**
     * Is sandbox enable
     *
     * @return bool
     */
    public function isSandbox()
    {
        return $this->getConfig(self::XML_PATH_SANDBOX);
    }

    /**
     * Get dummy email domain.
     *
     * @return string
     */
    public function getDummyEmailDomain()
    {
        return $this->getConfig(self::XML_PATH_EMAIL_DOMAIN);
    }

    /**
     * Get OTP length
     *
     * @return int
     */
    public function getOtpLength()
    {
        return (int) $this->getConfig(self::XML_PATH_OTP_LENGTH);
    }

    /**
     * Get OTP format
     *
     * @return string
     */
    public function getOtpFormat()
    {
        return $this->getConfig(self::XML_PATH_OTP_FORMAT);
    }

    /**
     * Get OTP resend after
     *
     * @return int
     */
    public function getResendTime()
    {
        $resendTime = (int) $this->getConfig(self::XML_PATH_RESEND_TIME);

        if (!$this->isResendEnable()) {
            $resendTime = 5;
        } elseif ($resendTime < 5) {
            $resendTime = 5;
        }
        return $resendTime;
    }

    /**
     * Get OTP resend limit
     *
     * @return int
     */
    public function getResendLimit()
    {
        return (int) $this->getConfig(self::XML_PATH_OTP_RESEND_LIMIT);
    }

    public function getSendOtpVia()
    {
        return $this->getConfig(self::XML_PATH_SEND_OTP_VIA);
    }

    public function getMobileLength()
    {
        return (int) $this->getConfig(self::XML_PATH_MO_LENGTH);
    }

    public function isResendEnable()
    {
        return (bool) $this->getConfig(self::XML_PATH_OTP_RESEND_LIMIT_ENABLE);
    }

    public function getDefaultCustomerCountry()
    {
        return $this->getConfig(self::XML_PATH_DEFAULT_CUSTOMER_COUNTRY);
    }

    public function canAutoVerify()
    {
        return (bool) $this->getConfig(self::XML_PATH_AUTO_VERITY);
    }

    /**
     * Return if recaptcha enable.
     *
     * @return mixed
     */
    public function isRecaptchaEnable()
    {
        return $this->getConfig(self::XML_PATH_RECAPTCHA_STATUS);
    }

    /**
     * Get enable recaptcha forms.
     *
     * @return mixed
     */
    public function getRecaptchaForms()
    {
        $forms = $this->getConfig(self::XML_PATH_RECAPTCHA_FORMS);
        return $forms ? explode(",", $forms ?? '') : [];
    }

    /**
     * Convert event to recaptcha forms.
     *
     * @param string $event
     * @return string
     */
    public function eventToRecaptcha($event)
    {
        switch ($event) {
            case TransactionType::LOGIN:
                return RecaptchaForms::LOGIN;
            case TransactionType::FORGOT:
                return RecaptchaForms::FORGOT;
            case TransactionType::EDIT:
                return RecaptchaForms::EDIT;
            case TransactionType::SIGN_UP:
                return RecaptchaForms::SIGN_UP;
        }
        return 'none';
    }

    /**
     * Return recaptcha site key according to store
     *
     * @return string
     */
    public function getRecaptchaSiteKey()
    {
        return $this->getConfig(self::XML_PATH_RECAPTCHA_SITEKEY);
    }

    /**
     * Return recaptcha secret key according to store
     *
     * @return string
     */
    public function getRecaptchaSecretKey()
    {
        return $this->getConfig(self::XML_PATH_RECAPTCHA_SECRETKEY);
    }

    public function getAvailableCountries($checkout = false)
    {
        $countries = $this->getConfig(self::XML_PATH_AVAILABLE_COUNTRIES);
        if ($countries) {
            return explode(",", $countries ?? "");
        }
        return null;
    }

    public function isEmailOptional()
    {
        return $this->getConfig(self::XML_PATH_EMAILREQUIRED_SIGNUP);
    }

    public function isMobileRequireOnSignup()
    {
        return $this->getConfig(self::XML_PATH_PHONE_REGISTRATION);
    }

    public function getOTPSettingSignup()
    {
        return $this->getConfig(self::XML_PATH_OTP_REGISTRATION);
    }

    public function isOtpRequiredForCod()
    {
        return (bool) $this->getConfig(self::XML_PATH_OTP_COD);
    }
}
