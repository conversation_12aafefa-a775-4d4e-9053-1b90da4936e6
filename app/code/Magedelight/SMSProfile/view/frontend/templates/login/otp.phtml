<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magedelight\SMSProfile\ViewModel\ConfigHelper $configHelper */
$configHelper = $block->getConfigHelper();
$isCountryRequire = $configHelper->isCountryCodeRequire();
$note = $configHelper->getPhoneNote();
$url = $block->getUrl('smsprofile/account/loginPost');
?>
<form id="otp-login" action="<?= $url ?>" method="post" data-mage-init='{"validation":{}}'>
    <div class="smsprofile-login-mobile login-opt-mobile">
        <fieldset class="fieldset login" data-hasrequired="<?= $escaper->escapeHtml(__('* Required Fields')) ?>">
            <div class="field email required form-field">
                <div class="form-field__control control mobile">
                    <?php if($isCountryRequire): ?>
                        <input type="hidden"value="" name="countryreg" />
                        <input type="hidden" value="" name="countryregcode" />
                    <?php endif; ?>
                    <input name="customer_mobile" value="" autocomplete="off"
                           type="text"
                           placeholder="<?= $escaper->escapeHtmlAttr(__('Please Enter Email or Mobile Number')) ?>"
                           class="form-field__input input-text"
                           title="<?= $escaper->escapeHtmlAttr(__('Mobile Number')) ?>"
                           data-mage-init='{"mage/trim-input":{},"validation":{}}'
                           data-validate="{required:true}">
                    <?php if($note):?>
                        <div class="profile-notice-phone">
                            <span><?= $note ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </fieldset>
    </div>
    <?php if($isCountryRequire): ?>
        <script type="text/x-magento-init">
                {
                    "input[name='customer_mobile']": {
                        "intlTelWidget": {}
                    }
                }
            </script>
    <?php endif; ?>
    <script type="text/x-magento-init">
        {
            "form#otp-login" : {
                "Magedelight_SMSProfile/js/otp-widget": <?= $configHelper->getOtpWidgetConfig(
                    \Magedelight\SMSProfile\Model\TransactionType::LOGIN,
                    "input[name=customer_mobile]",
                    "input[name=countryreg]"
                ); ?>
            }
        }
    </script>
</form>
<?php //todo: move auto submit code to js file ?>
<script>
    require([
            'jquery'
        ],
        function ($) {
            $('form#otp-login').on('otp-verify', function (event, response) {
                if (response.message === 'Verified') {
                    $(this).submit();
                }
            });
        });
</script>
