<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magedelight\SMSProfile\ViewModel\FrontendHelper $frontendHelper */
$frontendHelper = $block->getFrontendHelper();
$both = $frontendHelper->isLoginWithBoth();
?>
<div class="mdsms login-left" <?= $both ? "data-mage-init='{\"mage/tabs\": {\"openedState\": \"active\"}}'" : "" ?>>
    <?php if ($both): ?>
    <div class="mdsms__button-container">
        <div data-role="collapsible" class="btn btn-otp">
            <button class="mdsms__button mdsms__button--new" data-toggle="trigger"><?= __('Login with OTP') ?></button>
        </div>
        <div data-role="collapsible" class="btn btn-password">
            <button class="mdsms__button mdsms__button--old" data-toggle="trigger"><?= __('Login with Password') ?></button>
        </div>
    </div>
    <?php endif; ?>
    <?php if ($frontendHelper->isLoginWithOTP()): ?>
        <div data-role="content" class="mdsms__new-content otp-login">
            <?= $block->getChildHtml('otp-login') ?>
        </div>
    <?php endif; ?>
    <?php if ($frontendHelper->isLoginWithPassword()): ?>
        <div data-role="content" class="mdsms__old-content pwd-login"
            <?php if($frontendHelper->isLoginWithOTP()): ?> style="display: none;" <?php endif; ?>>
            <?= $block->getChildHtml('pwd-login') ?>
        </div>
    <?php endif; ?>
</div>
<script type="text/x-magento-init">
    {
        ".form-login": {
            "validation": {
                "rules": {
                    "login[username]": {
                        "validate-email": false
                    }
                }
            }
        }
    }
</script>

<script>
    require([
            'jquery',
            'mage/translate'
        ],
        function ($, $t) {
            $('.form-login').find('label[for=email]')
                .html("<span>" + $t('Please Enter Email or Mobile Number') + "</span>");
        });
</script>
