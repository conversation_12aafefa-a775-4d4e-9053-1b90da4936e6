<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Form\Register $block */
    /** @var \Magento\Framework\Escaper $escaper */
    /** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */

    /** @var \Magedelight\SMSProfile\ViewModel\ConfigHelper $configHelper */
    $configHelper = $block->getConfigHelper();
    /** @var \Magedelight\SMSProfile\ViewModel\FrontendHelper $frontendHelper */
    $frontendHelper = $block->getFrontendHelper();
    $isCountryRequire = $configHelper->isCountryCodeRequire();
    $note = $configHelper->getPhoneNote();
    $emailOptional = $configHelper->isEmailOptional();
    $mobileVerificationBefore = $frontendHelper->isFirstMobileVerification();
    $otpRequired = $frontendHelper->isOtpVerificationRequireOnSignUp();
    $class = "show";
    $required = $frontendHelper->isMobileRequired();

    /** @var \Magento\Customer\Helper\Address $addressHelper */
    $addressHelper = $block->getData('addressHelper');
    /** @var \Magento\Directory\Helper\Data $directoryHelper */
    $directoryHelper = $block->getData('directoryHelper');
    /** @var \Magento\Customer\ViewModel\Address\RegionProvider $regionProvider */
    $regionProvider = $block->getRegionProvider();
    $formData = $block->getFormData();
    $displayAll = $block->getConfig('general/region/display_all');
    $length = $configHelper->getMobileLength();
    $validationClass = $isCountryRequire ?
        'validate-digits validate-intl-phone' :
        sprintf(
            "validate-digits validate-length validate-digits maximum-length-%s minimum-length-%s",
            $length,
            $length
        );
?>
<?= $block->getChildHtml('form_fields_before') ?>
<?php /* Extensions placeholder */ ?>
<?= $block->getChildHtml('customer.form.register.extra') ?>
<form class="form create account form-create-account"
      action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
      method="post"
      id="form-validate"
      enctype="multipart/form-data"
      autocomplete="off">
    <?= /* @noEscape */ $block->getBlockHtml('formkey') ?>
    <?php if ($mobileVerificationBefore): ?>
      <?php $class="none";?>
      <fieldset class="fieldset mobile-verification">
            <legend class="legend"><span><?= $block->escapeHtml(__('Mobile Verification')) ?></span></legend><br>
            <div class="field field-name-customer_mobile required">
                <label class="label" for="telephone"><span>Customer Mobile</span></label>
                <div class="control">
                  <?php if ($isCountryRequire): ?>
                    <input type="hidden" value="" id="countryreg" name="countryreg" />
                    <input type="hidden" value="" id="countryregcode" name="countryregcode" />
                  <?php endif; ?>
                  <input data-name="create" id="login_mobile" name="customer_mobile"
                         value="" title="customer_mobile"
                         class="input-text required-entry"
                         data-validate="{'validate-number':true,
                         'validate-intl-phone': Boolean(<?= $isCountryRequire ?>)}"
                         autocomplete="off" aria-required="true" type="text"
                         data-mage-init='{"validation":{}}'
                         onkeypress="return event.keyCode != 13;">
                  <div class="profile-notice-phone">
                        <span><?= $note ?></span>
                  </div>
                </div>
          </div>
        </fieldset>
    <?php endif;?>
    <div class="sms-profile-register" style="display: <?=$class;?>">
    <fieldset class="fieldset create info">
        <legend class="legend"><span><?= $escaper->escapeHtml(__('Personal Information')) ?></span></legend><br>
        <input type="hidden" name="success_url" value="<?= $escaper->escapeUrl($block->getSuccessUrl()) ?>">
        <input type="hidden" name="error_url" value="<?= $escaper->escapeUrl($block->getErrorUrl()) ?>">
        <?= $block->getLayout()
                ->createBlock(\Magento\Customer\Block\Widget\Name::class)
                ->setObject($formData)
                ->setForceUseCustomerAttributes(true)
                ->toHtml() ?>
        <?php if (!$mobileVerificationBefore): ?>
            <div class="field field-name-customer_mobile <?= $required ? 'required' : '' ?>">
                <label class="label" for="telephone"><span>Customer Mobile</span></label>
                <div class="control">
                    <?php if ($isCountryRequire): ?>
                        <input type="hidden" value="" id="countryreg" name="countryreg" />
                        <input type="hidden" value="" id="countryregcode" name="countryregcode" />
                    <?php endif; ?>
                    <input data-name="create" id="login_mobile" name="customer_mobile" value="" title="customer_mobile"
                           class="input-text <?= $validationClass ?> <?= $required ? 'required-entry' : '' ?>"
                           autocomplete="off" aria-required="true" type="text" data-mage-init='{"validation":{}}'>
                    <div class="profile-notice-phone">
                        <span><?= $note ?></span>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        <?php if ($block->isNewsletterEnabled()): ?>
            <div class="field choice newsletter">
                <input type="checkbox"
                       name="is_subscribed"
                       title="<?= $escaper->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>"
                       value="1"
                       id="is_subscribed"
                       <?php if ($formData->getIsSubscribed()): ?>checked="checked"<?php endif; ?>
                       class="checkbox">
                <label for="is_subscribed" class="label">
                    <span><?= $escaper->escapeHtml(__('Sign Up for Newsletter')) ?></span>
                </label>
            </div>
            <?php /* Extensions placeholder */ ?>
            <?= $block->getChildHtml('customer.form.register.newsletter') ?>
        <?php endif ?>

        <?php $_dob = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Dob::class) ?>
        <?php if ($_dob->isEnabled()): ?>
            <?= $_dob->setDate($formData->getDob())->toHtml() ?>
        <?php endif ?>

        <?php $_taxvat = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class) ?>
        <?php if ($_taxvat->isEnabled()): ?>
            <?= $_taxvat->setTaxvat($formData->getTaxvat())->toHtml() ?>
        <?php endif ?>

        <?php $_gender = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Gender::class) ?>
        <?php if ($_gender->isEnabled()): ?>
            <?= $_gender->setGender($formData->getGender())->toHtml() ?>
        <?php endif ?>
        <?= $block->getChildHtml('fieldset_create_info_additional') ?>
    </fieldset>
    <?php if ($block->getShowAddressFields()): ?>
        <?php $cityValidationClass = $addressHelper->getAttributeValidationClass('city'); ?>
        <?php $postcodeValidationClass = $addressHelper->getAttributeValidationClass('postcode'); ?>
        <?php $regionValidationClass = $addressHelper->getAttributeValidationClass('region'); ?>
        <fieldset class="fieldset address">
            <legend class="legend"><span><?= $escaper->escapeHtml(__('Address Information')) ?></span></legend><br>
            <input type="hidden" name="create_address" value="1" />

            <?php $_company = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Company::class) ?>
            <?php if ($_company->isEnabled()): ?>
                <?= $_company->setCompany($formData->getCompany())->toHtml() ?>
            <?php endif ?>

            <?php $_telephone = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Telephone::class) ?>
            <?php if ($_telephone->isEnabled()): ?>
                <?= $_telephone->setTelephone($formData->getTelephone())->toHtml() ?>
            <?php endif ?>

            <?php $_fax = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Fax::class) ?>
            <?php if ($_fax->isEnabled()): ?>
                <?= $_fax->setFax($formData->getFax())->toHtml() ?>
            <?php endif ?>

            <?php
                $_streetValidationClass = $addressHelper->getAttributeValidationClass('street');
            ?>

            <div class="field street required">
                <label for="street_1" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?></span>
                </label>
                <div class="control">
                    <input type="text"
                           name="street[0]"
                           value="<?= $escaper->escapeHtmlAttr($formData->getStreet(0)) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?>"
                           id="street_1"
                           class="input-text <?= $escaper->escapeHtmlAttr($_streetValidationClass) ?>">
                    <div class="nested">
                        <?php
                            $_streetValidationClass = $_streetValidationClass !== null ?
                                trim(str_replace('required-entry', '', $_streetValidationClass)) : '';
                            $streetLines = $addressHelper->getStreetLines();
                        ?>
                        <?php for ($_i = 2, $_n = $streetLines; $_i <= $_n; $_i++): ?>
                            <div class="field additional">
                                <label class="label" for="street_<?= /* @noEscape */ $_i ?>">
                                    <span><?= $escaper->escapeHtml(__('Address')) ?></span>
                                </label>
                                <div class="control">
                                    <input type="text"
                                           name="street[<?= /* @noEscape */ $_i ?>]"
                                           value="<?= $escaper->escapeHtml($formData->getStreetLine($_i - 1)) ?>"
                                           title="<?= $escaper->escapeHtmlAttr(__('Street Address %1', $_i)) ?>"
                                           id="street_<?= /* @noEscape */ $_i ?>"
                                           class="input-text <?= $escaper->escapeHtmlAttr($_streetValidationClass) ?>">
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <?php if ($addressHelper->isVatAttributeVisible()): ?>
                <?php $_vatidValidationClass = $addressHelper->getAttributeValidationClass('vat_id'); ?>
                <div class="field taxvat">
                    <label class="label" for="vat_id">
                        <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?></span>
                    </label>
                    <div class="control">
                        <input type="text"
                               name="vat_id"
                               value="<?= $escaper->escapeHtmlAttr($formData->getVatId()) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?>"
                               class="input-text <?= $escaper->escapeHtmlAttr($_vatidValidationClass) ?>"
                               id="vat_id">
                    </div>
                </div>
            <?php endif; ?>

            <div class="field country required">
                <label for="country" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?></span>
                </label>
                <div class="control">
                    <?= $block->getCountryHtmlSelect() ?>
                </div>
            </div>

            <div class="field region required">
                <label for="region_id" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?></span>
                </label>
                <div class="control">
                    <select id="region_id"
                            name="region_id"
                            title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?>"
                            class="validate-select region_id">
                        <option value="">
                            <?= $escaper->escapeHtml(__('Please select a region, state or province.')) ?>
                        </option>
                    </select>
                    <?= /* @noEscape */ $secureRenderer->renderStyleAsTag("display: none;", 'select#region_id') ?>
                    <input type="text"
                           id="region"
                           name="region"
                           value="<?= $escaper->escapeHtml($block->getRegion()) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?>"
                           class="input-text <?= $escaper->escapeHtmlAttr($regionValidationClass) ?>">
                    <?= /* @noEscape */ $secureRenderer->renderStyleAsTag("display: none;", 'input#region') ?>
                </div>
            </div>

            <div class="field required">
                <label for="city" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?></span>
                </label>
                <div class="control">
                    <input type="text"
                           name="city"
                           value="<?= $escaper->escapeHtmlAttr($formData->getCity()) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>"
                           class="input-text <?= $escaper->escapeHtmlAttr($cityValidationClass) ?>"
                           id="city">
                </div>
            </div>

            <div class="field zip required">
                <label for="zip" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?></span>
                </label>
                <div class="control">
                    <input type="text"
                           name="postcode"
                           value="<?= $escaper->escapeHtmlAttr($formData->getPostcode()) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                           id="zip"
                           class="input-text validate-zip-international
                            <?= $escaper->escapeHtmlAttr($postcodeValidationClass) ?>">
                </div>
            </div>

            <?php $addressAttributes = $block->getChildBlock('customer_form_address_user_attributes');?>
            <?php if ($addressAttributes): ?>
                <?php $addressAttributes->setEntityType('customer_address'); ?>
                <?php $addressAttributes->setFieldIdFormat('address:%1$s')->setFieldNameFormat('address[%1$s]');?>
                <?php $block->restoreSessionData($addressAttributes->getMetadataForm(), 'address');?>
                <?= $addressAttributes->setShowContainer(false)->toHtml() ?>
            <?php endif;?>
            <input type="hidden" name="default_billing" value="1">
            <input type="hidden" name="default_shipping" value="1">
        </fieldset>

    <?php endif; ?>
    <fieldset class="fieldset create account"
              data-hasrequired="<?= $escaper->escapeHtmlAttr(__('* Required Fields')) ?>">
        <legend class="legend"><span><?= $escaper->escapeHtml(__('Sign-in Information')) ?></span></legend><br>
        <div class="field <?= !$emailOptional ? 'required' : '' ?>">
            <label for="email_address" class="label"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label>
            <div class="control">
                <input type="email"
                       name="email"
                       autocomplete="email"
                       id="email_address"
                       value="<?= $escaper->escapeHtmlAttr($formData->getEmail()) ?>"
                       title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                       class="input-text"
                       data-mage-init='{"mage/trim-input":{}}'
                       data-validate="{required: Boolean(<?= !$emailOptional ?>), 'validate-email':true}">
            </div>
        </div>
        <div class="field password required">
            <label for="password" class="label"><span><?= $escaper->escapeHtml(__('Password')) ?></span></label>
            <div class="control">
                <input type="password" name="password" id="password"
                       title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                       class="input-text"
                       data-password-min-length="<?=
                        $escaper->escapeHtmlAttr($block->getMinimumPasswordLength()) ?>"
                       data-password-min-character-sets="<?=
                        $escaper->escapeHtmlAttr($block->getRequiredCharacterClassesNumber()) ?>"
                       data-validate="{required:true, 'validate-customer-password':true}"
                       autocomplete="off">
                <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                    <div id="password-strength-meter" class="password-strength-meter">
                        <?= $escaper->escapeHtml(__('Password Strength')) ?>:
                        <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                            <?= $escaper->escapeHtml(__('No Password')) ?>
                        </span>
                    </div>
                </div>
            </div>

        </div>
        <div class="field confirmation required">
            <label for="password-confirmation" class="label">
                <span><?= $escaper->escapeHtml(__('Confirm Password')) ?></span>
            </label>
            <div class="control">
                <input type="password"
                       name="password_confirmation"
                       title="<?= $escaper->escapeHtmlAttr(__('Confirm Password')) ?>"
                       id="password-confirmation"
                       class="input-text"
                       data-validate="{required:true, equalTo:'form.form-create-account input[name=password]'}"
                       autocomplete="off">
            </div>
        </div>
        <div class="field choice" data-bind="scope: 'showPassword'">
            <!-- ko template: getTemplate() --><!-- /ko -->
        </div>
    </fieldset>

    <fieldset class="fieldset additional_info">
        <?= $block->getChildHtml('form_additional_info') ?>
    </fieldset>

    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit"
                    class="action submit primary"
                    title="<?= $escaper->escapeHtmlAttr(__('Create an Account')) ?>"
                    id="send2"
                    <?php if ($block->getButtonLockManager()->isDisabled('customer_create_form_submit')): ?>
                    disabled="disabled"
                    <?php endif; ?>>
                <span><?= $escaper->escapeHtml(__('Create an Account')) ?></span>
            </button>
        </div>
        <div class="secondary">
            <a class="action back"
               href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                <span><?= $escaper->escapeHtml(__('Back')) ?></span>
            </a>
        </div>
    </div>
  </div>
</form>
<?php $ignore = /* @noEscape */ $_dob->isEnabled() ? '\'input[id$="full"]\'' : 'null';
$scriptString = <<<script
require([
    'jquery',
    'mage/mage'
], function($){

    var dataForm = $('#form-validate');
    var ignore = {$ignore};

    dataForm.mage('validation', {
script;
if ($_dob->isEnabled()):
    $scriptString .= <<<script
        errorPlacement: function(error, element) {
            if (element.prop('id').search('full') !== -1) {
                var dobElement = $(element).parents('.customer-dob'),
                    errorClass = error.prop('class');
                error.insertAfter(element.parent());
                dobElement.find('.validate-custom').addClass(errorClass)
                    .after('<div class="' + errorClass + '"></div>');
            }
            else {
                error.insertAfter(element);
            }
        },
        ignore: ':hidden:not(' + ignore + ')'
script;
else:
    $scriptString .= <<<script
        ignore: ignore ? ':hidden:not(' + ignore + ')' : ':hidden'
script;
endif;
$scriptString .= <<<script
    }).find('input:text').attr('autocomplete', 'off');
});
script;
?>
<?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>
<?php if ($block->getShowAddressFields()): ?>
    <?php
    $regionJson = /* @noEscape */ $regionProvider->getRegionJson();
    $regionId = (int) $formData->getRegionId();
    $countriesWithOptionalZip = /* @noEscape */ $directoryHelper->getCountriesWithOptionalZip(true);
    ?>
<?php // phpcs:ignore Magento2.Legacy.PhtmlTemplate?>
<script type="text/x-magento-init">
    {
        "#country": {
            "directoryRegionUpdater": {
                "optionalRegionAllowed": <?= /* @noEscape */ $displayAll ? 'true' : 'false' ?>,
                "regionListId": "#region_id",
                "regionInputId": "#region",
                "postcodeId": "#zip",
                "form": "#form-validate",
                "regionJson": <?= /* @noEscape */ $regionJson ?>,
                "defaultRegion": <?= /* @noEscape */ $regionId ?>,
                "countriesWithOptionalZip": <?=  /* @noEscape */ $countriesWithOptionalZip ?>
            }
        }
    }
</script>
<?php endif; ?>
<?php // phpcs:ignore Magento2.Legacy.PhtmlTemplate?>
<script type="text/x-magento-init">
    {
        ".field.password": {
            "passwordStrengthIndicator": {
                "formSelector": "form.form-create-account"
            }
        },
        "*": {
            "Magento_Customer/js/block-submit-on-send": {
                "formId": "form-validate"
            },
            "Magento_Ui/js/core/app": {
                "components": {
                    "showPassword": {
                        "component": "Magento_Customer/js/show-password",
                        "passwordSelector": "#password,#password-confirmation"
                    }
                }
            }
        }
    }
</script>
<?php if ($isCountryRequire): ?>
    <script type="text/x-magento-init">
            {
                "input[name='customer_mobile']:not([type='hidden']": {
                    "intlTelWidget": {}
                }
            }
        </script>
<?php endif; ?>
<?php if (in_array($configHelper->getSendOtpVia(), ['sms', 'both'])): ?>
    <script>
        require([
                'jquery'
            ],
            function ($) {
                $('form.form-create-account').on('otp-verify', function (event, response) {
                    if (response.message === 'Verified') {
                        $(".sms-profile-register").show();
                        $(".fieldset.mobile-verification").hide();
                    }
                });
            });
    </script>
<?php endif;?>


<?php if ($otpRequired): ?>
    <script type="text/x-magento-init">
            {
                "form.form-create-account" : {
                    "Magedelight_SMSProfile/js/otp-widget": <?= $configHelper->getOtpWidgetConfig(
        \Magedelight\SMSProfile\Model\TransactionType::SIGN_UP,
        "input[name=customer_mobile]",
        "input[name=countryreg]"
    ); ?>
                }
            }
            </script>
<?php endif; ?>
<script type="text/x-magento-init">
    {
        ".form-create-account": {
            "validation": {
                "rules": {
                    "email": {
                        "required": false
                    }
                }
            }
        }
    }
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-validate');
    const submitButton = document.getElementById('send2');
    form.addEventListener('input', handleInputChange);
    form.addEventListener('change', handleInputChange);
    function handleInputChange(event) {
        event.preventDefault(); // Prevent default behavior
        const isValid = form.checkValidity(); // Check validity of the form

        if (isValid) {
            submitButton.disabled = false;
        } else {
            submitButton.disabled = true;
        }
    }
});
</script>