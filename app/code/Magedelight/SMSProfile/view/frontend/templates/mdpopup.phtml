<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
?>
<?php /** @var \Magedelight\SMSProfile\Block\MdPopup $block */
$currentUrl = $block->getUrl('*/*/*', ['_current' => true, '_use_rewrite' => true]);
$myLastElement='';
$getIdentifier=explode('/', $currentUrl);
if (str_contains($currentUrl, 'checkout')) {
    $cnt=count($getIdentifier);
    $myLastElement = $getIdentifier[$cnt-2];
}
if ($block->isModuleEnabled() && !str_contains($currentUrl, 'customer/account/create/') && !str_contains($currentUrl, 'customer/account/login/') && !str_contains($currentUrl, 'customer/account/forgotpassword/') && $myLastElement!='checkout') : ?>
    <div class="md-login-container" data-md-js="md-login-container" style="display: none">
        <div id="md-login-popup" class="md-login-popup" data-md-js="md-tab-container">
            <div class="md-tabs-wrapper" data-md-js="md-tabs-wrapper" data-mage-init='{"tabs":{"openedState":"active"}}'>
                <ul class="md-tablist">
                    <li data-role="collapsible" class="md-title" data-md-js="md-popup-original">
                        <a href="#md-login-content"
                           class="md-link"
                           data-toggle="switch"
                           tabindex="-1"><?= $block->escapeHtml(__('Login')) ?></a>
                    </li>
                    <li data-role="collapsible" class="md-title" data-md-js="md-popup-original">
                        <a href="#md-register-content"
                           class="md-link"
                           data-toggle="switch"
                           tabindex="-1"><?= $block->escapeHtml(__('Register')) ?></a>
                    </li>
                </ul>
                <div class="md-error" style="color: red;"><span></span></div>
                <div class="md-success" style="color: green;"><span></span></div>
                <div id="md-login-content" class="md-content md-login-content" data-role="content">
                    <?= $block->getChildHtml('md_customer_login_popup'); ?>
                </div>
                <div id="md-register-content" class="md-content md-register-content" data-role="content">
                    <?= $block->getChildHtml('md_customer_register_popup'); ?>
                </div>
            </div>
            <div class="md-tabs-wrapper -forgot" data-md-js="md-tabs-wrapper-forgot">
                <ul class="md-tablist">
                    <li class="md-title active">
                        <a href="#md-forgot-content" class="md-link">
                            <?= $block->escapeHtml(__('Forgot Your Password?')) ?>
                        </a>
                    </li>
                </ul>
                <div class="md-error" style="color: red;"><span></span></div>
                <div class="md-success" style="color: green;"><span></span></div>
                <div id="md-forgot-content" class="md-content md-forgot-content">
                    <?= $block->getChildHtml('md_customer_forgot_password'); ?>
                </div>
            </div>
        </div>
        <script type="text/x-magento-init">
        {
            "[data-md-js='md-login-container']" : {
                "Magedelight_SMSProfile/js/md-popup":{
                    "enablePopup": <?= (int) $block->isPopupEnabled() ?>
                }
            }
        }

        </script>
    </div>
<?php endif; ?>
