<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

/** @var \Magedelight\SMSProfile\ViewModel\ConfigHelper $configHelper */
$configHelper = $block->getConfigHelper();
/** @var \Magedelight\SMSProfile\ViewModel\FrontendHelper $frontendHelper */
$frontendHelper = $block->getFrontendHelper();
$isCountryRequire = $configHelper->isCountryCodeRequire();
$note = $configHelper->getPhoneNote();
$required = 0;
$enableModule = $configHelper->isModuleEnable();
$required = $frontendHelper->isMobileRequired();
?>
<?php if ($enableModule):?>
    <fieldset class="fieldset create account" data-hasrequired="<?= __('* Required Fields') ?>">
        <legend class="legend">
            <span><?= __('Additional Information') ?></span>
        </legend>
        <div class="field customer_mobile <?php if ($required) {echo 'required'; }?>">
            <label for="customer_mobile" class="label">
                <span><?= /* @escapeNotVerified */ __('Customer Mobile') ?></span>
            </label>
            <div class="control">
                    <input type="hidden" value="<?= $escaper->escapeHtmlAttr($block->getCountryCode()) ?>" id="countryreg" name="countryreg" />
                    <input type="hidden" value="" id="countryregcode" name="countryregcode" />

                    <input type="text" name="customer_mobile" id="login_mobile"
                           title="<?= __('Customer Mobile') ?>" class="input-text <?php if ($required) {echo 'required-entry ';} ?>"
                        <?php if ($required) {echo 'data-validate="{required:true}"';}?> autocomplete="off"
                           value='<?= $block->getMobile(); ?>'
                           data-val='<?= $block->getMobile(); ?>'
                           data-mage-init='{"validation":{}}' />
                <div class="profile-notice-phone">
                    <span><?= $note; ?></span>
                </div>
            </div>
        </div>
    </fieldset>
    <?php
    echo "<input type='hidden' name='form_type' id='form_type' value='edit'/>";
    if ($block->getMagentoEdition() !='Community') {
        echo "<input type='hidden' class='note' value='".$note."'/>";
    }
    ?>
    <?php if ($isCountryRequire): ?>
        <script type="text/x-magento-init">
            {
                "input[name='customer_mobile']": {
                    "intlTelWidget": {}
                }
            }
        </script>
    <?php endif; ?>
    <?php if ($frontendHelper->isSmsEnable()): ?>
        <script type="text/x-magento-init">
            {
                "form.form-edit-account" : {
                    "Magedelight_SMSProfile/js/otp-widget": <?= $configHelper->getOtpWidgetConfig(
                        \Magedelight\SMSProfile\Model\TransactionType::EDIT,
                        "input[name=customer_mobile]",
                        "input[name=countryreg]"
                    ); ?>
                }
            }
            </script>
    <?php endif; ?>
<?php endif;?>
