<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magedelight\SMSProfile\ViewModel\FrontendHelper $frontendHelper */
$frontendHelper = $block->getFrontendHelper();
?>
<div class="mdsms forgot-parent" data-mage-init='{"mage/tabs": {"openedState": "active", "active": 0}}'>
    <div class="mdsms__button-container">
        <div data-role="collapsible" class="btn btn-otp">
            <button class="mdsms__button mdsms__button--otp" data-toggle="trigger"><?= __('Reset with OTP') ?></button>
        </div>
        <div data-role="collapsible" class="btn btn-password">
            <button class="mdsms__button mdsms__button--email" data-toggle="trigger"><?= __('Reset with Email') ?></button>
        </div>
    </div>
    <div data-role="content" class="mdsms__new-content otp-forgot">
        <?= $block->getChildHtml('otp-forgot') ?>
    </div>
    <div data-role="content" class="mdsms__old-content email-forgot"
        <?php if($frontendHelper->isLoginWithOTP()): ?> style="display: none;" <?php endif; ?>>
        <?= $block->getChildHtml('email-forgot') ?>
    </div>
</div>
