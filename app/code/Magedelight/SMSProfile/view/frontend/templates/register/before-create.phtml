<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magedelight\SMSProfile\ViewModel\ConfigHelper $configHelper */
$configHelper = $block->getConfigHelper();
/** @var \Magedelight\SMSProfile\ViewModel\FrontendHelper $frontendHelper */
$frontendHelper = $block->getFrontendHelper();
$isCountryRequire = $configHelper->isCountryCodeRequire();
$note = $configHelper->getPhoneNote();
?>
<?php if ($frontendHelper->isFirstMobileVerification()): ?>
    <style type="text/css">
        .form.create.account.form-create-account { display: none; }
    </style>
    <form id="before-create" action="#" method="get" onsubmit="return false" data-mage-init='{"validation":{}}'>
        <fieldset class="fieldset mobile-verification">
            <legend class="legend"><span><?= $escaper->escapeHtml(__('Mobile Verification')) ?></span></legend>
            <br>
            <div class="field field-name-customer_mobile required">
                <label class="label" for="telephone"><span>Customer Mobile</span></label>
                <div class="control">
                    <?php if ($isCountryRequire): ?>
                        <input type="hidden" value="" name="countryreg" />
                        <input type="hidden" value="" name="countryregcode" />
                    <?php endif; ?>
                    <input data-name="create" id="login_mobile" name="customer_mobile"
                           value="" title="customer_mobile"
                           class="input-text required-entry"
                           data-validate="{'validate-digits':true,
                             'validate-intl-phone': Boolean(<?= $isCountryRequire ?>)}"
                           autocomplete="off" aria-required="true" type="text"
                           data-mage-init='{"validation":{}}'
                           onkeypress="return event.keyCode != 13;">
                    <div class="profile-notice-phone">
                        <span><?= $note ?></span>
                    </div>
                </div>
            </div>
        </fieldset>
    </form>
    <?php if ($isCountryRequire): ?>
        <script type="text/x-magento-init">
            {
                "input[name='customer_mobile']:not([type='hidden'])": {
                    "intlTelWidget": {}
                }
            }
        </script>
    <?php endif; ?>
    <script type="text/x-magento-init">
        {
            "form#before-create" : {
                "Magedelight_SMSProfile/js/otp-widget": <?= $configHelper->getOtpWidgetConfig(
    \Magedelight\SMSProfile\Model\TransactionType::SIGN_UP,
    "input[name=customer_mobile]",
    "input[name=countryreg]"
); ?>
            }
        }
    </script>
    <script>
        require([
                'jquery'
            ],
            function ($) {
                $('form#before-create').on('otp-verify', function (event, response) {
                    if (response.message === 'Verified') {
                        let form = $('form.form-create-account');
                        $(this).serializeArray().forEach(function(e) {
                            let input = form.find('input[name=' + e.name + ']');
                            if (input.length) {
                                input.val(e.value);
                            }
                        });
                        form.show();
                        $(this).hide();
                    }
                });
            });
    </script>
<?php endif; ?>
