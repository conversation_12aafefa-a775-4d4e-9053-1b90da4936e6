<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
?>

<?php
/** @var \Magedelight\SMSProfile\ViewModel\ConfigHelper $configHelper */
$configHelper = $block->getData('config_helper');
?>
<?php if($configHelper->isModuleEnable() && $configHelper->isRecaptchaEnable()):?>
    <script type="text/javascript">
        var onloadCallback = function() {
            require([
                    'Magedelight_SMSProfile/js/model/captcha-forms'
                ],
                function (captcha) {
                    captcha.initRecaptcha("<?= $configHelper->getRecaptchaSiteKey();?>")
                });
        }
    </script>
	<script src="https://www.google.com/recaptcha/api.js?onload=onloadCallback&render=explicit" async defer>
    </script>
<?php endif;?>
<?php if ($configHelper->isModuleEnable()): ?>
    <?php $scriptString = 'window.smsotp = ' . /* @noEscape */ $configHelper->getSerializedConfig(); ?>
    <?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false); ?>
<?php endif; ?>
