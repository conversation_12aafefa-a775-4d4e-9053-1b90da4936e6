<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

/**
 * @var \Magento\Customer\Block\Form\Login $block
 * @var \Magento\Framework\Escaper $escaper
 */
?>
<?php
    $smsProfileHelper = $block->getData('view_model');
    /** @var \Magedelight\SMSProfile\ViewModel\ConfigHelper $configHelper */
    $configHelper = $block->getConfigHelper();
    /** @var \Magedelight\SMSProfile\Helper\Data $smsProfileHelper */
    $smsProfileHelper = $smsProfileHelper->getHelperData();
    $isModuleEnable = $configHelper->isModuleEnable();
    $loginWith = $configHelper->getLoginWith();
    $isCountryRequire = $configHelper->isCountryCodeRequire();
    $note = $smsProfileHelper->getPhoneNote();
?>
<form class="form form-login" action="<?= $block->escapeUrl($block->getPostActionUrl()) ?>" method="post" id="login-form" data-mage-init='{"validation":{}}'>
    <?= $block->getBlockHtml('formkey') ?>
    <?php if(in_array($loginWith, ['login_both','login_otp'])): ?>
        <div class="sms-profile-login">
            <?php if(in_array($loginWith, ['login_both'])): ?>
                <?= /* @noEscape */ $secureRenderer->renderTag('style', [], ".block-customer-login .block-title{display: none;} .block-customer-login{display: none;}.iti__flag-container{display: none;}", false) ?>
                <div class="smsprofile-login-option">
                    <div class="block-title">
                        <strong id="block-customer-login-heading" role="heading" aria-level="2"><?= $block->escapeHtml(__('Registered Customers')) ?></strong>
                    </div>
                    <div class="login-option-tab">
                        <div class="login-mobile active" data-md-js="md-login-opt">
                            <input type="button" class="login-opt" name="Mobile-Button" id="login-opt-mobile" value="<?= __('Login with OTP') ?>"/>
                        </div>
                        <div class="login-email" data-md-js="md-login-password">
                            <input type="button" class="login-opt" name="Email-Button" id="login-opt-email" value="<?= __('Login with Password') ?>"/>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <?php if(in_array($loginWith, ['login_otp'])): ?>
                <?= /* @noEscape */ $secureRenderer->renderTag('style', [], ".block-customer-login .block-title{display: none;} .block-customer-login{display: none;}", false) ?>
                <div class="smsprofile-login-option">
                    <div class="block-title">
                        <strong id="block-customer-login-heading" role="heading" aria-level="2">
                            <?= $block->escapeHtml(__('Registered Customers')) ?>
                        </strong>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif;?>
    <?php if(in_array($loginWith, ['login_both','login_otp'])):?>
        <input type='hidden' name='form_type' id="form_type" value="<?= $escaper->escapeHtmlAttr('login') ?>"/>
        <input type='hidden' class='logintype' value="<?= $escaper->escapeHtmlAttr($loginWith) ?>"/>

        <div class="smsprofile-login-mobile login-opt-mobile">
            <fieldset class="fieldset login" data-hasrequired="<?= $escaper->escapeHtml(__('* Required Fields')) ?>">
                <div class="field email required form-field">
                    <div class="form-field__control control mobile">
                        <?php if($isCountryRequire): ?>
                            <input type="hidden" name='login[countrycodeval]' value="" id="countryreg" name="countryreg" />
                            <input type="hidden" value="" id="countryregcode" name="countryregcode" />
                        <?php endif; ?>
                        <input name="login[mobile]" value=""
                            <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
                               id="login_mobile"
                               type="text"
                               placeholder="<?= $escaper->escapeHtmlAttr(__('Please Enter Email or Mobile Number')) ?>"
                               class="form-field__input input-text"
                               title="<?= $escaper->escapeHtmlAttr(__('Mobile Number')) ?>"
                               data-mage-init='{"mage/trim-input":{},"validation":{}}'
                               data-validate="{required:true}">
                        <?php if($note):?>
                            <div class="profile-notice-phone">
                                <span><?= $note ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </fieldset>
        </div>
        <?php if($isCountryRequire): ?>
            <script type="text/x-magento-init">
                {
                    "input[name='login[mobile]']": {
                        "intlTelWidget": {}
                    }
                }
            </script>
        <?php endif; ?>
        <?php if ($configHelper): ?>
            <script type="text/x-magento-init">
            {
                "form#login-form" : {
                    "Magedelight_SMSProfile/js/otp-widget": <?= $configHelper->getOtpWidgetConfig(
                        \Magedelight\SMSProfile\Model\TransactionType::LOGIN,
                        "input[name='login[mobile]']",
                        "input[name='login[countrycodeval]']"
                    ); ?>
                }
            }
            </script>
        <?php endif; ?>
    <?php endif;?>
    <?php if(in_array($loginWith, ['login_both','login_pwd'])):?>
        <?php $loginButtonViewModel = $block->getData('login_button_view_model');?>
        <div class="block block-customer-login login-opt-email">
            <fieldset class="fieldset login" data-hasrequired="<?= $block->escapeHtml(__('* Required Fields')) ?>">
                    <div class="field note"><?= $block->escapeHtml(__('If you have an account, sign in with your email address.')) ?></div>
                    <div class="field email required">
                        <label class="label" for="email"><span><?= $block->escapeHtml(__('Please Enter Email or Mobile Number')) ?></span></label>
                        <div class="control">
                            <input name="login[username]" value="<?= $block->escapeHtmlAttr($block->getUsername()) ?>"
                                <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
                                   id="email" type="text" class="input-text"
                                   title="<?= $block->escapeHtmlAttr(__('Email or Mobile Number')) ?>"
                                   data-mage-init='{"mage/trim-input":{}}'
                                   data-validate="{required:true}">
                        </div>
                    </div>
                    <div class="field password required">
                        <label for="password" class="label"><span><?= $block->escapeHtml(__('Password')) ?></span></label>
                        <div class="control">
                            <input name="login[password]" type="password"
                                <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
                                   class="input-text pass" id="password"
                                   title="<?= $block->escapeHtmlAttr(__('Password')) ?>"
                                   data-validate="{required:true}">
                        </div>
                    </div>
                    <div class="field choice" data-bind="scope: 'showPassword'">
                        <!-- ko template: getTemplate() --><!-- /ko -->
                    </div>
            </fieldset>
            <?= $block->getChildHtml('form_additional_info') ?>
            <div class="actions-toolbar">
                <div class="primary"><button type="submit" class="action login primary" name="send" id="send2" data-md-js="md-submit-button"><span><?= $block->escapeHtml(__('Sign In')) ?></span></button></div>
                <div class="secondary"><a class="action remind" href="<?= $block->escapeUrl($block->getForgotPasswordUrl()) ?>"><span><?= $block->escapeHtml(__('Forgot Your Password?')) ?></span></a></div>
            </div>
        </div>
        <?php // phpcs:ignore Magento2.Legacy.PhtmlTemplate ?>
        <script type="text/x-magento-init">
            {
                "*": {
                    "Magento_Customer/js/block-submit-on-send": {
                        "formId": "login-form"
                    },
                    "Magento_Ui/js/core/app": {
                        "components": {
                            "showPassword": {
                                "component": "Magento_Customer/js/show-password",
                                "passwordSelector": "#password"
                            }
                        }
                    }
                }
            }
        </script>
    <?php endif?>
</form>
<?php if($isModuleEnable && in_array($configHelper->getSendOtpVia(), ['sms', 'both'])): ?>
    <script>
        require([
                'jquery'
            ],
            function ($) {
                $('form#login-form').on('otp-verify', function (event, response) {
                    if (response.message === 'Verified') {
                        $(this).find('input[name="login[username]"], input[name="login[password]"]').val("otplogin");
                        $(this).submit();
                    }
                });
            });
    </script>
<?php endif;?>
