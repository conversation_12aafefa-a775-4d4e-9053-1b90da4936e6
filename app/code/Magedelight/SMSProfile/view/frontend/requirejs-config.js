if(typeof window.otpModuleEnable === 'undefined' || window.otpModuleEnable === 'enable') {
    var config = {
        map: {
            '*': {
                intlTelInput: 'Magedelight_SMSProfile/js/intlTelInput',
                'Magento_Checkout/template/authentication.html':
                    'Magedelight_SMSProfile/template/authentication.html',
                'Magento_Checkout/template/form/element/email.html':
                    'Magedelight_SMSProfile/template/form/element/email.html',
                intlErrorHelper: 'Magedelight_SMSProfile/js/model/intl-error-helper',
                otpdesigner: 'Magedelight_SMSProfile/js/otpdesigner',
                intlTelWidget: 'Magedelight_SMSProfile/js/intl-widget'
            }
        },
        shim: {
            'Magedelight_SMSProfile/js/otpdesigner': {
                deps: ['jquery', 'jquery/ui']
            }
        },
        config: {
            mixins: {
                'Magento_Checkout/js/view/authentication': {
                    'Magedelight_SMSProfile/js/view/authentication-mixin': true
                },
                'Magento_Checkout/js/view/form/element/email': {
                    'Magedelight_SMSProfile/js/view/form/element/email-mixin': true
                },
                'Magento_Customer/js/model/authentication-popup': {
                    'Magedelight_SMSProfile/js/model/authentication-popup-mixin': true
                },
                'Magento_Checkout/js/checkout-data': {
                    'Magedelight_SMSProfile/js/checkout-data-ext': true
                },
                'Magento_Ui/js/lib/validation/validator': {
                    'Magedelight_SMSProfile/js/intlValidatorCheckout': true
                },
                'Magento_Ui/js/form/element/abstract': {
                    'Magedelight_SMSProfile/js/setAdditionalParams': true
                },
                'mage/validation': {
                    'Magedelight_SMSProfile/js/intlValidator': true
                },
                'Magento_OfflinePayments/js/view/payment/method-renderer/cashondelivery-method': {
                    'Magedelight_SMSProfile/js/payment/cod-mixin': true
                },
                'Magento_Checkout/js/action/place-order': {
                    'Magedelight_SMSProfile/js/model/place-order-mixin': true
                },
                'Magento_Checkout/js/action/set-payment-information': {
                    'Magedelight_SMSProfile/js/model/set-payment-information-mixin': true
                }
            }
        }
    };
}

