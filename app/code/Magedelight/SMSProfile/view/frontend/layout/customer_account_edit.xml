<?xml version="1.0"?>
 <!--
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
 -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="form.additional.info">
            <block class="Magedelight\SMSProfile\Block\Account\Edit\Form" name="smsprofile_form_additional_info_customer" template="Magedelight_SMSProfile::additionalinfocustomer.phtml">
                <arguments>
                    <argument name="config_helper" xsi:type="object">Magedelight\SMSProfile\ViewModel\ConfigHelper</argument>
                    <argument name="frontend_helper" xsi:type="object">Magedelight\SMSProfile\ViewModel\FrontendHelper</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
