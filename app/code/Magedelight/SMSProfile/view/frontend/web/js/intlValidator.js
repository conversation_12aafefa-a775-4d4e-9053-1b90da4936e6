define([
    'jquery',
    'jquery/validate',
    'intlErrorHelper'
], function ($, jqueryValidate, errorHelper) {
    'use strict';

    var errorMap = errorHelper.getErrorMap(),
        validatorObj = {
            validate: function (value, element) {
                let countryCode,
                    isValid,
                    errorCode,
                    countryCodeClass = $(element).prev('.iti__flag-container').find('.iti__selected-flag .iti__flag').attr('class');

                try {
                    isValid = errorHelper.isValid(value, countryCodeClass);

                    if (!isValid) {
                        countryCodeClass = countryCodeClass.split(' ')[1];
                        countryCode = countryCodeClass.split('__')[1];
                        errorCode = errorHelper.getErrorCode(value, countryCode);

                        $.validator.messages['validate-intl-phone'] = typeof errorMap[errorCode] === 'undefined' ?
                            errorMap[0] :
                            errorMap[errorCode];
                    }
                } catch (e) {
                    $.validator.messages['validate-intl-phone'] = errorMap[1];
                    isValid = false;
                }

                return isValid;
            }
        };

    $.validator.addMethod(
        'validate-intl-phone',
        validatorObj.validate,
        $.validator.messages['validate-intl-phone']
    );

    return function (widget) {
        return widget;
    };
});
