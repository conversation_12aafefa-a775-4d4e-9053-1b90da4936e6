/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'ko',
    'jquery',
    'uiComponent',
    'Magento_Checkout/js/model/quote',
    'Magedelight_SMSProfile/js/model/checkout-helper',
    'Magedelight_SMSProfile/js/model/otp-request',
    'Magento_Ui/js/model/messageList',
    'Magedelight_SMSProfile/js/otp-widget-cod'
], function (ko, $, Component, quote, checkoutHelper, otpRequest, messageList) {
    'use strict';

    var checkoutConfig = window.checkoutConfig,
        otpConfig = checkoutConfig ? checkoutConfig.smsotp : {};

    return Component.extend({
        defaults: {
            template: 'Magedelight_SMSProfile/checkout/otp-cod'
        },
        isVisible: checkoutHelper.isOtpRequire,
        isVerified: checkoutHelper.otpVerified,
        otpCode: checkoutHelper.otpCode,
        errorMsg: ko.observable(''),
        successMsg: ko.observable(''),
        telephone: ko.observable(quote.shippingAddress() ? quote.shippingAddress().telephone : ''),
        initialize: function () {
            var self = this;
            this._super();
            if (checkoutHelper.isOtpRequire()) {
                quote.paymentMethod.subscribe(function (payment) {
                    self.isVisible(false);
                    if (payment.method === "cashondelivery" && otpConfig.otpcod && !self.isVerified()) {
                        self.isVisible(true);
                    }
                });
                quote.shippingAddress.subscribe(function (address) {
                    if (self.telephone() !== address.telephone) {
                        self.telephone(address.telephone);
                        self.reset();
                    }
                });
            }
        },
        reset: function () {
            this.isVisible(otpConfig.otpcod);
            this.isVerified(false);
            this.otpCode('');
        },
        initializeWidgets: function (element) {
            let common = otpConfig['otp-widget'];
            common.eventType = 'cod_otp';
            common.mobileInputSelector = 'input[name=customer_mobile]';
            common.hiddenInput = 'input[name=otp-code]:hidden';
            if (!common.recaptchaForms.includes('cod')) {
                common.enableRecaptcha = false;
            }
            $(element).codWidget(common);
        },
        verifyOtp: function (data, event, response) {
            let success = response.message === 'Verified';
            if (success) {
                this.isVerified(true);
                this.isVisible(false);
                messageList.addSuccessMessage(({message: response.message}));
            } else {
                messageList.addErrorMessage({message: response.message});
            }
        },
        sendOtp: function (data, event, response) {
            if (response.Success !== 'success') {
                messageList.addErrorMessage({ message: response.Success });
            }
        }
    });
});
