define([
    'ko',
    'jquery',
    'Magento_Ui/js/model/messageList',
    'jquery/jquery-storageapi'
], function (ko, $, messageContainer) {
    'use strict';

    var checkoutConfig = window.checkoutConfig,
        otpConfig = checkoutConfig ? checkoutConfig.smsotp : {};

    return {
        getOtpConfig: function (event, mobileInputSelector) {
            let common = otpConfig['otp-widget'];
            common.eventType = event;
            common.mobileInputSelector = mobileInputSelector;
            event = event.includes('login') ? 'login' : event;
            if (!common.recaptchaForms.includes(event)) {
                common.enableRecaptcha = false;
            }
            return common;
        },
        submitFormWithAjax: function (form) {
            let params = form.serializeArray();
            $.ajax({
                url: form.attr('action'),
                data: $.param(params),
                type: 'post',
                dataType: 'html',
                showLoader: true,
                success: function (response) {
                    var cookieMessages = $.cookieStorage.get('mage-messages');
                    $.cookieStorage.set('mage-messages', '');
                    if (cookieMessages.length) {
                        var flag = true;
                        $(cookieMessages).each(function (i, m) {
                            if (m.type === 'error') {
                                flag = false;
                            }
                        });

                        if (!flag) {
                            messageContainer.addErrorMessage({
                                message: cookieMessages[0].text
                            });
                            return;
                        }
                    }
                    window.location.reload(true);
                },
                error: function () {
                    // $('.md-error').html($t('Sorry, an unspecified error occurred. Please try again.'));
                    setTimeout(function () {
                        window.location.reload(true);
                    }, 3000);
                }
            });
        },
        isOtpRequire: ko.observable(otpConfig.otpcod),
        otpCode: ko.observable(''),
        otpVerified: ko.observable(false),
    };

});
