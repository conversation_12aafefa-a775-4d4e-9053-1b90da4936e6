/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>noLabs. All Rights reserved.
 */

define([
    'jquery',
    'ko',
    'mage/url',
    'Magedelight_SMSProfile/js/model/checkout-helper',
    'Magedelight_SMSProfile/js/otp-widget',
    'intlTelWidget'
], function ($,ko, urlBuilder,checkoutHelper) {
    'use strict';
    var mixin = {
        initConfig: function () {
            this._super();
            this.otpLoginUrl = urlBuilder.build('smsprofile/account/loginPost');
            ko.bindingHandlers.otpWidgetAuth = {
                /**
                 * Initializes 'otp-widget' binding.
                 */
                init: function (element) {
                    $(element).otpWidget(checkoutHelper.getOtpConfig('customer_login_otp', 'input[name=customer_mobile]'));
                    $(element).on('otp-verify', function (event, response) {
                        if (response.message === 'Verified') {
                            $(this).submit();
                        }
                    });
                }
            };
            ko.bindingHandlers.intlTelWidgetAuth = {
                /**
                 * Initializes 'intl-widget' binding.
                 */
                init: function (element) {
                    if (window.checkoutConfig.smsotp.customer_country_enabled) {
                        $(element).intlTelWidget();
                    }
                }
            };
            return this;
        },
        login: function (loginForm) {
            if ($(loginForm).find('input[name=customer_mobile]').val() && $(loginForm).find('input[name=otp]:hidden').val()) {
                checkoutHelper.submitFormWithAjax($(loginForm));
                return false;
            }
            return this._super(loginForm);
        }
    };

    return function (target) {
 // target == Result that Magento_Ui/.../columns returns.
        return target.extend(mixin); // new result that all other modules receive
    };

});
