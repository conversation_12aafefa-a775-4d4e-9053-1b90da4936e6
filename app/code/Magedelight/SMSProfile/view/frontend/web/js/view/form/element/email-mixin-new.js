/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>noLabs. All Rights reserved.
 */

define([
    'jquery',
    'ko',
    'Magento_Checkout/js/model/full-screen-loader',
    'Magento_Customer/js/action/login',
    'Magedelight_SMSProfile/js/model/checkout-config',
    'Magedelight_SMSProfile/js/otp-widget',
    'intlTelWidget'
], function ($, ko, fullScreenLoader,loginAction, otpWidgetHelper) {
    'use strict';
    var mixin = {
        initConfig: function () {
            this._super();
            this.isOtpVisible = ko.observable(['login_otp', 'login_both'].includes(window.checkoutConfig.otplogin));
            this.isPwdVisible = ko.observable(window.checkoutConfig.otplogin === 'login_pwd');
            ko.bindingHandlers.otpWidget = {
                /**
                 * Initializes 'otp-widget' binding.
                 */
                init: function (element) {
                    $(element).otpWidget(otpWidgetHelper.getOtpConfig('login', 'input[name=mobile]'));
                    $(element).on('otp-verify', function (event, response) {
                        if (response.message === 'Verified') {
                            $(this).submit();
                        }
                    });
                }
            };
            ko.bindingHandlers.intlTelWidget = {
                /**
                 * Initializes 'intl-widget' binding.
                 */
                init: function (element) {
                    if (window.checkoutConfig.smsotp.customer_country_enabled) {
                        $(element).intlTelWidget(otpWidgetHelper.getIntlConfig());
                    }
                }
            };
            return this;
        },

        emailHasChanged: function () {
            if (window.checkoutConfig.enable_on_checkoutpage==1) {
                this.toggleValidationClass()
                this._super();
            }
        },

        toggleValidationClass: function () {
            let loginFormSelector = 'form[data-role=email-with-possible-login]',
                usernameSelector = loginFormSelector + ' input[name=username]';
            let input = $(usernameSelector);
            let emailValidation = "validate-email";
            let phoneValidation = "validate-length validate-digits maximum-length-$l minimum-length-$l".
            replaceAll("$l", window.checkoutConfig.smsotp.mobile_default_validation);
            let intlPhoneValidation = window.checkoutConfig.smsotp.customer_country_enabled ? "validate-intl-phone" : phoneValidation;
            if ($.isNumeric(input.val())) {
                input.prev('.iti__flag-container').show();
                input.addClass(intlPhoneValidation);
                input.removeClass(emailValidation);
            } else {
                input.prev('.iti__flag-container').hide();
                input.css('padding-left', '6px');
                input.removeClass(intlPhoneValidation);
                input.addClass(emailValidation);
            }
        },

        showOtp: function () {

        },

        showPassword: function () {

        },

        login: function (loginForm) {
            var loginData = {},
                formDataArray = $(loginForm).serializeArray();

            formDataArray.forEach(function (entry) {
                loginData[entry.name] = entry.value;
            });

            if (window.checkoutConfig.enable_on_checkoutpage==1) {
                if ($(loginForm).validation() && $(loginForm).validation('isValid')) {
                    fullScreenLoader.startLoader();
                    loginAction(loginData).always(function () {
                        fullScreenLoader.stopLoader();
                    });
                }
            } else {
                if (this.isPasswordVisible() && $(loginForm).validation() && $(loginForm).validation('isValid')) {
                    fullScreenLoader.startLoader();
                    loginAction(loginData).always(function () {
                        fullScreenLoader.stopLoader();
                    });
                }
            }
        },
    };

    return function (target) {
        return target.extend(mixin);
    };

});
