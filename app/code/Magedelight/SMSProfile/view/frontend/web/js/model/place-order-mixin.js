define([
    'j<PERSON>y',
    'mage/utils/wrapper',
    'Magedelight_SMSProfile/js/model/otp-assigner'
], function ($, wrapper, otpAssigner) {
    'use strict';

    return function (placeOrderAction) {

        /** Override default place order action and add agreement_ids to request */
        return wrapper.wrap(placeOrderAction, function (originalAction, paymentData, messageContainer) {
            otpAssigner(paymentData);

            return originalAction(paymentData, messageContainer);
        });
    };
});
