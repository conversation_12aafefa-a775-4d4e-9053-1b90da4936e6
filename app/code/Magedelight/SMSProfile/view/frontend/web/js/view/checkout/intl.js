define([
    'Magento_Ui/js/lib/view/utils/async',
    'uiComponent',
    'Magento_Checkout/js/checkout-data',
    'intlTelInput'
], function ($, Component, checkoutData) {
    'use strict';

    /**
     * The custom UI component.
     *
     * @class
     * @extends uiComponent
     */
    return Component.extend({
        defaults: {
            selectors: [
                '.intlPhone'
            ],
            configData: {
                "geoIpLookup": function(success, failure) {
                    fetch('https://ipapi.co/json')
                        .then(function(res) { return res.json(); })
                        .then(function(data) { success(data.country_code); })
                        .catch(function() { failure(); });
                }
            }
        },

        /**
         * Attaches the international telephone input library to a DOM node.
         *
         * @param {HTMLElement} node - The DOM node to attach the library to.
         */
        attachIntlTelInput: function(node) {
            let data = checkoutData.getIntlData(),
                telephoneInput = $(node)[0],
                iti = window.intlTelInput(telephoneInput, this.configData),
                formId = iti.telInput.form.id || 'billing',
                realInput = $(iti.telInput.form).find('input[name=telephone]');

            if (!data) {
                data = {};
            }
            if (!data.hasOwnProperty(formId)) {
                data[formId] = {};
            }

            if (Object.keys(data[formId]).length) {
                iti.telInput.value = data[formId]['value'];
                iti._updateFlagFromNumber('+' + data[formId]['dialCode'] + data[formId]['value']);
            }

            telephoneInput.addEventListener('blur', function() {
                let mo = iti.getNumber();
                if (formId) {
                    data[formId]['dialCode'] = iti.selectedCountryData.dialCode;
                    data[formId]['value'] = mo.replace('+', '').replace(iti.selectedCountryData.dialCode, '');
                }
                checkoutData.setIntlData(data);
                // realInput.val('+' + data[formId]['dialCode'] + data[formId]['value']).change();
                realInput.val(mo).change();
            });
        },

        initialize: function (configData) {
            let self = this;
            this._super();
            _.extend(this.configData, configData);
            this.selectors.forEach((selector) =>  {
                $.async(selector, (node) => {
                    this.attachIntlTelInput(node);
                });
            });

            return this;
        }
    });
});
