define([
    'jquery',
    'intlTelInput',
    'jquery-ui-modules/widget'
], function ($) {
    'use strict';

    $.widget('mage.intlTelWidget', {
        // Define intlOptions and options separately
        intlOptions: {
            utilsScript: window.smsotp.utilsScript,
            initialCountry: window.smsotp.initialCountry,
            onlyCountries: window.smsotp.onlyCountries,
            preferredCountries: window.smsotp.preferredCountries,
            geoIpLookup: function (callback) {
                $.get('https://ipinfo.io', function () {}, 'jsonp').always(function (resp) {
                    let countryCode = (resp && resp.country) ? resp.country : '';
                    callback(countryCode);
                });
            },
            nationalMode: true,
            separateDialCode: true,
            formatOnDisplay: false
        },

        options: {
            // inputMode = how the value will be stored into field
            inputMode: 'withoutDialCode', // or withDialCode
            dialCodeSelector: 'input[name=countryreg]:hidden'
        },

        _create: function () {
            if (this.options.inputMode === 'withDialCode') {
                this.withDialCodeSetup();
            }
            if (this.options.inputMode === 'withoutDialCode') {
                this.withoutDialCodeSetup();
            }
        },

        withDialCodeSetup: function () {
            let originalInput = this.element;
            let randomId = 'intl-tel-' + Math.floor(Math.random() * 100000);

            let clonedInput = originalInput.clone();
            clonedInput.attr('id', randomId);
            clonedInput.attr('name', randomId);
            clonedInput.addClass('validate-intl-phone');
            clonedInput.attr('value', '');
            clonedInput.val('');

            originalInput.hide();
            originalInput.after(clonedInput);

            let mergedOptions = $.extend({}, this.intlOptions, this.options);
            this.intlTelInstance = window.intlTelInput(clonedInput[0], mergedOptions);

            // If the original input has a value, set it in the intl-tel-input instance
            if (originalInput.val()) {
                this.intlTelInstance.setNumber(originalInput.val());
            }

            this._bind(clonedInput);
        },

        withoutDialCodeSetup: function () {
            let mobileInput = this.element;
            this.dialCodeInput = this.element.parents('form').find(this.options.dialCodeSelector);
            this.countryCodeInput = this.element.parents('form').find('input[name=countryregcode]:hidden');
            mobileInput.addClass('validate-digits validate-intl-phone');
            let mergedOptions = $.extend({}, this.intlOptions, this.options);
            this.intlTelInstance = window.intlTelInput(mobileInput[0], mergedOptions);
            if (mobileInput.val() && this.dialCodeInput.val()) {
                this.intlTelInstance.setNumber(this.dialCodeInput.val() + mobileInput.val());
            }
            this._on(mobileInput, {
                countrychange: this.updateCountry
            });
            this.updateCountry();
        },

        updateCountry: function () {
            this.countryCodeInput.attr('value', this.intlTelInstance.getSelectedCountryData().iso2);
            this.dialCodeInput.attr('value', '+' + this.intlTelInstance.getSelectedCountryData().dialCode);
        },

        _bind: function (clonedInput) {
            this._on(clonedInput, {
                change: this.updateOriginalField,
                blur: this.updateOriginalField
            });
        },

        updateOriginalField: function (event) {
            let intlNumber = this.intlTelInstance.getNumber();
            this.element.val(intlNumber);
            this.element.attr('value', intlNumber);
        },

        getIntlInstance: function () {
            return this.intlTelInstance;
        }
    });

    return $.mage.intlTelWidget;
});
