define([
    "jquery",
    'mage/translate',
    'underscore',
    'Magento_Ui/js/modal/modal'
], function ($,$t, _,modal) {
    'use strict';

    $.widget('mage.mdLoginPopup', {
        options: {
            "enablePopup": false
        },
        mdoptions: {
            fields: {
                login: 'a[href*="customer/account/login"]',
                createAccount: 'a[href*="customer/account/create"]',
                mdTabContainer:'[data-md-js="md-tabs-wrapper"]',
                popupForgotLink: '#login-form .action.remind',
                mdTabWrapper: '[data-md-js="md-tabs-wrapper"]',
                mdTabWrapperForgot: '[data-md-js="md-tabs-wrapper-forgot"]',
                form:'[data-md-js="md-login-container"] .form',
                passwordInputType:'password',
                textInputType:'text',
                showPassword :'.md-login-popup #show-password',
                passwordSelector:'.md-login-popup #pass'
            }
        },

        _create: function () {
            if (this.options.enablePopup) {
                this.modifyAnchors();
            }
            this.initObservable();
        },

        modifyAnchors: function () {
            var self = this;
            /* login links*/
            $(self.mdoptions.fields.login).prop('href', '#').on('click', function (event) {
                self.openPopupModal(0);
                event.preventDefault();
                return false;
            });

            /* create account links*/
            $(self.mdoptions.fields.createAccount).prop('href', '#').on('click', function (event) {
                self.openPopupModal(1);
                $('.md-register-content .iti__flag-container').show();
                event.preventDefault();
                return false;
            });
        },

        initObservable: function () {

            var self = this;

            /* forgot links*/
            $(self.mdoptions.fields.popupForgotLink).unbind('click').on('click', function (event) {
                self.toggleWrappers();
                event.preventDefault();
                return false;
            });
            this.element.on('click', this.mdoptions.fields.popupForgotLink, function (e) {
                self.toggleWrappers();
                e.preventDefault();
                return false;
            })

            /* ajax form submit */
            $(self.mdoptions.fields.form).unbind('submit').on('submit', function (event) {
                var form = $(this);
                if (form.valid()) {
                    form.find('button.action').prop('disabled', true);
                    self.submitFormWithAjax(form);
                }
                event.preventDefault();
                return false;
            });

            /* login hide/show password */
            $(document).on("click", self.mdoptions.fields.showPassword, function (event) {
                if ($(this).is(':checked')) {
                    self.showPassword(true);
                } else {
                    self.showPassword(false);
                }
            });
        },

        openPopupModal: function (activeTabIndex) {

            $(this.mdoptions.fields.mdTabWrapperForgot).hide();
            $(this.mdoptions.fields.mdTabWrapper).show();

            var mdoptions = {
                type: 'popup',
                responsive: true,
                innerScroll: true,
                buttons: false,
                modalClass: 'md-popup md-smsprofile-popup'
            };

            this.element.modal(mdoptions).modal('openModal');
            $('.modal-inner-wrap').addClass('md-smsprofile-popup');

            if ($('html').hasClass('nav-open')) {
                $('.navigation > .ui-menu').menu('toggle');
            }

            $(this.mdoptions.fields.mdTabContainer).tabs('activate', activeTabIndex);
        },

        toggleWrappers: function () {
            $(this.mdoptions.fields.mdTabWrapper).toggle();
            $(this.mdoptions.fields.mdTabWrapperForgot).toggle();
        },

        submitFormWithAjax: function (form) {
            var self = this;
            let params = form.serializeArray();
            let username = params.find(item => item.name === 'login[username]');
            let password = params.find(item => item.name === 'login[password]');
            if (username !== undefined && !username.value) {
                username.value = 'otplogin';
            }
            if (password !== undefined && !password.value) {
                password.value = 'otplogin';
            }
            $('body').trigger('processStart');
            $.ajax({
                url: form.attr('action'),
                data: $.param(params),
                type: 'post',
                dataType: 'html',
                showLoader: true,
                success: function (response) {
                    $('.md-error span').text('');
                    $('.md-success span').text('');
                    var cookieMessages = $.cookieStorage.get('mage-messages');
                    $.cookieStorage.set('mage-messages', '');
                    if (cookieMessages.length) {
                        var flag = true;
                        $(cookieMessages).each(function (i, m) {
                            if (m.type === 'error') {
                                flag = false;
                            }
                        });

                        if (!flag) {
                            form.find('button.action').prop('disabled', false);
                            $('.md-error span').append($.parseHTML(cookieMessages[0].text));
                            $('body').trigger('processStop');
                            return;
                        }
                    }

                    if (cookieMessages.length) {
                        $('.md-success').html(cookieMessages[0].text);
                    } else {
                        if (form.hasClass('form-login')) {
                            if (response.indexOf('customer/account/logout') !== -1) {
                                $('.md-success span').text(($t('You have successfully logged in.')));
                            }
                        } else if (form.hasClass('form-create-account')) {
                            $('.md-success span').text(($t('Thank you for registering with us.')));
                        }
                    }
                    setTimeout(function () {
                        window.location.reload(true);
                    }, 1000);
                },
                error: function () {
                    $('.md-error').html();
                    $('.md-error').html($t('Sorry, an unspecified error occurred. Please try again.'));
                    setTimeout(function () {
                        window.location.reload(true);
                    }, 3000);
                }
            });
        },

        /**
         * Show/Hide password
         * @private
         */
        showPassword: function (isChecked) {
            var self = this;
            $(self.mdoptions.fields.passwordSelector).attr(
                'type',
                isChecked ? self.mdoptions.fields.textInputType : self.mdoptions.fields.passwordInputType
            );
        }



    });

    return $.mage.mdLoginPopup;
});
