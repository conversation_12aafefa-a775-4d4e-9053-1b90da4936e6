define([
    'jquery',
    'mage/translate',
    'Magedelight_SMSProfile/js/model/captcha-forms',
    'Magedelight_SMSProfile/js/model/otp-request',
    'otpdesigner',
    'jquery-ui-modules/widget',
    'mage/validation'
], function ($, $t, captcha, otpRequest) {
    'use strict';

    $.widget('mage.otpWidget', {
        mobileInput: null,
        mobileInputParent: null,
        contactDetail: null,
        otpInput: null,
        verifyContainer: null,
        resendButton: null,
        messageContainer: null,
        isVerified: false,
        canModifyData: true,
        captchaDiv: `<div>
                            <div class="recaptcha"></div>
                            <input type="hidden" name="captcha-response" />
                            <input type="hidden" name="captcha-widget-id" />
                        </div>`,
        otpContainer: `<div class="otp-container" style="display:none;">
                            <p class="otp__text">${$t('OTP has been sent to ')} <span></span>
                                <a href="#" class="otp__change"> ${$t('Change')}</a>
                            </p>
                            <div class="otp__input otp_target"></div>
                            <div class="otp__msg"></div>
                            <div class="otp__button">
                                <button type="button" class="action otp__button-verify primary" >${$t('Verify')}</button>
                                <button type="button" class="action otp__button-resend secondary" disabled>${$t('Resend OTP')} <span class="counter"></span></button>
                            </div>
                        </div>`,
        sendButton: `<button type="button" class="generate-otp action primary" disabled>${$t('Generate OTP')}</button>`,
        options: {
            mobileInputSelector: '',
            dialCodeSelector: 'input[name=countryreg]:hidden',
            hiddenInput: 'input[name=otp]:hidden',
            eventType: '',
            otpFormat: 'alphanum',
            otpLength: '4',
            messageContainer: '<div class="otp-message-container"></div>',
            isIntl: true,
            resendEnable: true,
            resendAfter: 10,
            resendLimit: 10,
            mobileLength: 10,
            autoVerify: true,
            enableRecaptcha: false,
            sitekey: false,
        },

        _create: function () {
            this._initializeInputs();
            this._initializeOtpFeature();
            if (this.options.enableRecaptcha) {
                this._initializeRecaptcha()
            }
            this._bind();
        },

        _initializeInputs: function () {
            let form = this.element;
            this.messageContainer = $(this.options.messageContainer);
            this.mobileInput = form.find(this.options.mobileInputSelector);
            this.mobileInputParent = this.mobileInput.parents('.control');
            if (this.mobileInput.length) {
                if (this.mobileInput.data('val')) {
                    this.canModifyData = false;
                }
                form.append('<input type="hidden" name="otp" value="0" />');
                this.otpContainer = $(this.otpContainer).insertAfter(this.mobileInputParent);
                this.messageContainer.insertAfter(this.mobileInputParent);
                this.sendButton = $(this.sendButton).insertAfter(this.mobileInputParent);
                this.captchaDiv = $(this.captchaDiv).insertAfter(this.mobileInputParent);
            }
            this.otpInput = this.otpContainer.find('.otp__input');
            this.contactDetail = this.otpContainer.find('.otp__text > span');
            this.verifyContainer = this.otpContainer.find('.otp__msg');
            this.resendButton = this.otpContainer.find('.otp__button > button.otp__button-resend');
            if (!this.options.resendEnable) {
                this.resendButton.hide();
            }
            form.trigger('contentUpdated');
        },

        _initializeOtpFeature: function () {
            let self = this;
            this.otpInput.otpdesigner({
                length: parseInt(this.options.otpLength),
                onlyNumbers: this.options.otpFormat == 'num',
                typingDone:  function (code) {
                    if (self.options.autoVerify) {
                        self.verifyOtp(code);
                    }
                }
            });
        },

        _initializeRecaptcha: function () {
            let self = this;
            let siteKey = this.options.sitekey;
            if (typeof grecaptcha !== 'undefined') {
                console.log(grecaptcha);
                let id = grecaptcha.render(this.captchaDiv.find('.recaptcha')[0], {
                    'sitekey' : siteKey,
                    'callback' : function (response) {
                        self.captchaDiv.find('input[name=captcha-response]:hidden').val(response);
                    },
                    'expired-callback' : function () {
                        self.resetCaptcha();
                    }
                });
                this.captchaDiv.find('input[name=captcha-widget-id]:hidden').val(id);
            } else {
                captcha.registerForm(this.element);
            }
        },

        _bind: function () {
            let self = this;
            this.element.find('.login-option-tab input.login-opt[type=button]').on('click', function () {
                $(this).parents('.login-option-tab').find('input.login-opt[type=button]').each(function () {
                    $(this).parent().removeClass('active');
                    self.element.find('.' + $(this).attr('id')).hide();
                });
                $(this).parent().addClass('active');
                self.element.find('.' + $(this).attr('id')).show();
            });
            this.sendButton.on('click', function () {
                if ($(this).is(':enabled')) {
                    self.sendOtp();
                }
            });
            this.resendButton.on('click', function () {
                if ($(this).is(':enabled')) {
                    self.sendOtp();
                }
            });
            this.element.on('keyup', self.options.mobileInputSelector, function () {
                self.checkGenerateOtp($(this));
            });
            this.otpContainer.find('.otp__button > button.otp__button-verify').on('click', function () {
                self.verifyOtpButton();
            });
            this.otpContainer.find('.otp__text > a.otp__change').on('click', function (e) {
                e.preventDefault()
                self.changeNumber();
            });
        },

        changeNumber: function () {
            this.mobileInputParent.show();
            this.sendButton.show();
            this.otpContainer.hide();
            if (this.options.enableRecaptcha) {
                this.captchaDiv.show();
            }
        },

        checkGenerateOtp: function (mobileInput) {
            this.toggleValidationClass(mobileInput);
            if (mobileInput.valid()) {
                let oldVal = mobileInput.data('val') || '';
                if (mobileInput.val() != oldVal) {
                    this.enable(this.sendButton);
                }
                this.contactDetail.html(mobileInput.val())
            } else {
                this.disable(this.sendButton);
            }
        },

        toggleValidationClass(mobileInput) {
            let emailValidation = "validate-email";
            let phoneValidation = "validate-length validate-digits maximum-length-$l minimum-length-$l".replaceAll("$l", this.options.mobileLength);
            let intlPhoneValidation = this.options.isIntl ? "validate-digits validate-intl-phone" : phoneValidation;
            if (
                this.isMobile(mobileInput.val()) ||
                ['customer_signup_otp', 'customer_account_edit_otp'].includes(this.options.eventType)
            ) {
                mobileInput.prev('.iti__flag-container').show();
                mobileInput.addClass(intlPhoneValidation);
                mobileInput.removeClass(emailValidation);
            } else {
                mobileInput.prev('.iti__flag-container').hide();
                mobileInput.css('padding-left', '6px');
                mobileInput.removeClass(intlPhoneValidation);
                mobileInput.addClass(emailValidation);
            }
        },

        sendOtp: function () {
            if (!this.isCaptchaVerified()) {
                let error = $('<p class="smserror">' + $t('Please verify captcha first.') + '</p>').css("color", "red").delay(5000).fadeOut(800);
                this.messageContainer.html(error);
                return;
            }
            let self = this;
            let form = this.element;
            let value = this.mobileInput.val();
            let otpData = {
                countrycode: this.isMobile(value) && this.options.isIntl ? form.find(this.options.dialCodeSelector).val() : '',
                mobile: this.isMobile(value) ? value : '',
                eventType: this.options.eventType,
                email: this.isEmail(value) ? value : '',
                formType: form.find("#form_type").val(),
                captchaResponse: this.captchaDiv.find('input[name=captcha-response]:hidden').val()
            };
            this.otpInput.otpdesigner('clear');
            otpRequest.sendOtp(otpData).done(function (response) {
                self.handleSendOtp(response)
            });
        },

        isCaptchaVerified: function () {
            if (this.options.enableRecaptcha) {
                return this.captchaDiv.find('input[name=captcha-response]:hidden').val();
            }
            return true;
        },

        handleSendOtp: function (response) {
            this.messageContainer.empty();
            this.verifyContainer.empty();
            this.element.trigger('otp-send', [response]);
            this.disable(this.resendButton);
            if (response.Success === 'success') {
                this.resetCaptcha();
                this.disable(this.sendButton);
                this.showResendCounter(response.resend_link_count);
                this.otpContainer.show();
                this.otpInput.otpdesigner('focus');
                this.mobileInputParent.hide()
                this.sendButton.hide();
                if (!this.options.resendEnable) {
                    this.captchaDiv.hide();
                }
            } else {
                if (response.Success === 'Robot verification failed, please try again.') {
                    this.resetCaptcha();
                }
                let error = $('<p class="smserror">' + response.Success + '</p>').css("color", "red").delay(5000).fadeOut(800);
                this.messageContainer.html(error);
            }
        },

        verifyOtpButton: function () {
            let result = this.otpInput.otpdesigner('code');
            if (result.done) {
                return this.verifyOtp(result.code);
            }
            this.verifyContainer.html('<p class="smserror">' + $t('Please enter the OTP.') + '</p>').css('color', 'red');
        },

        verifyOtp: function (code) {
                let self = this;
                let form = this.element;
                let value = this.mobileInput.val();
                form.find(this.options.hiddenInput).val(code).trigger('change');
                let otpData = {
                    countrycode: this.isMobile(value) && this.options.isIntl ? form.find(this.options.dialCodeSelector).val() : '',
                    mobile: this.isMobile(value) ? value : '',
                    otp: code,
                    email: this.isEmail(value) ? value : ''
                };
                otpRequest.verifyOtp(otpData).done(function (response) {
                    self.handleVerifyOtp(response);
                });
        },

        handleVerifyOtp: function (response) {
            this.element.trigger('otp-verify', [response]);
            let success = response.message === 'Verified';
            this.verifyContainer.html('<p class="smserror">' + response.message + '</p>').css('color', success ? 'green' : 'red');
            if (success) {
                this.messageContainer.html('<p class="smserror">' + response.message + '</p>').css('color', 'green');
                this.isVerified = true;
                this.otpContainer.hide();
                this.captchaDiv.hide();
                this.mobileInputParent.show()
                this.mobileInput.prop("readonly", true).css('pointer-events', 'none');
            }
        },
        showResendCounter: function (attempts) {
            if (!this.options.resendEnable) {
                return;
            }
            if (attempts >= this.options.resendLimit) {
                return;
            }
            let self = this;
            let counter = this.resendButton.find('span');
            let timeLeft = this.options.resendAfter;
            counter.html('00:' + timeLeft);
            timeLeft--;
            let downloadTimer = setInterval(function () {
                if (timeLeft <= 0) {
                    counter.html('');
                    clearInterval(downloadTimer);
                    self.enable(self.resendButton);
                } else {
                    if (self.isVerified) {
                        counter.html('');
                        self.disable(self.resendButton);
                        clearInterval(downloadTimer);
                    }
                    counter.html('00:' + timeLeft);
                }
                timeLeft -= 1;
            }, 1000);
        },
        disable: function (button) {
            button.prop('disabled', true);
        },
        enable: function (button) {
            button.prop('disabled', false);
        },
        isEmail: function (email) {
            const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        },
        isMobile: function (phone) {
            return phone.match(/^[+\d]?(?:[\d.\s()\-\[\]]*)$/);
        },
        resetCaptcha() {
            if (!this.options.enableRecaptcha) {
                return;
            }
            grecaptcha.reset(this.captchaDiv.find('input[name=captcha-widget-id]:hidden').val());
            this.captchaDiv.find('input[name=captcha-response]:hidden').val('');
        }
    });

    return $.mage.otpWidget;
});
