define([], function () {
    'use strict';

    let errorMap = [
        'Invalid telephone number',
        'Invalid country code',
        'Telephone number is too short',
        'Telephone number is too long',
        'Telephone number cannot start with 0',
        'Invalid telephone number'
    ];

    return {
        getErrorMap: function () {
            return errorMap;
        },

        getErrorCode: function (value, countryCode) {
            return window.intlTelInputUtils.getValidationError(value, countryCode);
        },

        isValid: function (telephoneNumber, countryCodeClass) {
            // Consider an empty number as valid
            if (!telephoneNumber.length) {
                return true;
            }

            // Check if the number starts with '0'
            if (telephoneNumber.startsWith('0')) {
                return false; // Invalid if it starts with '0'
            }

            // Extract the country code
            let countryCode = this.extractCountryCode(countryCodeClass);
            if (!countryCode) {
                throw new Error("Cannot find country code");
            }

            // Special case for Hungary (HU)
            if (countryCode === "hu") {
                // Allow only 9-digit numbers for Hungary
                if (telephoneNumber.length === 9) {
                    return true; // Valid Hungarian number with 9 digits
                } else {
                    return false; // Invalid if not 9 digits
                }
            }

            // Validate with intl-tel-input for other countries
            return window.intlTelInputUtils.isValidNumber(telephoneNumber, countryCode);
        },

        extractCountryCode: function (countryCodeClass) {
            if (!countryCodeClass || countryCodeClass.indexOf(' ') === -1 || countryCodeClass.endsWith('__')) {
                return null; // Cannot find country code
            }
            return countryCodeClass.split(' ')[1].split('__')[1];
        }
    };
});
