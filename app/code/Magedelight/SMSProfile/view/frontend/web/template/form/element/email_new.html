<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!-- ko ifnot: isCustomerLoggedIn() -->

<!-- ko foreach: getRegion('before-login-form') -->
<!-- ko template: getTemplate() --><!-- /ko -->
<!-- /ko -->
<form class="form form-login" data-role="email-with-possible-login"
      data-bind="submit:login"
      method="post">
    <fieldset id="customer-email-fieldset" class="fieldset" data-bind="blockLoader: isLoading">
        <div class="field required">
            <label class="label" for="customer-email"><span data-bind="i18n: 'Please Enter Email or Mobile Number'"></span></label>
            <div class="control _with-tooltip">
                <input class="input-text"
                       type="text"
                       data-bind="
                            intlTelWidget: true,
                            textInput: email,
                            hasFocus: emailFocused,
                            afterRender: emailHasChanged,
                            mageInit: {'mage/trim-input':{}}"
                       name="username"
                       data-validate="{required:true}"
                       id="customer-email" />
                <!-- ko template: 'ui/form/element/helper/tooltip' --><!-- /ko -->
                <span class="note" data-bind="fadeVisible: isPasswordVisible() == false"><!-- ko i18n: 'You can create an account after checkout.'--><!-- /ko --></span>
            </div>
        </div>

        <!--Hidden fields -->
        <fieldset class="fieldset hidden-fields" data-bind="fadeVisible: isPasswordVisible">
            <!-- ko if: (window.checkoutConfig.otplogin == 'login_both') -->
            <div class="login-container">
                <div class="login-left">
                    <div class="smsprofile-login-option">
                        <div class="login-option-tab">
                            <div class="login-mobile active" data-md-js="md-login-opt"><input type="button" class="login-opt" name="Mobile" id="login-opt-mobile" data-bind="value: 'Login with OTP'" /></div>
                            <div class="login-email" data-md-js="md-login-password"><input type="button" class="login-opt" name="Email" id="login-opt-email" data-bind="value: 'Login with Password'"  /></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /ko -->
            <div class="field">
                <label class="label" for="customer-password"><span data-bind="i18n: 'Password'"></span></label>
                <div class="control">
                    <input class="input-text"
                           data-bind="
                                attr: {
                                    placeholder: $t('Password'),
                                }"
                           type="password"
                           name="password"
                           id="customer-password"
                           data-validate="{required:true}" autocomplete="off"/>
                    <span class="note" data-bind="i18n: 'You already have an account with us. Sign in or continue as guest.'"></span>
                </div>

            </div>
            <!-- ko foreach: getRegion('additional-login-form-fields') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!-- /ko -->
            <div class="actions-toolbar">
                <input name="context" type="hidden" value="checkout" />
                <div class="primary">
                    <button type="submit" class="action login primary" data-action="checkout-method-login"><span data-bind="i18n: 'Login'"></span></button>
                </div>
                <div class="secondary">
                    <a class="action remind" data-bind="attr: { href: forgotPasswordUrl }">
                        <span data-bind="i18n: 'Forgot Your Password?'"></span>
                    </a>
                </div>
            </div>
        </fieldset>
        <!--Hidden fields -->
    </fieldset>
</form>
<!-- /ko -->


