<?php

namespace Magedelight\SMSProfile\Plugin;

use Magento\Customer\Block\Account\AuthenticationPopup;
use Magento\Framework\Serialize\Serializer\Json;

class RecaptchaForLoginPopup
{
    /**
     * @var Json
     */
    private Json $serializer;

    /**
     * RecaptchaForLoginPopup constructor.
     * @param Json $serializer
     */
    public function __construct(Json $serializer)
    {
        $this->serializer = $serializer;
    }

    /**
     * Google re-captcha for login popup.
     *
     * @param AuthenticationPopup $subject
     * @param string $result
     * @return string
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetJsLayout(AuthenticationPopup $subject, $result)
    {
        $layout = $this->serializer->unserialize($result);
        if (isset($layout['components']['authenticationPopup']['children']['recaptcha']['settings'])) {
            $layout['components']['mdAuthenticationPopup']['children']['recaptcha']['settings'] =
                $layout['components']['authenticationPopup']['children']['recaptcha']['settings'];
        } else {
            unset($layout['components']['mdAuthenticationPopup']['children']['recaptcha']);
        }
        return $this->serializer->serialize($layout);
    }
}
