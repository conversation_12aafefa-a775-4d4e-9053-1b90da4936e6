<?php

namespace Magedelight\SMSProfile\Plugin;

use Magedelight\SMSProfile\Helper\ConfigHelper;

class ChangeEmail
{
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;

    /**
     * ChangeEmail constructor.
     * @param ConfigHelper $configHelper
     */
    public function __construct(ConfigHelper $configHelper)
    {
        $this->configHelper = $configHelper;
    }

    /**
     * Change email type.
     *
     * @param \Magento\Customer\Block\Form\Login $subject
     * @param string $result
     */
    public function afterToHtml(\Magento\Customer\Block\Form\Login $subject, $result)
    {
        if ($this->configHelper->isModuleEnable()) {
            return str_replace('type="email"', 'type="text"', $result);
        }
        return $result;
    }
}
