<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Plugin;

use Magedelight\SMSProfile\Helper\Data as HelperData;

/**
 * Class CustomerRepository
 *
 * Set dummy email if email is optional and empty.
 */
class CustomerRepository
{

    /**  @var HelperData */
    private $datahelper;

    public function __construct(HelperData $dataHelper)
    {
        $this->datahelper = $dataHelper;
    }

     /**
      * Before customer save.
      *
      * @param CustomerRepositoryInterface $customerRepository
      * @param CustomerInterface $customer
      * @param null $passwordHash
      * @return array
      *
      */
    public function beforeSave(
        $subject,
        \Magento\Customer\Api\Data\CustomerInterface $customer,
        $passwordHash = null
    ) {
        if ($this->datahelper->getModuleStatus() && $this->datahelper->getSmsProfileEmailOptionalOnSignUp()) {
            if ($customer->getEmail()== null) {
                $email = $customer->getCustomAttribute('countryreg')->getValue().$customer->getCustomAttribute('customer_mobile')->getValue()."@".$this->datahelper->getDummyEmailDomain();
                $customer->setEmail($email);
                $customer->setConfirmation(null);
                //$customer->setStatus(1);
            }
        }
        return [$customer, $passwordHash];
    }
}
