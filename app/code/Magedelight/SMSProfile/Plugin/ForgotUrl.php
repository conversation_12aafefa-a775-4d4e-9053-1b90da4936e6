<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Plugin;

use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magento\Customer\Block\Account\Forgotpassword;

class ForgotUrl
{
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;

    /**
     * ChangeEmail constructor.
     * @param ConfigHelper $configHelper
     */
    public function __construct(ConfigHelper $configHelper)
    {
        $this->configHelper = $configHelper;
    }

    /**
     * Modify forgot password form url.
     *
     * @param Forgotpassword $subject
     * @param string $route
     * @param array $params
     * @return array
     */
    public function beforeGetUrl(Forgotpassword $subject, $route = '', $params = [])
    {
        if ($this->configHelper->isModuleEnable()) {
            if (strpos($route, 'forgotpasswordpost') !== false) {
                $route = "customer/account/forgotpasswordpost";
            }
        }
        return [$route, $params];
    }
}
