<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Plugin;

use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magedelight\SMSProfile\Helper\Data as HelperData;
use Magedelight\SMSProfile\Helper\OtpHelper;
use Magedelight\SMSProfile\Model\Config\Source\Registration\OtpSetting;
use Magento\Customer\Controller\Account\CreatePost;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Message\ManagerInterface;

class CreateCustomerPlugin
{
    /**
     * @var HelperData
     */
    private HelperData $dataHelper;
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;
    /**
     * @var ManagerInterface
     */
    private ManagerInterface $messageManager;
    /**
     * @var OtpHelper
     */
    private OtpHelper $otpHelper;
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;

    /**
     * Constructor
     * @param HelperData $dataHelper
     * @param RequestInterface $request
     * @param ManagerInterface $messageManager
     * @param OtpHelper $otpHelper
     * @param ConfigHelper $configHelper
     */
    public function __construct(
        HelperData $dataHelper,
        RequestInterface $request,
        ManagerInterface $messageManager,
        OtpHelper $otpHelper,
        ConfigHelper $configHelper
    ) {
        $this->dataHelper = $dataHelper;
        $this->request = $request;
        $this->messageManager = $messageManager;
        $this->otpHelper = $otpHelper;
        $this->configHelper = $configHelper;
    }

    public function beforeExecute(CreatePost $subject)
    {
        $postData = $this->request->getPost();
        $mobile = $postData['customer_mobile'] ?? null;
        $dialCode = $postData['countryreg'] ?? null;
        $otp = $postData['otp'] ?? '';

        if (!$this->configHelper->isModuleEnable()) {
            return;
        }
        if ($this->isMobileRequireOnSignup() && !$mobile) {
            $this->messageManager->addErrorMessage(__("Mobile number is require."));
            $subject->getRequest()->setParam('form_key', '');
            return;
        }

        if ($this->configHelper->getOTPSettingSignup() == OtpSetting::DISABLE) {
            return;
        }

        if (!$otp) {
            $this->messageManager->addErrorMessage(__("Please verify OTP."));
            $subject->getRequest()->setParam('form_key', '');
            return;
        }

        if ($this->configHelper->isCountryCodeRequire()) {
            $mobile = $dialCode . $mobile;
        }

        try {
            $this->otpHelper->verifyOtp($mobile, $otp);
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $this->messageManager->addError(__($e->getMessage()));
            $subject->getRequest()->setParam('form_key', '');
        }
    }

    private function isMobileRequireOnSignup()
    {
        if ($this->configHelper->isEmailOptional()) {
            return true;
        }
        if ($this->configHelper->getOTPSettingSignup() != OtpSetting::DISABLE) {
            return true;
        }
        return $this->configHelper->isMobileRequireOnSignup();
    }
}
