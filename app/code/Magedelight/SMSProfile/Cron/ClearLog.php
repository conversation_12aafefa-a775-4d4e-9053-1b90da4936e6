<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Cron;

use Magedelight\SMSProfile\Model\SMSLogFactory;
use Magedelight\SMSProfile\Helper\Data as HelperData;

class ClearLog
{
    /**
     * @var SMSLogFactory
     */
    private $smslog;

    /**
     * @var HelperData
     */
    private $datahelper;

    /**
     * @param SMSLogFactory $smslog
     * @param HelperData $dataHelper
     */
    public function __construct(
        HelperData $dataHelper,
        SMSLogFactory $smslog
    ) {
        $this->smslog = $smslog;
        $this->datahelper = $dataHelper;
    }

    /**
     * SmsLog clear for Cron request
     *
     * @return RedirectFactory
     */
    public function execute()
    {
        if ($this->datahelper->getSmsLogEnable() && $this->datahelper->getCronStatus()) {
            $sms  = $this->smslog->create();
            try {
                $sms->clearelog();
            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());
            }
        }
        return $this;
    }
}
