<?php
/**
 * Magedelight
 * Copyright (C) 2019 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_StorelocatorGraphQl
 * @copyright Copyright (c) 2019 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */
 
declare(strict_types=1);

namespace Magedelight\StorelocatorGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\Resolver\Argument\SearchCriteria\Builder as SearchCriteriaBuilder;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magedelight\Storelocator\Api\StoreInformationManagementInterface;

/**
 * Class StoreHolidayInfo
 * @package Magedelight\StorelocatorGraphQl\Model\Resolver
 */
class StoreTimeInfo implements ResolverInterface
{
    /**
     * @var StoreInformationManagementInterface
     */
    protected $storeInformationManagement;

    /**
     * StoreHolidayInfo constructor.
     * @param StoreInformationManagementInterface $storeInformationManagement
     */
    public function __construct(
        StoreInformationManagementInterface $storeInformationManagement
    ) {
        $this->storeInformationManagement = $storeInformationManagement;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($args['storelocator_id']) && !isset($args['date']) || empty($args['storelocator_id']) && empty($args['date'])) {
            throw new GraphQlInputException(__('"storelocator_id" and "date" argument should be specified and not empty'));
        }

        $storeTime = $this->storeInformationManagement->getStoreTimeInformation($args['date'], $args['storelocator_id']);

        return ['storetime' => $storeTime];
    }
}