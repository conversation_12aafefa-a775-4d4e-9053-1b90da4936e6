<?php
/**
 * Magedelight
 * Copyright (C) 2019 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_StorelocatorGraphQl
 * @copyright Copyright (c) 2019 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */

declare(strict_types=1);

namespace Magedelight\StorelocatorGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magedelight\Storelocator\Api\StorelocatorRepositoryInterface;

/**
 * Delete Payment Token resolver, used for GraphQL mutation processing.
 */
class DeleteStoreLocator implements ResolverInterface
{
    /**
     * @var StorelocatorRepositoryInterface
     */
    private $storelocatorRepository;


    /**
     * DeleteStorePickup constructor.
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     */
    public function __construct(
        StorelocatorRepositoryInterface $storelocatorRepository
    ) {
        $this->storelocatorRepository = $storelocatorRepository;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($args['storelocator_id'])) {
            throw new GraphQlInputException(__('Specify the "storelocator_id" value.'));
        }

        $id = $this->storelocatorRepository->getById($args['storelocator_id']);
        if (!$id) {
            throw new GraphQlNoSuchEntityException(
                __('Could not find a store : %1', $args['storelocator_id'])
            );
        }

        return ['result' => $this->storelocatorRepository->deleteById($args['storelocator_id'])];
    }
}
