<?php
/**
 * Magedelight
 * Copyright (C) 2019 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_StorelocatorGraphQl
 * @copyright Copyright (c) 2019 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */

declare(strict_types=1);

namespace Magedelight\StorelocatorGraphQl\Model;

use Magedelight\Storelocator\Api\Data\StorelocatorInterface;
use Magedelight\Storelocator\Api\Data\StorelocatorInterfaceFactory;
use Magedelight\Storelocator\Api\StorelocatorRepositoryInterface;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;

/**
 * Class CreatStorePickUpService
 * @package Magedelight\StorelocatorGraphQl\Model
 */
class CreatStorePickUpService
{
    /**
     * @var DataObjectHelper
     */
    private $dataObjectHelper;

    /**
     * @var StorelocatorRepositoryInterface
     */
    private $storelocatorRepository;

    /**
     * @var StorelocatorInterfaceFactory
     */
    private $storelocatorInterfaceFactory;

    /**
     * CreatStorePickUpService constructor.
     * @param DataObjectHelper $dataObjectHelper
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     * @param StorelocatorInterfaceFactory $storelocatorInterfaceFactory
     */
    public function __construct(
        DataObjectHelper $dataObjectHelper,
        StorelocatorRepositoryInterface $storelocatorRepository,
        StorelocatorInterfaceFactory $storelocatorInterfaceFactory
    ) {
        $this->dataObjectHelper = $dataObjectHelper;
        $this->storelocatorRepository = $storelocatorRepository;
        $this->storelocatorInterfaceFactory = $storelocatorInterfaceFactory;
    }

    /**
     * Creates new store
     * @param array $data
     * @return StorelocatorInterface
     * @throws GraphQlInputException
     */
    public function execute(array $data): StorelocatorInterface
    {
        try {
            $store = $this->createStore($data);
        } catch (LocalizedException $e) {
            throw new GraphQlInputException(__($e->getMessage()));
        }

        return $store;
    }

    /**
     * Creates store
     *
     * @param array $data
     * @return StorelocatorInterface
     * @throws LocalizedException
     */
    private function createStore(array $data): StorelocatorInterface
    {
        /** @var StorelocatorInterface $storeDataObject */
        $storeDataObject = $this->storelocatorInterfaceFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $storeDataObject,
            $data,
            StorelocatorInterface::class
        );

        $storeDataObject = $this->storelocatorRepository->save($storeDataObject);

        return $storeDataObject;
    }
}