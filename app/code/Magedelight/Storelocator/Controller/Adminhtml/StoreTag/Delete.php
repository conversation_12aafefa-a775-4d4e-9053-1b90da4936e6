<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
namespace Magedelight\Storelocator\Controller\Adminhtml\StoreTag;

use Magedelight\Storelocator\Api\StoreTagRepositoryInterface;
use Magedelight\Storelocator\Controller\Adminhtml\StoreTag;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Registry;

class Delete extends StoreTag
{
    /**
     * @var StoreTagRepositoryInterface
     */
    protected $storeTagRepositoryInterface;

    /**
     * Delete constructor.
     * @param Context $context
     * @param Registry $coreRegistry
     * @param StoreTagRepositoryInterface $storeTagRepositoryInterface
     */
    public function __construct(
        Context $context,
        Registry $coreRegistry,
        StoreTagRepositoryInterface $storeTagRepositoryInterface
    ) {
        $this->storeTagRepositoryInterface = $storeTagRepositoryInterface;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Delete action
     *
     * @return ResultInterface
     */
    public function execute()
    {
        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $id = $this->getRequest()->getParam('tag_attribute_id');
        if ($id) {
            try {
                $this->storeTagRepositoryInterface->deleteById($id);
                $this->messageManager->addSuccessMessage(__('You deleted the Store Tag.'));
                return $resultRedirect->setPath('*/*/');
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
                return $resultRedirect->setPath('*/*/edit', ['tag_attribute_id' => $id]);
            }
        }
        $this->messageManager->addErrorMessage(__('We can\'t find a tag attribute to delete.'));
        return $resultRedirect->setPath('*/*/');
    }
}
