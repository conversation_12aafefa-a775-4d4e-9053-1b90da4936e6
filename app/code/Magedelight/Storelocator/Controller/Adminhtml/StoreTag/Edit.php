<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Controller\Adminhtml\StoreTag;

use Magedelight\Storelocator\Controller\Adminhtml\StoreTag;
use Magedelight\Storelocator\Model\TagAttributeFactory;
use Magedelight\Storelocator\Model\ResourceModel\TagAttribute as TagAttributeResource;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\View\Result\Page;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Registry;
use Magento\Framework\View\Result\PageFactory;

class Edit extends StoreTag
{
    /**
     * Authorization level of a basic admin session
     *
     * @see _isAllowed()
     */
    public const ADMIN_RESOURCE = 'Magedelight_Storelocator::storetag_root';

    /**
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var TagAttributeFactory
     */
    protected $tagAttributeModel;

    /**
     * @var TagAttributeResource
     */
    protected $tagAttributeResource;

    /**
     * Edit constructor.
     * @param Context $context
     * @param Registry $coreRegistry
     * @param PageFactory $resultPageFactory
     * @param TagAttributeFactory $tagAttributeModel
     * @param TagAttributeResource $tagAttributeResource
     */
    public function __construct(
        Context $context,
        Registry $coreRegistry,
        PageFactory $resultPageFactory,
        TagAttributeFactory $tagAttributeModel,
        TagAttributeResource $tagAttributeResource
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->tagAttributeModel = $tagAttributeModel;
        $this->tagAttributeResource = $tagAttributeResource;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Edit Tag Attribute
     *
     * @return ResultInterface
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function execute()
    {
        // 1. Get ID and create model
        $id = $this->getRequest()->getParam('tag_attribute_id');
        $model = $this->tagAttributeModel->create();

        // 2. Initial checking
        if ($id) {
            $this->tagAttributeResource->load($model, $id);
            if (!$model->getId()) {
                $this->messageManager->addErrorMessage(__('This block no longer exists.'));
                /** @var Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
        }

        $this->_coreRegistry->register('tag_attribute', $model);

        // 5. Build edit form
        /** @var Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            $id ? __('Edit Tag') : __('New Tag'),
            $id ? __('Edit Tag') : __('New Tag')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Tag Attribute'));
        $resultPage->getConfig()->getTitle()->prepend(
            $model->getId() ? __('%1', $model->getTagAttributeFrontendLabel()) : __('New Tag')
        );
        return $resultPage;
    }
}
