<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Controller\Adminhtml\Storeholiday;

use Magedelight\Storelocator\Model\StoreholidayFactory;
use Magedelight\Storelocator\Model\ResourceModel\Storeholiday as StoreholidayResource;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultInterface;

class InlineEdit extends Action
{
    /**
     * Authorization level of a basic admin session
     *
     * @see _isAllowed()
     */
    public const ADMIN_RESOURCE = 'Magedelight_Storelocator::storeholiday_root';

    /**
     * @var JsonFactory
     */
    protected $jsonFactory;

    /**
     * @var StoreholidayFactory
     */
    protected $storeholidayFactory;

    /**
     * @var StoreholidayResource
     */
    protected $storeholidayResource;

    /**
     * @param Context $context
     * @param JsonFactory $jsonFactory
     * @param StoreholidayFactory $storeholidayFactory
     * @param StoreholidayResource $storeholidayResource
     */
    public function __construct(
        Context $context,
        JsonFactory $jsonFactory,
        StoreholidayFactory $storeholidayFactory,
        StoreholidayResource $storeholidayResource
    ) {
        parent::__construct($context);
        $this->jsonFactory = $jsonFactory;
        $this->storeholidayFactory = $storeholidayFactory;
        $this->storeholidayResource = $storeholidayResource;
    }

    /**
     * Inline edit action
     *
     * @return ResultInterface
     */
    public function execute()
    {
        $resultJson = $this->jsonFactory->create();
        $error = false;
        $messages = [];

        if ($this->getRequest()->getParam('isAjax')) {
            $postItems = $this->getRequest()->getParam('items', []);
            if (!count($postItems)) {
                $messages[] = __('Please correct the data sent.');
                $error = true;
            } else {
                foreach (array_keys($postItems) as $itemId) {
                    $model = $this->storeholidayFactory->create();
                    $this->storeholidayResource->load($model, $itemId);
                    try {
                        $model->addData($postItems[$itemId]);
                        $this->storeholidayResource->save($model);
                    } catch (\Exception $e) {
                        $messages[] = "[Error : {$itemId}]  {$e->getMessage()}";
                        $error = true;
                    }
                }
            }
        }

        return $resultJson->setData([
            'messages' => $messages,
            'error' => $error
        ]);
    }
}
