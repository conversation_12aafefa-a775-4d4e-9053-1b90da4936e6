<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Controller\Adminhtml\Storeholiday;

use Magedelight\Storelocator\Controller\Adminhtml\Storeholiday;
use Magedelight\Storelocator\Model\ResourceModel\Storeholiday\CollectionFactory;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Registry;

class Delete extends Storeholiday
{
    /**
     * @var $storeholidayCollectionFactory
     */
    protected $storeholidayCollectionFactory;

    /**
     * Delete constructor.
     *
     * @param Context $context
     * @param Registry $coreRegistry
     * @param CollectionFactory $storeholidayCollectionFactory
     */
    public function __construct(
        Context $context,
        Registry $coreRegistry,
        CollectionFactory $storeholidayCollectionFactory
    ) {
        $this->storeholidayCollectionFactory = $storeholidayCollectionFactory;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Delete action
     *
     * @return ResultInterface
     */
    public function execute()
    {
        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $id = $this->getRequest()->getParam('holiday_id');
        $ids = [];
        if (isset($id) && !empty($id)) {
            $ids[] = $id;
        }
        if (isset($ids)) {
            try {
                $model = $this->storeholidayCollectionFactory->create()
                    ->addFieldToFilter('holiday_id', ['in' => $ids]);
                $model->walk('delete');
                $this->messageManager->addSuccessMessage(__('You deleted the holiday.'));
                return $resultRedirect->setPath('*/*/');
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
                return $resultRedirect->setPath('*/*/edit', ['holiday_id' => $id]);
            }
        }
        $this->messageManager->addErrorMessage(__('We can\'t find a holiday to delete.'));
        return $resultRedirect->setPath('*/*/');
    }
}
