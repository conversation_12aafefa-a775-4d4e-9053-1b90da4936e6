<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Controller;

use Magedelight\Storelocator\Helper\Storelocator as StoreHelper;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\Redirect;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ActionFactory;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Exception\NotFoundException;
use Magento\Framework\Exception\SessionException;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Result\PageFactory;
use Magedelight\Storelocator\Helper\StoreView as StoreViewHelper;
use Magedelight\Storelocator\Api\StoreInformationManagementInterface;

abstract class Storelocator extends Action
{
    /**
     * @var ResponseInterface
     */
    protected $response;

    /**
     * @var PageFactory
     */
    protected $pageFactory;

    /**
     * @var UrlInterface
     */
    protected $url;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @var ActionFactory
     */
    protected $actionFactory;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var StoreHelper
     */
    protected $storeHelper;

    /**
     * @var StoreViewHelper
     */
    protected $storeViewHelper;

    /**
     * @var StoreInformationManagementInterface
     */
    protected $storeInformationManagementInterface;

    /**
     * Storelocator constructor.
     *
     * @param Context $context
     * @param ActionFactory $actionFactory
     * @param PageFactory $pageFactory
     * @param ScopeConfigInterface $scopeConfig
     * @param Session $customerSession
     * @param StoreHelper $storeHelper
     * @param StoreViewHelper $storeViewHelper
     * @param StoreInformationManagementInterface $storeInformationManagementInterface
     */
    public function __construct(
        Context $context,
        ActionFactory $actionFactory,
        PageFactory $pageFactory,
        ScopeConfigInterface $scopeConfig,
        Session $customerSession,
        StoreHelper $storeHelper,
        StoreViewHelper $storeViewHelper,
        StoreInformationManagementInterface $storeInformationManagementInterface
    ) {
        $this->pageFactory = $pageFactory;
        $this->actionFactory = $actionFactory;
        $this->url = $context->getUrl();
        $this->response = $context->getResponse();
        $this->scopeConfig = $scopeConfig;
        $this->customerSession = $customerSession;
        $this->storeHelper = $storeHelper;
        $this->storeViewHelper = $storeViewHelper;
        $this->storeInformationManagementInterface = $storeInformationManagementInterface;
        return parent::__construct($context);
    }

    /**
     * Get No Route.
     */
    public function noRoute()
    {
        $this->_actionFlag->set('', 'no-dispatch', true);
        $this->getResponse()->setRedirect(
            $this->_url->getUrl('cms/noroute/index')
        );
    }

    /**
     * Dispatch.
     *
     * @param RequestInterface $request
     * @return ResponseInterface
     * @throws NotFoundException
     */
    public function dispatch(RequestInterface $request)
    {
        if (!$this->storeHelper->isModuleEnable()) {
            $this->noRoute();
        }

        $this->checkRedirect($request);
        return parent::dispatch($request);
    }

    /**
     * Get Check Redirect.
     *
     * @param RequestInterface $request
     * @return ActionInterface
     * @throws SessionException
     */
    protected function checkRedirect(RequestInterface $request)
    {
        if (!$this->storeHelper->isAllowGustCustomerEnable()) {
            /* Check customer is login or not */
            if (!$this->customerSession->authenticate()) {
                $url = $this->url->getUrl('customer/account/login');
                $this->response->setRedirect($url);
                $request->setDispatched(true);
                return $this->actionFactory->create(Redirect::class);
            }
        }
    }
}
