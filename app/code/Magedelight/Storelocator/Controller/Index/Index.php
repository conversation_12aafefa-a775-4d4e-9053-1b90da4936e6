<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Controller\Index;

use Magedelight\Storelocator\Controller\Storelocator;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;

class Index extends Storelocator
{
    /**
     * Show Store Locator page
     *
     * @return ResultInterface
     */
    public function execute()
    {
        $resultPage = $this->pageFactory->create();
        $title = $this->storeHelper->getMetaTitle();

        if (empty($title)) {
            $title = 'Storelocator';
        }

        if ($title) {
            $resultPage->getConfig()->getTitle()->set($title);
        }

        if ($this->storeHelper->getMetaDescription()) {
            $resultPage->getConfig()->setDescription($this->storeHelper->getMetaDescription());
        }

        if ($this->storeHelper->getMetaKeywords()) {
            $resultPage->getConfig()->setKeywords($this->storeHelper->getMetaKeywords());
        }

        return $this->resultFactory->create(ResultFactory::TYPE_PAGE);
    }
}
