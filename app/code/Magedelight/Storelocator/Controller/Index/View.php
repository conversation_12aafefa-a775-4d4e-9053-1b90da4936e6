<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Controller\Index;

use Magedelight\Storelocator\Controller\Storelocator;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Page;

class View extends Storelocator
{
    /**
     * Execute.
     *
     * @return bool|ResponseInterface|ResultInterface|Page|void
     */
    public function execute()
    {
        $storeLocatorId = $this->getRequest()->getParam('id');
        if ($storeLocatorId) {
            $storeLocator = $this->storeViewHelper->prepareStoreView($storeLocatorId);
            $requestURI = $this->_request->getRequestUri();
            $hasViewURL   = '/view/id/';
            if (strpos($requestURI, $hasViewURL) !== false) {
                $this->_redirect($this->storeViewHelper->getIdentifier());
            }
            if (!$storeLocator) {
                return $this->noRoute();
            }
            return $storeLocator;
        }
        return false;
    }
}
