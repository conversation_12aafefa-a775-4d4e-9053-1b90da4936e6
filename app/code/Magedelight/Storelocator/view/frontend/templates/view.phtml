<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

/** @var View $block */

$storelocator = $block->getStoreLocator();
$name = $escaper->escapeHtml($storelocator->getStorename());
?>
<div class="block">
    <aside class="store-map">
        <div class="ajax-info"
        data-mage-init='{"mdMapWidget": {
        "mapId": "map",
        "ajaxUrl": "<?= /* @noEscape */ $block->getStoreAjaxUrl(); ?>",
        "countries": "<?= /* @noEscape */ $block->getCountry(); ?>",
        "storeId": "#store-list",
        "storeListId": "li .store-list-information",
        "streetViewButton": ".street-view-btn",
        "directionViewButton": ".direction-view-btn",
        "directionOptionId": "#option-direction ul li",
        "directionPopupId": "#direction-popup-modal",
        "endLocationId": "#end-location",
        "startLocationId": "#start-location",
        "directionButtonId": "#submit-direction",
        "directionOption":"DRIVING",
        "directionPanelId": "#store-list-direction-panel",
        "searchInputId": "#start-location",
        "mapTheme": <?= /* @noEscape */ $block->getMapStyle() ?>,
        "liElement": ".store-list ul .store-list-item",
        "radiusInputId": "#circleRadius",
        "reverseLocation":".reverse-location",
        "currentLocationId": "#getCurrentPosition"}}'>
            <div class="md-locatore-container">
                <div class="md-locator-left">
                    <?= /* @noEscape */ $block->getStoreDirectionTemplate() ?>
                    <?= /* @noEscape */ /** @var Info */ $block->getChildHtml('storelocator.info'); ?>
                    <?= /** @var Holidays */$block->getChildHtml('storelocator.holidays');?>
                    <?= /* @noEscape */ $block->getStoreOtherInfoTemplate() ?>
                    <?= /** @var Gallery */ $block->getChildHtml('storelocator.gallery');?>
                </div>
                <div class="md-locator-right">
                    <div id="map" class="map-container"></div>
                    <div>
                        <?= /* @noEscape */ /** @var Tags */
                            $block->getChildHtml('storelocator.tags');
                        ?>
                        <?php if ($block->getStoreLocator()->getDescription() != ""):?>
                            <div class="store-detail">
                                <h2 class="title"><?= /* @noEscape */ $name ?></h2>
                                <div class="store-description">
                                    <?= /* @noEscape */ html_entity_decode($block->getStoreLocator()->getDescription());?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </aside>
</div>
<script>
    var googleApi = "<?= /* @noEscape */ $block->getMapApi() ?>";
    var booleanTemplate = <?= /* @noEscape */ trim($block->getStoreListTemplate()) ?>;
    var markerIcon = '<?= /* @noEscape */ $block->getMarkerImage() ?>';
</script>
