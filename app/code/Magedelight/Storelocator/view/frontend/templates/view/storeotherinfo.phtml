<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
?>


<?php
$storelocator = $this->getStoreLocator();
$showPhone = $storelocator->getPhoneFrontendStatus();
$phone = $escaper->escapeHtml($storelocator->getTelephone());
$email = $escaper->escapeHtml($storelocator->getStoreemail());
$storeWebsiteURL = $escaper->escapeUrl($storelocator->getWebsiteUrl());
$storeFacebookURL = $escaper->escapeUrl($storelocator->getFacebookUrl());
$storeTwitterURL = $escaper->escapeUrl($storelocator->getTwitterUrl());
?>

<?php if ($showPhone && $phone): ?>
	<div class="phone">
		<?php $firstPhone = explode(':', $phone);
		foreach ($firstPhone as $number): ?>
			<svg xmlns="http://www.w3.org/2000/svg" width="15.352" height="15.352" viewBox="0 0 15.352 15.352">
				<path id="phone-call" d="M15.858,12.053c-.042-.033-3.088-2.229-3.924-2.072-.4.071-.627.343-1.085.888-.074.088-.251.3-.388.449a6.405,6.405,0,0,1-.845-.344A7.011,7.011,0,0,1,6.38,7.74,6.375,6.375,0,0,1,6.036,6.9c.15-.138.361-.315.451-.391.542-.455.815-.684.886-1.084C7.518,4.591,5.324,1.524,5.3,1.5A1.171,1.171,0,0,0,4.429,1C3.539,1,1,4.294,1,4.849c0,.032.047,3.309,4.088,7.42,4.107,4.037,7.383,4.083,7.416,4.083.555,0,3.849-2.539,3.849-3.429a1.168,1.168,0,0,0-.494-.87Z" transform="translate(-1 -1)" fill="#fff"/>
			</svg>
			<a href="tel:<?=  /* @noEscape */ $number ?>"><?= /* @noEscape */ $number ?></a></br>
		<?php endforeach; ?>
	</div>
<?php endif; ?>

<?php if ($email): ?>
	<div class="email">
		<div class="icon">
			<svg xmlns="http://www.w3.org/2000/svg" width="15.352" height="11.498" viewBox="0 0 15.352 11.498">
				<g id="email" transform="translate(0 -64.266)">
					<path id="Path_44856" data-name="Path 44856" d="M8.959,175.669a2.309,2.309,0,0,1-2.566,0L.1,171.474c-.035-.023-.069-.048-.1-.072v6.872a1.413,1.413,0,0,0,1.413,1.413H13.939a1.413,1.413,0,0,0,1.413-1.413V171.4c-.033.025-.067.049-.1.073Z" transform="translate(0 -103.924)" fill="#fff"/>
					<path id="Path_44857" data-name="Path 44857" d="M.6,66.8,6.892,71A1.41,1.41,0,0,0,8.46,71L14.751,66.8a1.347,1.347,0,0,0,.6-1.124,1.414,1.414,0,0,0-1.413-1.413H1.413A1.415,1.415,0,0,0,0,65.68,1.347,1.347,0,0,0,.6,66.8Z" fill="#fff"/>
				</g>
			</svg>
		</div>
		<a href="mailto:<?= /* @noEscape */ $email ?>"><?= /* @noEscape */ $email ?></a></br>
	</div>
<?php endif; ?>

<?php if ($storeWebsiteURL): ?>
	<div class="website">
		<svg xmlns="http://www.w3.org/2000/svg" width="16.178" height="16.178" viewBox="0 0 16.178 16.178">
			<g id="internet" transform="translate(-97 -97)">
				<path id="Path_44859" data-name="Path 44859" d="M112.616,102.125a8.107,8.107,0,0,1,0,5.929,23.089,23.089,0,0,0-4.1-.82c.05-.69.077-1.407.077-2.144s-.027-1.454-.077-2.144A23.089,23.089,0,0,0,112.616,102.125Zm-15.055,5.929a8.107,8.107,0,0,1,0-5.929,23.089,23.089,0,0,0,4.1.82c-.05.69-.077,1.407-.077,2.144s.027,1.454.077,2.144A23.088,23.088,0,0,0,97.561,108.053Zm9.883-.922c-.761-.058-1.549-.088-2.355-.088s-1.594.031-2.355.088c-.046-.657-.071-1.341-.071-2.043s.025-1.386.071-2.043c.761.058,1.549.088,2.355.088s1.594-.031,2.355-.088c.046.657.071,1.341.071,2.043S107.491,106.475,107.444,107.132Zm4.675,1.957a8.113,8.113,0,0,1-4.766,3.767,17.18,17.18,0,0,0,1.056-4.489A22.2,22.2,0,0,1,112.12,109.089ZM106,113.127a8.21,8.21,0,0,1-1.831,0,5.67,5.67,0,0,1-.345-.683,15.474,15.474,0,0,1-.99-4.177c.728-.054,1.48-.083,2.25-.083s1.522.028,2.25.083a15.474,15.474,0,0,1-.99,4.177A5.67,5.67,0,0,1,106,113.127Zm-3.181-.271a8.113,8.113,0,0,1-4.766-3.767,22.2,22.2,0,0,1,3.71-.722A17.179,17.179,0,0,0,102.824,112.856Zm-4.766-11.767a8.113,8.113,0,0,1,4.766-3.767,17.179,17.179,0,0,0-1.056,4.489A22.2,22.2,0,0,1,98.058,101.089Zm6.115-4.038a8.208,8.208,0,0,1,1.831,0,5.671,5.671,0,0,1,.345.683,15.474,15.474,0,0,1,.99,4.177c-.728.054-1.48.083-2.25.083s-1.522-.028-2.25-.083a15.474,15.474,0,0,1,.99-4.177A5.67,5.67,0,0,1,104.173,97.051Zm3.181.271a8.113,8.113,0,0,1,4.766,3.767,22.2,22.2,0,0,1-3.71.722A17.179,17.179,0,0,0,107.354,97.322Z" fill="#fff" fill-rule="evenodd"/>
			</g>
		</svg>
		<a href="<?= /* @noEscape */ $storeWebsiteURL ?>" target="_blank">
			<?= /* @noEscape */ $storeWebsiteURL ?>
		</a></br>
	</div>
<?php endif; ?>

<div class="social-link">
	<?php if ($storeFacebookURL): ?>
		<div class="facebook">
			<svg xmlns="http://www.w3.org/2000/svg" width="11.525" height="22.194" viewBox="0 0 11.525 22.194">
				<g id="ic_facebook" transform="translate(-37.29)">
					<path id="f_1_" d="M44.771,22.194V12.071h3.4l.51-3.946H44.771V5.606c0-1.142.316-1.921,1.956-1.921h2.088V.155A28.312,28.312,0,0,0,45.772,0C42.759,0,40.7,1.839,40.7,5.215v2.91H37.29v3.946H40.7V22.194Z" fill="#1979c1"/>
				</g>
			</svg>
			<a href="<?= /* @noEscape */ $storeFacebookURL ?>" target="_blank">Facebook</a>
		</div>
	<?php endif; ?>

	<?php if ($storeTwitterURL): ?>
		<div class="twitter">
			<svg xmlns="http://www.w3.org/2000/svg" width="16.578" height="16.942" viewBox="0 0 16.578 16.942">
				<path id="ic_twitter" d="M23.051,7.174,29.223,0H27.76L22.4,6.229,18.121,0H13.185l6.472,9.419-6.472,7.523h1.463l5.659-6.578,4.52,6.578h4.937L23.051,7.174Zm-2,2.328-.656-.938L15.174,1.1h2.246l4.211,6.023.656.938,5.474,7.829H25.515L21.048,9.5Z" transform="translate(-13.185)"/>
			</svg>
			<a href="<?= /* @noEscape */ $storeTwitterURL ?>" target="_blank">Twitter</a>
		</div>
	<?php endif; ?>
</div>