<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */
?>
<?php $store = $this->getStore();?>
<div id="direction-popup-modal" style="display:block;" data-long=<?php echo $this->escapeHtml($store['longitude']); ?> data-lat=<?php echo $this->escapeHtml($store['latitude']); ?> data-index=<?php echo $this->escapeHtml($store['storelocator_id']); ?>>
    <div id="option-direction">
        <ul class="vertical">
            <li data-mvalue="DRIVING" class="travel car">
                <span>
                    <svg id="ic_car" xmlns="http://www.w3.org/2000/svg" width="25.461" height="18.056" viewBox="0 0 25.461 18.056">
                        <g id="Group_25919" data-name="Group 25919" transform="translate(22.343 4.229)">
                            <path id="Path_44861" data-name="Path 44861" d="M48.1,18.106c-.156-.691-1.205-1.018-2.385-.748A4.092,4.092,0,0,0,45,17.6l.535,1.346c.031.026.062.047.094.073a4.187,4.187,0,0,1,.748.769c1.143-.3,1.876-1.013,1.72-1.683Z" transform="translate(-45.001 -17.263)" fill="#6aa8d6"/>
                        </g>
                        <g id="Group_25920" data-name="Group 25920" transform="translate(0 4.225)">
                            <path id="Path_44862" data-name="Path 44862" d="M4.416,19.028a1.134,1.134,0,0,1,.094-.073l.551-1.382a3.23,3.23,0,0,0-.655-.218c-1.2-.275-2.224.047-2.385.748-.156.66.556,1.367,1.663,1.673a4.154,4.154,0,0,1,.733-.748Z" transform="translate(-1.999 -17.257)" fill="#6aa8d6"/>
                        </g>
                        <g id="Group_25921" data-name="Group 25921" transform="translate(2.515 14.845)">
                            <path id="Path_44863" data-name="Path 44863" d="M6.84,37.7v2.291a.918.918,0,0,0,.92.92h2.53a.917.917,0,0,0,.914-.92v-2H8a2.416,2.416,0,0,1-1.159-.3Z" transform="translate(-6.84 -37.695)" fill="#6aa8d6"/>
                        </g>
                        <g id="Group_25922" data-name="Group 25922" transform="translate(18.514 14.845)">
                            <path id="Path_44864" data-name="Path 44864" d="M37.63,37.991v2a.919.919,0,0,0,.925.92h2.525a.921.921,0,0,0,.92-.92V37.7a2.452,2.452,0,0,1-1.164.3Z" transform="translate(-37.63 -37.695)" fill="#6aa8d6"/>
                        </g>
                        <g id="Group_25923" data-name="Group 25923" transform="translate(1.26 0)">
                            <path id="Path_44865" data-name="Path 44865" d="M25.808,15.516c-.069-.056-.139-.108-.193-.144l-2.072-5.185a1.682,1.682,0,0,0-1.571-1.063H9.746a1.682,1.682,0,0,0-1.571,1.063L6.1,15.378c-.067.048-.133.1-.2.148-1.143.914-1.617,2.333-1.447,4.34a9.684,9.684,0,0,0,.629,2.706,1.767,1.767,0,0,0,.707.861,1.884,1.884,0,0,0,1.042.313H24.881a1.92,1.92,0,0,0,1.046-.312,1.813,1.813,0,0,0,.7-.861,9.676,9.676,0,0,0,.63-2.707c.169-2.009-.305-3.432-1.452-4.35ZM8.8,14.131l1.257-3.159a.5.5,0,0,1,.466-.314H21.193a.5.5,0,0,1,.465.313l1.258,3.16a.487.487,0,0,1-.047.462.5.5,0,0,1-.419.221H9.268a.5.5,0,0,1-.466-.684Zm.939,6.1H7.371a1.193,1.193,0,0,1-1.19-1.19V18.5a.419.419,0,0,1,.421-.421H8.431a1.721,1.721,0,0,1,1.73,1.73.419.419,0,0,1-.42.421Zm10.537-1.577-.726,1.963a.382.382,0,0,1-.37.234H12.538a.38.38,0,0,1-.37-.234l-.726-1.963a.348.348,0,0,1,.37-.438h8.1a.348.348,0,0,1,.37.438Zm5.669.387a1.18,1.18,0,0,1-.094.462,1.194,1.194,0,0,1-1.1.728H22.387a.419.419,0,0,1-.421-.421,1.73,1.73,0,0,1,1.73-1.73h1.829a.416.416,0,0,1,.421.421Z" transform="translate(-4.424 -9.125)" fill="#6aa8d6"/>
                        </g>
                    </svg>
                    <?= /* @noEscape */ __("Driving") ?>
                </span>
            </li>
            <li data-mvalue="TRANSIT" class="travel bus active">
                <span>
                    <svg id="ic_train" xmlns="http://www.w3.org/2000/svg" width="16.818" height="20.77" viewBox="0 0 16.818 20.77">
                        <g id="Group_25924" data-name="Group 25924" transform="translate(0.764 0)">
                            <path id="Path_44869" data-name="Path 44869" d="M6.5,16.472H18.524a1.634,1.634,0,0,0,1.634-1.634V4.964A4.086,4.086,0,0,0,16.072.877H8.952A4.086,4.086,0,0,0,4.867,4.964v9.874A1.634,1.634,0,0,0,6.5,16.472Zm10.437-2.247a1.09,1.09,0,1,1,1.09-1.09A1.09,1.09,0,0,1,16.939,14.225ZM18.028,5.1V9.593H13.371V5.1ZM7,5.1h4.657V9.593H7Zm1.09,6.947A1.09,1.09,0,1,1,7,13.135,1.089,1.089,0,0,1,8.086,12.046Z" transform="translate(-4.867 -0.877)" fill="#6aa8d6"/>
                        </g>
                        <g id="Group_25925" data-name="Group 25925" transform="translate(0 16.82)">
                            <path id="Path_44870" data-name="Path 44870" d="M18.519,29.321h2.054l-3.018-3.95H15.5l.82,1.073H8.007l.82-1.073H6.773l-3.018,3.95H5.808L6.87,27.932H17.458Z" transform="translate(-3.755 -25.371)" fill="#6aa8d6"/>
                        </g>
                    </svg>
                    <?= /* @noEscape */ __("Transit") ?>
                </span>
            </li>
            <li data-mvalue="WALKING" class="travel walk">
                <span>
                    <svg id="ic_person" xmlns="http://www.w3.org/2000/svg" width="11.509" height="20.77" viewBox="0 0 11.509 20.77">
                        <path id="Path_44866" data-name="Path 44866" d="M21.778,14.621,19.56,13.393l-.566-.8v2.993l1.953.653a.914.914,0,0,0,.831-1.621Z" transform="translate(-10.718 -4.739)" fill="#6aa8d6"/>
                        <circle id="Ellipse_8" data-name="Ellipse 8" cx="2.218" cy="2.218" r="2.218" transform="translate(3.643 0)" fill="#6aa8d6"/>
                        <path id="Path_44867" data-name="Path 44867" d="M11.768,20a2.757,2.757,0,0,1-.478-.514l-.207,2.449L8.4,25.71a.958.958,0,0,0,1.384,1.319l2.7-3.094a3.934,3.934,0,0,0,.771-1.356l.353-1.074Z" transform="translate(-7.927 -6.516)" fill="#6aa8d6"/>
                        <path id="Path_44868" data-name="Path 44868" d="M12.994,9h.381a2.056,2.056,0,0,1,2.056,2.056v3.665a1.757,1.757,0,0,0,.515,1.243l1.419,1.419a3.8,3.8,0,0,1,1.02,1.848l.952,4.2a1,1,0,0,1-.991,1.153h0a1,1,0,0,1-.966-.736l-1.461-4-3.668-3a2.421,2.421,0,0,1-.885-1.967l.109-2.816-1.309,1.059-.437,2.207a.953.953,0,0,1-1.126.749h0a.953.953,0,0,1-.751-1.083l.4-2.53a2.221,2.221,0,0,1,.871-1.437l1.8-1.336A3.472,3.472,0,0,1,12.994,9Z" transform="translate(-7.84 -3.811)" fill="#6aa8d6"/>
                    </svg>
                    <?= /* @noEscape */ __("Walking") ?>
                </span>
            </li>
            <li data-mvalue="BICYCLING" class="travel bicycle">
                <span>
                    <svg id="ic_cycle" xmlns="http://www.w3.org/2000/svg" width="21.006" height="18.056" viewBox="0 0 21.006 18.056">
                        <path id="XMLID_512_" d="M6,268.986a2.918,2.918,0,0,1-1.786.619,2.947,2.947,0,1,1,2.721-4.072l.923-.957a4.215,4.215,0,1,0-.81,5.184,2.093,2.093,0,0,1-.794-.474A2.138,2.138,0,0,1,6,268.986Z" transform="translate(0 -252.817)" fill="#6aa8d6"/>
                        <path id="XMLID_513_" d="M133.491,97.258l-2.232-1.3,1.622-1.015-.031.317a1.077,1.077,0,0,0,.845,1.135c.012,0,3.918.666,4.061.666a1.057,1.057,0,0,0,.172-2.1l-2.876-.478.137-1.372c.138-.877.2-1.152-.13-1.675l-.412-.658a1.818,1.818,0,0,0-2.5-.576l-4.505,2.821a2.172,2.172,0,0,0-.743,3.228,44.793,44.793,0,0,0,3.784,2.529l-2.779,2.883a1.268,1.268,0,1,0,1.826,1.76l4.042-4.192A1.269,1.269,0,0,0,133.491,97.258Z" transform="translate(-121.09 -87.601)" fill="#6aa8d6"/>
                        <path id="XMLID_514_" d="M331.991,38.768a2.111,2.111,0,1,0-2.909-.67A2.11,2.11,0,0,0,331.991,38.768Z" transform="translate(-314.856 -34.868)" fill="#6aa8d6"/>
                        <path id="XMLID_515_" d="M301.534,262.443a4.215,4.215,0,1,0,4.215,4.215A4.234,4.234,0,0,0,301.534,262.443Zm0,7.162a2.947,2.947,0,1,1,2.947-2.947A2.95,2.95,0,0,1,301.534,269.605Z" transform="translate(-284.743 -252.817)" fill="#6aa8d6"/>
                    </svg>
                    <?= /* @noEscape */ __("ByCycle") ?>
                </span>
            </li>
        </ul>
    </div>
    <div class="directions-input" id="directions-el">
        <div class="form-inputs">
            <form id="get-direction" method="post" action="#"  data-mage-init='{"validation":{}}'>
                <label for="start-location"></label>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" class="location">
                    <g id="round" fill="#1979c1" stroke="#fff" stroke-width="4">
                        <circle cx="8" cy="8" r="8" stroke="none"/>
                        <circle cx="8" cy="8" r="6" fill="none"/>
                    </g>
                </svg>
                <input id="start-location" name="start-location" data-validate="{required:true}" type="text"
                       autocomplete="off" class="form-control start-location direction-popup" placeholder="Enter a location" />
                <a href="#" id="getCurrentPosition" class="current-position">
                    <?= /* @noEscape */ __("current location"); ?>
                </a>
                <label for="end-location-"></label>
                <svg xmlns="http://www.w3.org/2000/svg" width="15.25" height="20.333" viewBox="0 0 15.25 20.333" class="locaton-pin">
                    <g id="ic_Pin" transform="translate(-8)">
                        <path id="Path_44860" data-name="Path 44860" d="M15.625,0A7.635,7.635,0,0,0,8,7.625c0,5.474,7.1,12.33,7.406,12.619a.315.315,0,0,0,.438,0c.3-.289,7.406-7.145,7.406-12.619A7.635,7.635,0,0,0,15.625,0Zm0,11.12A3.495,3.495,0,1,1,19.12,7.625,3.495,3.495,0,0,1,15.625,11.12Z" fill="#fff"/>
                    </g>
                </svg>
                <input id="end-location" name="end-location" type="text" data-validate="{required:true}" readonly="true" value="<?=$this->escapeHtml($this->getCurrentAddress());?>" class="form-control store-location end" />
                <div class="reverse-location"></div>
                <div class="box-input">
                <button id="submit-direction" class="button btn btn-show btn-go-direction" title="Go">
                    <span><?= /* @noEscape */ __("Go"); ?></span>
                </button>
            </div>
            </form>
            <div id="directions-panel" class="directions-panel"></div>
        </div>
    </div>
</div>
