<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

/**
 * @var \Magedelight\Storelocator\Block\View\Info $block
 */

$storelocator = $block->getStoreLocator();
$helper = $this->helper('Magedelight\Storelocator\Helper\Storelocator');
$name = $escaper->escapeHtml($storelocator->getStorename());
$email = $escaper->escapeHtml($storelocator->getStoreemail());
$showPhone = $storelocator->getPhoneFrontendStatus();
$phone = $escaper->escapeHtml($storelocator->getTelephone());
$description = $storelocator->getDescription();

$address = $escaper->escapeHtml($block->filterOutputHtml($storelocator->getAddress()));
$city = $escaper->escapeHtml($storelocator->getCity());
$zipcode = $escaper->escapeHtml($storelocator->getZipcode());
/*$region = $escaper->escapeHtml($storelocator->getRegion());*/

if ($storelocator->getRegionId() !== null) {
    $region = $escaper->escapeHtml($helper->getRegionById($storelocator->getRegionId()));
}

if ($storelocator->getRegion() !== null) {
    $region = $escaper->escapeHtml($storelocator->getRegion());
}

$country = $escaper->escapeHtml($block->getCountryByCode($storelocator->getCountryId()));

$storeWebsiteURL = $escaper->escapeUrl($storelocator->getWebsiteUrl());
$storeFacebookURL = $escaper->escapeUrl($storelocator->getFacebookUrl());
$storeTwitterURL = $escaper->escapeUrl($storelocator->getTwitterUrl());
?>
<div class="store-list-title" data-long="<?php echo $this->escapeHtml($storelocator->getLongitude()); ?>" data-lat="<?php echo $this->escapeHtml($storelocator->getLatitude()); ?>">
    <h2 class="title"><?= /* @noEscape */ __("Store Details"); ?></h2>
    <span id="street-view" class="street-view-btn">
        <svg id="ic_StreetView_white" xmlns="http://www.w3.org/2000/svg" width="21" height="22.252" viewBox="0 0 21 22.252">
            <path id="Path_44842" data-name="Path 44842" d="M188.894,117.4h-4.107a1.087,1.087,0,0,0-1.087,1.087v4.6a1.09,1.09,0,0,0,1.026,1.087l.63,5.076a.649.649,0,0,0,.648.569h1.678a.649.649,0,0,0,.648-.569l.63-5.076a1.086,1.086,0,0,0,1.021-1.087v-4.6A1.084,1.084,0,0,0,188.894,117.4Z" transform="translate(-176.342 -112.298)" fill="#fff"/>
            <circle id="Ellipse_2" data-name="Ellipse 2" cx="2.282" cy="2.282" r="2.282" transform="translate(8.218)" fill="#fff"/>
            <path id="Path_44843" data-name="Path 44843" d="M34.309,316.027a7.458,7.458,0,0,0-2.36-1.369c-.191-.074-.382-.143-.587-.209a1.085,1.085,0,1,0-.682,2.06c.**************.487.174,1.417.548,2.064,1.2,2.064,1.565s-.643,1.017-2.064,1.565a17.982,17.982,0,0,1-6.267,1,17.939,17.939,0,0,1-6.263-1c-1.417-.548-2.064-1.2-2.064-1.565s.643-1.017,2.064-1.565c.156-.061.317-.117.487-.174a1.085,1.085,0,1,0-.682-2.06q-.3.1-.587.209a7.483,7.483,0,0,0-2.364,1.369,2.806,2.806,0,0,0,0,4.442,7.458,7.458,0,0,0,2.36,1.369,20.1,20.1,0,0,0,7.049,1.143,20.1,20.1,0,0,0,7.049-1.143,7.458,7.458,0,0,0,2.36-1.369,2.806,2.806,0,0,0,0-4.442Z" transform="translate(-14.4 -300.729)" fill="#fff"/>
        </svg>
        <?= /* @noEscape */ __("Street View"); ?>
    </span>
</div>
<div class="store-view-info" data-index="<?=  /* @noEscape */ $storelocator->getStorelocatorId(); ?>">
    <div class="md-locator-information">
        <div class="md-information-left-block">
            <div class="store-image-block">
                <img src="<?=  /* @noEscape */ $block->getStoreImage($storelocator->getStoreimage()); ?>"
                     alt="<?=  /* @noEscape */ $name ?>" />
            </div>
        </div>
        <div class="md-information-right-block">
            <h4><?=  /* @noEscape */ $name ?></h4>
            <ul id="store-view-list">
                <?php if ($address && $zipcode): ?>
                <li class="address">
                    <p><span><?= /* @noEscape */ __("City : "); ?></span><?= /* @noEscape */ $city ?></p>
                    <p><span><?= /* @noEscape */ __("Zip : "); ?></span><?= /* @noEscape */ $zipcode ?></p>
                    <p><span><?= /* @noEscape */ __("State : "); ?></span><?= /* @noEscape */ $region ?></p>
                    <p><span><?= /* @noEscape */ __("Address : "); ?></span><?= /* @noEscape */ $address ?> <?= /* @noEscape */ $country ?></p>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
    <div class="store-list-direction-panel"
         id="store-list-direction-panel-<?= /* @noEscape */ $storelocator->getStorelocatorId(); ?>">
    </div>
</div>


<?php /*
<div class="store-list-button" data-index="<?= $storelocator->getStorelocatorId(); ?>"
         data-long="<?= $storelocator->getLongitude(); ?>"
         data-lat="<?= $storelocator->getLatitude(); ?>">
        <button id="street-view" class="street-view-btn" type="button">
            <span><?=  __('Street View')?></span>
        </button>
        <button id="direction-view" class="direction-view-btn" type="button">
            <span><?=  __('Direction')?></span>
        </button>
</div>
*/?>
