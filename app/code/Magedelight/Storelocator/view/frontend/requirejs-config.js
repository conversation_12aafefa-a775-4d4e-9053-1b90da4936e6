/**
* @package Magedelight_Storelocator for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>abs. All Rights reserved.
*/

var config = {
    "map": {
        "*": {
            "mdMapWidget": "Magedelight_Storelocator/js/map",
            "chosen":"Magedelight_Storelocator/js/chosen.jquery",
            "fancybox":"Magedelight_Storelocator/js/fancybox"
        }
    },
    shim: { 
       'chosen': { 
           deps: ['jquery'] 
       },
       'fancybox': { 
           deps: ['jquery'] 
       },
    }
};
