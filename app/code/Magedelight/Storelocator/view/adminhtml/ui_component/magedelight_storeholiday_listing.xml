<?xml version="1.0" ?>
<!--
/**
* @package Magedelight_Storelocator for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">magedelight_storeholiday_listing.managestores_storeholiday_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>magedelight_storeholiday_columns</spinner>
        <deps>
            <dep>magedelight_storeholiday_listing.managestores_storeholiday_listing_data_source</dep>
        </deps>
        <buttons>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">Add Holiday</label>
            </button>
        </buttons>
    </settings>
    <dataSource component="Magento_Ui/js/grid/provider" name="managestores_storeholiday_listing_data_source">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">holiday_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <dataProvider class="MagedelightStoreHolidayFilterPool" name="managestores_storeholiday_listing_data_source">
            <settings>
                <requestFieldName>holiday_id</requestFieldName>
                <primaryFieldName>holiday_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">magedelight_storeholiday_listing.magedelight_storeholiday_listing.magedelight_storeholiday_columns.ids</item>
                    <item name="indexField" xsi:type="string">holiday_id</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/tree-massactions</item>
                </item>
            </argument>
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete selected items?</message>
                        <title translate="true">Delete items</title>
                    </confirm>
                    <url path="managestores/storeholiday/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
            <action name="disable">
                <settings>
                    <url path="managestores/storeholiday/massStatus">
                        <param name="is_active">0</param>
                    </url>
                    <type>disable</type>
                    <label translate="true">Disable</label>
                </settings>
            </action>
            <action name="enable">
                <settings>
                    <url path="managestores/storeholiday/massStatus">
                        <param name="is_active">1</param>
                    </url>
                    <type>enable</type>
                    <label translate="true">Enable</label>
                </settings>
            </action>
        </massaction>
    </listingToolbar>
    <columns name="magedelight_storeholiday_columns">
        <settings>
            <editorConfig>
                <param name="selectProvider" xsi:type="string">magedelight_storeholiday_listing.magedelight_storeholiday_listing.magedelight_storeholiday_columns.ids</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="indexField" xsi:type="string">holiday_id</param>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" path="managestores/storeholiday/inlineEdit" xsi:type="url"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">magedelight_storeholiday_listing.magedelight_storeholiday_listing.magedelight_storeholiday_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>holiday_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="holiday_id" sortOrder="10">
            <settings>
                <filter>text</filter>
                <sorting>asc</sorting>
                <label translate="true">ID</label>
            </settings>
        </column>
        <column name="holiday_name" sortOrder="30">
            <settings>
                <filter>text</filter>
                <label translate="true">Holiday Name</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
        <column name="is_active" component="Magento_Ui/js/grid/columns/select" sortOrder="40">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <options class="Magedelight\Storelocator\Ui\Component\Listing\Column\IsActive"/>
                <label translate="true">Status</label>
            </settings>
        </column>
        <column name="holiday_date_from" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date" sortOrder="50">
            <settings>
                <timezone>false</timezone>
                <dateFormat>MMM d, y</dateFormat>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Start From</label>
            </settings>
        </column>
        <column name="holiday_date_to" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date" sortOrder="60">
            <settings>
                <timezone>false</timezone>
                <dateFormat>MMM d, y</dateFormat>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">End To</label>
            </settings>
        </column>
        <actionsColumn class="Magedelight\Storelocator\Ui\Component\Listing\Column\StoreholidayActions" name="actions" sortOrder="70">
            <settings>
                <indexField>holiday_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>107</resizeDefaultWidth>
            </settings>
        </actionsColumn>
    </columns>
</listing>
