
.admin__menu .item-storelocator-root-commonlyvisible > a:before,
.admin__menu .item-storelocator-root.parent.level-0 .submenu > .submenu-title:before,
.config-nav-block .md_section_storelocator a:before {
    content: '';
    background-image: url('@{baseDir}Magedelight_Storelocator/images/store-locator-logo.svg');
    background-repeat: no-repeat;
    background-size: 50px;
    background-position-x:center;
    background-position-y: -13px;
    height: 32px;
    display: inline-block;
    padding: 0;
    width: 32px;
    margin-right: 5px;
    float: left;
}

.admin__menu .item-storelocator-root.parent.level-0 .submenu > .submenu-title:before {
    background-position-y: -13px;
}
.config-nav-block .md_section_storelocator a:before {
    background-position-y: -57px;
    margin: -5px 0 0;
}


.admin__fieldset > .admin__control-storetime-container > .admin__field-control {
    width: 98%;
}
.storeaddress-container {
    &.admin__control-fields,
    &[class*="admin__control-grouped"] {
        > .admin__field {
            &:first-child {
                > .admin__field-label {
                    display: none;
                }
            }
        }
    }
}

.admin__control-telephone-container .admin__control-table tr.data-row:first-child td .action-delete:last-child{
    display: none;
}
