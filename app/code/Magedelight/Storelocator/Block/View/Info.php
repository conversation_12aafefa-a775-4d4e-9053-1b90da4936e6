<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Block\View;

use Magedelight\Storelocator\Block\View as ParentView;
use Magedelight\Storelocator\Helper\Storelocator as Helper;
use Magedelight\Storelocator\Model\Storelocator;
use Magedelight\Storelocator\Model\StorelocatorFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\Storelocator\Api\StorelocatorRepositoryInterface;

class Info extends ParentView
{
    /** @var string */
    protected $_template = 'view/info.phtml';

    /** @var StorelocatorRepositoryInterface */
    private $storelocatorRepository;

    /**
     * @param Context $context
     * @param Storelocator $storeLocator
     * @param StorelocatorFactory $storelocatorFactory
     * @param StoreManagerInterface $storeManager
     * @param Helper $helper
     * @param Json $json
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     */
    public function __construct(
        Context $context,
        Storelocator $storeLocator,
        StorelocatorFactory $storelocatorFactory,
        StoreManagerInterface $storeManager,
        Helper $helper,
        Json $json,
        StorelocatorRepositoryInterface $storelocatorRepository
    ) {
        $this->storelocatorRepository = $storelocatorRepository;
        parent::__construct(
            $context,
            $storeLocator,
            $storelocatorFactory,
            $storeManager,
            $helper,
            $json,
            $storelocatorRepository
        );
    }

    /**
     * Get Country By Country Code.
     *
     * @param string $countryCode
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function getCountryByCode($countryCode)
    {
        return $this->helper->getCountryByCode($countryCode);
    }

    /**
     * Get Filter Output HTML.
     *
     * @param string $string
     * @return string|string[]
     */
    public function filterOutputHtml($string)
    {
        return str_replace('\n', ', ', $string ?? "");
    }

    /**
     * Get Store Image.
     *
     * @param  string $storeImage
     * @return string|null
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getStoreImage($storeImage)
    {
        return $this->helper->getStoreImage($storeImage);
    }
}
