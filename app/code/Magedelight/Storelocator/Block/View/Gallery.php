<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Block\View;

use Magedelight\Storelocator\Block\View as ParentView;
use Magedelight\Storelocator\Helper\Storelocator as Helper;
use Magedelight\Storelocator\Model\Storelocator;
use Magedelight\Storelocator\Model\StorelocatorFactory;
use Magedelight\Storelocator\Model\StorelocatorImagesFactory;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\Storelocator\Api\StorelocatorRepositoryInterface;


class Gallery extends ParentView
{
    /** @var StorelocatorImagesFactory */
    protected $mediaFactory;

    /** @var string */
    protected $_template = 'view/gallery.phtml';

    /** @var StorelocatorRepositoryInterface */
    private $storelocatorRepository;

    /**
     * @param Context $context
     * @param Storelocator $storeLocator
     * @param StorelocatorFactory $storelocatorFactory
     * @param StoreManagerInterface $storeManager
     * @param Helper $helper
     * @param Json $json
     * @param StorelocatorImagesFactory $mediaFactory
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     */
    public function __construct(
        Context $context,
        Storelocator $storeLocator,
        StorelocatorFactory $storelocatorFactory,
        StoreManagerInterface $storeManager,
        Helper $helper,
        Json $json,
        StorelocatorImagesFactory $mediaFactory,
        StorelocatorRepositoryInterface $storelocatorRepository
    ) {
        $this->mediaFactory = $mediaFactory;
        $this->storelocatorRepository = $storelocatorRepository;
        parent::__construct(
            $context,
            $storeLocator,
            $storelocatorFactory,
            $storeManager,
            $helper,
            $json,
            $storelocatorRepository
        );

        $this->addAsset();
    }

    /**
     * Get Image.
     */
    public function getImages()
    {
        $id = $this->getStoreLocator()->getId();
        return $this->mediaFactory->create()->getImages($id);
    }

    /**
     * Get Asset.
     */
    public function addAsset()
    {
        if ($this->isShown()) {
            $this->pageConfig->addPageAsset('Magedelight_Storelocator::css/fancybox.css');
            $this->pageConfig->addPageAsset('Magedelight_Storelocator::css/slick.css');
        }
    }
}
