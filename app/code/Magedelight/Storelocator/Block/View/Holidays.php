<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Block\View;

use Magedelight\Storelocator\Api\StoreInformationManagementInterface;
use Magedelight\Storelocator\Api\StorelocatorRepositoryInterface;
use Magedelight\Storelocator\Block\View as ParentView;
use Magedelight\Storelocator\Helper\Storelocator as Helper;
use Magedelight\Storelocator\Model\ResourceModel\Storeholiday\CollectionFactory as StoreholidayFactory;
use Magedelight\Storelocator\Model\Storelocator;
use Magedelight\Storelocator\Model\StorelocatorFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;

class Holidays extends ParentView
{
    /**
     * @var string
     */
    protected $_template = 'view/holidays.phtml';

    /**
     * @var array
     */
    private $hours = [];

    /**
     * @var string[]
     */
    private $days = [
        'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
    ];

    /**
     * @var DateTime
     */
    protected $date;

    /**
     * @var StoreholidayFactory
     */
    protected $storeholidayFactory;

    /**
     * @var StoreInformationManagementInterface
     */
    protected $storeInformationManagement;

    /**
     * @var TimezoneInterface
     */
    protected $timezone;

    /**
     * @var StorelocatorRepositoryInterface
     */
    private $storelocatorRepository;

    /**
     * @param Context $context
     * @param Storelocator $storeLocator
     * @param StorelocatorFactory $storeLocatorFactory
     * @param StoreManagerInterface $storeManager
     * @param Helper $helper
     * @param Json $json
     * @param StoreholidayFactory $storeHolidayFactory
     * @param StoreInformationManagementInterface $storeInformationManagement
     * @param TimezoneInterface $timezone
     * @param DateTime $date
     * @param StorelocatorRepositoryInterface $storeLocatorRepository
     */
    public function __construct(
        Context $context,
        Storelocator $storeLocator,
        StorelocatorFactory $storeLocatorFactory,
        StoreManagerInterface $storeManager,
        Helper $helper,
        Json $json,
        StoreholidayFactory $storeHolidayFactory,
        StoreInformationManagementInterface $storeInformationManagement,
        TimezoneInterface $timezone,
        DateTime $date,
        StorelocatorRepositoryInterface $storeLocatorRepository
    ) {
        $this->storeholidayFactory = $storeHolidayFactory;
        $this->storeInformationManagement = $storeInformationManagement;
        $this->timezone = $timezone;
        $this->date = $date;
        $this->storelocatorRepository = $storeLocatorRepository;
        parent::__construct(
            $context,
            $storeLocator,
            $storeLocatorFactory,
            $storeManager,
            $helper,
            $json,
            $storeLocatorRepository
        );
    }

    /**
     * Get Title Hours.
     *
     * @return array|string
     */
    public function getTitleHours()
    {
        $hours = $this->hours;
        $weekOfDay = $this->date->date('w');
        $currentDay = $this->days[$weekOfDay];
        if (!empty($hours)) {
            $new = array_filter($hours, function ($var) use ($currentDay) {
                return ($var['days'] == $currentDay && $var['day_status'] == 1);
            });
            if (empty($new)) {
                return sprintf("%s", $this->closeDayLabel());
            } else {
                $newArray = array_values($new);
                $openTime = $this->prepareOpentimeLabel($newArray[0]['open_hour'], $newArray[0]['open_minute']);
                $closeTime = $this->prepareOpentimeLabel($newArray[0]['close_hour'], $newArray[0]['close_minute']);
                return sprintf("%s - %s", $openTime, $closeTime);
            }
        }
        return sprintf("%s", $this->closeDayLabel());
    }

    /**
     * Get Store Hours.
     */
    public function getStoreHours()
    {
        $storelocator = $this->getStoreLocator();

        $hours = '';
        if ($storelocator && $time = $storelocator->getStoretime()) {
            $this->hours = $this->json->unserialize($time);
            $hours = $this->hours;
        }

        return $hours;
    }

    /**
     * Get Active Holiday Collection.
     *
     * @param bool $storeFilter
     * @return AbstractCollection|mixed
     * @throws NoSuchEntityException
     */
    public function getActiveHolidayCollection($storeFilter = false)
    {
        $collection = $this->storeholidayFactory->create()
            ->addFieldToFilter('is_active', '1');
        if ($storeFilter) {
            return $this->setStoreFilterWithHoliday($collection);
        }
        return $collection;
    }

    /**
     * Set store filter with holiday.
     *
     * @param mixed $collection
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function setStoreFilterWithHoliday($collection)
    {
        return $this->storeInformationManagement->getStoreFilter(
            $collection,
            $this->getStoreLocator()->getId()
        );
    }

    /**
     * Get Holidays.
     *
     * @return array
     * @throws NoSuchEntityException
     */
    public function getHolidays()
    {
        $holiday = [];
        $_holidaydays = $this->getActiveHolidayCollection(true);
        if ($_holidaydays) {
            foreach ($_holidaydays as $data) {
                $fromDate = date("jS F", strtotime($data['holiday_date_from'] . "+0 days"));
                $toDate = date("jS F", strtotime($data['holiday_date_to'] . "+0 days"));
                $holiday[] = $this->renderHolidayHtml($data['holiday_name'], $fromDate, $toDate);
            }
        }
        return $holiday;
    }

    /**
     * Render HTML.
     *
     * @param array $dayArray
     * @return string
     */
    public function renderHtml($dayArray = [])
    {
        $day = null;
        $hour = null;
        if ($dayArray && $dayArray) {
            $day = $this->prepareDayLabel($dayArray['days']);
            if ($dayArray['day_status']) {
                $openTime = $this->prepareOpentimeLabel($dayArray['open_hour'], $dayArray['open_minute']);
                $closeTime = $this->prepareOpentimeLabel($dayArray['close_hour'], $dayArray['close_minute']);
                $hour = sprintf('<span class="open-time">%s</span>', __($openTime.' - '.$closeTime));
            } else {
                $hour = $this->closeDayLabel();
            }
        }
        return sprintf("%s %s", __($day), __($hour));
    }

    /**
     * Render Label HTML.
     *
     * @param array $dayArray
     * @return string
     */
    public function renderLabelHtml($dayArray = [])
    {
        $hour = null;
        if ($dayArray && $dayArray) {
            if ($dayArray['day_status']) {
                $openTime = $this->prepareOpentimeLabel($dayArray['open_hour'], $dayArray['open_minute']);
                $closeTime = $this->prepareOpentimeLabel($dayArray['close_hour'], $dayArray['close_minute']);
                $hour = sprintf('<span class="open-time">%s</span>', __($openTime.' - '.$closeTime));
            } else {
                $hour = $this->closeDayLabel();
            }
        }
        return sprintf("%s", $hour);
    }

    /**
     * Prepare Day label.
     *
     * @param string $day
     * @return string
     */
    public function prepareDayLabel($day)
    {
        return sprintf('<span class="day" style="width:78px;display:inline-block;">%s</span>', __($day));
    }

    /**
     * Prepare Open Time Label.
     *
     * @param string $openHour
     * @param string $openMinute
     * @return string
     */
    public function prepareOpentimeLabel($openHour, $openMinute)
    {
        return $openHour.":".$openMinute;
    }

    /**
     * Prepare Close Time Label.
     *
     * @param string $closeHour
     * @param string $closeMinute
     * @return string
     */
    public function prepareClosetimeLabel($closeHour, $closeMinute)
    {
        return sprintf('<span class="close-time">%s:%s</span>', __($closeHour), __($closeMinute));
    }

    /**
     * Close Day Label.
     */
    public function closeDayLabel()
    {
        return sprintf('<span class="close-label">%s</span>', __("Closed"));
    }

    /**
     * Render Holyday HTML.
     *
     * @param string $holidayName
     * @param string $fromDate
     * @param string $toDate
     * @return string
     */
    public function renderHolidayHtml($holidayName, $fromDate, $toDate)
    {
        if ($fromDate == $toDate) {
            return sprintf(
                '<tr class="first odd"><th>%s</th><td><i>%s</i></td><td> %s</td></tr>',
                __($holidayName),
                __($fromDate),
                $this->closeDayLabel()
            );
        } else {
            return sprintf(
                '<tr class="first odd"><th>%s</th><td><i>%s</i> To <i>%s</i></td><td> %s</td></tr>',
                __($holidayName),
                __($fromDate),
                __($toDate),
                $this->closeDayLabel()
            );
        }
    }
}
