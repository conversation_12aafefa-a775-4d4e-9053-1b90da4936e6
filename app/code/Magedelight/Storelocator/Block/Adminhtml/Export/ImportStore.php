<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Block\Adminhtml\Export;

use Magento\Framework\Data\Form\Element\AbstractElement;

class ImportStore extends AbstractElement
{
    /**
     * Constructor.
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setType('file');
    }

    /**
     * Enter description here...
     *
     * @return string
     */
    public function getElementHtml()
    {
        $html = '';

        $html .= '<input id="time_condition" type="hidden" name="' . $this->getName() . '" value="' . time() . '" />';
        $html .= '<input id="store_sample_import" name="storeimport" data-ui-id="file-groups-sample-fields-import-value"
 value="" class="" type="file" />';
        return $html;
    }
}
