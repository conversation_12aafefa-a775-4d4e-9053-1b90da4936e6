<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Block\Adminhtml\Export;

use Exception;
use Magedelight\Storelocator\Model\StorelocatorFactory;
use Magento\Backend\Block\Template\Context;
use Magento\Backend\Block\Widget\Grid\Extended;
use Magento\Backend\Helper\Data;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Serialize\Serializer\Json;
use Magedelight\Storelocator\Model\ResourceModel\Storelocator\CollectionFactory;
use Magento\Directory\Model\RegionFactory;
use Magento\Directory\Model\CountryFactory;

class Grid extends Extended
{
    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var StorelocatorFactory
     */
    protected $storelocatorFactory;

    /**
     * @var Json
     */
    protected $serialize;

    /**
     * @var RegionFactory
     */
    protected $regionFactory;

    /**
     * @var CountryFactory
     */
    protected $countryFactory;

    /**
     * Grid constructor.
     *
     * @param Context $context
     * @param Data $backendHelper
     * @param CollectionFactory $collectionFactory
     * @param StorelocatorFactory $storelocatorFactory
     * @param Json $serialize
     * @param RegionFactory $regionFactory
     * @param CountryFactory $countryFactory
     * @param array $data
     */
    public function __construct(
        Context $context,
        Data $backendHelper,
        CollectionFactory $collectionFactory,
        StorelocatorFactory $storelocatorFactory,
        Json $serialize,
        RegionFactory $regionFactory,
        CountryFactory $countryFactory,
        array $data = []
    ) {
        $this->collectionFactory = $collectionFactory;
        $this->storelocatorFactory = $storelocatorFactory;
        $this->serialize = $serialize;
        $this->regionFactory = $regionFactory;
        $this->countryFactory = $countryFactory;
        parent::__construct($context, $backendHelper, $data);
    }

    /**
     * Define grid properties
     *
     * @return void
     * @throws FileSystemException
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('storeGrid');
        $this->_exportPageSize = 10000;
    }

    /**
     * Prepare Collection.
     *
     * @return Extended
     */
    protected function _prepareCollection()
    {
        /** @var $collection CollectionFactory */
        $collection = $this->collectionFactory->create();

        $collection->getSelect()->join(
            $collection->getResource()->getTable('magedelight_storelocator_store') . ' as stores',
            'main_table.storelocator_id = stores.storelocator_id',
            ['store_ids'=> new \Zend_Db_Expr('GROUP_CONCAT(stores.store_ids)')]
        )->group('stores.storelocator_id');

        $this->setCollection($collection);

        return parent::_prepareCollection();
    }

    /**
     * Prepare table columns
     *
     * @return Extended
     * @throws Exception
     */
    protected function _prepareColumns()
    {
        $this->addColumn(
            'storelocator_id',
            ['header' => __('Storelocator Id'), 'index' => 'storelocator_id', 'default' => '']
        );

        $this->addColumn(
            'storename',
            ['header' => __('Store Name'), 'index' => 'storename', 'default' => '']
        );

        $this->addColumn(
            'url_key',
            ['header' => __('URL Key'), 'index' => 'url_key', 'default' => '']
        );

        $this->addColumn(
            'description',
            ['header' => __('Description'), 'index' => 'description', 'default' => '']
        );

        $this->addColumn(
            'website_url',
            ['header' => __('Website Url'), 'index' => 'website_url', 'default' => '']
        );

        $this->addColumn(
            'facebook_url',
            ['header' => __('Facebook Url'), 'index' => 'facebook_url', 'default' => '']
        );

        $this->addColumn(
            'twitter_url',
            ['header' => __('Twitter Url'), 'index' => 'twitter_url', 'default' => '']
        );

        $this->addColumn(
            'is_active',
            ['header' => __('Status'), 'index' => 'is_active', 'default' => '']
        );

        $this->addColumn(
            'address',
            ['header' => __('Address'), 'index' => 'address', 'default' => '']
        );

        $this->addColumn(
            'city',
            ['header' => __('City'), 'index' => 'city', 'default' => '']
        );

        $this->addColumn(
            'region',
            ['header' => __('State'), 'index' => 'region', 'default' => '']
        );

        $this->addColumn(
            'country_id',
            ['header' => __('Country'), 'index' => 'country_id', 'default' => '']
        );

        $this->addColumn(
            'zipcode',
            ['header' => __('Zipcode'), 'index' => 'zipcode', 'default' => '']
        );

        $this->addColumn(
            'longitude',
            ['header' => __('Longitude'), 'index' => 'longitude', 'default' => '']
        );

        $this->addColumn(
            'latitude',
            ['header' => __('Latitude'), 'index' => 'latitude', 'default' => '']
        );

        $this->addColumn(
            'phone_frontend_status',
            ['header' => __('Phone Frontend Status'), 'index' => 'phone_frontend_status', 'default' => '']
        );

        $this->addColumn(
            'telephone',
            ['header' => __('Telephone'), 'index' => 'telephone', 'default' => '']
        );

        $this->addColumn(
            'storetime',
            ['header' => __('Store Time'), 'index' => 'storetime', 'default' => '']
        );

        $this->addColumn(
            'meta_title',
            ['header' => __('Meta Title'), 'index' => 'meta_title', 'default' => '']
        );

        $this->addColumn(
            'meta_keywords',
            ['header' => __('Meta Keywords'), 'index' => 'meta_keywords', 'default' => '']
        );

        $this->addColumn(
            'meta_description',
            ['header' => __('Meta Description'), 'index' => 'meta_description', 'default' => '']
        );

        $this->addColumn(
            'store_ids',
            ['header' => __('Store Id'), 'index' => 'store_ids', 'default' => '']
        );

        $this->addColumn(
            'storeemail',
            ['header' => __('Store Email'), 'index' => 'storeemail', 'default' => '']
        );

        return parent::_prepareColumns();
    }

    /**
     *  Export Iterate Collection.
     *
     * @param string $callback
     * @param array $args
     */
    public function _exportIterateCollection($callback, array $args)
    {
        $originalCollection = $this->getCollection();
        $count = null;
        $page = 1;
        $lPage = null;
        $break = false;

        while ($break !== true) {
            $collection = clone $originalCollection;
            $collection->setPageSize($this->_exportPageSize);
            $collection->setCurPage($page);
            $collection->load();
            if ($count === null) {
                $count = $collection->getSize();
                $lPage = $collection->getLastPageNumber();
            }
            if ($lPage == $page) {
                $break = true;
            }
            $page++;

            foreach ($collection as $item) {
                $item['store_ids'] = implode(', ', $item['store_ids']);
                $itemDescription = ($item['description']) ? $item['description'] : '';
                $item['description'] = strip_tags((string)htmlspecialchars_decode($itemDescription));

                if (!empty($item['storetime'])) {
                    $item['storetime'] = $this->unserializeData($item['storetime']);
                    $storeTime = $item['storetime'];
                    $modifiedTime = [];
                    if (!empty($storeTime)) {
                        foreach ($storeTime as $time) {
                            $days = ($time['days']) ? $time['days'] : '';
                            $dayStatus = ($time['day_status']) ? $time['day_status'] : '';
                            $openHour = ($time['open_hour']) ? $time['open_hour'] : '';
                            $openMinute = ($time['open_minute']) ? $time['open_minute'] : '0';
                            $closeHour = ($time['close_hour']) ? $time['close_hour'] : '';
                            $closeMinute = ($time['close_minute']) ? $time['close_minute'] : '0';
                            $modifiedTime[] = 'days:' . $days .
                                ';day_status:' . $dayStatus .
                                ';open_hour:' . $openHour .
                                ';open_minute:' . $openMinute .
                                ';close_hour:' . $closeHour .
                                ';close_minute:' . $closeMinute;
                        }
                    }
                    $item['storetime'] = (!empty($modifiedTime)) ? implode('|', $modifiedTime) : '';
                }

                if (empty($item['region'])) {
                    $region = $this->regionFactory->create()->load($item['region_id'])->getName();
                    $item['region'] = $region;
                }

                if (!empty($item['country_id'])) {
                    $country = $this->countryFactory->create()->loadByCode($item['country_id'])->getName();
                    $item['country_id'] = $country;
                }
                $this->{$callback}($item, ...$args);
//                call_user_func_array([$this, $callback], array_merge([$item], $args));
            }
        }
    }

    /**
     * Get UnSerialize Data.
     *
     * @param string $string
     * @return mixed
     */
    public function unserializeData($string)
    {
        $result = json_decode($string, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
//            if (false !== @unserialize($string)) {
//                return unserialize($string);
//            }
            if (is_string($string) && !empty($string)) {
                return $this->serializer->unserialize($string);
            }
            throw new \InvalidArgumentException('Unable to unserialize value.');
        }
        return $result;
    }
}
