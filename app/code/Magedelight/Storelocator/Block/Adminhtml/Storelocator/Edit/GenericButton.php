<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Block\Adminhtml\Storelocator\Edit;

use Magento\Backend\Block\Widget\Context;

abstract class GenericButton
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @param Context $context
     */
    public function __construct(Context $context)
    {
        $this->context = $context;
    }

    /**
     * Return model ID
     *
     * @return int|null
     */
    public function getModelId()
    {
        return $this->context->getRequest()->getParam('storelocator_id');
    }

    /**
     * Generate url by route and parameters
     *
     * @param string $route
     * @param array $params
     * @return string
     */
    public function getUrl($route = '', $params = [])
    {
        return $this->context->getUrlBuilder()->getUrl($route, $params);
    }
}
