<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Block;

use Magedelight\Storelocator\Api\StorelocatorRepositoryInterface;
use Magedelight\Storelocator\Model\Storelocator;
use Magedelight\Storelocator\Model\StorelocatorFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\ValidatorException;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\Storelocator\Helper\Storelocator as Helper;
use Magento\Framework\Serialize\Serializer\Json;


class View extends Template
{
    /**
     * @var StorelocatorFactory
     */
    protected $storeLocatorFactory;

    /**
     * @var Storelocator
     */
    protected $storeLocator;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var Helper
     */
    protected $helper;

    /**
     * @var Json
     */
    protected $json;

    /**
     * @var StorelocatorRepositoryInterface
     */
    private $storelocatorRepository;


    /**
     * View constructor.
     * @param Context $context
     * @param Storelocator $storeLocator
     * @param StorelocatorFactory $storelocatorFactory
     * @param StoreManagerInterface $storeManager
     * @param Helper $helper
     * @param Json $json
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     */
    public function __construct(
        Context $context,
        Storelocator $storeLocator,
        StorelocatorFactory $storelocatorFactory,
        StoreManagerInterface $storeManager,
        Helper $helper,
        Json $json,
        StorelocatorRepositoryInterface $storelocatorRepository
    ) {
        $this->storeLocator = $storeLocator;
        $this->storeLocatorFactory = $storelocatorFactory;
        $this->storeManager = $storeManager;
        $this->helper = $helper;
        $this->json = $json;
        $this->storelocatorRepository = $storelocatorRepository;
        parent::__construct($context);
    }

    /**
     * Prepare Layout.
     */
    protected function _prepareLayout()
    {
        $locator = $this->getStoreLocator();

        $this->setMetaContent($locator);
        $this->setBreadcrumb($locator);
    }

    /**
     * Retrieve Store Locator instance
     *
     * @return Storelocator
     * @throws LocalizedException
     */
    public function getStoreLocator()
    {
        if (!$this->hasData('storelocator')) {
            if ($this->getStorelocatorId()) {
                /** @var Storelocator */
                $storelocator = $this->storelocatorRepository->getById($this->getStorelocatorId());
            } else {
                $storelocator = $this->storeLocator;
            }
            $this->setData('storelocator', $storelocator);
        }
        return $this->getData('storelocator');
    }

    /**
     * Set Meta Content.
     *
     * @param mixed $locator
     * @return View
     * @throws LocalizedException
     */
    protected function setMetaContent($locator)
    {
        $storeTitle = $locator->getStorename();
        $metaTitle = $locator->getMetaTitle() ?? $storeTitle;
        $metaDescription = $locator->getMetaDescription() ??
            substr($this->stripTags($locator->getDescription()), 0, 255);
        $keywords = $locator->getMetaKeywords();

        $this->pageConfig->getTitle()->set($metaTitle);
        //$this->pageConfig->setMetaTitle($metaTitle);
        $this->pageConfig->setDescription($metaDescription);
        $this->pageConfig->setKeywords($keywords);

        if ($pageMainTitle = $this->getLayout()->getBlock('page.main.title')) {
            $pageMainTitle->setPageTitle($storeTitle);
        }

        return $this;
    }

    /**
     * Set Bread Crumb.
     *
     * @param mixed $locator
     * @throws LocalizedException
     */
    protected function setBreadcrumb($locator)
    {
        $locatorUrl = $this->helper->getListingPageUrl();
        $locatorTitle = $this->helper->getMetaTitle();
        $storeTitle = $locator->getStorename();

        if ($breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb(
                'storelocator',
                [
                    'label' => $locatorTitle,
                    'title' => $locatorTitle,
                    'link' => $locatorUrl
                ]
            )->addCrumb(
                'storelocator-view',
                [
                    'label' => __($storeTitle),
                    'title' => __($storeTitle)
                ]
            );
        }
    }

    /**
     * Show Particular Hide/Block
     *
     * Implemented for default _toHtml() render
     *
     * @return bool
     */
    protected function isShown()
    {
        return true;
    }

    /**
     * To HTML.
     *
     * @return string|null
     * @throws ValidatorException
     */
    protected function _toHtml()
    {
        if (!$this->isShown()) {
            return null;
        }

        if (!$this->getTemplate()) {
            return '';
        }
        return $this->fetchView($this->getTemplateFile());
    }

    /**
     * Get Base URL.
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getBaseUrl()
    {
        return $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_WEB);
    }

    /**
     * Get Store Ajax URL.
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getStoreAjaxUrl()
    {
        return $this->getUrl(
            'storelocator/index/ajax',
            [
                '_secure' => $this->getRequest()->isSecure(),
                'storelocator_id' => $this->getStoreLocator()->getId(),
            ]
        );
    }

    /**
     * Get Store Direction Template.
     */
    public function getStoreDirectionTemplate()
    {
        $store = $this->getStoreLocator();
        $regionName = '';
        $countryName = '';

        if ($store->getRegionId() !== null) {
            $regionName = $this->helper->getRegionById($store->getRegionId());
        }
        if ($store->getRegion() !== null) {
            $regionName = $store->getRegion();
        }
        if ($store->getCountryId() !== null) {
            $countryId = $store->getCountryId();
            $countryName = $this->helper->getCountryByCode($countryId);
        }

        $streetAddress = $store->getAddress();

        $pos = strpos($streetAddress, '\n');
        if ($pos !== false && substr($streetAddress, $pos + 1) !== 'n') {
            $streetAddress = str_replace('\n', ', ', $streetAddress);
        } else {
            $streetAddress = str_replace('\n', '', $streetAddress);
        }

        $currentAddress = $streetAddress.', '.$store->getCity().', '. $store->getZipcode().', '.$regionName.', '.$countryName;

        return $this->setTemplate('Magedelight_Storelocator::view/direction.phtml')->setCurrentAddress($currentAddress)->setStore($store->getData())->toHtml();
    }

    /**
     * Get Store Direction Template.
     */
    public function getStoreOtherInfoTemplate()
    {
        $store = $this->getStoreLocator();
        return $this->setTemplate('Magedelight_Storelocator::view/storeotherinfo.phtml')->setStoreLocator($store)->toHtml();
    }

    /**
     * Get Map API.
     */
    public function getMapApi()
    {
        return $this->helper->getMapConfig();
    }

    /**
     * Get Store List Template.
     */
    public function getStoreListTemplate()
    {
        //return $this->setTemplate('Magedelight_Storelocator::storestest.phtml')->toHtml();
        return $this->setTemplate('Magedelight_Storelocator::stores.phtml')->toHtml();
    }

    /**
     * Get Marker Image.
     */
    public function getMarkerImage()
    {
        return $this->helper->getMarkerImage();
    }

    /**
     * Get Map Style.
     *
     * @return mixed
     */
    public function getMapStyle()
    {
        $mapStyle = $this->helper->getMapStyle();
        return $this->json->serialize($this->helper->getMapStyleTheme($mapStyle));
    }

    /**
     * Get Store Locator Id.
     */
    public function getStorelocatorId()
    {
        return $this->_request->getParam('id');
    }

     /**
     * Get Map API.
     *
     * @return mixed
     */
    public function getCountry()
    {
        return $this->helper->getCountryConfig();
    }
}
