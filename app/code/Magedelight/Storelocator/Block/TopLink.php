<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Block;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Phrase;
use Magento\Framework\View\Element\Html\Link;
use Magento\Framework\View\Element\Template;
use Magedelight\Storelocator\Helper\Storelocator;
use Magento\Store\Model\StoreManagerInterface;

class TopLink extends Link
{
    /**
     * @var string
     */
    protected $_template = 'Magedelight_Storelocator::topmenulink.phtml';

    /**
     * @var Storelocator
     */
    protected $storeHelper;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @param Template\Context $context
     * @param Storelocator $storeHelper
     * @param StoreManagerInterface $storeManager
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        Storelocator $storeHelper,
        StoreManagerInterface $storeManager,
        array $data = []
    ) {
        $this->storeHelper = $storeHelper;
        $this->storeManager = $storeManager;
        parent::__construct($context, $data);
    }

    /**
     * Get href.
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getHref()
    {
        $urlKey = $this->storeHelper->getStoreFrontendUrl();
        if (empty($urlKey)) {
            $urlKey = 'storelocator';
        }
        $suffix = $this->storeHelper->getStorePageUrlSuffix();
        // $urlKey .= (strlen($suffix ?? '') > 0 || $suffix != '') ? '.' . str_replace('.', '', $suffix) : '/';
        $urlKey .= (strlen($suffix ?? '') > 0 || $suffix != '') ? '.' . str_replace('.', '', $suffix ?? '') : '/';
        return $this->storeManager->getStore()->getBaseUrl() . $urlKey;
    }

    /**
     * Get Menu Link Label.
     *
     * @return Phrase
     */
    public function getMenuLinkLabel()
    {
        $label = $this->storeHelper->getMenuLinkTitle();
        if (empty($label)) {
            $label = 'storelocator';
        }
        return __($label);
    }

    /**
     * Get Module Status.
     *
     * @return bool
     */
    public function isModuleEnable()
    {
        return $this->storeHelper->isModuleEnable();
    }

    /**
     * Top Link Display.
     *
     * @return bool
     */
    public function isTopLinkDisplay()
    {
        return $this->storeHelper->getDisplayMenuLink() == 'toplink' ? true : false;
    }
}
