<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model;

use Magedelight\Storelocator\Api\Data\StorelocatorInterface;
use Magedelight\Storelocator\Model\ResourceModel\Storelocator\CollectionFactory;
use Magento\CatalogRule\Model\Rule\Action\Collection;
use Magento\CatalogRule\Model\Rule\Condition\CombineFactory;
use Magento\Catalog\Model\ProductFactory;
use Magento\Framework\Api\AttributeValueFactory;
use Magento\Framework\Api\ExtensionAttributesFactory;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Data\FormFactory;
use Magento\Framework\Model\Context;
use Magento\Framework\Model\ResourceModel\AbstractResource;
use Magento\Framework\Registry;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Quote\Model\Quote\Address;
use Magento\Rule\Model\AbstractModel;
use Magento\CatalogRule\Model\Rule\Action\CollectionFactory as RuleCollectionFactory;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Magento\Framework\Model\ResourceModel\Iterator;
use Magento\Rule\Model\Condition\Combine;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\Storelocator\Api\Data\StorelocatorInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Store\Model\Store;

class Storelocator extends AbstractModel
{
    /**
     * @var RuleCollectionFactory
     */
    private $actionCollectionFactory;

    /**
     * @var CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var string
     */
    protected $_eventPrefix = 'storelocator_rules';

    /**
     * @var string
     */
    protected $_eventObject = 'rule';

    /**
     * @var array
     */
    protected $productIds;

    /**
     * @var CombineFactory
     */
    protected $condCombineFactory;

    /**
     * @var array
     */
    protected $validatedAddresses = [];

    /**
     * @var ProductCollectionFactory
     */
    protected $productCollectionFactory;

    /**
     * @var int|array|null
     */
    protected $productsFilter = null;

    /**
     * @var Iterator
     */
    protected $resourceIterator;

    /**
     * @var ProductFactory
     */
    protected $productFactory;

    /**
     * @var array
     */
    private $websitesMap;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var StorelocatorInterfaceFactory
     */
    protected $storelocatorDataFactory;

    /**
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * Storelocator constructor.
     *
     * @param Context $context
     * @param Registry $registry
     * @param FormFactory $formFactory
     * @param TimezoneInterface $localeDate
     * @param StoreManagerInterface $storeManager
     * @param CombineFactory $condCombineFactory
     * @param RuleCollectionFactory $actionCollectionFactory
     * @param ResourceModel\Storelocator\CollectionFactory $collectionFactory
     * @param ProductCollectionFactory $productCollectionFactory
     * @param ProductFactory $productFactory
     * @param Iterator $resourceIterator
     * @param StorelocatorInterfaceFactory $storelocatorDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param AbstractResource|null $resource
     * @param AbstractDb|null $resourceCollection
     * @param array $data
     * @param ExtensionAttributesFactory|null $extensionFactory
     * @param AttributeValueFactory|null $customAttributeFactory
     * @param Json|null $serializer
     *
     * suppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        Context $context,
        Registry $registry,
        FormFactory $formFactory,
        TimezoneInterface $localeDate,
        StoreManagerInterface $storeManager,
        CombineFactory $condCombineFactory,
        RuleCollectionFactory $actionCollectionFactory,
        CollectionFactory $collectionFactory,
        ProductCollectionFactory $productCollectionFactory,
        ProductFactory $productFactory,
        Iterator $resourceIterator,
        StorelocatorInterfaceFactory $storelocatorDataFactory,
        DataObjectHelper $dataObjectHelper,
        AbstractResource $resource = null,
        AbstractDb $resourceCollection = null,
        array $data = [],
        ExtensionAttributesFactory $extensionFactory = null,
        AttributeValueFactory $customAttributeFactory = null,
        Json $serializer = null
    ) {
        $this->storeManager = $storeManager;
        $this->condCombineFactory = $condCombineFactory;
        $this->collectionFactory = $collectionFactory;
        $this->actionCollectionFactory = $actionCollectionFactory;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->productFactory = $productFactory;
        $this->resourceIterator = $resourceIterator;
        $this->storelocatorDataFactory = $storelocatorDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        parent::__construct(
            $context,
            $registry,
            $formFactory,
            $localeDate,
            $resource,
            $resourceCollection,
            $data,
            $extensionFactory,
            $customAttributeFactory
        );
    }

    /**
     * Set resource model and Id field name
     *
     * @return void
     */
    protected function _construct()
    {
        parent::_construct();
        $this->_init(ResourceModel\Storelocator::class);
        $this->setIdFieldName('storelocator_id');
    }

    /**
     * Retrieve storelocator model with storelocator data
     *
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function getDataModel()
    {
        $storeholidayData = $this->getData();

        $storelocatorDataObject = $this->storelocatorDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $storelocatorDataObject,
            $storeholidayData,
            StorelocatorInterface::class
        );

        return $storelocatorDataObject;
    }

    /**
     * Getter for rule conditions collection
     *
     * @return Combine
     */
    public function getConditionsInstance()
    {
        return $this->condCombineFactory->create();
    }

    /**
     * Getter for rule actions collection
     *
     * @return Collection
     */
    public function getActionsInstance()
    {
        return $this->actionCollectionFactory->create();
    }

    /**
     * Check cached validation result for specific address
     *
     * @param Address $address
     * @return bool
     */
    public function hasIsValidForAddress($address)
    {
        $addressId = $this->_getAddressId($address);
        return isset($this->validatedAddresses[$addressId]) ? true : false;
    }

    /**
     * Set validation result for specific address to results cache
     *
     * @param Address $address
     * @param bool $validationResult
     * @return $this
     */
    public function setIsValidForAddress($address, $validationResult)
    {
        $addressId = $this->_getAddressId($address);
        $this->validatedAddresses[$addressId] = $validationResult;
        return $this;
    }

    /**
     * Get cached validation result for specific address
     *
     * @param Address $address
     * @return bool
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function getIsValidForAddress($address)
    {
        $addressId = $this->_getAddressId($address);
        return isset($this->validatedAddresses[$addressId]) ? $this->validatedAddresses[$addressId] : false;
    }

    /**
     * Return id for address
     *
     * @param Address $address
     * @return string
     */
    private function _getAddressId($address)
    {
        if ($address instanceof Address) {
            return $address->getId();
        }
        return $address;
    }

    /**
     * Get conditions field set id.
     *
     * @param string $formName
     * @return string
     */
    public function getConditionsFieldSetId($formName = '')
    {
        return $formName . 'rule_conditions_fieldset_' . $this->getId();
    }

    /**
     * Get matching product ids.
     *
     * @return array|null
     */
    public function getMatchingProductIds()
    {
        if ($this->productIds === null) {
            $this->productIds = [];
            $this->setCollectedAttributes([]);

            $productCollection = $this->productCollectionFactory->create();
            if ($this->productsFilter) {
                $productCollection->addIdFilter($this->productsFilter);
            }
            $this->getConditions()->collectValidatedAttributes($productCollection);

            $this->resourceIterator->walk(
                $productCollection->getSelect(),
                [[$this, 'callbackValidateProduct']],
                [
                    'attributes' => $this->getCollectedAttributes(),
                    'product' => $this->productFactory->create()
                ]
            );
        }

        return $this->productIds;
    }

    /**
     * Get all validate product ids.
     *
     * @return array
     */
    public function getAllValidateProductIds()
    {
        return array_keys($this->getMatchingProductIds());
    }

    /**
     * Check if we can use mapping for rule conditions
     *
     * @return bool
     */
    private function canPreMapProducts()
    {
        $conditions = $this->getConditions();

        // No need to map products if there is no conditions in rule
        if (!$conditions || !$conditions->getConditions()) {
            return false;
        }

        return true;
    }

    /**
     * Filtering products that must be checked for matching with rule
     *
     * @param  int|array $productIds
     * @return void
     * @codeCoverageIgnore
     */
    public function setProductsFilter($productIds)
    {
        $this->productsFilter = $productIds;
    }

    /**
     * Returns products filter
     *
     * @return array|int|null
     * @codeCoverageIgnore
     */
    public function getProductsFilter()
    {
        return $this->productsFilter;
    }

    /**
     * Call back validate product.
     *
     * @param array $args
     */
    public function callbackValidateProduct($args)
    {
        $product = clone $args['product'];
        $product->setData($args['row']);

        $websites = $this->_getWebsitesMap();
        $results = [];

        foreach ($websites as $websiteId => $defaultStoreId) {
            $product->setStoreId($defaultStoreId);
            $results[$websiteId] = $this->getConditions()->validate($product);
        }
        $this->productIds[$product->getId()] = $results;
    }

    /**
     * Get websites map.
     *
     * @return array|null
     */
    protected function _getWebsitesMap()
    {
        if ($this->websitesMap === null) {
            $this->websitesMap = [];
            $websites = $this->storeManager->getWebsites();
            foreach ($websites as $website) {
                // Continue if website has no store to be able to create catalog rule for website without store
                if ($website->getDefaultStore() === null) {
                    continue;
                }
                $this->websitesMap[$website->getId()] = $website->getDefaultStore()->getId();
            }
        }

        return $this->websitesMap;
    }

    /**
     * Checked Identifier.
     *
     * @param string $urlKey
     * @param int|string $storeId
     * @return mixed
     */
    public function checkIdentifier($urlKey, $storeId)
    {
        $collection = $this->collectionFactory->create()
                    ->addFieldToFilter('is_active', 1);
        $storeIds = [
            Store::DEFAULT_STORE_ID,
            (int) $storeId
        ];

        $collection->getSelect()->join(
            [
                'store' => $collection->getResource()->getTable('magedelight_storelocator_store')
            ],
            'main_table.storelocator_id = store.storelocator_id',
            []
        );

        $collection->addFieldToFilter('main_table.url_key', $urlKey);
        $collection->addFieldToFilter('store.store_ids', ['in'=>$storeIds]);

        if ($collection->getSize() > 1) {
            return $collection->getData()[0]['storelocator_id'];
        }
        return $collection->getFirstItem()->getId();
    }
}
