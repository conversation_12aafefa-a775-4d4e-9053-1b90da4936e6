<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Data;

use Magedelight\Storelocator\Api\Data\StoreholidayInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

class Storeholiday extends AbstractExtensibleModel implements StoreholidayInterface
{
    /**
     * Get holiday_id
     *
     * @return string|null
     */
    public function getHolidayId()
    {
        return $this->_get(self::HOLIDAY_ID);
    }

    /**
     * Set holiday_id
     *
     * @param string $holidayId
     * @return StoreholidayInterface
     */
    public function setHolidayId($holidayId)
    {
        return $this->setData(self::HOLIDAY_ID, $holidayId);
    }

    /**
     * Get holiday_name
     *
     * @return string|null
     */
    public function getHolidayName()
    {
        return $this->_get(self::HOLIDAY_NAME);
    }

    /**
     * Set holiday_name
     *
     * @param string $holidayName
     * @return StoreholidayInterface
     */
    public function setHolidayName($holidayName)
    {
        return $this->setData(self::HOLIDAY_NAME, $holidayName);
    }

    /**
     * Get is_active
     *
     * @return string|null
     */
    public function getIsActive()
    {
        return $this->_get(self::IS_ACTIVE);
    }

    /**
     * Set is_active
     *
     * @param string $isActive
     * @return StoreholidayInterface
     */
    public function setIsActive($isActive)
    {
        return $this->setData(self::IS_ACTIVE, $isActive);
    }

    /**
     * Get all_store
     *
     * @return string|null
     */
    public function getAllStore()
    {
        return $this->_get(self::ALL_STORE);
    }

    /**
     * Set all_store
     *
     * @param string $allStore
     * @return StoreholidayInterface
     */
    public function setAllStore($allStore)
    {
        return $this->setData(self::ALL_STORE, $allStore);
    }

    /**
     * Get holiday_applied_stores
     *
     * @return string[]
     */
    public function getHolidayAppliedStores()
    {
        return $this->_get(self::HOLIDAY_APPLIED_STORE);
    }

    /**
     * Set holiday_applied_stores
     *
     * @param string[] $holidayAppliedStores
     * @return StoreholidayInterface
     */
    public function setHolidayAppliedStores($holidayAppliedStores)
    {
        return $this->setData(self::HOLIDAY_APPLIED_STORE, $holidayAppliedStores);
    }

    /**
     * Get holiday_date_from
     *
     * @return string|null
     */
    public function getHolidayDateFrom()
    {
        return $this->_get(self::HOLIDAY_DATE_FROM);
    }

    /**
     * Set holiday_date_from
     *
     * @param string $holidayDateFrom
     * @return StoreholidayInterface
     */
    public function setHolidayDateFrom($holidayDateFrom)
    {
        return $this->setData(self::HOLIDAY_DATE_FROM, $holidayDateFrom);
    }

    /**
     * Get holiday_date_to
     *
     * @return string|null
     */
    public function getHolidayDateTo()
    {
        return $this->_get(self::HOLIDAY_DATE_TO);
    }

    /**
     * Set holiday_date_to
     *
     * @param string $holidayDateTo
     * @return StoreholidayInterface
     */
    public function setHolidayDateTo($holidayDateTo)
    {
        return $this->setData(self::HOLIDAY_DATE_TO, $holidayDateTo);
    }

    /**
     * Get holiday_comment
     *
     * @return string|null
     */
    public function getHolidayComment()
    {
        return $this->_get(self::HOLIDAY_COMMENT);
    }

    /**
     * Set holiday_comment
     *
     * @param string $holidayComment
     * @return StoreholidayInterface
     */
    public function setHolidayComment($holidayComment)
    {
        return $this->setData(self::HOLIDAY_COMMENT, $holidayComment);
    }

    /**
     * Get is_repetitive
     *
     * @return string|null
     */
    public function getIsRepetitive()
    {
        return $this->_get(self::IS_REPETITIVE);
    }

    /**
     * Set is_repetitive
     *
     * @param string $isRepetitive
     * @return StoreholidayInterface
     */
    public function setIsRepetitive($isRepetitive)
    {
        return $this->setData(self::IS_REPETITIVE, $isRepetitive);
    }
}
