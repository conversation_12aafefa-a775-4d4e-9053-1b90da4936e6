<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model;

use Exception;
use Magedelight\Storelocator\Api\Data\StorelocatorInterface;
use Magedelight\Storelocator\Api\StoreInformationManagementInterface;
use Magedelight\Storelocator\Model\Source\Country;
use Magedelight\Storelocator\Model\Source\Region;
use Magento\Checkout\Model\Session;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Model\Quote\Item;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\Storelocator\Api\StorelocatorRepositoryInterface;
use Magedelight\Storelocator\Api\Data\StorelocatorInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magedelight\Storelocator\Helper\Storelocator as storeHelper;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magedelight\Storelocator\Model\ResourceModel\TagAttributeValue\CollectionFactory;
use Magedelight\Storelocator\Model\ResourceModel\Storelocator\CollectionFactory as StorelocatorCollectionFactory;

/**
 * Payment information management
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class StoreInformationManagement implements StoreInformationManagementInterface
{
    public const XML_PATH_EMAIL_RECIPIENT = 'contact/email/recipient_email';

    /**
     * @var StorelocatorFactory
     */
    protected $storelocatorFactory;

    /**
     * @var $storeManager
     */
    protected $storeManager;

    /**
     * @var StorelocatorRepository
     */
    protected $storelocatorRepository;

    /**
     * @var Country
     */
    protected $countryOptions;

    /**
     * @var Region
     */
    protected $regionOptions;

    /**
     * @var StorelocatorInterfaceFactory
     */
    protected $storelocatorDataFactory;

    /**
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @var StoreholidayFactory
     */
    protected $storeholidayFactory;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var Json
     */
    protected $serialize;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var storeHelper
     */
    protected $storeHelper;

    /**
     * @var CollectionFactory
     */
    protected $tagValueCollectionFactory;

    /**
     * @var TagsAttributesFactory
     */
    protected $TagAttributeFactory;

    /**
     * @var tagAttributeOptionFactory
     */
    protected $tagAttributeOptionFactory;

    /**
     * @var TagAttributeFactory
     */
    protected $tagAttributeFactory;

    /**
     * @var \Magento\Directory\Model\ResourceModel\Region\CollectionFactory
     */
    protected $regionCollectionFactory;

    /**
     * @var Session
     */
    protected $checkoutSession;

    /**
     * StoreInformationManagement constructor.
     *
     * @param StorelocatorCollectionFactory $storelocatorFactory
     * @param StoreManagerInterface $storeManager
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     * @param Country $countryOptions
     * @param Region $regionOptions
     * @param StorelocatorInterfaceFactory $storelocatorDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param StoreholidayFactory $storeholidayFactory
     * @param ScopeConfigInterface $scopeConfig
     * @param Json $serialize
     * @param DateTime $dateTime
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param storeHelper $storeHelper
     * @param CollectionFactory $tagValueCollectionFactory
     * @param TagAttributeFactory $TagAttributeFactory
     * @param TagAttributeOptionFactory $TagAttributeOptionFactory
     * @param Session $checkoutSession
     * @param \Magento\Directory\Model\ResourceModel\Region\CollectionFactory $regionCollectionFactory
     */
    public function __construct(
        StorelocatorCollectionFactory $storelocatorFactory,
        StoreManagerInterface $storeManager,
        StorelocatorRepositoryInterface $storelocatorRepository,
        Country $countryOptions,
        Region $regionOptions,
        StorelocatorInterfaceFactory $storelocatorDataFactory,
        DataObjectHelper $dataObjectHelper,
        StoreholidayFactory $storeholidayFactory,
        ScopeConfigInterface $scopeConfig,
        Json $serialize,
        DateTime $dateTime,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        storeHelper $storeHelper,
        CollectionFactory $tagValueCollectionFactory,
        TagAttributeFactory $TagAttributeFactory,
        TagAttributeOptionFactory $TagAttributeOptionFactory,
        Session $checkoutSession,
        \Magento\Directory\Model\ResourceModel\Region\CollectionFactory $regionCollectionFactory
    ) {
        $this->storelocatorFactory = $storelocatorFactory;
        $this->storeManager = $storeManager;
        $this->storelocatorRepository = $storelocatorRepository;
        $this->countryOptions = $countryOptions;
        $this->regionOptions = $regionOptions;
        $this->storelocatorDataFactory = $storelocatorDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->storeholidayFactory = $storeholidayFactory;
        $this->scopeConfig = $scopeConfig;
        $this->serialize = $serialize;
        $this->dateTime = $dateTime;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->storeHelper = $storeHelper;
        $this->tagValueCollectionFactory = $tagValueCollectionFactory;
        $this->tagAttributeFactory = $TagAttributeFactory;
        $this->tagAttributeOptionFactory = $TagAttributeOptionFactory;
        $this->checkoutSession = $checkoutSession;
        $this->regionCollectionFactory = $regionCollectionFactory;
    }

    /**
     * Get store information.
     *
     * @return array|StorelocatorRepositoryInterface[]
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws InputException
     */
    public function getStoreInformation()
    {
        $storeDetails = $this->storelocatorFactory->create()
                        ->addFieldToFilter('is_active', 1)
                        ->addStoreFilter($this->storeManager->getStore()->getId());

        $storeDataArray = [];

        if ($this->storeHelper->isCheckProductOnCheckout()) {
            $quoteItems = $this->getProductFromQuote();
            $itemId = [];
            foreach ($quoteItems as $item) {
                $itemId[] = $item->getProductId();
            }

            foreach ($storeDetails as $store) {
                /* @var $store Storelocator */
                $allProducts = $store->getAllValidateProductIds();
                $countProductIds = count(array_intersect($itemId, $allProducts));
                $itemCount = count($itemId);

                if ($countProductIds == $itemCount) {
                    $storeDataArray[] = $store->getData();
                }
            }
        } else {
            foreach ($storeDetails as $storeData) {
                $storeDataArray[] = $storeData->getData();
            }
        }

        return $storeDataArray;
    }

    /**
     * Get product from quote.
     *
     * @return Item[]
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getProductFromQuote()
    {
        return $this->checkoutSession->getQuote()->getAllVisibleItems();
    }

    /**
     * @inheritdoc
     */
    public function getStoreInformationById($storeloctorId)
    {
        /** @var StorelocatorInterface $storelocatorCollection */
        $storelocatorCollection = $this->storelocatorFactory->create()
                            ->addFieldToFilter('is_active', 1, 'eq')
                            ->addFieldToFilter('storelocator_id', $storeloctorId)
                            ->addStoreFilter($this->storeManager->getStore()->getId());

        $storeInfo = $storelocatorCollection->getData();

        if ($storeInfo) {
            $countryname = $this->getCountryName($storeInfo[0]['country_id']);
            $storeInfo[0]['countryname'] = $countryname;

            $storeInfo[0]['statename'] ='';
            if ($storeInfo[0]["region"] === null) {
                if (isset($storeInfo[0]["region_id"]) && $storeInfo[0]["region_id"] != 0) {
                    $storeInfo[0]['statename'] = $this->getRegionName($storeInfo[0]["region_id"]);
                    $storeInfo[0]['region_code'] = $this->getRegionCode($storeInfo[0]["region_id"]);
                }
            } else {
                $storeInfo[0]['statename'] = $storeInfo[0]['region'];
            }

            $storeInfo[0]['address'] = str_replace('\n', ', ', $storeInfo[0]['address']);
        }
        return $storeInfo;
    }

    /**
     * @inheritdoc
     */
    public function getStoreHolidayInformation($storeloctorId)
    {
        $holidayModel = $this->storeholidayFactory->create();
        $holildayCollection = $holidayModel->getCollection();
        $holildayCollection->addFieldToFilter('is_active', '1');
        $_holildayCollection = $this->getStoreFilter($holildayCollection, $storeloctorId);

        $holidayData['dates'] = $this->getStoreHolidays($_holildayCollection);
        $holidayData['days'] = $this->getStoreOffdays($storeloctorId);
        $holidayData['details'] = $this->getStoreFilter($holildayCollection, $storeloctorId);

        $holidayObj[] = $holidayData;

        return $holidayObj;
    }

    /**
     * @inheritdoc
     */
    public function getStoreTimeInformation($dateVal, $storeVal)
    {
        $storeDate = $dateVal;
        $storeID = $storeVal;

        $timeStamp = $this->dateTime->gmtTimestamp($storeDate);
        $selectedday = $this->dateTime->date('l', $timeStamp);
        $timeSloats = [];

        //$_timeInterval = $this->storeHelper->isTimeInterval();
        $storelocatorCollection = $this->storelocatorFactory->create();
        $storelocatorCollection->addFieldToFilter('storelocator_id', $storeID);
        $storelocatorCollection->addFieldToSelect('storetime')
            ->addStoreFilter($this->storeManager->getStore()->getId());

        $storelocatorData = $storelocatorCollection->getData();
        if (!empty($storelocatorData[0]['storetime'])) {
            $storetime = $this->storeHelper->unserializeData($storelocatorData[0]['storetime']);
        } else {
            /*$daysIndexs = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            foreach ($daysIndexs as $daysIndex) {
                $storetime[] = [
                    'days' => $daysIndex,
                    'day_status' => 1,
                    'open_hour' => 0,
                    'open_minute' => 0,
                    'close_hour' => 23,
                    'close_minute' => 0
                ];
            }*/
        }
        $timeSloats = [];
        if (!empty($storetime)) {
            foreach ($storetime as $key => $day) {
                if ($day['days'] == $selectedday) {
                    $timeSloats[$key] = $this->getTimeSloats($storeDate, $day);
                }
            }
            if ($timeSloats) {
                return $timeSloats;           
            }
        }
        return $timeSloats;
    }

    /**
     * Get country name.
     *
     * @param string $countryval
     * @return string
     */
    public function getCountryName($countryval)
    {
        $countryArray = $this->countryOptions->getOptions();
        return $countryArray[$countryval];
    }

    /**
     * Get region name.
     *
     * @param int|string $region_id
     * @return string
     */
    public function getRegionName($region_id)
    {
        $regionArray = $this->regionOptions->getOptions();
        return $regionArray[$region_id];
    }

    /**
     * Get region code.
     *
     * @param int|string $region_id
     * @return string
     */
    public function getRegionCode($region_id)
    {
        $regions = $this->regionCollectionFactory->create()
            ->addFieldToFilter('main_table.region_id', $region_id);
        $regionCode = '';
        foreach ($regions as $region) {
            $regionCode = $region['code'];
        }
        return $regionCode;
    }

    /**
     * Get store filter.
     *
     * @param object|array|mixed $holildayCollection
     * @param int|string $storeloctorId
     * @return mixed
     */
    public function getStoreFilter($holildayCollection, $storeloctorId)
    {
        $_holildayCollection = $holildayCollection->getData();
        /* holiday_applied_stores || 0 */
        $elementkey = 0;
        foreach ($holildayCollection as $collection) {
            $appliedStore = $collection->getHolidayAppliedStores();
            $appliedStore = explode(',', $appliedStore);

//            if (in_array('0', $appliedStore, true) || in_array($storeloctorId, $appliedStore, true)) {
//            } else {
//                unset($_holildayCollection[$elementkey]);
//            }
            if (!(in_array('0', $appliedStore, true) ||
                in_array($storeloctorId, $appliedStore, true))) {
                unset($_holildayCollection[$elementkey]);
            }

            $elementkey++;
        }
        return $_holildayCollection;
    }

    /**
     * Get store holidays.
     *
     * @param object|array $_holildayCollection
     * @return array|bool
     */
    public function getStoreHolidays($_holildayCollection)
    {
        $holidayDays = [];
        $holidayDays['repetitive'] = [];
        $holidayDays['normal'] = [];
        $holidayDays['shipping_off_day'] = [];

        foreach ($_holildayCollection as $key => $store) {
            $fromDate = date_create($store['holiday_date_from']);
            $toDate = date_create($store['holiday_date_to']);
            $diff = date_diff($fromDate, $toDate);

            if ($diff->format("%a") == 0) {

                $date = date("d-m-Y", strtotime($store['holiday_date_from'] . "+0 days"));
                $holidayDays['normal'][] = $date;

                if ($store['is_repetitive'] == 1) {
                    $repetitiveDate = $holidayDays['normal'];
                    foreach ($repetitiveDate as &$value) {
                        $tmpDate = explode('-', $value);
                        $value = $tmpDate[2].'-'.$tmpDate[0].'-'.$tmpDate[1];
                        $holidayDays['repetitive'][] = date('n-j', strtotime($value.' +1 year'));
                    }
                }

            } elseif ($diff->format("%a") > 0) {
                $diffFormat = $diff->format("%a");
                for ($i = 0; $i <= $diffFormat; $i++) {
                    $date = date("d-m-Y", strtotime($store['holiday_date_from'] . "+" . $i . "days"));
                    $holidayDays['normal'][] = $date;
                }

                if ($store['is_repetitive'] == 1) {
                    $repetitiveDate = $holidayDays['normal'];
                    foreach ($repetitiveDate as &$value) {
                        $tmpDate = explode('-', $value);
                        $value = $tmpDate[2].'-'.$tmpDate[0].'-'.$tmpDate[1];
                        $holidayDays['repetitive'][] = date('n-j', strtotime($value.' +1 year'));
                    }
                }
            }
        }

        if (empty($holidayDays)) {
            return true;
        }
        return $holidayDays;
    }

    /**
     * Get store off days.
     *
     * @param int|string $storeloctorId
     * @return array|bool
     * @throws NoSuchEntityException
     */
    public function getStoreOffdays($storeloctorId)
    {
        $storeCollection = $this->storelocatorFactory->create();
        $storeCollection->addFieldToFilter('storelocator_id', $storeloctorId);
        $storeCollection->addFieldToSelect('storetime')
            ->addStoreFilter($this->storeManager->getStore()->getId());
        $storeData = $storeCollection->getData();
        if (!empty($storeData[0]['storetime'])) {
            $storetime = $this->storeHelper->unserializeData($storeData[0]['storetime']);
        }

        $daysIndex = [
            'Sunday' => 0,
            'Monday' => 1,
            'Tuesday' => 2,
            'Wednesday' => 3,
            'Thursday' => 4,
            'Friday' => 5,
            'Saturday' => 6
        ];

        if (!empty($storetime)) {
            foreach ($storetime as $day) {
                if ($day['day_status'] == '0') {
                    $days[] = $daysIndex[$day['days']];
                }
            }
        }

        if (empty($days)) {
            return true;
        }
        return $days;
    }

    /**
     * Get time slots.
     *
     * @param string $storeDate
     * @param string $day
     * @return bool
     * @throws Exception
     */
    public function getTimeSloats($storeDate, $day)
    {
        $tmpstoreDate = $storeDate;
        $storeDate = explode("-", $storeDate);
        $d = mktime($day['open_hour'], $day['open_minute'], 00, $storeDate[1], $storeDate[0], $storeDate[2]);
        $stTime = date("h:i:sa", $d);

        $storeDate = $tmpstoreDate;
        if ($day['close_hour'] == 00) {
            $date = $storeDate;
            $date1 = str_replace('-', '/', $date);
            $storeDate = date('Y-m-d', strtotime($date1 . "+1 days"));
        }

        $storeDate = explode("-", $storeDate);
        $d = mktime($day['close_hour'], $day['close_minute'], 00, $storeDate[1], $storeDate[0], $storeDate[2]);
        $enTime = date("h:i:sa", $d);

        return $tmpstoreDate." ".$stTime." - ".$enTime;
    }

    /**
     * Get items.
     *
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface[]
     * @throws LocalizedException
     */
    public function getItems()
    {
        $searchCriteria = $this->searchCriteriaBuilder->create();
        $searchResult = $this->storelocatorRepository->getList($searchCriteria);
        return $searchResult->getItems();
    }

    /**
     * Get store tags information.
     *
     * @param int|string $storeloctorId
     * @return array
     */
    public function getStoretagsInformation($storeloctorId)
    {
        $collection = $this->tagValueCollectionFactory->create();
        $collection->addFieldToFilter('storelocator_id', $storeloctorId);
        $tagsObj=$collection->getData();
        $tagsAttributesObj=[];
        if (!empty($tagsObj)) {
            foreach ($tagsObj as $key => $store) {
                $storeTagsmodel = $this->tagAttributeFactory->create()
                    ->getCollection()->addFieldToFilter('tag_attribute_id', $store['tag_attribute_id'])
                    ->getFirstItem();
                $tagsAttributesObj['tag_name'][]= $storeTagsmodel->getData('tag_attribute_frontend_label');
                $tagsAttributesObj['tag_code'][]= $storeTagsmodel->getData('tag_attribute_code');
                if (!is_numeric($store['tag_value'])) {
                    $tagsAttributesObj['tag_value'][]= $store['tag_value'];
                } else {
                     $storeTagsOptionmodel = $this->tagAttributeOptionFactory->create()
                    ->getCollection()->addFieldToFilter('tag_attribute_id', $store['tag_attribute_id'])
                    ->getFirstItem();
                    $tagsAttributesObj['tag_value'][]= $storeTagsOptionmodel->getData('tag_option');
                }
            }
        }
        return $tagsAttributesObj;
    }
}
