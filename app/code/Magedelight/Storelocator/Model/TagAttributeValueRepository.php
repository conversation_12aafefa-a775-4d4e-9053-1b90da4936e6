<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model;

use Magedelight\Storelocator\Api\Data\StoreTagValueInterface;
use Magedelight\Storelocator\Api\Data\StoreTagValueSearchResultInterface;
use Magedelight\Storelocator\Api\Data\StoreTagValueSearchResultInterfaceFactory;
use Magedelight\Storelocator\Api\StoreTagValueRepositoryInterface;
use Magedelight\Storelocator\Model\ResourceModel\TagAttributeValue as ResourceModel;
use Magedelight\Storelocator\Model\ResourceModel\TagAttributeValue\CollectionFactory;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

class TagAttributeValueRepository implements StoreTagValueRepositoryInterface
{
    /**
     * @var ResourceModel
     */
    protected $resource;

    /**
     * @var TagAttributValueeFactory
     */
    protected $tagAttributeValueFactory;

    /**
     * @var CollectionFactory
     */
    protected $tagValueCollectionFactory;

    /**
     * @var StoreTagValueSearchResultInterfaceFactory
     */
    protected $searchResultsFactory;

    /**
     * TagAttributeValueRepository constructor.
     * @param ResourceModel $resource
     * @param TagAttributeValueFactory $tagAttributeValueFactory
     * @param CollectionFactory $tagValueCollectionFactory
     * @param StoreTagValueSearchResultInterfaceFactory $searchResultsFactory
     */
    public function __construct(
        ResourceModel $resource,
        TagAttributeValueFactory $tagAttributeValueFactory,
        CollectionFactory $tagValueCollectionFactory,
        StoreTagValueSearchResultInterfaceFactory $searchResultsFactory
    ) {
        $this->resource = $resource;
        $this->tagAttributeValueFactory = $tagAttributeValueFactory;
        $this->tagValueCollectionFactory = $tagValueCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
    }

    /**
     * Save tag value attribute.
     *
     * @param StoreTagValueInterface $tagValue
     * @return StoreTagValueInterface
     * @throws LocalizedException
     */
    public function save(StoreTagValueInterface $tagValue)
    {
        try {
            /** @noinspection PhpParamsInspection */
            $this->resource->save($tagValue);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__($exception->getMessage()));
        }
        return $tagValue;
    }

    /**
     * Retrieve tag value.
     *
     * @param int $tagValueId
     * @return StoreTagValueInterface
     * @throws LocalizedException
     */
    public function getById($tagValueId)
    {
        $tag = $this->tagAttributeValueFactory->create();
        $this->resource->load($tag, $tagValueId);
        if (!$tag->getId()) {
            throw new NoSuchEntityException(
                __('The Tag Attribute Value with the "%1" ID doesn\'t exist.', $tagValueId)
            );
        }
        return $tag;
    }

    /**
     * Retrieve tags value matching the specified criteria.
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return StoreTagValueSearchResultInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        $collection = $this->tagValueCollectionFactory->create();
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * Delete tag value.
     *
     * @param StoreTagValueInterface $tagValue
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(StoreTagValueInterface $tagValue)
    {
        try {
            /** @noinspection PhpParamsInspection */
            $this->resource->delete($tagValue);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__($exception->getMessage()));
        }
        return true;
    }

    /**
     * Delete tag value by ID.
     *
     * @param int $tagValueId
     * @return bool true on success
     * @throws LocalizedException
     */
    public function deleteById($tagValueId)
    {
        return $this->delete($this->getById($tagValueId));
    }
}
