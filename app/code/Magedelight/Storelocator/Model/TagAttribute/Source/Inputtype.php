<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\TagAttribute\Source;

use Magento\Framework\Data\OptionSourceInterface;

class Inputtype implements OptionSourceInterface
{
    /**
     * Options.
     *
     * @return array|array[]
     */
    public function toOptionArray()
    {
        return [
            ['value' => 'select', 'label' => __('Dropdown')],
            ['value' => 'multiselect', 'label' => __('Multiselect')],
            ['value' => 'input', 'label' => __('Text')],
        ];
    }
}
