<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\TagAttribute;

use Magedelight\Storelocator\Model\ResourceModel\TagAttribute\Collection;
use Magedelight\Storelocator\Model\ResourceModel\TagAttribute\CollectionFactory;
use Magedelight\Storelocator\Model\ResourceModel\TagAttributeOption\CollectionFactory as OptionCollection;
use Magedelight\Storelocator\Model\TagAttributeOption;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Stdlib\ArrayManager;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Store\Model\Store;
use Magento\Ui\Component\Form\Element\DataType\Text;
use Magento\Ui\Component\Form\Element\Input;
use Magento\Ui\Component\Form\Field;
use Magento\Ui\DataProvider\AbstractDataProvider;

class DataProvider extends AbstractDataProvider
{
    /**
     * @var Collection
     */
    protected $collection;

    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @var StoreRepositoryInterface
     */
    protected $storeRepository;

    /**
     * @var ArrayManager
     */
    protected $arrayManager;

    /**
     * @var SerializerInterface
     */
    protected $serializer;

    /**
     * @var OptionCollection
     */
    protected $optionCollection;

    /**
     * Constructor
     *
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $tagCollectionFactory
     * @param OptionCollection $optionCollection
     * @param StoreRepositoryInterface $storeRepository
     * @param ArrayManager $arrayManager
     * @param SerializerInterface $serializer
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $tagCollectionFactory,
        OptionCollection $optionCollection,
        StoreRepositoryInterface $storeRepository,
        ArrayManager $arrayManager,
        SerializerInterface $serializer,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->collection = $tagCollectionFactory->create();
        $this->optionCollection = $optionCollection;
        $this->storeRepository = $storeRepository;
        $this->arrayManager = $arrayManager;
        $this->serializer = $serializer;
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }

        $items = $this->collection->getItems();

        foreach ($items as $item) {
            $data = $item->getData();

            $data['tag_attribute_code_exist'] = false;
            if (isset($data['tag_attribute_code'])) {
                if (!empty($data['tag_attribute_code'])) {
                    $data['tag_attribute_code_exist'] = true;
                }
            }

            if (isset($data['tag_attribute_frontend_label'])) {
                $data['tag_attribute_frontend_label[0]'] = $data['tag_attribute_frontend_label'];
                unset($data['tag_attribute_frontend_label']);
            }

            if (isset($data['tag_attribute_label'])) {
                if (!empty($data['tag_attribute_label'])) {
                    $tagLables = $this->serializer->unserialize($data['tag_attribute_label']);
                    foreach ($tagLables as $store => $label) {
                        $data['tag_attribute_frontend_label['.$store.']'] = $label;
                    }
                    unset($data['tag_attribute_label']);
                }
            }

            $options = $this->optionCollection->create()
                ->addFieldToFilter('tag_attribute_id', $data['tag_attribute_id']);

            if ($options->getSize() > 0) {
                $tagOptions = [];
                foreach ($options as $option) {
                    /*  @var $option TagAttributeOption  */
                    $allStores = $this->serializer->unserialize($option->getTagOptionsSerialized());
                    $tagOption = [
                        'position' => $option['position'],
                        'record_id' => $option['record_id']
                    ];
                    foreach ($allStores as $key => $store) {
                        $tagOption['value_option_'.$key] = $store;
                    }
                    $tagOptions[] = $tagOption;
                }
                $data['attribute_options_select'] = $tagOptions;
            }
            $this->loadedData[$item->getData('tag_attribute_id')] = $data;
        }
        return $this->loadedData;
    }

    /**
     * Get meta information
     *
     * @return array
     */
    public function getMeta()
    {
        $meta = parent::getMeta();
        $meta = $this->customizeFrontendLabels($meta);
        return $meta;
    }

    /**
     * Customize Frontend Labels.
     *
     * @param array $meta
     * @return mixed
     */
    private function customizeFrontendLabels($meta)
    {
        $sortOrder = 10;
        foreach ($this->storeRepository->getList() as $store) {
            $storeId = $store->getId();
            if ($storeId) {
                $meta['manage-titles']['children']
                ['tag_attribute_frontend_label[' . $storeId . ']'] = $this->arrayManager->set(
                    'arguments/data/config',
                    [],
                    [
                        'formElement' => Input::NAME,
                        'componentType' => Field::NAME,
                        'label' => $store->getName(),
                        'dataType' => Text::NAME,
                        'dataScope' => 'tag_attribute_frontend_label[' . $storeId . ']'
                    ]
                );
            }

            $storeLabelConfiguration = [
                'dataType' => 'text',
                'formElement' => 'input',
                'component' => 'Magento_Catalog/js/form/element/input',
                'template' => 'Magento_Catalog/form/element/input',
                'prefixName' => 'option.value',
                'prefixElementName' => 'option_',
                'suffixName' => (string)$storeId,
                'label' => $store->getName(),
                'sortOrder' => $sortOrder,
                'componentType' => Field::NAME,
            ];
            // JS code can't understand 'required-entry' => false|null, we have to avoid even empty property.
            if ($store->getCode() == Store::ADMIN_CODE) {
                $storeLabelConfiguration['validation'] = [
                    'required-entry' => true,
                ];
                $storeLabelConfiguration['sortOrder'] = 1;
            }
            $meta['attribute_options_select_container']['children']['attribute_options_select']['children']
            ['record']['children']['value_option_' . $storeId] = $this->arrayManager->set(
                'arguments/data/config',
                [],
                $storeLabelConfiguration
            );

            ++$sortOrder;
        }

        $meta['attribute_options_select_container']['children']['attribute_options_select']['children']
        ['record']['children']['action_delete'] = $this->arrayManager->set(
            'arguments/data/config',
            [],
            [
                'componentType' => 'actionDelete',
                'dataType' => 'text',
                'fit' => true,
                'sortOrder' => $sortOrder,
                'component' => 'Magento_Catalog/js/form/element/action-delete',
                'elementTmpl' => 'Magento_Catalog/form/element/action-delete',
                'template' => 'Magento_Catalog/form/element/action-delete',
                'prefixName' => 'option.delete',
                'prefixElementName' => 'option_',
            ]
        );
        return $meta;
    }
}
