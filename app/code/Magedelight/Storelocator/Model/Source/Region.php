<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Magento\Directory\Model\ResourceModel\Region\CollectionFactory as RegionCollectionFactory;

class Region implements OptionSourceInterface
{
    /**
     * @var RegionCollectionFactory
     */
    protected $regionCollectionFactory;

    /**
     * @var array
     */
    protected $options;

    /**
     * Region constructor.
     *
     * @param RegionCollectionFactory $regionCollectionFactory
     */
    public function __construct(RegionCollectionFactory $regionCollectionFactory)
    {
        $this->regionCollectionFactory = $regionCollectionFactory;
    }

    /**
     * Get options as key value pair
     *
     * @return array
     */
    public function toOptionArray()
    {
        if ($this->options === null) {
            $this->options = $this->regionCollectionFactory->create()->toOptionArray(' ');
        }
        return $this->options;
    }

    /**
     * Get options.
     *
     * @param array $options
     * @return array
     */
    public function getOptions(array $options = [])
    {
        $regionOptions = $this->toOptionArray($options);
        $_options = [];
        foreach ($regionOptions as $option) {
            $_options[$option['value']] = $option['label'];
        }
        return $_options;
    }
}
