<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Storeholiday\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Magedelight\Storelocator\Model\ResourceModel\Storelocator\CollectionFactory;

class StoreList implements OptionSourceInterface
{
    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * StoreList constructor.
     *
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Get options as key value pair
     *
     * @return array
     */
    public function toOptionArray()
    {
        $storelocator = $this->collectionFactory->create();
        if ($storelocator->getSize() < 1) {
            $_options[] = ['label' => '', 'value' => ''];
            return $_options;
        }
        foreach ($storelocator as $option) {
            $_options[] = [
                'value' => $option['storelocator_id'],
                'label' => $option['storename'],
            ];
        }
        return $_options;
    }
}
