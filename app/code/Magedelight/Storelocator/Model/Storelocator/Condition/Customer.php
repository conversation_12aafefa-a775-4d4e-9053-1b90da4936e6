<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Storelocator\Condition;

use Exception;
use Magento\Backend\Helper\Data;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Rule\Model\Condition\AbstractCondition;
use Magento\Rule\Model\Condition\Context;

class Customer extends AbstractCondition
{
    /**
     * @var \Magento\Customer\Model\Customer
     */
    protected $customer;

    /**
     * @var \Magento\Customer\Model\Customer
     */
    protected $customerCollection;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepositoryInterface;

    /**
     * @var Data
     */
    protected $_backendData;

    /**
     * @var TimezoneInterface
     */
    protected $timezone;

    /**
     * Customer constructor.
     *
     * @param Context $context
     * @param \Magento\Customer\Model\ResourceModel\Customer $customer
     * @param \Magento\Customer\Model\Customer $customerCollection
     * @param CustomerRepositoryInterface $customerRepositoryInterface
     * @param Data $backendData
     * @param TimezoneInterface $timezone
     * @param array $data
     */
    public function __construct(
        Context $context,
        \Magento\Customer\Model\ResourceModel\Customer $customer,
        \Magento\Customer\Model\Customer $customerCollection,
        CustomerRepositoryInterface $customerRepositoryInterface,
        Data $backendData,
        TimezoneInterface $timezone,
        array $data = []
    ) {
        $this->customer = $customer;
        $this->customerCollection = $customerCollection;
        $this->customerRepositoryInterface = $customerRepositoryInterface;
        $this->_backendData = $backendData;
        $this->timezone = $timezone;
        parent::__construct($context, $data);
    }

    /**
     * Load attribute options
     *
     * @return $this
     */
    public function loadAttributeOptions()
    {
        $attributes = [
            'entity_id' => __('Select Customer'),
            'email' => __('E-Mail'),
            'firstname' => __('First Name'),
            'middlename' => __('Middle Name'),
            'lastname' => __('Last Name'),
            'customer_dob' => __('Date Of Birth'),
            'gender' => __('Gender'),
            'md_customer_created_at' => __('Created At'),
        ];
        $this->setAttributeOption($attributes);

        return $this;
    }

    /**
     * Customize default operator input by type mapper for some types
     *
     * @return array
     */
    public function getDefaultOperatorInputByType()
    {
        if (null === $this->_defaultOperatorInputByType) {
            parent::getDefaultOperatorInputByType();
            /*
             * '{}' and '!{}' are left for back-compatibility and equal to '==' and '!='
             */
            $this->_defaultOperatorInputByType['entity_id'] = ['==', '!=', '{}', '!{}', '()', '!()'];
        }
        return $this->_defaultOperatorInputByType;
    }

    /**
     * Get attribute element
     *
     * @return $this
     */
    public function getAttributeElement()
    {
        $element = parent::getAttributeElement();
        $element->setShowAsText(true);
        return $element;
    }

    /**
     * Get input type
     *
     * @return string
     */
    public function getInputType()
    {
        if ($this->getAttribute() == 'entity_id') {
            return 'entity_id';
        }
        switch ($this->getAttribute()) {
            case 'gender':
                return 'select';
            case 'md_customer_created_at':
            case 'customer_dob':
                return 'date';
        }
        return 'string';
    }

    /**
     * Get value element type
     *
     * @return string
     */
    public function getValueElementType()
    {
        switch ($this->getAttribute()) {
            case 'gender':
                return 'select';
            case 'md_customer_created_at':
            case 'customer_dob':
                return 'date';
        }
        return 'text';
    }

    /**
     * Get value select options
     *
     * @return array|mixed
     * @throws LocalizedException
     */
    public function getValueSelectOptions()
    {
        if (!$this->hasData('value_select_options')) {
            switch ($this->getAttribute()) {
                case 'gender':
                    $options = $this->customer->getAttribute('gender')->getSource()->getAllOptions();
                    break;
                default:
                    $options = [];
            }
            $this->setData('value_select_options', $options);
        }
        return $this->getData('value_select_options');
    }

    /**
     * Retrieve after element HTML
     *
     * @return string
     */
    public function getValueAfterElementHtml()
    {
        $html = '';

        switch ($this->getAttribute()) {
            case 'entity_id':
                $image = $this->_assetRepo->getUrl('images/rule_chooser_trigger.gif');
                break;
        }

        if (!empty($image)) {
            $html = '<a href="javascript:void(0)" class="rule-chooser-trigger"><img src="' .
                $image .
                '" alt="" class="v-middle rule-chooser-trigger" title="' .
                __(
                    'Open Chooser'
                ) . '" /></a>';
        }
        return $html;
    }

    /**
     * Retrieve value element chooser URL
     *
     * @return string
     */
    public function getValueElementChooserUrl()
    {
        $url = false;
        switch ($this->getAttribute()) {
            case 'entity_id':
                $url = 'magedelight/storelocator/chooser/attribute/' . $this->getAttribute();
                if ($this->getJsFormObject()) {
                    $url .= '/form/' . $this->getJsFormObject();
                }
                break;
            default:
                break;
        }
        return $url !== false ? $this->_backendData->getUrl($url) : '';
    }

    /**
     * Retrieve Explicit Apply
     *
     * @return bool
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function getExplicitApply()
    {
        switch ($this->getAttribute()) {
            case 'entity_id':
                return true;
            default:
                break;
        }
        return false;
    }

    /**
     * Validate method.
     *
     * @param AbstractModel $model
     * @return bool
     * @throws Exception
     */
    public function validate(AbstractModel $model)
    {
        $customer = $model;
        if (!$customer instanceof \Magento\Customer\Model\Customer) {
            $customer = $model->getQuote()->getCustomer();
            $customerData = $customer->__toArray();
            $customer = $this->customerCollection;
            $attribute = $this->getAttribute();
            if (!empty($customerData['id'])) {
                $customer->load($customerData['id']);
                if ($attribute == 'customer_dob') {
                    $customer->setData(
                        'customer_dob',
                        (new \DateTime($customer->getData('dob')))->format('Y-m-d H:i:s')
                    );
                }
                if ($attribute == 'md_customer_created_at') {
                    $customer->setData(
                        'md_customer_created_at',
                        $this->timezone->date(
                            new \DateTime($customer->getData('created_at'))
                        )->format('Y-m-d 00:00:00')
                    );
                }
            }

            if ($attribute != 'entity_id' && !$customer->getData($attribute)) {
                $address = $model->getQuote()->getBillingAddress();
                $customer->setData($attribute, $address->getData($attribute));
            }
        }
        return parent::validate($customer);
    }
}
