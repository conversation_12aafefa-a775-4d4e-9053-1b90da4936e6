<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Storelocator\Source;

use Magento\Framework\Data\OptionSourceInterface;

class TimeOptionsMin implements OptionSourceInterface
{
    /**
     * Get time options minute array.
     *
     * @return array
     */
    public function toOptionArray()
    {
        $time = [];
        $minutsArray = range(0, 59);

        foreach ($minutsArray as $minut) {
            $time[] = [
                'value' => str_pad($minut, 2, '0', STR_PAD_LEFT),
                'label' => __(str_pad($minut, 2, '0', STR_PAD_LEFT))
            ];
        }
        return $time;
    }
}
