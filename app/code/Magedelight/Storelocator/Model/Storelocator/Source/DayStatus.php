<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Storelocator\Source;

use Magento\Framework\Data\OptionSourceInterface;

class DayStatus implements OptionSourceInterface
{
    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            ['label' => __('Open'), 'value' => '1'],
            ['label' => __('Closed'), 'value' => '0']
        ];
    }
}
