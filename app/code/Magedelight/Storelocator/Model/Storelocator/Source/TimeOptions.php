<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Storelocator\Source;

use Magento\Framework\Data\OptionSourceInterface;

class TimeOptions implements OptionSourceInterface
{
    /**
     * Get time option array.
     *
     * @return array
     */
    public function toOptionArray()
    {
        $time = [];
        $hoursArray = range(0, 23);

        foreach ($hoursArray as $hour) {
            $time[] = [
                'value' => str_pad($hour, 2, '0', STR_PAD_LEFT),
                'label' => __(str_pad($hour, 2, '0', STR_PAD_LEFT))
            ];
        }
        return $time;
    }
}
