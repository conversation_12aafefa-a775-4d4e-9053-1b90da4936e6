<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Storelocator\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Magento\Inventory\Model\ResourceModel\Source\CollectionFactory;

class MSIStore implements OptionSourceInterface
{

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        $sourceCollection = $this->collectionFactory->create();

        $options[] = ['label' => __('Please Select'), 'value' => ''];

        if ($sourceCollection) {
            foreach ($sourceCollection as $source) {
                $options[] = ['label' => $source->getName(), 'value' => $source->getSourceCode()];
            }
        }
        
        return $options;
    }
}
