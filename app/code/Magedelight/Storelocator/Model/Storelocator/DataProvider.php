<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\Storelocator;

use Magedelight\Storelocator\Model\ResourceModel\Storelocator\Collection;
use Magedelight\Storelocator\Model\TagAttribute;
use Magedelight\Storelocator\Model\Uploader;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magedelight\Storelocator\Model\ResourceModel\Storelocator\CollectionFactory;
use Magedelight\Storelocator\Model\ResourceModel\TagAttributeOption\CollectionFactory as OptionCollectionFactory;
use Magedelight\Storelocator\Model\StorelocatorImages;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Stdlib\ArrayManager;
use Magento\Ui\Component\Form\Element\DataType\Text;
use Magento\Ui\Component\Form\Field;
use Magedelight\Storelocator\Api\StoreTagRepositoryInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Ui\DataProvider\AbstractDataProvider;

class DataProvider extends AbstractDataProvider
{
    /**
     * @var $loadedData
     */
    protected $loadedData;

    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var Collection
     */
    protected $collection;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * @var StorelocatorImages
     */
    protected $storelocatorImages;

    /**
     * @var StoreTagRepositoryInterface
     */
    protected $tagAttributeRepository;

    /**
     * @var ArrayManager
     */
    protected $arrayManager;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    /**
     * @var \Magedelight\Storelocator\Model\ResourceModel\TagAttributeOption\Collection
     */
    protected $optionCollectionFactory;

    /**
     * DataProvider constructor.
     *
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param OptionCollectionFactory $optionCollectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param StoreManagerInterface $storeManager
     * @param Json $serializer
     * @param StorelocatorImages $storelocatorImages
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param StoreTagRepositoryInterface $tagAttributeRepository
     * @param ArrayManager $arrayManager
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        OptionCollectionFactory $optionCollectionFactory,
        DataPersistorInterface $dataPersistor,
        StoreManagerInterface $storeManager,
        Json $serializer,
        StorelocatorImages $storelocatorImages,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        StoreTagRepositoryInterface $tagAttributeRepository,
        ArrayManager $arrayManager,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->optionCollectionFactory = $optionCollectionFactory;
        $this->dataPersistor = $dataPersistor;
        $this->storeManager = $storeManager;
        $this->storelocatorImages = $storelocatorImages;
        $this->serializer = $serializer;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->tagAttributeRepository = $tagAttributeRepository;
        $this->arrayManager = $arrayManager;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * Get data
     *
     * @return array
     * @throws NoSuchEntityException
     */
    public function getData()
    {
        $baseurl =  $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }
        $items = $this->collection->getItems();
        $telephoneArray = [];
        foreach ($items as $model) {
            $_data = $model->getData();
            $_data['images'] = $this->storelocatorImages->getImages($model->getId());

            /*** Get image code ***/
            if ($_data['storeimage']):
                $img = [];
                $img[0]['name'] = $_data['storeimage'];
                $img[0]['url'] = $baseurl. Uploader::IMAGE_PATH.$_data['storeimage'];
                $_data['storeimage'] = $img;
            endif;

            if ($_data['markerimage']):
                $imgMark = [];
                $imgMark[0]['name'] = $_data['markerimage'];
                $imgMark[0]['url'] = $baseurl. Uploader::IMAGE_PATH.$_data['markerimage'];
                $_data['markerimage'] = $imgMark;
            endif;

            if ($_data['address']):
                $add = explode('\n', $_data['address']);
                $_data['address'] = $add;
            endif;
            if (isset($_data['telephone'])) {
                if ($_data['telephone']) {
                    $telephone = explode(':', $_data['telephone']);
                    foreach ($telephone as $key => $tele) {
                        $telephoneArray[$key]['telephone'] = $tele;
                    }
                }
            }
            $_data['telephone'] = $telephoneArray;
            if ($_data['storetime']):
                $storetime = $this->unserializeData($_data['storetime']);
                $_data['storetime'] = $storetime;
            endif;
            $model->setData($_data);
            $this->loadedData[$model->getId()] = $model->getData();
        }
        $data = $this->dataPersistor->get('magedelight_storelocator_store');

        if (!empty($data)) {
            $model = $this->collection->getNewEmptyItem();
            $model->setData($data);
            $this->loadedData[$model->getId()] = $model->getData();
            $this->dataPersistor->clear('magedelight_storelocator_store');
        }
        return $this->loadedData;
    }

    /**
     * Get meta.
     *
     * @return array
     * @throws LocalizedException
     */
    public function getMeta()
    {
        $meta = parent::getMeta();
        $meta = $this->customizeTagField($meta);
        return $meta;
    }

    /**
     * Get customize tag field.
     *
     * @param array|mixed $meta
     * @return mixed
     * @throws LocalizedException
     */
    private function customizeTagField($meta)
    {
        $labelConfigs = [];
        $searchCriteria = $this->searchCriteriaBuilder->create();
        $searchResult = $this->tagAttributeRepository->getList($searchCriteria);
        $count = $searchResult->getTotalCount();

        if ($count <= 0) {
            return $meta;
        }

        $tags = $searchResult->getItems();
        foreach ($tags as $tag) {
            /* @var $tag TagAttribute */
            $input = $tag->getTagAttributeFrontendInput();
            $tagId = $tag->getTagAttributeId();

            $labelConfigs['store_tag_attribute['.$tagId.']'] = $this->arrayManager->set(
                'arguments/data/config',
                [],
                [
                    'formElement' => $input,
                    'componentType' => Field::NAME,
                    'label' => $tag->getTagAttributeFrontendLabel(),
                    'dataType' => Text::NAME,
                    'dataScope' => 'store_tag_attribute['.$tagId.']'
                ]
            );

            if ($input == 'select' || $input == 'multiselect') {
                $tagOptions = $this->optionCollectionFactory->create()
                    ->addFieldToFilter('tag_attribute_id', (int) $tagId);
                $adminOption = [];
                foreach ($tagOptions as $option) {
                    $adminOption[] = ['label'=>$option['tag_option'],'value'=>$option['option_id']];
                }

                $labelConfigs['store_tag_attribute['.$tagId.']']['arguments']['data']['options'] = $adminOption;
            }

            if ($tag->getData('tag_attribute_required')) {
                $labelConfigs['store_tag_attribute[' . $tagId . ']']['arguments']['data']['config']
                ['validation']['required-entry'] = true;
            }
        }
        $meta['manage-tags']['children'] = $labelConfigs;
        return $meta;
    }

    /**
     * Get unSerialize data.
     *
     * @param string $string
     * @return mixed
     */
    public function unserializeData($string)
    {
        $result = json_decode($string, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            //if (false !== @unserialize($string)) {
            if (is_string($string) && !empty($string)) {
                return $this->serializer->unserialize($string);
                //return unserialize($string);
            }
            throw new \InvalidArgumentException('Unable to unserialize value.');
        }
        return $result;
    }
}
