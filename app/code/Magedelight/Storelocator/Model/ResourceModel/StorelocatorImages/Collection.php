<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\ResourceModel\StorelocatorImages;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Magedelight\Storelocator\Model\StorelocatorImages as StorelocatorImagesModel;
use Magedelight\Storelocator\Model\ResourceModel\StorelocatorImages as StorelocatorImagesResourceModel;

class Collection extends AbstractCollection
{

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            StorelocatorImagesModel::class,
            StorelocatorImagesResourceModel::class
        );
    }
}
