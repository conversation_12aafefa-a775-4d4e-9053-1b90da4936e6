<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\Model\ResourceModel\Db\Context;
use Magento\Framework\Serialize\SerializerInterface;

class TagAttributeValue extends AbstractDb
{
    /**
     * @var SerializerInterface
     */
    protected $serializer;

    /**
     * TagAttributeValue constructor.
     *
     * @param Context $context
     * @param SerializerInterface $serializer
     * @param string $connectionName
     */
    public function __construct(
        Context $context,
        SerializerInterface $serializer,
        $connectionName = null
    ) {
        parent::__construct($context, $connectionName);
        $this->serializer = $serializer;
    }

    /**
     *  Define main table and id field name.
     */
    protected function _construct()
    {
        $this->_init('magedelight_storelocator_tag_value', 'tag_value_id');
    }
}
