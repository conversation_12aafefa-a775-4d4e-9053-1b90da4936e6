<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Model\ResourceModel\Storelocator;

use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\DB\Select;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Magento\Framework\Data\Collection\Db\FetchStrategyInterface;
use Magento\Framework\Data\Collection\EntityFactoryInterface;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Magedelight\Storelocator\Model\Storelocator;
use Magedelight\Storelocator\Model\ResourceModel\Storelocator as StorelocatorResourceModel;
use Magedelight\Storelocator\Api\StoreTagRepositoryInterface;

class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected string $idFieldName = 'storelocator_id';

    /**
     * @var string
     */
    protected string $eventPrefix = 'magedelight_storelocator_collection';

    /**
     * @var string
     */
    protected string $eventObject = 'storelocator_collection';

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var StoreTagRepositoryInterface
     */
    protected $storeTagRepository;

    /**
     * Collection constructor.
     *
     * @param EntityFactoryInterface $entityFactory
     * @param LoggerInterface $logger
     * @param FetchStrategyInterface $fetchStrategy
     * @param ManagerInterface $eventManager
     * @param StoreManagerInterface $storeManager
     * @param StoreTagRepositoryInterface $storeTagRepository
     * @param AdapterInterface $connection
     * @param AbstractDb|null $resource
     */
    public function __construct(
        EntityFactoryInterface $entityFactory,
        LoggerInterface $logger,
        FetchStrategyInterface $fetchStrategy,
        ManagerInterface $eventManager,
        StoreManagerInterface $storeManager,
        StoreTagRepositoryInterface $storeTagRepository,
        $connection = null,
        AbstractDb $resource = null
    ) {
        $this->storeManager = $storeManager;
        $this->storeTagRepository = $storeTagRepository;
        parent::__construct($entityFactory, $logger, $fetchStrategy, $eventManager, $connection, $resource);
    }

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(Storelocator::class, StorelocatorResourceModel::class);
        $this->_map['fields']['storelocator_id'] = 'main_table.storelocator_id';
        $this->_map['fields']['store_ids'] = 'store_table.store_ids';
    }

    /**
     * After load method.
     *
     * @return AbstractCollection
     * @throws LocalizedException
     */
    protected function _afterLoad()
    {
        $this->performAfterLoad('magedelight_storelocator_store', 'storelocator_id');
        $this->performTagAttributeAfterLoad('magedelight_storelocator_tag_value', 'storelocator_id');
        return parent::_afterLoad();
    }

    /**
     * Perform tag attribute after load.
     *
     * @param string $table
     * @param string $field
     * @throws LocalizedException
     */
    protected function performTagAttributeAfterLoad($table, $field)
    {
        $linkedIds = $this->getColumnValues($field);
        $connection = $this->getConnection();
        $select = $connection->select()->from([$table => $this->getTable($table)])
            ->where($table.'.'.$field . ' IN (?)', $linkedIds);
        $result = $connection->fetchAll($select);

        if ($result) {
            foreach ($result as $tag) {
                $tagInput = $this->storeTagRepository
                    ->getById($tag['tag_attribute_id'])
                    ->getTagAttributeFrontendInput();
                if ($tagInput == 'multiselect') {
                    $tagValue = explode(',', $tag['tag_value']);
                } else {
                    $tagValue = $tag['tag_value'];
                }
                $tagData[$tag['tag_attribute_id']] = $tagValue;
            }
            foreach ($this as $item) {
                if (isset($tagData)) {
                    $item->setData('store_tag_attribute', $tagData);
                }
                foreach ($item['store_tag_attribute'] as $key => $tagAttribute) {
                    $item->setData('store_tag_attribute['.$key.']', $tagAttribute);
                }
                if (isset($tagData)) {
                    $item->unSetData('store_tag_attribute', $tagData);
                }
            }
        }
    }

    /**
     * Add field to filter.
     *
     * @param array|string $field
     * @param array|string|null $condition
     * @return AbstractCollection
     */
    public function addFieldToFilter($field, $condition = null)
    {
        if ($field === 'store_ids') {
            return $this->addStoreFilter($condition, false);
        }
        return parent::addFieldToFilter($field, $condition);
    }

    /**
     * Add store filter.
     *
     * @param array|object $store
     * @param bool $withAdmin
     * @return $this
     */
    public function addStoreFilter($store, $withAdmin = true)
    {
        if (!$this->getFlag('store_filter_added')) {
            if ($store instanceof Store) {
                $store = [$store->getId()];
            }
            if (!is_array($store)) {
                $store = [$store];
            }
            if ($withAdmin) {
                $store[] = Store::DEFAULT_STORE_ID;
            }
            $this->addFilter('store_ids', ['in' => $store], 'public');
        }
        return $this;
    }

    /**
     * Join store relation table if there is store filter
     *
     * @return void
     * @SuppressWarnings(PHPMD.Ecg.Sql.SlowQuery)
     */
    protected function _renderFiltersBefore()
    {
        if ($this->getFilter('store_ids')) {
            $this->getSelect()->join(
                ['store_table' => $this->getTable('magedelight_storelocator_store')],
                'main_table.storelocator_id = store_table.storelocator_id',
                []
            )
                // @codingStandardsIgnoreStart
                ->group('main_table.storelocator_id');
            // @codingStandardsIgnoreEnd
        }
        parent::_renderFiltersBefore();
    }

    /**
     * Get count select.
     *
     * @return Select
     */
    public function getSelectCountSql()
    {
        $countSelect = parent::getSelectCountSql();
        $countSelect->reset(Select::GROUP);
        return $countSelect;
    }

    /**
     * Perform after load method.
     *
     * @param string $tableName
     * @param string $linkField
     * @throws NoSuchEntityException
     */
    protected function performAfterLoad($tableName, $linkField)
    {
        $linkedIds = $this->getColumnValues($linkField);
        if (count($linkedIds)) {
            $connection = $this->getConnection();
            $select = $connection->select()->from(['magedelight_storelocator_store' => $this->getTable($tableName)])
                ->where('magedelight_storelocator_store.' . $linkField . ' IN (?)', $linkedIds);
            $result = $connection->fetchAll($select);
            if ($result) {
                $storesData = [];
                foreach ($result as $storeData) {
                    $storesData[$storeData[$linkField]][] = $storeData['store_ids'];
                }

                foreach ($this as $item) {
                    $linkedId = $item->getData($linkField);
                    if (!isset($storesData[$linkedId])) {
                        continue;
                    }
                    $storeIdKey = array_search(Store::DEFAULT_STORE_ID, $storesData[$linkedId], true);
                    if ($storeIdKey !== false) {
                        $stores = $this->storeManager->getStores(false, true);
                        $storeId = current($stores)->getId();
                        $storeCode = key($stores);
                    } else {
                        $storeId = current($storesData[$linkedId]);
                        $storeCode = $this->storeManager->getStore($storeId)->getCode();
                    }
                    $item->setData('_first_store_id', $storeId);
                    $item->setData('store_code', $storeCode);
                    $item->setData('store_ids', $storesData[$linkedId]);
                }
            }
        }
    }

    /**
     * From date or to date filter
     *
     * @param string $now
     * @return $this
     */
    public function dateFilter($now)
    {
        $this->getSelect()->where(
            'start_date is null or start_date <= ?',
            $now
        )->where(
            'end_date is null or end_date >= ?',
            $now
        );

        return $this;
    }
}
