<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\View\Asset\Repository;
use Magedelight\Storelocator\Model\Source\MapStyle;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Directory\Api\CountryInformationAcquirerInterface;
use Magedelight\Storelocator\Model\Source\Region;

class Storelocator extends AbstractHelper
{
    public const XML_PATH_MODULE_ACTIVE = 'magedelight_storelocator/general/enable';
    public const XML_PATH_MENU_LINK_TITLE = 'magedelight_storelocator/general/linktitle';
    public const XML_PATH_DISPLAY_MENU_LINK = 'magedelight_storelocator/general/displaytopmenu';
    public const XML_PATH_SHOW_STORES = 'magedelight_storelocator/general/show_product_availability';
    public const XML_PATH_SEARCHSUGGESTIONS = 'magedelight_storelocator/general/searchsuggestions';
    public const XML_PATH_IS_ALLOW_GEST_CUSTOMER = 'magedelight_storelocator/general/allowguestcustomer';
    public const XML_PATH_SHOW_SUGGESTIONS_BUTTON = 'magedelight_storelocator/general/button_suggestion';
    public const XML_PATH_META_DESCRIPTION = 'magedelight_storelocator/listviewinfo/meta_description';
    public const XML_PATH_META_TITLE = 'magedelight_storelocator/listviewinfo/meta_title';
    public const XML_PATH_META_KEYWORDS = 'magedelight_storelocator/listviewinfo/meta_keywords';
    public const XML_PATH_STORE_FRONTEND_URL = 'magedelight_storelocator/listviewinfo/frontend_url';
    public const XML_PATH_STORE_PAGE_URL_SUFFIX = 'magedelight_storelocator/listviewinfo/listpage_suffix';
    public const XML_PATH_GOOGLE_MAP_API = 'magedelight_storelocator/googlemap/mapapi';
    public const XML_PATH_GOOGLE_MAP_STYLE = 'magedelight_storelocator/googlemap/map_style';
    public const XML_PATH_GOOGLE_MAP_MARKER_IMAGE = 'magedelight_storelocator/googlemap/markericon';
    public const XML_PATH_STORE_IMAGE = 'magedelight_storelocator/general/logo';
    public const XML_PATH_STORE_COUNTRY = 'magedelight_storelocator/general/allowed_countries';
    public const XML_PATH_STORESEARCH_DEFAULTRADIOUS = 'magedelight_storelocator/storesearch/defaultradious';
    public const XML_PATH_STORESEARCH_MAXRADIOUS = 'magedelight_storelocator/storesearch/maxradious';
    public const XML_PATH_STORESEARCH_DISTANCEUNIT = 'magedelight_storelocator/storesearch/distanceunit';
    public const XML_PATH_URL_SUFFIX = 'magedelight_storelocator/listviewinfo/listpage_suffix';
    public const XML_PATH_STORE_LISTING_URL = 'magedelight_storelocator/listviewinfo/frontend_url';
    public const XML_PATH_STORE_PER_PAGE = 'magedelight_storelocator/listviewinfo/row_per_page';
    public const XML_PATH_PAGINATION_VISIBLE_TAB = 'magedelight_storelocator/listviewinfo/pagination_visible_tab';
    public const XML_PATH_STORE_TYPE = 'magedelight_storelocator/listviewinfo/store_type';
    public const XML_PATH_STORES_ON_LOAD = 'magedelight_storelocator/listviewinfo/stores_on_load';

    /**
     * @var ScopeConfigInterface
     */
    protected $storeConfig;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var Repository
     */
    protected $assetRepository;

    /**
     * @var MapStyle
     */
    protected $mapStyle;

    /**
     * @var Json
     */
    protected $serialize;

    /**
     * @var countryInformationAcquirerInterface
     */
    protected $countryInformationAcquirerInterface;

    /**
     * @var Region
     */
    protected $regionOptions;

    /**
     * Storelocator constructor.
     *
     * @param Context $context
     * @param StoreManagerInterface $storeManager
     * @param CountryInformationAcquirerInterface $countryInformationAcquirerInterface
     * @param Repository $assetRepository
     * @param MapStyle $mapStyle
     * @param Json $serialize
     */
    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        CountryInformationAcquirerInterface $countryInformationAcquirerInterface,
        Repository $assetRepository,
        MapStyle $mapStyle,
        Json $serialize,
        Region $regionOptions
    ) {
        $this->storeConfig = $context->getScopeConfig();
        $this->storeManager = $storeManager;
        $this->countryInformationAcquirerInterface = $countryInformationAcquirerInterface;
        $this->assetRepository = $assetRepository;
        $this->mapStyle = $mapStyle;
        $this->serialize = $serialize;
        $this->regionOptions = $regionOptions;
        parent::__construct($context);
    }

    /**
     * Store Locator Module is Enabled or Not
     *
     * @return boolean
     */
    public function isModuleEnable()
    {
        return (bool)$this->storeConfig->getValue(
            self::XML_PATH_MODULE_ACTIVE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Store Availability Show On Product Page
     *
     * @return boolean
     */
    public function isStoreAvailabilityEnabled()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_SHOW_STORES,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Search With Suggestions
     *
     * @return boolean
     */
    public function isSearchsuggestions()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_SEARCHSUGGESTIONS,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Allow gust customer
     *
     * @return boolean
     */
    public function isAllowGustCustomerEnable()
    {
        return (bool)$this->storeConfig->getValue(
            self::XML_PATH_IS_ALLOW_GEST_CUSTOMER,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Allow Button for suggestion
     *
     * @return boolean
     */
    public function isShowButtonEnable()
    {
        return (bool)$this->storeConfig->getValue(
            self::XML_PATH_SHOW_SUGGESTIONS_BUTTON,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get meta description.
     *
     * @return mixed
     */
    public function getMetaDescription()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_META_DESCRIPTION,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get meta title.
     *
     * @return mixed
     */
    public function getMetaTitle()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_META_TITLE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get meta keywords.
     *
     * @return mixed
     */
    public function getMetaKeywords()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_META_KEYWORDS,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get map config.
     *
     * @return mixed
     */
    public function getMapConfig()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_GOOGLE_MAP_API,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get map config.
     *
     * @return mixed
     */
    public function getCountryConfig()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORE_COUNTRY,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get Marker Image.
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getMarkerImage()
    {
        $_marker_image = $this->storeConfig->getValue(
            self::XML_PATH_GOOGLE_MAP_MARKER_IMAGE,
            ScopeInterface::SCOPE_STORE
        );
        $_marker_image_Url = $this->storeManager->getStore()->getBaseUrl(
            UrlInterface::URL_TYPE_MEDIA
        ). 'magedelight/storelocator/storeinfo/image/' . $_marker_image;
        if (empty($_marker_image)) {
            return '';
        }
        return $_marker_image_Url;
    }

    /**
     * Get default radius.
     *
     * @return mixed
     */
    public function getDefaultRadious()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORESEARCH_DEFAULTRADIOUS,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get max radius.
     *
     * @return mixed
     */
    public function getMaximumRadious()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORESEARCH_MAXRADIOUS,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get distance unit.
     *
     * @return mixed
     */
    public function getDistanceUnit()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORESEARCH_DISTANCEUNIT,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get map style.
     *
     * @return mixed
     */
    public function getMapStyle()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_GOOGLE_MAP_STYLE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get map style theme.
     *
     * @param  mixed|string $themeName
     * @return mixed
     */
    public function getMapStyleTheme($themeName)
    {
        return $this->mapStyle->getMapData($themeName);
    }

    /**
     * Get menu link title.
     *
     * @return mixed
     */
    public function getMenuLinkTitle()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_MENU_LINK_TITLE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get store frontend url.
     *
     * @return mixed
     */
    public function getStoreFrontendUrl()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORE_FRONTEND_URL,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get store page url suffix.
     *
     * @return mixed
     */
    public function getStorePageUrlSuffix()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORE_PAGE_URL_SUFFIX,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get display menu link.
     *
     * @return mixed
     */
    public function getDisplayMenuLink()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_DISPLAY_MENU_LINK,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get store image.
     *
     * @param  string $storeImage
     * @return string|null
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getStoreImage($storeImage)
    {
        $_config_image = $this->storeConfig->getValue(
            self::XML_PATH_STORE_IMAGE,
            ScopeInterface::SCOPE_STORE
        );

        if ($storeImage != '') {
            $url = $this->storeManager->getStore()->getBaseUrl(
                UrlInterface::URL_TYPE_MEDIA
            ).'magedelight/storelocator/storeinfo/image'.$storeImage;
        } elseif ($_config_image != '') {
            $url = $this->storeManager->getStore()->getBaseUrl(
                UrlInterface::URL_TYPE_MEDIA
            ).'magedelight/storelocator/storeinfo/image/'.$_config_image;
        } else {
            $fileId = 'Magedelight_Storelocator::images/store-placeholder.png';
            $params = [
                'area' => 'adminhtml'
            ];
            $asset = $this->assetRepository->createAsset($fileId, $params);

            try {
                return $asset->getUrl();
            } catch (\Exception $e) {
                return null;
            }
        }
        return $url;
    }

    /**
     * Get listing url suffix.
     *
     * @param int $storeId
     * @return mixed
     */
    public function getListingUrlSuffix($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_URL_SUFFIX,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get listing page url.
     *
     * @param int $storeId
     * @return string
     */
    public function getListingPageUrl($storeId = 0)
    {
        $url = $this->storeConfig->getValue(
            self::XML_PATH_STORE_LISTING_URL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );

        if ($suffix = $this->getListingUrlSuffix()) {
            $url .= ".".$suffix;
        }

        return $url;
    }

    /**
     * UnSerialize data
     *
     * @param  string $string
     * @return mixed
     */
    public function unserializeData($string)
    {
        $result = json_decode($string, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            if (is_string($string) && !empty($string)) {
                return $this->serializer->unserialize($string);
            }
            throw new \InvalidArgumentException('Unable to unserialize value.');
        }
        return $result;
    }

    /**
     * Pagination Config: Per Page Store Limit
     *
     * @return mixed
     */
    public function getStorePerPageValue()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORE_PER_PAGE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Pagination Config: Visible tabs of the Pages
     *
     * @return mixed
     */
    public function getVisiblePages()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_PAGINATION_VISIBLE_TAB,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * GetCountryByCode
     *
     * @param string $countryCode
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function getCountryByCode($countryCode)
    {
        $countryName = null;
        $data = $this->countryInformationAcquirerInterface->getCountryInfo($countryCode);
        return $data->getFullNameLocale();
    }

    /**
     * GetRegionByCode
     *
     * @param string $regionId
     * @return string
     * @throws NoSuchEntityException
     */
    public function getRegionById($regionId)
    {   
        $regionName = '';
        
        if($regionId){
            $options = $this->regionOptions->getOptions();
            $regionName = $options[$regionId];
        }

        return $regionName;
    }

    /**
     * Get Store Type
     *
     * @return mixed
     */
    public function getStoreType()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORE_TYPE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get Product On Load Value
     *
     * @return mixed
     */
    public function getStoresValue()
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_STORES_ON_LOAD,
            ScopeInterface::SCOPE_STORE
        );
    }
}
