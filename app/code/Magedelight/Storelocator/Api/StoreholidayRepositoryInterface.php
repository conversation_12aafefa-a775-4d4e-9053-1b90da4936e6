<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Api;

use Magedelight\Storelocator\Api\Data\StoreholidayInterface;
use Magedelight\Storelocator\Api\Data\StoreholidaySearchResultsInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

interface StoreholidayRepositoryInterface
{
    /**
     * Save Storeholiday
     *
     * @param StoreholidayInterface $storeholiday
     * @return StoreholidayInterface
     * @throws LocalizedException
     */
    public function save(
        StoreholidayInterface $storeholiday
    );

    /**
     * Retrieve Storeholiday
     *
     * @param string $storeholidayId
     * @return StoreholidayInterface
     * @throws LocalizedException
     */
    public function getById($storeholidayId);

    /**
     * Retrieve Storeholiday matching the specified criteria.
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return StoreholidaySearchResultsInterface
     * @throws LocalizedException
     */
    public function getList(
        SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Storeholiday
     *
     * @param StoreholidayInterface $storeholiday
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(
        StoreholidayInterface $storeholiday
    );

    /**
     * Delete Storeholiday by ID
     *
     * @param string $storeholidayId
     * @return bool true on success
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById($storeholidayId);
}
