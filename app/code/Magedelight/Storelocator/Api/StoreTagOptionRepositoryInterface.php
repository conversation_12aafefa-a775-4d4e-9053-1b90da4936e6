<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Api;

use Magedelight\Storelocator\Api\Data\StoreTagOptionInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

interface StoreTagOptionRepositoryInterface
{
    /**
     * Save tag attribute option.
     *
     * @param StoreTagOptionInterface $tagValue
     * @return StoreTagOptionInterface
     */
    public function save(Data\StoreTagOptionInterface $tagValue);

    /**
     * Retrieve tag option.
     *
     * @param int $optionId
     * @return StoreTagOptionInterface
     * @throws LocalizedException
     */
    public function getById($optionId);

    /**
     * Delete tag option.
     *
     * @param StoreTagOptionInterface $option
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(Data\StoreTagOptionInterface $option);

    /**
     * Delete tag option by ID.
     *
     * @param int $optionId
     * @return bool true on success
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById($optionId);
}
