<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

interface StoreholidaySearchResultsInterface extends SearchResultsInterface
{
    /**
     * Get Storeholiday list.
     *
     * @return StoreholidayInterface[]
     */
    public function getItems();

    /**
     * Set holiday_id list.
     *
     * @param StoreholidayInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
