<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Api\Data;

interface StoreTagInterface
{
    public const ID                = 'tag_attribute_id';
    public const CODE              = 'tag_attribute_code';
    public const LABEL             = 'tag_attribute_frontend_label';
    public const INPUT             = 'tag_attribute_frontend_input';
    public const REQUIRED          = 'tag_attribute_required';
    public const STORE_LABEL       = 'tag_attribute_label';

    /**
     * Get Tag Attribute ID
     *
     * @return int|null
     */
    public function getTagAttributeId();

    /**
     * Get Tag Attribute Code
     *
     * @return string
     */
    public function getTagAttributeCode();

    /**
     * Get Tag Frontend Label
     *
     * @return string|null
     */
    public function getTagAttributeFrontendLabel();

    /**
     * Get Tag Frontend Input Type
     *
     * @return string|null
     */
    public function getTagAttributeFrontendInput();

    /**
     * Get Tag Attribute Is Required Or Not
     *
     * @return bool|null
     */
    public function getTagAttributeRequired();

    /**
     * Get Tag Attribute Default Label
     *
     * @return string|null
     */
    public function getTagAttributeLabel();

    /**
     * Set Tag Attribute ID
     *
     * @param int $id
     * @return StoreTagInterface
     */
    public function setTagAttributeId($id);

    /**
     * Set Tag Attribute Code
     *
     * @param string $code
     * @return StoreTagInterface
     */
    public function setTagAttributeCode($code);

    /**
     * Set Tag Frontend Label
     *
     * @param string $label
     * @return StoreTagInterface
     */
    public function setTagAttributeFrontendLabel($label);

    /**
     * Set Tag Frontend Input Type
     *
     * @param string $type
     * @return StoreTagInterface
     */
    public function setTagAttributeFrontendInput($type);

    /**
     * Set Tag Attribute Is Required Or Not
     *
     * @param bool|int $required
     * @return StoreTagInterface
     */
    public function setTagAttributeRequired($required);

    /**
     * Set Tag Attribute Default Label
     *
     * @param string $defaultLabel
     * @return StoreTagInterface
     */
    public function setTagAttributeLabel($defaultLabel);
}
