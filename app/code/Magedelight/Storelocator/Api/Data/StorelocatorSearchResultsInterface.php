<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

interface StorelocatorSearchResultsInterface extends SearchResultsInterface
{

    /**
     * Get Storelocator list.
     *
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface[]
     */
    public function getItems();

    /**
     * Set storename list.
     *
     * @param \Magedelight\Storelocator\Api\Data\StorelocatorInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
