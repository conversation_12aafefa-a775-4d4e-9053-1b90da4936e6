<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Api\Data;

use Magento\CatalogRule\Api\Data\ConditionInterface;
use Magento\Framework\Api\ExtensibleDataInterface;

interface StorelocatorInterface extends ExtensibleDataInterface
{

    public const STORELOCATOR_ID = 'storelocator_id';
    public const IS_ACTIVE = 'is_active';
    public const STORENAME = 'storename';
    public const STOREEMAIL = 'storeemail';
    public const STORE_IDS = 'store_ids';
    public const URL_KEY = 'url_key';
    public const WEBSITE_URL = 'website_url';
    public const FACEBOOK_URL = 'facebook_url';
    public const TWITTER_URL = 'twitter_url';
    public const DESCRIPTION = 'description';
    public const ADDRESS = 'address';
    public const COUNTRY_ID = 'country_id';
    public const REGION = 'region';
    public const REGION_ID = 'region_id';
    public const CITY = 'city';
    public const ZIPCODE = 'zipcode';
    public const PHONE_FRONTEND_STATUS = 'phone_frontend_status';
    public const TELEPHONE = 'telephone';
    public const LONGITUDE = 'longitude';
    public const LATITUDE = 'latitude';
    public const STOREIMAGE = 'storeimage';
    public const STORETIME = 'storetime';
    public const META_TITLE = 'meta_title';
    public const META_KEYWORDS = 'meta_keywords';
    public const META_DESCRIPTION = 'meta_description';
    public const KEY_CONDITION   = 'condition';
    public const MSI_SOURCE   = 'msi_source';

    /**
     * Get storelocator_id
     *
     * @return string|null
     */
    public function getStorelocatorId();

    /**
     * Set storelocator_id
     *
     * @param string $storelocatorId
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setStorelocatorId($storelocatorId);

    /**
     * Get store name.
     *
     * @api
     *
     * Get storename
     * @return string|null
     */
    public function getStorename();

    /**
     * Set storename
     *
     * @param string $storename
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setStorename($storename);

    /**
     * Get store email.
     *
     * @api
     *
     * Get storeemail
     * @return string|null
     */
    public function getStoreemail();

    /**
     * Set storeemail
     *
     * @param string $storeemail
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setStoreemail($storeemail);

    /**
     * Get is_active
     *
     * @return string|null
     */
    public function getIsActive();

    /**
     * Set is_active
     *
     * @param string $isActive
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setIsActive($isActive);

    /**
     * Get url_key
     *
     * @return string|null
     */
    public function getUrlKey();

    /**
     * Set url_key
     *
     * @param string $urlKey
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setUrlKey($urlKey);

    /**
     * Get website_url
     *
     * @return string|null
     */
    public function getWebsiteUrl();

    /**
     * Set website_url
     *
     * @param string $websiteUrl
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setWebsiteUrl($websiteUrl);

    /**
     * Get facebook_url
     *
     * @return string|null
     */
    public function getFacebookUrl();

    /**
     * Set facebook_url
     *
     * @param string $facebookUrl
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setFacebookUrl($facebookUrl);

    /**
     * Get twitter_url
     *
     * @return string|null
     */
    public function getTwitterUrl();

    /**
     * Set twitter_url
     *
     * @param string $twitterUrl
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setTwitterUrl($twitterUrl);

    /**
     * Get description
     *
     * @return string|null
     */
    public function getDescription();

    /**
     * Set description
     *
     * @param string $description
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setDescription($description);

    /**
     * Get address
     *
     * @return string|null
     */
    public function getAddress();

    /**
     * Set address
     *
     * @param string $address
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setAddress($address);

    /**
     * Get country_id
     *
     * @return string|null
     */
    public function getCountryId();

    /**
     * Set country_id
     *
     * @param string $countryId
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setCountryId($countryId);

    /**
     * Get region
     *
     * @return string|null
     */
    public function getRegion();

    /**
     * Set region
     *
     * @param string $region
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setRegion($region);

    /**
     * Get region_id
     *
     * @return string|null
     */
    public function getRegionId();

    /**
     * Set region_id
     *
     * @param string $regionId
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setRegionId($regionId);

    /**
     * Get city
     *
     * @return string|null
     */
    public function getCity();

    /**
     * Set city
     *
     * @param string $city
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setCity($city);

    /**
     * Get zipcode
     *
     * @return string|null
     */
    public function getZipcode();

    /**
     * Set zipcode
     *
     * @param string $zipcode
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setZipcode($zipcode);

    /**
     * Get phone_frontend_status
     *
     * @return string|null
     */
    public function getPhoneFrontendStatus();

    /**
     * Set phone_frontend_status
     *
     * @param string $phoneFrontendStatus
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setPhoneFrontendStatus($phoneFrontendStatus);

    /**
     * Get telephone
     *
     * @return string|null
     */
    public function getTelephone();

    /**
     * Set telephone
     *
     * @param string $telephone
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setTelephone($telephone);

    /**
     * Get longitude
     *
     * @return string|null
     */
    public function getLongitude();

    /**
     * Set longitude
     *
     * @param string $longitude
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setLongitude($longitude);

    /**
     * Get latitude
     *
     * @return string|null
     */
    public function getLatitude();

    /**
     * Set latitude
     *
     * @param string $latitude
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setLatitude($latitude);

    /**
     * Get storeimage
     *
     * @return string|null
     */
    public function getStoreimage();

    /**
     * Set storeimage
     *
     * @param string $storeimage
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setStoreimage($storeimage);

    /**
     * Get storetime
     *
     * @return string[]
     */
    public function getStoretime();

    /**
     * Set storetime
     *
     * @param string[] $storetime
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setStoretime($storetime);

    /**
     * Get meta_title
     *
     * @return string|null
     */
    public function getMetaTitle();

    /**
     * Set meta_title
     *
     * @param string $metaTitle
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setMetaTitle($metaTitle);

    /**
     * Get meta_keywords
     *
     * @return string|null
     */
    public function getMetaKeywords();

    /**
     * Set meta_keywords
     *
     * @param string $metaKeywords
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setMetaKeywords($metaKeywords);

    /**
     * Get meta_description
     *
     * @return string|null
     */
    public function getMetaDescription();

    /**
     * Set meta_description
     *
     * @param string $metaDescription
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setMetaDescription($metaDescription);

    /**
     * Get condition.
     *
     * @return Magento\CatalogRule\Api\Data\ConditionInterface|null
     * @since 100.1.0
     */
    public function getConditions();

    /**
     * Set condition.
     *
     * @param Magento\CatalogRule\Api\Data\ConditionInterface $conditions
     * @return $this
     * @since 100.1.0
     */
    public function setConditions($conditions);

    /**
     * Get MSI Source.
     *
     * @api
     *
     * Get msisource
     * @return string|null
     */
    public function getMsiSource();

    /**
     * Set MSI Source
     *
     * @param string $source
     * @return \Magedelight\Storelocator\Api\Data\StorelocatorInterface
     */
    public function setMsiSource($source);
}
