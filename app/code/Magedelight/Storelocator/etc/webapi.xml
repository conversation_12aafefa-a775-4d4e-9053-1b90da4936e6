<?xml version="1.0" ?>
<!--
/**
* @package Magedelight_Storelocator for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->

<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <!-- Save store locator -->
    <route method="POST" url="/V1/magedelight-storelocator/storelocator">
        <service class="Magedelight\Storelocator\Api\StorelocatorRepositoryInterface" method="save"/>
        <resources>
            <resource ref="Magedelight_Storelocator::Storelocator_save"/>
        </resources>
    </route>

    <!-- Get store locator view -->
    <route method="GET" url="/V1/magedelight-storelocator/storelocator/search">
        <service class="Magedelight\Storelocator\Api\StorelocatorRepositoryInterface" method="getList"/>
        <resources>
            <resource ref="Magedelight_Storelocator::Storelocator_view"/>
        </resources>
    </route>

    <!-- Get store locator view by id -->
    <route method="GET" url="/V1/magedelight-storelocator/storelocator/:storelocatorId">
        <service class="Magedelight\Storelocator\Api\StorelocatorRepositoryInterface" method="getById"/>
        <resources>
            <resource ref="Magedelight_Storelocator::Storelocator_view"/>
        </resources>
    </route>

    <!-- Delete store locator -->
    <route method="DELETE" url="/V1/magedelight-storelocator/storelocator/:storelocatorId">
        <service class="Magedelight\Storelocator\Api\StorelocatorRepositoryInterface" method="deleteById"/>
        <resources>
            <resource ref="Magedelight_Storelocator::Storelocator_delete"/>
        </resources>
    </route>
</routes>
