<?xml version="1.0"?>
<!--
/**
* @package Magedelight_Storelocator for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->
<csp_whitelist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Csp/etc/csp_whitelist.xsd">
    <policies>
        <policy id="script-src">
            <values>
                <!-- Allow scripts from maps.googleapis.com -->
                <value id="google-maps" type="host">https://maps.googleapis.com</value>
            </values>
        </policy>
        <policy id="connect-src">
            <values>
                <!-- Add domain to connect-src whitelist -->
                <value id="google-maps-connect" type="host">https://maps.googleapis.com</value>
            </values>
        </policy>
        <policy id="img-src">
            <values>
                <!-- Allow scripts from maps.googleapis.com -->
                <value id="google-maps" type="host">https://maps.googleapis.com</value>
            </values>
        </policy>
    </policies>
</csp_whitelist>
