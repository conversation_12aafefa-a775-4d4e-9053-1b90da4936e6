<?xml version="1.0" ?>
<!--
/**
* @package Magedelight_Storelocator for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magedelight\Storelocator\Api\StorelocatorRepositoryInterface" type="Magedelight\Storelocator\Model\StorelocatorRepository"/>
    <preference for="Magedelight\Storelocator\Api\Data\StorelocatorInterface" type="Magedelight\Storelocator\Model\Data\Storelocator"/>
    <preference for="Magedelight\Storelocator\Api\Data\StorelocatorSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
    <preference for="Magedelight\Storelocator\Api\StoreInformationManagementInterface" type="Magedelight\Storelocator\Model\StoreInformationManagement"/>

    <!--Holiday -->
    <preference for="Magedelight\Storelocator\Api\StoreholidayRepositoryInterface" type="Magedelight\Storelocator\Model\StoreholidayRepository"/>
    <preference for="Magedelight\Storelocator\Api\Data\StoreholidayInterface" type="Magedelight\Storelocator\Model\Data\Storeholiday"/>
    <preference for="Magedelight\Storelocator\Api\Data\StoreholidaySearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>

    <preference for="Magedelight\Storelocator\Api\StoreTagRepositoryInterface" type="Magedelight\Storelocator\Model\TagAttributeRepository"/>
    <preference for="Magedelight\Storelocator\Api\Data\StoreTagInterface" type="Magedelight\Storelocator\Model\TagAttribute"/>
    <preference for="Magedelight\Storelocator\Api\Data\StoreTagSearchResultInterface" type="Magento\Framework\Api\SearchResults"/>

    <preference for="Magedelight\Storelocator\Api\StoreTagValueRepositoryInterface" type="Magedelight\Storelocator\Model\TagAttributeValueRepository"/>
    <preference for="Magedelight\Storelocator\Api\Data\StoreTagValueInterface" type="Magedelight\Storelocator\Model\TagAttributeValue"/>
    <preference for="Magedelight\Storelocator\Api\Data\StoreTagValueSearchResultInterface" type="Magento\Framework\Api\SearchResults"/>

    <preference for="Magedelight\Storelocator\Api\StoreTagOptionRepositoryInterface" type="Magedelight\Storelocator\Model\TagAttributeOptionRepository"/>
    <preference for="Magedelight\Storelocator\Api\Data\StoreTagOptionInterface" type="Magedelight\Storelocator\Model\TagAttributeOption"/>

    <!-- stores -->
    <virtualType name="MagedelightStoreLocatorFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="Magedelight\Storelocator\Model\ResourceModel\Storelocator\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_storelocator</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Storelocator\Model\ResourceModel\Storelocator</argument>
        </arguments>
    </virtualType>

    <virtualType name="MagedelightStoreLocatorFilterPool" type="Magedelight\Storelocator\Ui\Component\DataProviderLocator">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Magedelight\Storelocator\Model\ResourceModel\Storelocator\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">MagedelightStoreLocatorFilterPool</argument>
        </arguments>
    </virtualType>

    <type name="Magedelight\Storelocator\Ui\Component\DataProviderLocator">
        <arguments>
            <argument name="additionalFilterPool" xsi:type="array">
                <item name="fulltext" xsi:type="object">Magedelight\Storelocator\Ui\Component\Page\FulltextFilterLocator</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="managestores_storelocator_listing_data_source" xsi:type="string">Magedelight\Storelocator\Model\ResourceModel\Storelocator\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <!-- Holiday -->
    <virtualType name="MagedelightStoreHolidayFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="Magedelight\Storelocator\Model\ResourceModel\Storeholiday\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_store_holiday</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Storelocator\Model\ResourceModel\Storeholiday\Collection</argument>
        </arguments>
    </virtualType>

     <virtualType name="MagedelightStoreHolidayFilterPool" type="Magedelight\Storelocator\Ui\Component\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Magedelight\Storelocator\Model\ResourceModel\Storeholiday\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">MagedelightStoreHolidayFilterPool</argument>
        </arguments>
    </virtualType>

    <type name="Magedelight\Storelocator\Ui\Component\DataProvider">
        <arguments>
            <argument name="additionalFilterPool" xsi:type="array">
                <item name="fulltext" xsi:type="object">Magedelight\Storelocator\Ui\Component\Page\FulltextFilter</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="managestores_storeholiday_listing_data_source" xsi:type="string">Magedelight\Storelocator\Model\ResourceModel\Storeholiday\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <!-- Store image upload configuration -->
    <virtualType name="Magedelight\Storelocator\StoreImageUploader" type="Magedelight\Storelocator\Model\Uploader">
        <arguments>
            <argument name="baseTmpPath" xsi:type="const">Magedelight\Storelocator\Model\Uploader::IMAGE_TMP_PATH</argument>
            <argument name="basePath" xsi:type="const">Magedelight\Storelocator\Model\Uploader::IMAGE_PATH</argument>
            <argument name="allowedExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Magedelight\Storelocator\Model\UploaderPool">
        <arguments>
            <argument name="uploaders" xsi:type="array">
                <item name="image" xsi:type="string">Magedelight\Storelocator\StoreImageUploader</item>
            </argument>
        </arguments>
    </type>
    <type name="Magedelight\Storelocator\Controller\Adminhtml\Storelocator\Upload">
        <arguments>
            <argument name="uploader" xsi:type="object">Magedelight\Storelocator\StoreImageUploader</argument>
        </arguments>
    </type>
    <type name="Magedelight\Storelocator\Controller\Adminhtml\Storelocator\UploadMarker">
        <arguments>
            <argument name="markerUploader" xsi:type="object">Magedelight\Storelocator\StoreImageUploader</argument>
        </arguments>
    </type>
    <type name="Magedelight\Storelocator\Ui\Component\Listing\Column\Image">
        <arguments>
            <argument name="imageModel" xsi:type="object">Magedelight\Storelocator\StoreImageUploader</argument>
        </arguments>
    </type>
    <!-- Add Plugin for add store list page link in navigation -->
    <type name="Magento\Theme\Block\Html\Topmenu">
        <plugin name="add_menu_item_plugin" type="Magedelight\Storelocator\Plugin\TopMenuLink" sortOrder="10" disabled="false"/>
    </type>

    <!--Store Tag -->
    <virtualType name="MagedelightStoreTagFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="magedelight_storetag_listing_data_source" xsi:type="string">Magedelight\Storelocator\Model\ResourceModel\TagAttribute\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

     <virtualType name="MagedelightStoreTagFilterPool" type="Magedelight\Storelocator\Ui\Component\DataProviderTag">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Magedelight\Storelocator\Model\ResourceModel\TagAttribute\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">MagedelightStoreTagFilterPool</argument>
        </arguments>
    </virtualType>

    <type name="Magedelight\Storelocator\Ui\Component\DataProviderTag">
        <arguments>
            <argument name="additionalFilterPool" xsi:type="array">
                <item name="fulltext" xsi:type="object">Magedelight\Storelocator\Ui\Component\Page\FulltextFilterTag</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="Magedelight\Storelocator\Model\ResourceModel\TagAttribute\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_storelocator_tag_attribute</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Storelocator\Model\ResourceModel\TagAttribute\Collection</argument>
        </arguments>
    </virtualType>
</config>
