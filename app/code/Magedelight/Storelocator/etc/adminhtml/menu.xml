<?xml version="1.0"?>
<!--
/**
* @package Magedelight_Storelocator for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Magedelight_Storelocator::storelocator_root"
             title="Store Locator"
             module="Magedelight_Storelocator"
             sortOrder="50"
             resource="Magedelight_Storelocator::root"
             toolTip="magedelight_base" />

        <add id="Magedelight_Storelocator::storelocator_root_commonlyvisible"
             title="Store Locator"
             module="Magedelight_Storelocator"
             sortOrder="50"
             parent="Magedelight_Base::md_modules"
             resource="Magedelight_Storelocator::root" />

        <add id="Magedelight_Storelocator::managestore_root"
             title="Manage Stores"
             module="Magedelight_Storelocator"
             sortOrder="600"
             action="managestores/storelocator/"
             parent="Magedelight_Storelocator::storelocator_root"
             resource="Magedelight_Storelocator::managestore_root" />

        <add id="Magedelight_Storelocator::storeholiday_root"
             title="Manage Holiday"
             module="Magedelight_Storelocator"
             sortOrder="600"
             action="managestores/storeholiday/"
             parent="Magedelight_Storelocator::storelocator_root"
             resource="Magedelight_Storelocator::storeholiday_root" />

        <add id="Magedelight_Storelocator::storetag_root"
             title="Manage Store Tag"
             module="Magedelight_Storelocator"
             sortOrder="600"
             action="managestores/storetag/"
             parent="Magedelight_Storelocator::storelocator_root"
             resource="Magedelight_Storelocator::storetag_root" />

        <add id="Magedelight_Storelocator::config_root"
             title="Configuration"
             module="Magedelight_Storelocator"
             sortOrder="600"
             action="adminhtml/system_config/edit/section/magedelight_storelocator"
             parent="Magedelight_Storelocator::storelocator_root"
             resource="Magedelight_Storelocator::config_root" />

        <add id="Magedelight_Storelocator::useful_links"
             title="Useful Links"
             module="Magedelight_Storelocator"
             sortOrder="999"
             parent="Magedelight_Storelocator::storelocator_root"
             resource="Magedelight_Storelocator::root" />

        <add id="Magedelight_Storelocator::documentation"
             title="Documentation"
             module="Magedelight_Storelocator"
             sortOrder="10"
             target="_blank"
             parent="Magedelight_Storelocator::useful_links"
             resource="Magedelight_Storelocator::root" />
    </menu>
</config>
