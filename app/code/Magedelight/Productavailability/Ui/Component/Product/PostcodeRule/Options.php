<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Ui\Component\Product\PostcodeRule;

use Magento\Framework\Data\OptionSourceInterface;

class Options implements OptionSourceInterface
{
    /**
     * @var \Magedelight\Productavailability\Model\PostcodeRuleRepository
     */
    protected $postcodeRuleRepository;
    
    /**
     * @var \Magento\Framework\Api\SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    /**
     * Constructor
     *
     * @param \Magedelight\Productavailability\Model\Postcode $postcodeRuleRepository
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        \Magedelight\Productavailability\Model\PostcodeRuleRepository $postcodeRuleRepository,
        \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
        $this->postcodeRuleRepository = $postcodeRuleRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }
    
    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        $searchCriteria = $this->searchCriteriaBuilder->create();
        $getRule = $this->postcodeRuleRepository->getList($searchCriteria)->getItems();
        
        $options = [];
        foreach ($getRule as $value) {
            $options[] = [
                'label' => $value->getTitle(),
                'value' => $value->getRuleId(),
            ];
        }
        
        return $options;
    }
}
