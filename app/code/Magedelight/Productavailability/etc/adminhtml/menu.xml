<?xml version="1.0"?>
<!--
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <!-- Product Availability -->
        <add id="Magedelight_Productavailability::productavailability_root"
             title="Delivery Check"
             module="Magedelight_Productavailability"
             sortOrder="45"
             resource="Magedelight_Productavailability::root"
             toolTip="magedelight_base"/>
        <add id="Magedelight_Productavailability::productavailability_root_commonlyvisible"
             title="Delivery Check"
             module="Magedelight_Productavailability"
             sortOrder="10"
             parent="Magedelight_Base::md_modules"
             resource="Magedelight_Productavailability::root"/>

        <add id="Magedelight_Productavailability::productavailability_main"
             title="Postcode"
             module="Magedelight_Productavailability"
             sortOrder="60"
             parent="Magedelight_Productavailability::productavailability_root"
             resource="Magedelight_Productavailability::productavailability_main"/>

        <add id="Magedelight_Productavailability::postcode"
             title="Manage Postcode"
             module="Magedelight_Productavailability"
             sortOrder="10"
             parent="Magedelight_Productavailability::productavailability_main"
             action="productavailability/postcode/index"
             resource="Magedelight_Productavailability::postcode"/>

        <add id="Magedelight_Productavailability::import_postcode"
             title="Import Postcode"
             module="Magedelight_Productavailability"
             sortOrder="20"
             parent="Magedelight_Productavailability::productavailability_main"
             action="productavailability/import/index"
             resource="Magedelight_Productavailability::import_postcode"/>
            
        <add id="Magedelight_Productavailability::postcode_rule"
             title="Postcode Rule"
             module="Magedelight_Productavailability"
             sortOrder="30"
             parent="Magedelight_Productavailability::productavailability_main"
             action="productavailability/postcoderule/index"
             resource="Magedelight_Productavailability::postcode_rule"/>

        <!-- Product Availability Report -->
        <add id="Magedelight_Productavailability::productavailability_report"
             title="Reports"
             module="Magedelight_Productavailability"
             sortOrder="100"
             parent="Magedelight_Productavailability::productavailability_root"
             resource="Magedelight_Productavailability::productavailability_report"/>

        <add id="Magedelight_Productavailability::allpostcode"
             title="Postcode Search"
             module="Magedelight_Productavailability"
             sortOrder="10"
             parent="Magedelight_Productavailability::productavailability_report"
             action="productavailability/reports/allpostcode"
             resource="Magedelight_Productavailability::allpostcode"/>

        <add id="Magedelight_Productavailability::productpostcode"
             title="Product Wise Postcode"
             module="Magedelight_Productavailability"
             sortOrder="20"
             parent="Magedelight_Productavailability::productavailability_report"
             action="productavailability/reports/productpostcode"
             resource="Magedelight_Productavailability::productpostcode"/>

        <add id="Magedelight_Productavailability::notfound"
             title="Undeliverable Postcodes (Customer Search)"
             module="Magedelight_Productavailability"
             sortOrder="30"
             parent="Magedelight_Productavailability::productavailability_report"
             action="productavailability/reports/notfound"
             resource="Magedelight_Productavailability::notfound"/>

        <!-- Product Availability Configuration-->
        <add id="Magedelight_Productavailability::productavailability_config"
             title="Settings"
             module="Magedelight_Productavailability"
             sortOrder="110"
             parent="Magedelight_Productavailability::productavailability_root"
             resource="Magedelight_Productavailability::productavailability_config"/>

        <add id="Magedelight_Productavailability::settings"
             title="Configuration"
             module="Magedelight_Productavailability"
             sortOrder="10"
             parent="Magedelight_Productavailability::productavailability_config"
             action="adminhtml/system_config/edit/section/productavailability"
             resource="Magedelight_Productavailability::settings"/>

        <!-- Documentation -->
        <add id="Magedelight_Productavailability::useful_links"
             title="Useful Links"
             module="Magedelight_Productavailability"
             sortOrder="999"
             parent="Magedelight_Productavailability::productavailability_root"
             resource="Magedelight_Productavailability::productavailability_root" />

        <add id="Magedelight_Productavailability::documentation"
             title="Documentation"
             module="Magedelight_Productavailability"
             sortOrder="10"
             target="_blank"
             parent="Magedelight_Productavailability::useful_links"
             resource="Magedelight_Productavailability::productavailability_root" />
    </menu>
</config>