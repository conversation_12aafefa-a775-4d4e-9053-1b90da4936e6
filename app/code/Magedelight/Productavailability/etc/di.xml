<?xml version="1.0" ?>
<!--
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magedelight\Productavailability\Api\PostcodeRepositoryInterface" type="Magedelight\Productavailability\Model\PostcodeRepository" />
    <preference for="Magedelight\Productavailability\Api\Data\PostcodeInterface" type="Magedelight\Productavailability\Model\Postcode" />
    
    <preference for="Magedelight\Productavailability\Api\PostcodeRuleRepositoryInterface" type="Magedelight\Productavailability\Model\PostcodeRuleRepository"/>
    <preference for="Magedelight\Productavailability\Api\Data\PostcodeRuleInterface" type="Magedelight\Productavailability\Model\PostcodeRule"/>
    <preference for="Magedelight\Productavailability\Api\Data\PostcodeRuleSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
    
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="productavailability_postcoderule_listing_data_source" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\PostcodeRule\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    
    <type name="Magedelight\Productavailability\Model\ResourceModel\PostcodeRule\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="const">Magedelight\Productavailability\Model\ResourceModel\PostcodeRule::TBL_RULE</argument>
            <argument name="eventPrefix" xsi:type="string">productavailability_postcoderule_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">productavailability_postcoderule_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\PostcodeRule</argument>
        </arguments>
    </type>
    
    <!-- Start Postcodes admin grid -->
    <virtualType name="Magedelight\Productavailability\Model\ResourceModel\Postcode\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">md_postcode</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\Postcode</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="productavailability_postcode_grid_data_source" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\Postcode\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <!-- End postcodes admin grid -->
    
    <!-- Start Postcode Reports admin grid -->
    <type name="Magedelight\Productavailability\Model\ResourceModel\PostcodeReport\Grid\AllCollection">
        <arguments>
            <argument name="mainTable" xsi:type="string">md_postcode_report</argument>
            <argument name="eventPrefix" xsi:type="string">md_postcode_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">md_grid_postcode_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\PostcodeReport</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="productavailability_postcode_allpostcode_grid_data_source" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\PostcodeReport\Grid\AllCollection</item>
            </argument>
        </arguments>
    </type>

    <!-- Start Second  Report -->
    <type name="Magedelight\Productavailability\Model\ResourceModel\PostcodeReport\Grid\ProductCollection" >
        <arguments>
            <argument name="mainTable" xsi:type="string">md_postcode_report</argument>
            <argument name="eventPrefix" xsi:type="string">md_postcode_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">md_grid_postcode_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\PostcodeReport</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="productavailability_postcode_productpostcode_grid_data_source" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\PostcodeReport\Grid\ProductCollection</item>
            </argument>
        </arguments>
    </type>
    <!-- End Second  Report -->

    <!-- Start Third  Report -->
    <type name="Magedelight\Productavailability\Model\ResourceModel\PostcodeReport\Grid\NotFoundCollection">
        <arguments>
            <argument name="mainTable" xsi:type="string">md_postcode_report</argument>
            <argument name="eventPrefix" xsi:type="string">md_postcode_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">md_grid_postcode_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\PostcodeReport</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="productavailability_postcode_notfound_grid_data_source" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\PostcodeReport\Grid\NotFoundCollection</item>
            </argument>
        </arguments>
    </type>
    <!-- End Third  Report -->
    <!-- End Postcode Reports admin grid-->
    
    <!-- Start Display Postcodes list grid to product tab in admin -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="product_postcode_listing_data_source" xsi:type="string">Magedelight\Productavailability\Model\ResourceModel\Postcode\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <!-- End Display Postcodes list grid to product tab in admin -->
    
    <!-- checkout time shipping method -->
    <type name="Magento\Shipping\Model\Rate\Result">
        <plugin name="magedelight_productavailability_restrict_shipping" type="Magedelight\Productavailability\Plugin\Shipping\Rate\Result\Append" />
         <plugin name="magedelight_productavailability_restrict_shipping_rate" type="Magedelight\Productavailability\Plugin\Shipping\Rate\Result\GetAllRates" /> 
    </type>
    
    <!-- added postcode import CLI -->
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="magedelight_postcode_import" xsi:type="object">Magedelight\Productavailability\Console\Command\ImportCommand</item>
                <item name="magedelight_postcode_export" xsi:type="object">Magedelight\Productavailability\Console\Command\ExportCommand</item>
                <item name="magedelight_postcode_update" xsi:type="object">Magedelight\Productavailability\Console\Command\UpdateCommand</item>
            </argument>
        </arguments>
    </type>
</config>