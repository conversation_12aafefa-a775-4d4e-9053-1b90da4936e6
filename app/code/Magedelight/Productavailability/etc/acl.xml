<?xml version="1.0"?>
<!--
 /**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magedelight_Base::root" title="Magedelight" sortOrder="51">
                    <resource id="Magedelight_Base::md_modules">
                        <resource id="Magedelight_Productavailability::root" title="Delivery Check" sortOrder="52">
                            <resource id="Magedelight_Productavailability::productavailability_main" title="Postcode" sortOrder="60">
                                <resource id="Magedelight_Productavailability::postcode" title="Manage Postcode" sortOrder="10" />
                                <resource id="Magedelight_Productavailability::import_postcode" title="Import Postcode" sortOrder="20" />
                                <resource id="Magedelight_Productavailability::postcode_rule" title="Postcode Rule" sortOrder="30" />
                                <resource id="Magedelight_Productavailability::postcode_rule_save" sortOrder="10" title="Save postcode_rule"/>
                                <resource id="Magedelight_Productavailability::postcode_rule_delete" sortOrder="20" title="Delete postcode_rule"/>
                                <resource id="Magedelight_Productavailability::postcode_rule_update" sortOrder="30" title="Update postcode_rule"/>
                                <resource id="Magedelight_Productavailability::postcode_rule_view" sortOrder="40" title="View postcode_rule"/>
                            </resource>
                            <resource id="Magedelight_Productavailability::productavailability_report" title="Reports" sortOrder="100">
                                <resource id="Magedelight_Productavailability::allpostcode" title="Postcode Search" sortOrder="10" />
                                <resource id="Magedelight_Productavailability::productpostcode" title="Product Wise Postcode" sortOrder="20" />
                                <resource id="Magedelight_Productavailability::notfound" title="Undeliverable Postcodes (Customer Search)" sortOrder="30" />
                            </resource>
                            <resource id="Magedelight_Productavailability::productavailability_config" title="Settings" sortOrder="110">
                                <resource id="Magedelight_Productavailability::settings" title="Configuration" sortOrder="10" />
                            </resource>
                        </resource>
                    </resource>
                </resource>
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Magedelight_Base::config_root">
                                <resource id="Magedelight_Productavailability::config_root" title="Delivery Check" sortOrder="10" />
                            </resource>
                        </resource>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
