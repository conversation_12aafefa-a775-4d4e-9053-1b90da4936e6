<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\Model\ResourceModel\Db\Context;
use Magento\Framework\ObjectManagerInterface;
use Magento\Framework\Exception\CouldNotSaveException;

class ProductRule extends AbstractDb
{
    public const TBL_POSTCODE = 'md_postcode';
    public const TBL_RULE_POSTCODES = 'md_rule_postcodes';
    public const TBL_RULE_PRODUCTS = 'md_rule_products';
    public const TBL_RULE = 'md_postcode_rule';
    /**
     * @var \Magento\Framework\ObjectManagerInterface
     */
    protected $objectManager = null;

    /**
     * @param Context $context
     * @param ObjectManagerInterface $objectManager
     */
    public function __construct(
        Context $context,
        ObjectManagerInterface $objectManager
    ) {
        $this->objectManager = $objectManager;
        parent::__construct($context);
    }

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(self::TBL_RULE_PRODUCTS, 'product_id');
    }

    /**
     * Save Postcode Rule with Product ID in `md_rule_products`
     *
     * @param int $productId
     * @param int $ruleId
     * @param int $storeId
     * @return boolean
     */
    public function saveWithProduct($productId = 0, $ruleId = 0, $storeId = 0)
    {
        if (!$productId || !$ruleId) {
            return false;
        }

        $table = $this->getTable(self::TBL_RULE_PRODUCTS);
        $data = ['product_id' => $productId, 'rule_id' => $ruleId, 'store_id' => $storeId];

        $update = false;
        if ($this->isProductRule($productId, $storeId)) {
            $update = true;
        }

        try {
            if ($update) {
                $this->getConnection()->update($table, $data, ['product_id = ?' => (int) $productId]);
            } else {
                $this->getConnection()->insert($table, $data);
            }
        } catch (\Exception $ex) {
            throw new CouldNotSaveException(__(
                'Could not save the Product Rule: %1',
                $ex->getMessage()
            ));
        }

        return false;
    }

    /**
     * Check relation exist or not
     *
     * @param int $productId
     * @param int $storeId
     * @return mixed
     */
    public function isProductRule($productId = 0, $storeId = 0)
    {
        $select = $this->getConnection()->select()
            ->from(
                $this->getTable(self::TBL_RULE_PRODUCTS),
                'product_id'
            )->where(
                'product_id = ?',
                (int) $productId
            )->where(
                'store_id = ?',
                (int) $storeId
            );

        return $this->getConnection()->fetchOne($select);
    }

    /**
     * GetProductRuleSelect
     *
     * @param type $productId
     * @return type
     */
    private function productRuleSelect($productId)
    {
        return $select = $this->getConnection()->select()
            ->from(
                $this->getTable(self::TBL_RULE_PRODUCTS),
                'rule_id'
            )->where(
                'product_id = ?',
                (int) $productId
            );
    }

    /**
     * Check relation exist or not
     *
     * @param int $productId
     * @param int $storeId
     * @return mixed
     */
    public function getPostcodeRuleId($productId = 0, $storeId = 0)
    {
        $select = $this->productRuleSelect($productId)->where('store_id = ?', (int) $storeId);
        $id = $this->getConnection()->fetchOne($select);

        if (!empty($id)) {
            return $id;
        }

        $selectDefaultStore = $this->productRuleSelect($productId)->where('store_id = ?', 0);
        return $this->getConnection()->fetchOne($selectDefaultStore);
    }

    /**
     * Retrieve Postcode Related Information
     *
     * @param int $productId
     * @param int $storeId
     * @param string $postcode
     * @param string $columName
     *
     * <code>
     * SELECT
     *      `rproduct`.*,`postcode`.postcode, `postcode`.is_zip_range,
     *      `postcode`.zip_to, `postcode`.zip_from,
     *      `postcode`.shipping_info, `postcode`.is_shipping
     * FROM `md_rule_products` AS `rproduct`
     *   INNER JOIN `md_rule_postcodes` AS `rpostcode`
     *   ON `rproduct`.rule_id = `rpostcode`.rule_id
     *   INNER JOIN `md_postcode` AS `postcode`
     *   ON `rpostcode`.postcode_id = `postcode`.postcode_id
     *   AND  `postcode`.zip_from <= '$postcode' AND `postcode`.zip_to >= '$postcode'
     * WHERE product_id = $productId
     * </code>
     *
     * @return array $data
     */
    public function getPostcodeInfo($productId = 0, $storeId = 0, $postcode = null, $columName = null)
    {
        // define table & table alias
        $tblRuleProduct = $this->getTable(self::TBL_RULE_PRODUCTS);
        $tblRuleProductAs = 'rproduct';

        $tblRuleZipcode = $this->getTable(self::TBL_RULE_POSTCODES);
        $tblRuleZipcodeAs = 'rpostcode';

        $tblZipcode = $this->getTable(self::TBL_POSTCODE);
        $tblZipcodeAs = 'postcode';

        $tblZipcodeRule = $this->getTable(self::TBL_RULE);
        $tblZipcodeRuleAs = 'postcoderule';

        // Where condition
        $postcodeCondition = '';
        $postcodeRangeCondition = '';

        if (preg_match("/^[0-9]+$/", $postcode)) {
            $postcodeRangeCondition = $tblZipcodeAs . '.zip_from <= ' . $postcode .
            ' AND ' . $tblZipcodeAs . '.zip_to >= ' . $postcode;
            $postcodeCondition = $tblZipcodeAs . '.postcode = ' . $postcode . ' || ';
        } else {
            $postcodeCondition = $tblZipcodeAs . '.postcode = "' . $postcode . '" ';
        }

        // Set inner join codition
        $rulePostcodeJoin = $tblRuleProductAs . '.rule_id = ' . $tblRuleZipcodeAs . '.rule_id';
        $postcodeJoin = $tblRuleZipcodeAs . '.postcode_id = ' . $tblZipcodeAs . '.postcode_id'
            . ' AND (' . $postcodeCondition . $postcodeRangeCondition . ')';

        $selectedColumns = [
            'postcode', 'is_zip_range', 'zip_from', 'zip_to',
            'shipping_info', 'is_shipping', 'shipping_methods',
            'payment_methods', 'is_payment_restricted'
        ];

        if ($columName) {
            $selectedColumns = [$columName];
        }

        //Check whether current product rule have assign all post code?
        $select = $this->getConnection()->select()->from(
            [$tblRuleProductAs => $tblRuleProduct]
        )->join(
            [$tblZipcodeRuleAs => $tblZipcodeRule],
            $tblZipcodeRuleAs.'.rule_id = '.$tblRuleProductAs.'.rule_id'
        )->where(
            $tblRuleProductAs . '.product_id = ?',
            (int) $productId
        )->having('assing_all_zipcodes = ?', 1);

        //Either not assign all post code to rule then execute if condition nor else execute
        if (!$select->query()->rowCount() && preg_match("/^[0-9]+$/", $postcode)) {
            # $productId = 2; // for testing purpose
            // Select SQL
            $select = $this->getConnection()->select()->from(
                [$tblRuleProductAs => $tblRuleProduct]
            )->join(
                [$tblRuleZipcodeAs => $tblRuleZipcode],
                $rulePostcodeJoin,
                'postcode_id'
            )->join(
                [$tblZipcodeAs => $tblZipcode],
                $postcodeJoin,
                $selectedColumns
            )->where(
                $tblRuleProductAs . '.product_id = ?',
                (int)$productId
            )->where(
                $tblZipcodeAs . '.status = ?',
                1
            );
            
            return $this->selectByStoreId($select, $tblRuleProductAs, $tblZipcodeAs, $storeId, $productId);
        } else {
            $postcodeJoin = '(' . $postcodeCondition . $postcodeRangeCondition . ')';

            $select = $this->getConnection()->select()->from(
                [$tblRuleProductAs => $tblRuleProduct]
            )->join(
                [$tblZipcodeRuleAs => $tblZipcodeRule],
                $tblRuleProductAs . '.rule_id = ' . $tblZipcodeRuleAs . '.rule_id',
                'rule_id'
            )->join(
                [$tblZipcodeAs => $tblZipcode],
                $postcodeJoin,
                $selectedColumns
            )->where(
                $tblRuleProductAs . '.product_id = ?',
                (int)$productId
            )->where(
                $tblZipcodeAs . '.status = ?',
                1
            );

            return $this->selectByStoreId($select, $tblRuleProductAs, $tblZipcodeAs, $storeId, $productId);
        }
    }

    /**
     * Get Rule By Store
     *
     * @param int $storeId
     * @param int $productId
     */
    private function getRuleByStore($storeId = 0, $productId = 0)
    {

        // define table & table alias
        $tblRuleProduct = $this->getTable(self::TBL_RULE_PRODUCTS);
        $tblRuleProductAs = 'rproduct';

        $tblRuleZipcode = $this->getTable(self::TBL_RULE_POSTCODES);
        $tblRuleZipcodeAs = 'rpostcode';

        $tblZipcode = $this->getTable(self::TBL_POSTCODE);
        $tblZipcodeAs = 'postcode';

        // Set inner join codition
        $rulePostcodeJoin = $tblRuleProductAs . '.rule_id = ' . $tblRuleZipcodeAs . '.rule_id';
        $postcodeJoin = $tblRuleZipcodeAs . '.postcode_id = ' . $tblZipcodeAs . '.postcode_id';

        $selectedColumns = [
            'postcode', 'is_zip_range', 'zip_from', 'zip_to',
            'shipping_info', 'is_shipping', 'shipping_methods',
            'payment_methods', 'is_payment_restricted'
        ];

        // Select SQL
        $select = $this->getConnection()->select()->from(
            [$tblRuleProductAs => $tblRuleProduct]
        )->join(
            [$tblRuleZipcodeAs => $tblRuleZipcode],
            $rulePostcodeJoin,
            'postcode_id'
        )->join(
            [$tblZipcodeAs => $tblZipcode],
            $postcodeJoin,
            $selectedColumns
        )->where(
            $tblRuleProductAs . '.product_id = ?',
            (int) $productId
        )->where(
            $tblZipcodeAs . '.status = ?',
            1
        )->where($tblRuleProductAs . '.store_id = ?', (int) $storeId);

        return $this->getConnection()->fetchRow($select);
    }

    /**
     * Select By Store Id
     *
     * @param mixed $select
     * @param mixed $tblRuleProductAs
     * @param mixed $tblZipcodeAs
     * @param int $storeId
     * @param int $productId
     */
    private function selectByStoreId($select, $tblRuleProductAs, $tblZipcodeAs, $storeId, $productId)
    {
        if ($this->getRuleByStore($storeId, $productId)) {
            $select->where($tblRuleProductAs . '.store_id = ?', (int) $storeId);
            $select->order($tblZipcodeAs . '.is_zip_range ASC');
            return $this->getConnection()->fetchRow($select);
        }

        $select1 = clone $select;
        $select1->where($tblRuleProductAs . '.store_id = ?', (int) $storeId);
        $select1->order($tblZipcodeAs . '.is_zip_range ASC');

        $row = $this->getConnection()->fetchRow($select1);

        if (!empty($row)) {
            return $row;
        }

        if ($storeId >= 0) {
            $select->where($tblRuleProductAs . '.store_id = ?', 0);
            $select->order($tblZipcodeAs . '.is_zip_range ASC');
        }

        return $this->getConnection()->fetchRow($select);
    }

    /**
     * Get Product Rule Post Code
     *
     * @param int $productId
     * @param int $ruleId
     * @param array $postcodes
     * @return array postcodes
     */
    public function getProductRulePostcode($productId = 0, $ruleId = 0, $postcodes = [])
    {
        $tblRuleZipcode = $this->getTable(self::TBL_RULE_POSTCODES);
        $tblZipcode = $this->getTable(self::TBL_POSTCODE);

        foreach ($postcodes as $postcode) {
            $postcodeWhereArr[] = "(zip_from <= $postcode AND zip_to >= $postcode)";
        }

        $select = $this->getConnection()->select()->from(['pr' => $this->getMainTable()], 'pr.product_id')
            ->join(['rl' => $tblRuleZipcode], 'pr.rule_id = rl.rule_id', 'rl.postcode_id')
            ->join(['pc' => $tblZipcode], 'rl.postcode_id = pc.postcode_id', ['pc.postcode','pc.is_zip_range'])
            ->where('pr.rule_id = ?', (int) $ruleId)
            ->where('pr.product_id = ?', (int) $productId)
            ->where('pc.status = ?', 1)
            ->where('pc.postcode IN (?) OR '.implode(' OR ', $postcodeWhereArr), $postcodes);

        $data = $this->getConnection()->fetchAll($select);
        return $this->getPostcodeData($data, $postcodes);
    }

    /**
     * Get Postcode Data
     *
     * @param array $data
     * @param array $postcodes
     * @return array
     */
    private function getPostcodeData($data, $postcodes = [])
    {
        $result = [];
        if (!$data) {
            return $result;
        }

        foreach ($data as $raw) {
            $isRange  = $raw['is_zip_range'];
            $postcode = $raw['postcode'];

            $result[$postcode] = $raw;
            if ($isRange) {
                $result[$postcode]['postcode'] = $this->inRangePostcode($postcode, $postcodes);
            }
        }

        return $result;
    }

    /**
     * Return matched postcode
     *
     * @param string $postcode
     * @param array $postcodes
     * @return string|integer
     */
    private function inRangePostcode($postcode, $postcodes)
    {
        $range = explode('-', $postcode, 2);
        $result = $postcode;

        foreach ($postcodes as $code) {
            if ($this->isInRange($code, $range)) {
                $result = $code;
                break;
            }
        }

        return $result;
    }

    /**
     * Check Postcode is in Range
     *
     * @param int $val
     * @param array $range
     * @return boolean
     */
    private function isInRange($val, $range = [])
    {
        $min = $range[0];
        $max = $range[1];
        return ($val >= $min && $val <= $max);
    }
}
