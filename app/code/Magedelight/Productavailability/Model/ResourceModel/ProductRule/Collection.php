<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model\ResourceModel\ProductRule;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * Construct
     */
    protected function _construct()
    {
        $this->_init(
            \Magedelight\Productavailability\Model\ProductRule::class,
            \Magedelight\Productavailability\Model\ResourceModel\ProductRule::class
        );
    }
}
