<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model\ResourceModel\PostcodeRule;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Magedelight\Productavailability\Model\PostcodeRule as PostcodeRule;
use Magedelight\Productavailability\Model\ResourceModel\PostcodeRule as PostcodeRuleResourceModel;

class Collection extends AbstractCollection
{
    public const TBL_RULE_POSTCODES = 'md_rule_postcodes';
    /**
     * @var string
     */
    protected $_idFieldName = 'rule_id';
    
    /**
     * @var string
     */
    protected $_eventPrefix = 'productavailability_postcoderule_grid_collection';
    
    /**
     * @var string
     */
    protected $_eventObject = 'productavailability_postcoderule_collection';
    
    /**
     * @var array
     */
    protected $_joinedFields = [];

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(PostcodeRule::class, PostcodeRuleResourceModel::class);
    }
    
    /**
     * After collection load
     *
     * @return $this
     */
    protected function _afterLoad()
    {
        $this->performAfterLoad(self::TBL_RULE_POSTCODES, PostcodeRule::RULE_ID);
        return parent::_afterLoad();
    }
    
    /**
     * Retrieving Postcode
     *
     * @param string $tableName
     * @param string $columnName
     */
    protected function performAfterLoad($tableName, $columnName)
    {
        $ruleIds = $this->getColumnValues($columnName);
        if (count($ruleIds)) {
            $connection = $this->getConnection();
            $select = $connection->select()->from([self::TBL_RULE_POSTCODES => $this->getTable($tableName)])
                ->where(self::TBL_RULE_POSTCODES .'.'. $columnName . ' IN (?)', $ruleIds);
            // @codingStandardsIgnoreStart
            $result = $connection->fetchAll($select);
            // @codingStandardsIgnoreEnd
            
            if ($result) {
                $zipcodesData = [];
                foreach ($result as $zipcodeData) {
                    $zipcodesData[$zipcodeData[$columnName]][] = $zipcodeData['postcode_id'];
                }

                foreach ($this as $item) {
                    $linkedId = $item->getData($columnName);
                    
                    if (!isset($zipcodesData[$linkedId])) {
                        continue;
                    }
                    
                    $item->setData('assigned_postcodes', $zipcodesData[$linkedId]);
                }
            }
        }
    }
}
