<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */
namespace Magedelight\Productavailability\Model\ResourceModel\PostcodeReport;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * PostcodeReport Collection
 *
 * <AUTHOR> Team <<EMAIL>>
 */
class Collection extends AbstractCollection
{
    /**
     * Define resource model
     *
     * @var string
     */
    protected $_idFieldName = '	postcode_report_id';

    /**
     * Initialize resource collection
     *
     * @return void
     */
    public function _construct()
    {
        $this->_init(
            \Magedelight\Productavailability\Model\PostcodeReport::class,
            \Magedelight\Productavailability\Model\ResourceModel\PostcodeReport::class
        );
    }
}
