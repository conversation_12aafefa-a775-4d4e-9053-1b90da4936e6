<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\Model\ResourceModel\Db\Context;
use Magedelight\Productavailability\Api\Data\PostcodeRuleInterface;
use Magento\Framework\Model\AbstractModel;
use Magedelight\Productavailability\Model\PostcodeRule as PostcodeRuleModel;
use Magento\Framework\Json\DecoderInterface;
use Magento\Framework\Exception\InputException;

class PostcodeRule extends AbstractDb
{
    public const TBL_RULE = 'md_postcode_rule';
    public const TBL_RULE_POSTCODES = 'md_rule_postcodes';

    /**
     * @var DecoderInterface
     */
    private $jsonDecoder;
    
    /**
     * @param Context $context
     * @param DecoderInterface $jsonDecoder
     */
    public function __construct(
        Context $context,
        DecoderInterface $jsonDecoder
    ) {
        $this->jsonDecoder = $jsonDecoder;
        parent::__construct($context);
    }
          
    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(self::TBL_RULE, PostcodeRuleInterface::RULE_ID);
    }
    
    /**
     * Assign selected postcode to rule
     *
     * @param AbstractModel|\Magedelight\Productavailability\Model\PostcodeRule $object
     * @return $this
     */
    protected function _afterSave(AbstractModel $object)
    {
        $this->savePostcodeRelation($object);
        return parent::_afterSave($object);
    }
    
    /**
     * Save PostCode Relation
     *
     * @param PostcodeRuleModel $postcoderule
     */
    protected function savePostcodeRelation(PostcodeRuleModel $postcoderule)
    {
        $selectedPostcode = [];
        $selectedPostcodeJSON = $postcoderule->getData('sel_postcodes');

        if ($selectedPostcodeJSON) {
            $selectedPostcode = array_filter(array_keys($this->jsonDecoder->decode($selectedPostcodeJSON)), 'is_int');
        }
        
        /* if (!$selectedPostcode) {
            throw new InputException(__('Assing Zipcode(s) is required field, please select any postcode from grid'));
        } */
        
        $ruleId = $postcoderule->getRuleId();

        if (!$ruleId) {
            return;
        }

        $this->insertPostcodeRelation($ruleId, $selectedPostcode);
    }

    /**
     * Insert PostCode Relation
     *
     * @param type $ruleId
     * @param type $selectedPostcode
     */
    private function insertPostcodeRelation($ruleId, $selectedPostcode = [])
    {
        $assignedPostcode = $this->getCurrentPostcodes($ruleId);

        $table = $this->getTable(self::TBL_RULE_POSTCODES);
        $insert = array_diff($selectedPostcode, $assignedPostcode);
        $delete = array_diff($assignedPostcode, $selectedPostcode);

        // Delete UnChecked Zipcode's
        if ($delete) {
            $where = ['rule_id = ?' => (int) $ruleId, 'postcode_id IN (?)' => $delete];
            $this->getConnection()->delete($table, $where);
        }

        // Add Newly Assign Zipcode's
        if ($insert) {
            $data = [];
            foreach ($insert as $postcodeId) {
                $data[] = ['rule_id' => (int) $ruleId, 'postcode_id' => (int) $postcodeId];
            }
            $this->getConnection()->insertMultiple($table, $data);
        }
    }

    /**
     * Perform operations after object load
     *
     * @param AbstractModel $object
     * @return $this
     */
    protected function _afterLoad(AbstractModel $object)
    {
        if ($object->getId()) {
            $selectedPostcodes = $this->getCurrentPostcodes($object->getId());
            $object->setData('assigned_postcodes', $selectedPostcodes);
        }
        return parent::_afterLoad($object);
    }
    
    /**
     * Get all postcode related to current rule
     *
     * @param type $ruleId
     * @return array
     */
    protected function getCurrentPostcodes($ruleId)
    {
        $adapter = $this->getConnection();
        $select = $adapter->select()->from(
            $this->getTable(self::TBL_RULE_POSTCODES),
            'postcode_id'
        )->where(
            'rule_id = ?',
            (int) $ruleId
        );
        return $adapter->fetchCol($select);
    }
}
