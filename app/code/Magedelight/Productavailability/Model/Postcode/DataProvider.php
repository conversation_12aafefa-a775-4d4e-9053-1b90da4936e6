<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model\Postcode;

use Magedelight\Productavailability\Model\ResourceModel\Postcode\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Ui\DataProvider\AbstractDataProvider;

class DataProvider extends AbstractDataProvider
{

    /**
     * @var \Magedelight\Productavailability\Model\ResourceModel\Postcode\Collection
     */
    protected $collection;

    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var array
     */
    protected $loadedData;

    /**
     * Constructor
     *
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $postcodeCollectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $postcodeCollectionFactory,
        DataPersistorInterface $dataPersistor,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $postcodeCollectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }
        
        $items = $this->collection->getItems();
        foreach ($items as $model) {
            $model->setStores(explode(',', $model->getStoreId()));
            $this->loadedData[$model->getId()] = $model->getData();
            $this->setStateAsData($model);
        }
        
        $data = $this->dataPersistor->get('productavailability_postcode');
        
        if (!empty($data)) {
            $model = $this->collection->getNewEmptyItem();
            $model->setData($data);
            $this->loadedData[$model->getId()] = $model->getData();
            $this->dataPersistor->clear('productavailability_postcode');
        }
        
        return $this->loadedData;
    }
    
    /**
     * Set State As Data
     *
     * @param object $model
     * @return array
     */
    private function setStateAsData($model)
    {
        $this->loadedData[$model->getId()]['state'] = $model->getRegionId($model->getCountry(), $model->getState());
        
        if (is_string($this->loadedData[$model->getId()]['state'])) {
            $this->loadedData[$model->getId()]['state_name'] = $this->loadedData[$model->getId()]['state'];
        }
    }
}
