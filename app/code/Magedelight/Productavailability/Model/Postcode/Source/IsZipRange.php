<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model\Postcode\Source;

use Magento\Framework\Data\OptionSourceInterface;

class IsZipRange implements OptionSourceInterface
{
    public const IS_ZIP_RANGE_ENABLE = 1;
    public const IS_ZIP_RANGE_DISABLE = 0;

    /**
     * @var \Magedelight\Productavailability\Model\Postcode
     */
    protected $postcodeModel;

    /**
     * Constructor
     *
     * @param \Magedelight\Productavailability\Model\Postcode $postcodeModel
     */
    public function __construct(\Magedelight\Productavailability\Model\Postcode $postcodeModel)
    {
        $this->postcodeModel = $postcodeModel;
    }

    /**
     * Get option Array
     *
     * @return array
     */
    public function getOptionArray()
    {
        return [
            self::IS_ZIP_RANGE_ENABLE => __('Yes'),
            self::IS_ZIP_RANGE_DISABLE => __('No')
        ];
    }

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        $availableOptions = $this->getZipRangeOption();
        $options = [];
        foreach ($availableOptions as $key => $value) {
            $options[] = [
                'label' => $value,
                'value' => $key,
            ];
        }
        return $options;
    }

    /**
     * Retrieve option text by option value
     *
     * @param string $optionId
     * @return string
     */
    public function getOptionText($optionId)
    {
        $options = self::getOptionArray();

        return isset($options[$optionId]) ? $options[$optionId] : null;
    }

    /**
     * Prepare Zip Range Data
     *
     * @return array
     */
    public function getZipRangeOption()
    {
        return [
            self::IS_ZIP_RANGE_ENABLE => __('Enable'),
            self::IS_ZIP_RANGE_DISABLE => __('Disable')
        ];
    }
}
