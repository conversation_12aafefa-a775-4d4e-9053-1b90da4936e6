<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
 
namespace Magedelight\Productavailability\Model\Postcode;

use Magento\Framework\File\Csv;
use Magedelight\Productavailability\Model\PostcodeFactory;
use Magedelight\Productavailability\Helper\Data;

/**
 * Tax Postcode CSV Import Handler
 *
 * @api
 * @since 2.0.0
 */
class CsvImportHandler
{

    /**
     * @var \Magedelight\Productavailability\Model\PostcodeFactory
     */
    private $postcodeFactory;

    /**
     * @var \Magento\Framework\File\Csv
     */
    private $csvProcessor;

    /**
     * Get Helper
     *
     * @var \Magedelight\Productavailability\Helper\Data
     */
    private $helper;

    /**
     * @param PostcodeFactory $postcodeFactory
     * @param Csv $csvProcessor
     * @param Data $helper
     */
    public function __construct(
        PostcodeFactory $postcodeFactory,
        Csv $csvProcessor,
        Data $helper
    ) {
        $this->postcodeFactory = $postcodeFactory;
        $this->csvProcessor = $csvProcessor;
        $this->helper = $helper;
    }

    /**
     * Retrieve a list of fields required for CSV file (order is important!)
     *
     * @return array
     */
    public function getRequiredCsvFields()
    {
        // indexes are specified for clarity, they are used during import
        return [
            0 => __('Postcode/Zipcode'),
            1 => __('City'),
            2 => __('State'),
            3 => __('Country'),
            4 => __('Estimated Shipping Message'),
            5 => __('Is Restrict Shipping Method'),
            6 => __('Restricted Shipping Method(add comma sepepostcoded)'),
            7 => __('Is Restrict Payment Method'),
            8 => __('Restricted Payment Method(add comma sepepostcoded)'),
            9 => __('Status')
        ];
    }

    /**
     * Import Tax Postcodes from CSV file
     *
     * @param array $file file info retrieved from $_FILES array
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function importFromCsvFile($file)
    {
        if (!isset($file['tmp_name'])) {
            throw new \Magento\Framework\Exception\LocalizedException(__('Invalid file upload attempt.'));
        }
        $this->csvProcessor->setDelimiter("\t");
        if ($this->helper->getFieldSeperator()) {
            $this->csvProcessor->setDelimiter(",");
        }
        $postcodesRawData = $this->csvProcessor->getData($file['tmp_name']);
        
        // first row of file represents headers
        $fileFields = $postcodesRawData[0];
        $validFields = $this->_filterFileFields($fileFields);
        $invalidFields = array_diff_key($fileFields, $validFields);
        $postcodesData = $this->_filterPostcodeData($postcodesRawData, $invalidFields, $validFields);
        
        foreach ($postcodesData as $rowIndex => $dataRow) {
            // skip headers
            if ($rowIndex == 0) {
                continue;
            }
            
            $this->importPostcode($dataRow);
        }
    }

    /**
     * Filter file fields (i.e. unset invalid fields)
     *
     * @param array $fileFields
     * @return string[] filtered fields
     */
    protected function _filterFileFields(array $fileFields)
    {
        $filteredFields = $this->getRequiredCsvFields();
        $requiredFieldsNum = count($this->getRequiredCsvFields());
        $fileFieldsNum = count($fileFields);

        // process title-related fields that are located right after required fields with store code as field name)
        for ($index = $requiredFieldsNum; $index < $fileFieldsNum; $index++) {
            $titleFieldName = $fileFields[$index];
            $filteredFields[$index] = $titleFieldName;
        }

        return $filteredFields;
    }

    /**
     * Filter postcodes data (i.e. unset all invalid fields and check consistency)
     *
     * @param array $postcodeRawData
     * @param array $invalidFields assoc array of invalid file fields
     * @param array $validFields assoc array of valid file fields
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    protected function _filterPostcodeData(array $postcodeRawData, array $invalidFields, array $validFields)
    {
        $validFieldsNum = count($validFields);
        
        foreach ($postcodeRawData as $rowIndex => $dataRow) {
            // skip empty rows
            if (count($dataRow) <= 1) {
                unset($postcodeRawData[$rowIndex]);
                continue;
            }
            
            // unset invalid fields from data row
            foreach ($dataRow as $fieldIndex => $fieldValue) {
                if (isset($invalidFields[$fieldIndex])) {
                    unset($postcodeRawData[$rowIndex][$fieldIndex]);
                }
            }
            
            // skip postcode row if duplicate found
            if ($rowIndex > 0) {
                $data = [
                    'postcode'=>$dataRow[0],
                    'city'=>$dataRow[1],
                    'state'=>$dataRow[2],
                    'country'=>$dataRow[3],
                    'shipping_info'=>$dataRow[4],
                    'status'=>$dataRow[5],
                    'is_shipping'=>$dataRow[6],
                    'shipping_methods'=>$dataRow[7],
                    'is_payment_restricted'=>$dataRow[8],
                    'payment_methods'=>$dataRow[9]
                ];
                $postcodeModel = $this->postcodeFactory->create();
                $postcodeModel->load($dataRow[0], 'postcode');
                $postcodeModel->addData($data);
                $postcodeModel->save();
                unset($postcodeRawData[$rowIndex]);
                continue;
            }
            
            // check if number of fields in row match with number of valid fields
            if (count($postcodeRawData[$rowIndex]) != $validFieldsNum) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Invalid file format.'));
            }
        }
        return $postcodeRawData;
    }
    
    /**
     * Prepare Postcode Insert Data
     *
     * @param array $postcodeData
     */
    private function preparePostcodeInsertData($postcodeData)
    {
        // data with index 0 must represent postcode
        $postcode = $postcodeData[0];
        
        // data with index 2 must represent region code
        $regionId = $this->postcodeFactory->create()->getRegionId($postcodeData[3], $postcodeData[2]);
        
        // convert postcode to range if two postcode available
        $zipRangePostcode = $this->getZipRange($postcode);
        $isZipRange = $this->isZipRange($zipRangePostcode);
        $zipFrom = $isZipRange ? $zipRangePostcode[0] : null;
        $zipTo = $isZipRange ? $zipRangePostcode[1] : null;

        return [
                'postcode' => $postcode,
                'is_zip_range' => $isZipRange,
                'zip_from' => $zipFrom,
                'zip_to' => $zipTo,
                'city' => $postcodeData[1],
                'state' => $regionId,
                'country' => $postcodeData[3],
                'shipping_info' => $postcodeData[4],
                'status' => $postcodeData[5],
                'is_shipping' => $this->shippingRestrictionEnable($postcodeData[6]),
                'shipping_methods' => $postcodeData[6],
                'is_payment_restricted' => $this->shippingRestrictionEnable($postcodeData[7]),
                'payment_methods' => $postcodeData[7]
            ];
    }
    
    /**
     * Import single postcode
     *
     * @param array $postcodeData
     *
     * @return array regions cache populated with regions related to country of imported tax postcode
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function importPostcode(array $postcodeData)
    {
        // data with index 0 is postcode which can "123xxx" OR "123xxx-123xxx"
        $postcode = $postcodeData[0];
        
        if (!empty($postcode)) {
            $modelData = $this->preparePostcodeInsertData($postcodeData);
            
            // try to load existing postcode
            /** @var $postcodeModel \Magedelight\Productavailability\Model\Postcode  */
            $postcodeModel = $this->postcodeFactory->create()->loadByPostcode($modelData['postcode']);
        
            $postcodeModel->addData($modelData);
            $postcodeModel->save();
        }
    }
    
    /**
     * Get postcode ziprange
     *
     * @param string $postcode
     * @return array
     */
    private function getZipRange($postcode)
    {
        $postcodeRange = [];
        
        if ($postcode) {
            $postcodeRange = explode('-', $postcode);
        }
        
        return $postcodeRange;
    }
    
    /**
     * Check postcode is in ziprange
     *
     * @param string $zipRangePostcode
     * @return int
     */
    private function isZipRange($zipRangePostcode)
    {
        if ($zipRangePostcode) {
            if ($zipRangePostcode && isset($zipRangePostcode[1])) {
                return 1;
            }
        }
        return 0;
    }
    
    /**
     * Check Shipping Method Restriction enable
     *
     * @param string $shippingMethod
     * @return int
     */
    private function shippingRestrictionEnable($shippingMethod = null)
    {
        if (!empty($shippingMethod)) {
            return 1;
        }
        return 0;
    }
    
    /**
     * Check Payment Method Restriction enable
     *
     * @param string $paymentMethod
     * @return int
     */
    private function paymentRestrictionEnable($paymentMethod = null)
    {
        if (!empty($paymentMethod)) {
            return 1;
        }
        return 0;
    }
}
