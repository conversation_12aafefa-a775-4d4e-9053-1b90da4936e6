<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model;

use Magento\Framework\Model\AbstractModel;
use Magedelight\Productavailability\Model\ResourceModel\ProductRule as ProductRuleResourceModel;
use Magento\Payment\Model\Config as PaymentConfig;
use Magento\OfflinePayments\Model\Cashondelivery;

class ProductRule extends AbstractModel
{

    public const CACHE_TAG = 'productavailability_postcode_productrule';

    /**
     * @var string
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * @var string
     */
    protected $_eventPrefix = self::CACHE_TAG;
    
    /**
     * @var ProductRuleResourceModel
     */
    private $resource = null;

    /**
     * @var PaymentConfig
     */
    protected $paymentConfig = null;

    /**
     * @param ProductRuleResourceModel $resource
     * @param PaymentConfig $paymentConfig
     */
    public function __construct(
        ProductRuleResourceModel $resource,
        PaymentConfig $paymentConfig
    ) {
        $this->resource = $resource;
        $this->paymentConfig = $paymentConfig;
    }
    
    /**
     * Constructor
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(ProductRuleResourceModel::class);
    }
    
    /**
     * Save Product Rule Data
     *
     * @param int $productId
     * @param int $ruleId
     * @param int $storeId
     * @return mixed
     */
    public function saveProductRuleData($productId, $ruleId, $storeId)
    {
        $this->resource->saveWithProduct($productId, $ruleId, $storeId);
    }

    /**
     * Get Post Code Rule Id
     *
     * @param int $productId
     * @param int $storeId
     * @return null|int
     */
    public function getPostcodeRuleId($productId, $storeId)
    {
        return $this->resource->getPostcodeRuleId($productId, $storeId);
    }

    /**
     * Get Post Code Info
     *
     * @param int $productId
     * @param int $storeId
     * @param string $zipcode
     */
    public function getPostcodeInfo($productId = 0, $storeId = 0, $zipcode = null)
    {
        
        $data = $this->resource->getPostcodeInfo($productId, $storeId, $zipcode);
        
        if ($data) {
            $data['is_cod'] = 1;
            $CODMethod = Cashondelivery::PAYMENT_METHOD_CASHONDELIVERY_CODE;
            $paymentMethods = $this->paymentConfig->getActiveMethods();
            
            if (!in_array($CODMethod, array_keys($paymentMethods))) {
                $data['is_cod'] = 0;
            }

            if (isset($data['payment_methods']) &&
                $data['is_cod'] &&
                $data['is_payment_restricted'] &&
                !empty($data['payment_methods'])
            ) {
                $restrictedMethod = explode(',', $data['payment_methods']);
                $data['is_cod'] = (in_array($CODMethod, $restrictedMethod)) ? 0 : 1;
            }
        }
        
        return $data;
    }
    
    /**
     * Get Restricted Data
     *
     * @param int $productId
     * @param int $storeId
     * @param mixed $zipcode
     * @param string $columnName
     * @return array
     */
    public function getRestrictedData($productId, $storeId, $zipcode, $columnName)
    {
        return $this->resource->getPostcodeInfo($productId, $storeId, $zipcode, $columnName);
    }
    
    /**
     * Get Product Rule
     *
     * @param int $productId
     * @param int $ruleId
     * @param array $postcodes
     * @return array
     */
    public function getProductRulePostcode($productId = 0, $ruleId = 0, $postcodes = [])
    {
        return $this->resource->getProductRulePostcode($productId, $ruleId, $postcodes);
    }
}
