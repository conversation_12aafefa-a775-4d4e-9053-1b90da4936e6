<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */
 
namespace Magedelight\Productavailability\Model\Postcoderule;

use Magento\Framework\File\Csv;
use Magedelight\Productavailability\Model\ProductRule;
use Magento\Catalog\Model\ProductFactory;
use Magento\Framework\App\RequestInterface;
use Psr\Log\LoggerInterface;

/**
 * Tax Postcode CSV Import Handler
 *
 * @api
 * @since 2.0.0
 */
class CsvImportHandler
{

    /**
     * Postcode factory
     *
     * @var \Magedelight\Productavailability\Model\ProductRule
     */
    private $productRule;
    
    /**
     * @var Magento\Catalog\Model\ProductFactory
     */
    private $productFactory;
    
    /**
     * URL Request
     *
     * @var Magento\Catalog\Api\ProductRepositoryInterface
     */
    private $request;

    /**
     * @var \Magento\Framework\File\Csv
     */
    private $csvProcessor;
    
    /**
     * Mage Log
     *
     * @var \Psr\Log\LoggerInterface $log
     */
    private $log;

    /**
     *
     * @param ProductRule $productRule
     * @param ProductFactory $productFactory
     * @param RequestInterface $request
     * @param Csv $csvProcessor
     * @param LoggerInterface $log
     */
    public function __construct(
        ProductRule $productRule,
        ProductFactory $productFactory,
        RequestInterface $request,
        Csv $csvProcessor,
        LoggerInterface $log
    ) {
        $this->productRule = $productRule;
        $this->productFactory = $productFactory;
        $this->request = $request;
        $this->csvProcessor = $csvProcessor;
        $this->log = $log;
    }
    
    /**
     * Get Product By SKU
     *
     * @param string $sku
     * @return int
     */
    private function getProductId($sku = null)
    {
        $productId = 0;

        if ($sku) {
            $product = $this->productFactory->create();
            $productId = $product->getIdBySku($sku);
        }
        
        if (!$productId) {
            $this->importLog($sku, "entered sku is not available");
        }

        return $productId;
        
        /*  try {
            printLog(get_class_methods($this->productFactory));
            $result = $this->productFactory->getBySku($sku);
        } catch (Exception $ex) {
            $result = null;
            $this->importLog($sku, $ex->getMessage());
        }

        if(isset($result) && !empty($result)){
            $productId = $result->getId();
        }

        return $productId; */
    }
    
    /**
     * Get Rule ID
     *
     * @return int ruleId
     */
    public function getRuleId()
    {
        return $this->request->getParam('rule_id', 0);
    }

    /**
     * Retrieve a list of fields required for CSV file (order is important!)
     *
     * @return array
     */
    public function getRequiredCsvFields()
    {
        // indexes are specified for clarity, they are used during import
        return [
            0 => __('sku')
        ];
    }

    /**
     * Import Tax Postcodes from CSV file
     *
     * @param array $file file info retrieved from $_FILES array
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function importFromCsvFile($file)
    {
        if (!isset($file['tmp_name'])) {
            throw new \Magento\Framework\Exception\LocalizedException(__('Invalid file upload attempt.'));
        }
        
        $rawData = $this->csvProcessor->getData($file['tmp_name']);
        
        // first row of file represents headers
        $fileFields = $rawData[0];
        $validFields = $this->_filterFileFields($fileFields);
        $invalidFields = array_diff_key($fileFields, $validFields);
        $data = $this->_filterPostcodeData($rawData, $invalidFields, $validFields);
        
        $successfulRowCount = 0;
        foreach ($data as $rowIndex => $dataRow) {
            // skip headers
            if ($rowIndex == 0) {
                continue;
            }
            
            $result = $this->setProductProductRule($dataRow);
            
            if ($result) {
                $successfulRowCount++;
            }
        }
        
        return $successfulRowCount;
    }

    /**
     * Filter file fields (i.e. unset invalid fields)
     *
     * @param array $fileFields
     * @return string[] filtered fields
     */
    protected function _filterFileFields(array $fileFields)
    {
        $filteredFields = $this->getRequiredCsvFields();
        $requiredFieldsNum = count($this->getRequiredCsvFields());
        $fileFieldsNum = count($fileFields);

        // process title-related fields that are located right after required fields with store code as field name)
        for ($index = $requiredFieldsNum; $index < $fileFieldsNum; $index++) {
            $titleFieldName = $fileFields[$index];
            $filteredFields[$index] = $titleFieldName;
        }

        return $filteredFields;
    }

    /**
     * Filter  data (i.e. unset all invalid fields and check consistency)
     *
     * @param array $postcoderawData
     * @param array $invalidFields assoc array of invalid file fields
     * @param array $validFields assoc array of valid file fields
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    private function _filterPostcodeData(array $postcoderawData, array $invalidFields, array $validFields)
    {
        $validFieldsNum = count($validFields);
        foreach ($postcoderawData as $rowIndex => $dataRow) {
            // skip empty rows
            if (count($dataRow) <= 0) {
                unset($postcoderawData[$rowIndex]);
                continue;
            }
            // unset invalid fields from data row
            foreach ($dataRow as $fieldIndex => $fieldValue) {
                if (isset($invalidFields[$fieldIndex])) {
                    unset($postcoderawData[$rowIndex][$fieldIndex]);
                }
            }
            // check if number of fields in row match with number of valid fields
            if (count($postcoderawData[$rowIndex]) != $validFieldsNum) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Invalid file format.'));
            }
        }
        return $postcoderawData;
    }
    
    /**
     * Import single postcode
     *
     * @param array $postcodeData
     *
     * @return array regions cache populated with regions related to country of imported tax postcode
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function setProductProductRule(array $postcodeData)
    {
        // data with index 0 is postcode which can "123xxx" OR "123xxx-123xxx"
        $sku = $postcodeData[0];
        $productId = $this->getProductId($sku);
        $ruleId = $this->getRuleId();
        $storeId = 0;
                
        if (!empty($productId) && !empty($ruleId)) {
            $this->productRule->saveProductRuleData($productId, $ruleId, $storeId);
            return true;
        }
        
        return false;
    }

    /**
     * Import Log
     *
     * @param string $sku
     * @param string|null $message
     */
    private function importLog($sku, $message = null)
    {
        $ruleId = $this->getRuleId();
        $msg = "RuleId : " . $ruleId . "\t";
        
        $this->log->info($msg . "\t Error Message:" . $message . "\t" . $sku);
    }
}
