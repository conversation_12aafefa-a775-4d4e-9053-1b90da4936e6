<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model;

use Magento\Framework\Model\AbstractModel;
use Magedelight\Productavailability\Api\Data\PostcodeRuleInterface;
use Magedelight\Productavailability\Model\ResourceModel\PostcodeRule as PostcoderuleResourceModel;

class PostcodeRule extends AbstractModel implements PostcodeRuleInterface
{
    public const POSTCODES = 'assign_postcodes';
    
    /**
     * @var string
     */
    protected $_eventPrefix = 'productavailability_postcoderule';

    /**
     * Constructor
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(PostcoderuleResourceModel::class);
    }

    /**
     * Get rule_id
     *
     * @return string
     */
    public function getRuleId()
    {
        return $this->getData(self::RULE_ID);
    }

    /**
     * Set rule_id
     *
     * @param string $ruleId
     * @return \Magedelight\Productavailability\Api\Data\PostcodeRuleInterface
     */
    public function setRuleId($ruleId)
    {
        return $this->setData(self::RULE_ID, $ruleId);
    }

    /**
     * Get title
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->getData(self::TITLE);
    }

    /**
     * Set title
     *
     * @param string $title
     * @return \Magedelight\Productavailability\Api\Data\PostcodeRuleInterface
     */
    public function setTitle($title)
    {
        return $this->setData(self::TITLE, $title);
    }
    
    /**
     * Get postcodes
     *
     * @return string
     */
    public function getPostcodes()
    {
        return $this->getData(self::POSTCODES);
    }

    /**
     * Set postcode
     *
     * @param string $postcodes
     * @return \Magedelight\Productavailability\Api\Data\PostcodeRuleInterface
     */
    public function setPostcodes($postcodes)
    {
        return $this->setData(self::POSTCODES, $postcodes);
    }
}
