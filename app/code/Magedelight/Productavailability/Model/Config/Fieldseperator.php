<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model\Config;

class Fieldseperator
{
    /**
     * Get option array
     *
     * @return \string[][]
     */
    public function toOptionArray()
    {
        $options = [
            [
                'value' => 0,
                'label' => 'Tab'
            ],
            [
                'value' => 1,
                'label' => 'Comma'
            ],
        ];
        return $options;
    }
}
