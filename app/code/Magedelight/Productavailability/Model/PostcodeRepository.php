<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Model;

use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magedelight\Productavailability\Api\PostcodeRepositoryInterface;
use Magedelight\Productavailability\Api\Data\PostcodeInterface;
use Magedelight\Productavailability\Model\ResourceModel\Postcode as ResourceData;
use Magedelight\Productavailability\Model\ResourceModel\Postcode\CollectionFactory as PostcodeCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Store\Model\StoreManager;
use Magedelight\Productavailability\Model\ResourceModel\Postcode;
use Magedelight\Productavailability\Helper\Data;
use Magento\Framework\Controller\ResultFactory;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable;
use Magento\Bundle\Model\Product\Type as Bundle;
use Magento\GroupedProduct\Model\Product\Type\Grouped;
use Magedelight\Productavailability\Model\PostcodeReportFactory;
use Magedelight\Productavailability\Model\PostcodeCustomerReportFactory;
use Magento\Customer\Model\Session;
use Magento\Catalog\Model\ProductFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;

class PostcodeRepository implements PostcodeRepositoryInterface
{
    /**
     * @var Product[]
     */
    protected $data = [];

    /**
     * @var PostcodeFactory
     */
    protected $postcodeFactory;

    /**
     * @var PostcodeCollectionFactory
     */
    protected $postcodeCollectionFactory;

    /**
     * @var Magedelight\Productavailability\Model\PostcodeReportFactory
     */
    protected $postcodeReportFactory;

    /**
     * @var Magento\Customer\Model\Session;
     */
    protected $customerSession;

    /**
     * @var  Magedelight\Productavailability\Model\PostcodeCustomerReportFactory;
     */
    protected $postcodeCustomerFactory;

    /**
     * @var  Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable;
     */
    protected $_catalogProductTypeConfigurable;

    /**
     * @var  Magento\Bundle\Model\Product\Type;
     */
    protected $_catalogProductTypeBundle;

    /**
     * @var  Magento\GroupedProduct\Model\Product\Type\Grouped;
     */
    protected $_catalogProductTypeGrouped;

    /**
     * @var Magento\Catalog\Model\ProductFactory;
     */
    protected $_productloader;

    /**
     * @var Magento\Customer\Api\CustomerRepositoryInterface;
     */
    protected $_customerRepositoryInterface;

    /**
     * @var CollectionProcessorInterface
     */
    private $collectionProcessor;

    /**
     * @var ResourceData
     */
    protected $resource;

    /**
     * @var StoreManager
     */
    protected $_storeManager;

    /**
     * @var Postcode
     */
    protected $postcodeModel;

    /**
     * @var Data
     */
    protected $postcodeHelper;

    /**
     * @var ResultFactory
     */
    protected $resultFactory;

    /**
     * Constructor
     *
     * @param ResourceData $resource
     * @param StoreManager $storeManager
     * @param Postcode $postcodeModel
     * @param PostcodeFactory $postcodeFactory
     * @param Data $postcodeHelper
     * @param ResultFactory $resultFactory
     * @param Configurable $catalogProductTypeConfigurable
     * @param Bundle $catalogProductTypeBundle
     * @param Grouped $catalogProductTypeGrouped
     * @param PostcodeCollectionFactory $postcodeCollectionFactory
     * @param PostcodeReportFactory $postcodeReportFactory
     * @param Session $customerSession
     * @param ProductFactory $_productloader
     * @param PostcodeCustomerReportFactory $postcodeCustomerFactory
     * @param CustomerRepositoryInterface $customerRepositoryInterface
     */
    public function __construct(
        ResourceData $resource,
        StoreManager $storeManager,
        Postcode $postcodeModel,
        PostcodeFactory $postcodeFactory,
        Data $postcodeHelper,
        ResultFactory $resultFactory,
        Configurable $catalogProductTypeConfigurable,
        Bundle $catalogProductTypeBundle,
        Grouped $catalogProductTypeGrouped,
        PostcodeCollectionFactory $postcodeCollectionFactory,
        PostcodeReportFactory $postcodeReportFactory,
        Session $customerSession,
        ProductFactory $_productloader,
        PostcodeCustomerReportFactory $postcodeCustomerFactory,
        CustomerRepositoryInterface $customerRepositoryInterface
    ) {
        $this->resource = $resource;
        $this->_storeManager = $storeManager;
        $this->postcodeModel = $postcodeModel;
        $this->postcodeHelper = $postcodeHelper;
        $this->resultFactory = $resultFactory;
        $this->_catalogProductTypeConfigurable = $catalogProductTypeConfigurable;
        $this->_catalogProductTypeBundle = $catalogProductTypeBundle;
        $this->_catalogProductTypeGrouped = $catalogProductTypeGrouped;
        $this->postcodeFactory = $postcodeFactory;
        $this->postcodeCollectionFactory = $postcodeCollectionFactory;
        $this->postcodeReportFactory = $postcodeReportFactory;
        $this->customerSession = $customerSession;
        $this->_productloader = $_productloader;
        $this->postcodeCustomerFactory = $postcodeCustomerFactory;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
    }

    /**
     * Save Method
     *
     * @param PostcodeInterface $postcode
     */
    public function save(PostcodeInterface $postcode)
    {
        try {
            $this->resource->save($postcode);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__($exception->getMessage()));
        }
        return $postcode;
    }

    /**
     * Get By ID
     *
     * @param string|int $postcodeId
     */
    public function getById($postcodeId)
    {
        $postcode = $this->postcodeFactory->create();
        $this->resource->load($postcode, $postcodeId);
        if (!$postcode->getId()) {
            throw new NoSuchEntityException(__('Postcode with id "%1" does not exist.', $postcodeId));
        }
        return $postcode;
    }
    
    /**
     * Get Current Product ID
     *
     * @param string|int $productId
     */
    public function getCurrentProductId($productId)
    {
        $configurableProductId = $this->getConfigurableParentProduct($productId);
        $groupedProductId = $this->getGroupedParentIdsByChild($productId);
        $bundledProductId = $this->getBundleParentIdsByChild($productId);

        if ($configurableProductId) {
            $productId = $configurableProductId;
        }
        if ($groupedProductId) {
            $productId = $groupedProductId;
        }
        if ($bundledProductId) {
            $productId = $bundledProductId;
        }
        
        return $productId;
    }
    
    /**
     * Get Method
     *
     * @param mixed $postcode
     * @param string|int $productId
     * @param int|null $customerId
     */
    public function get($postcode, $productId, $customerId = null)
    {
        $postcodeModel = $this->postcodeFactory->create();
        $results = ['postcode_status' => 0, 'postcode_availability_error' => 'something went wrong'];

        if ((isset($postcode)) && (isset($productId))) {
            $productId = $this->getCurrentProductId($productId);

            $isProductExist = $this->_productloader->create()->load($productId)->getId();
            if (!$isProductExist) {
                throw new NoSuchEntityException(__("Requested product doesn't exist"));
            }
            
            $storeId = $this->postcodeHelper->getStoreId();
            
            $results = $postcodeModel->getPostcodeData($postcode, $productId, $storeId);
            $postcodeModel->setData($results);
            
            if ($this->postcodeHelper->getConfig('productavailability/general/add_api_reports')) {
                $this->savePostcodeReport($postcode, $productId, $results['postcode_status'], $customerId);
            }
        }
        
        return $postcodeModel;
    }

    /**
     * Get Configurable Parent Product ID
     *
     * @param string|int $productId
     */
    public function getConfigurableParentProduct($productId)
    {
        $parentByChild = $this->_catalogProductTypeConfigurable->getParentIdsByChild($productId);
        if (isset($parentByChild[0])) {
            //set id as parent product id...
            $id = $parentByChild[0];
            return $id;
        }
    }

    /**
     * Save Post Code Report
     *
     * @param mixed $postcode
     * @param string|int $productId
     * @param mixed $postcodeStatus
     * @param string|int $customerId
     */
    public function savePostcodeReport($postcode, $productId, $postcodeStatus, $customerId)
    {
        $reportModel = $this->postcodeReportFactory->create();
        
        $reportModel->setPostcode($postcode);
        $reportModel->setCount(1);
        $reportModel->setProductId($productId);
        $reportModel->setPostcodeStatus($postcodeStatus);
        
        try {
            $reportModel->save();
            
            if ($customerId) {
                $this->saveCustomerPostcode($reportModel, $customerId);
            }
        } catch (\Exception $e) {
            throw new \Magento\Framework\Exception\CouldNotSaveException(
                __($e->getMessage())
            );
            //$this->messageManager->addErrorMessage(__($e->getMessage()));
        }
    }

    /**
     * Save Customer Post Code
     *
     * @param mixed $reportModel
     * @param string|int $customerId
     */
    public function saveCustomerPostcode($reportModel, $customerId)
    {
        $pcModel = $this->postcodeCustomerFactory->create();
        
        $customer = $this->_customerRepositoryInterface->getById($customerId)->getEmail();
        $customerEmail = $this->_customerRepositoryInterface->getById($customerId)->getEmail();

        $pcModel->setpostcodeReportId($reportModel->getPostcodeReportId());
        $pcModel->setCustomerId($customerId);
        $pcModel->setCount(1);
        $pcModel->setCustomerEmail($customerEmail);
        
        try {
            $pcModel->save();
        } catch (\Exception $e) {
            throw new \Magento\Framework\Exception\CouldNotSaveException(
                __($e->getMessage())
            );
        }
    }

    /**
     * Get Bundle Parent Product ID By Child
     *
     * @param string|int $childId
     */
    public function getBundleParentIdsByChild($childId)
    {
        $parentByChild = $this->_catalogProductTypeBundle->getParentIdsByChild($childId);
        if (isset($parentByChild[0])) {
            //set id as parent product id...
            $id = $parentByChild[0];
            return $id;
        }
    }

    /**
     * Get Grouped Parent Product ID By Child
     *
     * @param string|int $childId
     */
    public function getGroupedParentIdsByChild($childId)
    {
        $parentByChild = $this->_catalogProductTypeGrouped->getParentIdsByChild(
            $childId,
            \Magento\GroupedProduct\Model\ResourceModel\Product\Link::LINK_TYPE_GROUPED
        );
        if (isset($parentByChild[0])) {
            //set id as parent product id...
            $id = $parentByChild[0];
            return $id;
        }
    }
    
    /**
     * Check For Duplicate Postcode
     *
     * @param mixed $postcode
     * @param string|int $postcodeId
     */
    public function isDuplicateCheck($postcode, $postcodeId = 0)
    {
        $postcodeModel = $this->postcodeFactory->create()->getCollection();
        $postcodeModel->getSelect()->where(
            'postcode = ? OR (zip_from <= ? AND zip_to >= ?)',
            $postcode,
            $postcode,
            $postcode
        );
        
        if ($postcodeId) {
            $postcodeModel->getSelect()->where('postcode_id <> ?', $postcodeId);
        }
        
        if ($postcodeModel->getFirstItem()->getId()) {
            return true;
        }
        
        return false;
    }

    /**
     * Validating zip from to
     *
     * @param int $from
     * @param int $to
     * @return array
     */
    public function validateZipFromTo($from = null, $to = null)
    {
        $result = ['valid' => 1, 'msg' => ''];
        
        if (!$from) {
            $result['valid'] = 0;
            $result['msg'] = __('Range From should not be blank');
        }
        
        if (!$from) {
            $result['valid'] = 0;
            $result['msg'] = __('Range To should not be blank');
        }

        if ($from >= $to) {
            $result['valid'] = 0;
            $result['msg'] = __('Range To must be greater than Range From');
        }
        
        return $result;
    }
    
    /**
     * Get Report Post codes
     *
     * @param int $productId
     * @param int $storeId
     * @return return []
     */
    public function getReportPostcodes($productId = 0, $storeId = 0)
    {

        if (!$productId) {
            return [];
        }

        $report = $this->postcodeReportFactory->create();
        $postcodes = $report->getResource()->getPostcode($productId, $storeId);

        if (!$postcodes) {
            $postcodes = [];
        }

        return $postcodes;
    }
    
    /**
     * Send Postcode notification via email
     *
     * @param int $productId
     * @param int $ruleId
     * @param int $storeId
     * @return boolean
     */
    public function sendCustomerEmail($productId = 0, $ruleId = 0, $storeId = 0)
    {
        $retunData = false;
        $postcodeModel = $this->postcodeFactory->create();
        $isSend = $this->postcodeHelper->sendEmailToCustomer($storeId);

        if (!$isSend || !$productId || !$ruleId) {
            return $retunData;
        }

        $reportPostcodes = $this->getReportPostcodes($productId, $storeId);
        
        if (!$reportPostcodes) {
            return $retunData;
        }
        
        $postcodes = $postcodeModel->getProductRulePostcode($productId, $ruleId, $reportPostcodes);
        
        if (!$postcodes) {
            return $retunData;
        }
        
        return $this->sendMailByStore($postcodeModel, $postcodes, $storeId);
    }
    
    /**
     * Send email from store id
     *
     * @param object $postcodeModel
     * @param array $postcodes
     * @param int $storeId
     * @return boolean
     */
    private function sendMailByStore($postcodeModel, $postcodes, $storeId)
    {
        $postcodeData = $postcodeModel->getPostcodeReportData($postcodes, $storeId);
        $result = false;
        
        foreach ($postcodeData as $data) {
            $email = $data['customer_email'];
            $postcode = $data['postcode'];
            $productId = $data['product_id'];

            $currentStoreId = ($storeId == 0) ? $data['store_id'] : $storeId;

            $result = $this->resource->sendEmailToCustomer($email, $postcode, $productId, $currentStoreId);
            $this->updatePostcodeReport($email, $postcode, $productId, $currentStoreId);
        }

        return $result;
    }

    /**
     * Update Postcode Report
     *
     * @param string|null $email
     * @param string|null $postcode
     * @param string|null $productId
     * @param string|null $storeId
     */
    public function updatePostcodeReport($email = null, $postcode = null, $productId = 0, $storeId = 0)
    {
        if ($email && $postcode && $productId) {
            $this->savePostcodeStatus($productId, $postcode, $storeId);
            $this->saveNotifyCustomer($email, $productId, $storeId);
        }
    }

    /**
     * Save Post Code Status
     *
     * @param int $productId
     * @param object $postcode
     * @param int $storeId
     */
    public function savePostcodeStatus($productId, $postcode, $storeId)
    {
        $reportModel = $this->postcodeReportFactory->create();

        $reports = $reportModel->getCollection()
                ->addFieldToFilter('postcode', ['eq' => $postcode])
                ->addFieldToFilter('product_id', ['eq' => $productId])
                ->addFieldToFilter('postcode_status', ['eq' => 0]);
        
        if ($storeId) {
            $reports->addFieldToFilter('store_id', ['eq' => $storeId]);
        }

        foreach ($reports as $item) {
            $id = $item->getPostcodeReportId();
            $reportModel->load($id);
            
            if ($reportModel->getPostcodeReportId()) {
                $reportModel->setPostcodeStatus(1);
                $reportModel->save();
            }
        }
    }

    /**
     * Save Notify Customer
     *
     * @param string $email
     * @param int $productId
     * @param int $storeId
     */
    public function saveNotifyCustomer($email = null, $productId = 0, $storeId = 0)
    {
        $customerReportModel = $this->postcodeCustomerFactory->create();
        $customerReports = $customerReportModel->getCollection()
            ->join(
                ['report' => 'md_postcode_report'],
                'main_table.postcode_report_id = report.postcode_report_id'
            )
            ->addFieldToFilter('customer_email', ['eq' => $email])
            ->addFieldToFilter('notify_status', ['eq' => 0]);
        
        if ($storeId) {
            $customerReports->addFieldToFilter('store_id', ['eq' => $storeId]);
        }

        foreach ($customerReports as $item) {
            $id = $item->getCustomerReportId();
            $customerReportModel->load($id);
            
            if ($customerReportModel->getCustomerReportId()) {
                $customerReportModel->setProductId($productId);
                $customerReportModel->setNotifyStatus(1);
                $customerReportModel->save();
            }
        }
    }
}
