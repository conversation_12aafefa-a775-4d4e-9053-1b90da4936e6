<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
 
namespace Magedelight\Productavailability\Controller\Adminhtml\Postcoderule;

use Magento\Backend\App\Action;
use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Response\Http\FileFactory;
use Magedelight\Productavailability\Model\Postcoderule\CsvImportHandler;

class Importproducts extends Action
{
    /**
     * Authorization level of a basic admin session
     *
     * @see _isAllowed()
     */
    public const ADMIN_RESOURCE = 'Magedelight_Productavailability::postcode_rule';
    
    /**
     * @var \Magento\Framework\App\Response\Http\FileFactory
     */
    private $fileFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\App\Response\Http\FileFactory $fileFactory
     */
    public function __construct(
        Context $context,
        FileFactory $fileFactory
    ) {
        $this->fileFactory = $fileFactory;
        parent::__construct($context);
    }
    
    /**
     * Import action from index/import
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        if ($this->getRequest()->isPost()
            && !empty($this->getRequest()->getFiles('product_file')['tmp_name'])
        ) {
            try {
                /** @var $importHandler Magedelight\Productavailability\Model\Postcode\CsvImportHandler */
                $importHandler = $this->_objectManager->create(CsvImportHandler::class);
                
                $count = $importHandler->importFromCsvFile($this->getRequest()->getFiles('product_file'));
                
                $this->messageManager->addSuccess(
                    __('The %1 product(s) had been imported and assigned to the postcode rule successfully.', $count)
                );
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $this->messageManager->addError($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addError(__('Invalid file upload attempt'));
            }
        } else {
            $this->messageManager->addError(__('Invalid file upload attempt'));
        }
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setUrl($this->_redirect->getRedirectUrl());
        return $resultRedirect;
    }

    /**
     * Is Allowed Method
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed(
            'Magedelight_Productavailability::postcode_rule'
        ) || $this->_authorization->isAllowed(
            'Magedelight_Productavailability::postcode_rule'
        );
    }
}
