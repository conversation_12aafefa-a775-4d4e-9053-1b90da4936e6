<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
namespace Magedelight\Productavailability\Controller\Adminhtml\Postcode;

use Magento\Backend\App\Action\Context;
use Magedelight\Productavailability\Model\Postcode;
use Magedelight\Productavailability\Model\PostcodeFactory;
use Magedelight\Productavailability\Api\PostcodeRepositoryInterface;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\Exception\LocalizedException;
use Magedelight\Productavailability\Controller\Adminhtml\Postcode as PostcodePage;
use Magento\Framework\Registry;

class Save extends PostcodePage
{
    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var PostcodeRepositoryInterface
     */
    private $postcodeRepository;

    /**
     * @var Magedelight\Productavailability\Model\PostcodeFactory
     */
    private $postcodeFactory;

    /**
     * @param Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param DataPersistorInterface $dataPersistor
     * @param PostcodeFactory $postcodFactory
     * @param PostcodeRepositoryInterface $postcodeRepository
     */
    public function __construct(
        Context $context,
        Registry $coreRegistry,
        DataPersistorInterface $dataPersistor,
        PostcodeFactory $postcodFactory = null,
        PostcodeRepositoryInterface $postcodeRepository = null
    ) {
        $this->dataPersistor = $dataPersistor;
        $this->postcodeFactory = $postcodFactory
            ?: \Magento\Framework\App\ObjectManager::getInstance()->get(PostcodeFactory::class);
        $this->postcodeRepository = $postcodeRepository
            ?: \Magento\Framework\App\ObjectManager::getInstance()->get(PostcodeRepositoryInterface::class);

        parent::__construct($context, $coreRegistry);
    }

    /**
     * Save action
     *
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->getRequest()->getPostValue();
        
        $isZipcodeEnable = false;
        if (isset($data['status']) && $data['status'] === '1') {
            $isZipcodeEnable = true;
        }
        $data['status'] = ($isZipcodeEnable) ? Postcode::STATUS_ENABLED : Postcode::STATUS_DISABLED;

        if (isset($data['shipping_methods']) && $data['shipping_methods']) {
            $data['shipping_methods'] = implode(',', $data['shipping_methods']);
        }
        
        if (isset($data['payment_methods']) && $data['payment_methods']) {
            $data['payment_methods'] = implode(',', $data['payment_methods']);
        }
        
        if (isset($data['is_shipping']) && $data['is_shipping'] === '0') {
            $data['shipping_methods'] = null;
        }
        
        if (isset($data['is_payment_restricted']) && $data['is_payment_restricted'] === '0') {
            $data['payment_methods'] = null;
        }

        if ($data['state_name']) {
            $data['state'] = $data['state_name'];
        }
        
        if ($data) {
            $model = $this->postcodeFactory->create();
            $id = $this->getRequest()->getParam('postcode_id');
            
            // Validating Zip Range
            if (isset($data['is_zip_range']) && $data['is_zip_range'] === '1') {
                $data['postcode'] = $data['zip_from'] . "-" . $data['zip_to'];

                $zipRangeValidation = $this->postcodeRepository->validateZipFromTo($data['zip_from'], $data['zip_to']);

                if (!$zipRangeValidation['valid']) {
                    $this->messageManager->addErrorMessage($zipRangeValidation['msg']);
                    return $resultRedirect->setPath('*/*/edit', ['postcode_id' => $id]);
                }
            }
            
            if ($this->postcodeRepository->isDuplicateCheck($data['postcode'], $id)) {
                $this->dataPersistor->set('productavailability_postcode', $data);
                $this->messageManager->addErrorMessage(__('same postcode already exists'));
                return $resultRedirect->setPath('*/*/edit', ['postcode_id' => $id]);
            }

            // Update Current Record
            if ($id) {
                try {
                    $model = $this->postcodeRepository->getById($id);
                } catch (LocalizedException $e) {
                    $this->messageManager->addErrorMessage(__('This postcode no longer exists.'));
                    return $resultRedirect->setPath('*/*/');
                }
            }

            $model->setData($data);
            try {
                $this->postcodeRepository->save($model);
                $this->messageManager->addSuccessMessage(__('You saved the postcode.'));
                $this->dataPersistor->clear('productavailability_postcode');
                
                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['postcode_id' => $model->getId()]);
                }
                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('Something went wrong while saving the postcode.'));
            }

            $this->dataPersistor->set('productavailability_postcode', $data);
            return $resultRedirect->setPath(
                '*/*/edit',
                ['postcode_id' => $this->getRequest()->getParam('postcode_id')]
            );
        }
        return $resultRedirect->setPath('*/*/');
    }
}
