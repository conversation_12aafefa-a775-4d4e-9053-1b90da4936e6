<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magedelight\Productavailability\Model\PostcodeFactory;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Magento\Framework\App\State;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\Filesystem\Driver\File as FileDriver;
use Magedelight\Productavailability\Helper\DataFactory;

class ImportCommand extends Command
{
    public const FILE_NAME = 'file';
    public const FILE_NAME_LABEL = 'File Name';
    public const IS_TRUNCATE_FLAG_NAME = 'clear';
    public const IS_TRUNCATE_FLAG_LABEL = 'Clear Postcode data';
    public const COMMAND_NAME = 'magedelight:postcode:import';
    public const COMMAND_DESCRIPTION = 'Postcode Import command via geonames.org';
    
    /**
     * @var ApplicationFactory
     */
    private $postcodeModel = null;
    
    /**
     * @var Filesystem
     */
    private $fileSystem = null;
    
    /**
     * @var \Magento\Framework\App\State
     */
    private $state = null;
    
    /**
     * @var Symfony\Component\Console\Input\InputInterface
     */
    private $input = null;
    
    /**
     * @var Symfony\Component\Console\Output\OutputInterface
     */
    private $output = null;

    /**
     * @var File
     */
    private $file = null;

    /**
     * @var FileDriver
     */
    private $fileDriver = null;

    /**
     * Get Helper
     *
     * @var \Magedelight\Productavailability\Helper\Data
     */
    private $helper;

    /**
     * Constructor
     *
     * @param Filesystem $fileSystem
     * @param PostcodeFactory $postcodeModel
     * @param State $state
     * @param File $file
     * @param FileDriver $fileDriver
     * @param DataFactory $helper
     */
    public function __construct(
        Filesystem $fileSystem,
        PostcodeFactory $postcodeModel,
        State $state,
        File $file,
        FileDriver $fileDriver,
        DataFactory $helper
    ) {
        $this->state = $state;
        $this->fileSystem = $fileSystem;
        $this->postcodeModel = $postcodeModel;
        $this->file = $file;
        $this->fileDriver = $fileDriver;
        $this->helper = $helper;
        
        parent::__construct();
    }
    
    /**
     * Get file name without extension of file (eg .txt .zip)
     *
     * @return string filename without extension
     */
    private function getFileWOExt()
    {
        $filePathInfo = $this->file->getPathInfo($this->getFileName());
        return $filePathInfo['filename'];
    }
    
    /**
     * Configure Command Line
     */
    protected function configure()
    {
        
        $options = [
            new InputOption(
                self::FILE_NAME,
                null,
                InputOption::VALUE_REQUIRED,
                self::FILE_NAME_LABEL
            ),
            new InputOption(
                self::IS_TRUNCATE_FLAG_NAME,
                null,
                InputOption::VALUE_NONE,
                self::IS_TRUNCATE_FLAG_LABEL
            ),
        ];

        $this->setName(self::COMMAND_NAME)
                ->setDescription(self::COMMAND_DESCRIPTION)
                ->setDefinition($options);

        parent::configure();
    }

    /**
     * Check filename is specified or not in command time
     *
     * @return boolean
     */
    private function isFileNameInputted()
    {
        $fileName = $this->input->getOption(self::FILE_NAME);
        if (!$fileName) {
            $this->output->writeln('<error>' . 'Please specify the filename with ' .
            self::COMMAND_NAME . ' --file filename' . '</error>');
            return false;
        }
        return true;
    }
    
    /**
     * Get Country Name Method
     */
    private function getCountryName()
    {
        $file = $this->getFileWOExt();
        
        if (strpos($file, 'allCountries') !== false) {
            return "All existing";
        } else {
            return "CountryCode --".$file;
        }
    }
    
    /**
     * Confirm the process with dialog message
     *
     * @return boolean
     */
    private function showProcessConfirmation()
    {
        if (!$this->isFileExist($this->getPostcodeZip())) {
            return false;
        }

        if ($this->isTruncate()) {
            $dialog = $this->getHelperSet()->get('question');
            $question = new ConfirmationQuestion('<question>'.'It will delete '.
            $this->getCountryName().' records. Do you want to continue ?[Y/N]'.'</question>', false);
            
            if (!$dialog->ask(
                $this->input,
                $this->output,
                $question
            )) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check entered file is exist or not
     *
     * @param mixed $file
     * @return boolean
     */
    private function isFileExist($file)
    {
        $filePathInfo = $this->file->getPathInfo($this->getFileName());
        $filename = $filePathInfo['basename'];

        if ($this->fileDriver->isExists($filePathInfo['dirname'].'/'.$filename)) {
            $this->output->writeln('<error>' . '"' . $filename . '" file not found.' . '</error>');
            return false;
        }
        return true;
    }

    /**
     * Import Command Handler
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->input = $input;
        $this->output = $output;
        
        if (!$this->isFileNameInputted()) {
            return false;
        }
        
        $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_GLOBAL);
    
        if (!$this->showProcessConfirmation()) {
            return false;
        }

        /* Start Import Process */
        $result = $this->import();

        if ($result) {
            $output->writeln('');
            $output->writeln('<info>' . 'Postcode import successfully completed.' . '</info>');
            return 1;
        } else {
            $output->writeln('');
            $output->writeln('<info>' . 'Unable to import postcodes.' . '</info>');
            return 0;
        }
    }
    
    /**
     * Import Method
     */
    protected function import()
    {
        /* To show progress bar */
        $postcodeModel = $this->postcodeModel->create();

        $this->output->writeln('<info>' . 'Postcode import started please wait...' . '</info>');

        $data = $this->getPostcodeInsertArray();
        $options = [
            'truncate' => $this->isTruncate(),
            'country' => $this->getFileWOExt()
        ];

        $result = $postcodeModel->doProcess($data, $this->output, $options);

        return $result;
    }

    /**
     * Get var DIR
     */
    private function getDir()
    {
        return $this->fileSystem->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath('/');
    }
    
    /**
     * Get User inputted cli zipfile name
     *
     * @return string
     */
    private function getFileName()
    {
        return $this->input->getOption(self::FILE_NAME);
    }
    
    /**
     * Get User inputted cli zipfile name
     *
     * @return boolean
     */
    private function isTruncate()
    {
        if ($this->input->getOption(self::IS_TRUNCATE_FLAG_NAME)) {
            return true;
        }
        return false;
    }

    /**
     * Get Zip from var directory
     *
     * @return string
     */
    public function getPostcodeZip()
    {
        return $this->getDir() . $this->getFileName();
    }
    
    /**
     * Check Ignored String
     *
     * @param string $string
     * @return boolean
     */
    protected function isIgnoredString($string = null)
    {
        $ignoreStringArray = [null,"NA","N/A"];
        return in_array(trim($string), $ignoreStringArray) ? true : false;
    }
    
    /**
     * Zip Range Function
     *
     * @param string $zipfrom
     * @param string $zipto
     * @return string
     */
    protected function zipRangeString($zipfrom, $zipto)
    {
        if ($zipfrom == $zipto) {
            $result = $zipfrom;
        } else {
            $result = $zipfrom . '-' . $zipto;
        }
        
        return $result;
    }

    /**
     * Set Zip Code Range
     *
     * @param array $postcodes
     * @retun array
     */
    protected function setZipInRange($postcodes = [])
    {
        $prev = null;
        $postcodeArray = [];
        
        foreach ($postcodes as $item) {
            if ($prev) {
                if ($item != $prev + 1) {
                    $postcodeArray[] = $this->zipRangeString($start, $prev);
                    $start = $item;
                }
            } else {
                $start = $item;
            }
            $prev = $item;
        }

        $postcodeArray[] =  $this->zipRangeString($start, $prev);
        
        return $postcodeArray;
    }
    
    /**
     * Post Code Data Array
     *
     * @param string $country
     * @param string $state
     * @param string $city
     * @param array $postcode
     * @return array
     */
    protected function postcodeDataArray($country, $state, $city, $postcode)
    {
        $data = [
            'country' => $country,
            'state' => $state,
            'city' => $city,
            'postcode' => $postcode,
            'is_zip_range' => 0,
            'zip_from' => null,
            'zip_to' => null,
        ];
                
        $postcodeRange = explode('-', $postcode);
        if ($postcodeRange && isset($postcodeRange[1])) {
            $data['is_zip_range'] = 1;
            $data['zip_from'] = trim($postcodeRange[0]);
            $data['zip_to'] = trim($postcodeRange[1]);
        }

        return $data;
    }
    
    /**
     * Extract Zip File
     */
    private function extractZipFile()
    {
        $file = $this->getPostcodeZip();
        $filePathInfo = $this->file->getPathInfo($file);
        //$path = pathinfo(realpath($file), PATHINFO_DIRNAME);
        $path = $filePathInfo['dirname'];
        
        $zip = new \ZipArchive;
        $res = $zip->open($file);
        
        if ($res === true) {
            $zip->extractTo($path);
            $zip->close();
            return true;
        }
        
        $this->output->writeln('');
        $this->output->writeln('<error>' . '"' . $this->getFileName() . '" zip file is currupted.' . '</error>');
        return false;
    }
    
    /**
     * Get txt file of current zip
     *
     * @return string
     */
    private function getTextFile()
    {
        $textFile = $this->getDir() . $this->getFileWOExt() . ".txt";
        
        if ($this->isFileExist($textFile)) {
            return $textFile;
        }
    }
    
    /**
     * Get extracted zip postcode text file
     *
     * @return string
     */
    private function getPostcodeFile()
    {
        $this->extractZipFile();
        return $this->getTextFile();
    }

    /**
     * Prepare City Wise Post Code
     *
     * @return array $lines
     */
    protected function preparedPostcodeCityWise()
    {
        $file = $this->getPostcodeFile();
        $handle = $this->fileDriver->fileOpen($file, "r");
        $lines = [];

        if (($handle = $this->fileDriver->fileOpen($file, "r")) !== false) {
            if ($this->helper->create()->getFieldSeperator()) {
                while (($data = $this->fileDriver->fileGetCsv($handle, 1000, ",")) !== false) {
                    $countryCode = $data[0]; // Country Code
                    $state = $data[3]; // State
                    // District/City (order subdivision (county/province))
                    $city = $this->isIgnoredString($data[5]) ? $state : $data[5];
                    $postcode = $data[1]; // PostalCode / ZipCode
    
                    /**  ignore duplicate postcodes */
                    if (isset($lines[$countryCode]) &&
                       isset($lines[$countryCode][$state]) &&
                       isset($lines[$countryCode][$state][$city]) &&
                       in_array($postcode, $lines[$countryCode][$state][$city])
                    ) {
                        continue;
                    }
    
                    $lines[$countryCode][$state][$city][] = (string) $postcode;
                }
            } else {
                while (($data = $this->fileDriver->fileGetCsv($handle, 1000, "\t")) !== false) {
                    $countryCode = $data[0]; // Country Code
                    $state = $data[3]; // State
                    // District/City (order subdivision (county/province))
                    $city = $this->isIgnoredString($data[5]) ? $state : $data[5];
                    $postcode = $data[1]; // PostalCode / ZipCode
    
                    /**  ignore duplicate postcodes */
                    if (isset($lines[$countryCode]) &&
                       isset($lines[$countryCode][$state]) &&
                       isset($lines[$countryCode][$state][$city]) &&
                       in_array($postcode, $lines[$countryCode][$state][$city])
                    ) {
                        continue;
                    }
    
                    $lines[$countryCode][$state][$city][] = (string) $postcode;
                }
            }
            $this->fileDriver->fileClose($handle);
        }

        return $lines;
    }

    /**
     * Get Post Code Insert Array
     *
     * @return array postcode associative array for insertion
     */
    protected function getPostcodeInsertArray()
    {
        $zipcodes = $this->preparedPostcodeCityWise();
        $result = [];

        $i = 0;
        foreach ($zipcodes as $country => $states) {
            foreach ($states as $state => $cities) {
                foreach ($cities as $city => $postcodeArr) {
                    $postcodes = $postcodeArr;
                    foreach ($postcodes as $postcode) {
                        $result[$i] = $this->postcodeDataArray($country, $state, $city, $postcode);
                        $i++;
                    }
                }
            }
        }

        return $result;
    }
}
