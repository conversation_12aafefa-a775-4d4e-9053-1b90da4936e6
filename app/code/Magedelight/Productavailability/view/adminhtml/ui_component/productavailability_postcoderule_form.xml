<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">productavailability_postcoderule_form.postcode_rule_form_data_source</item>
            <item name="deps" xsi:type="string">productavailability_postcoderule_form.postcode_rule_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">PostcodeRule Information</item>
        <item name="config" xsi:type="array">
            <item name="dataScope" xsi:type="string">data</item>
            <item name="namespace" xsi:type="string">productavailability_postcoderule_form</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="string">Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Edit\BackButton</item>
            <item name="delete" xsi:type="string">Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Edit\DeleteButton</item>
            <item name="save" xsi:type="string">Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Edit\SaveButton</item>
            <item name="save_and_continue" xsi:type="string">Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Edit\SaveAndContinueButton</item>
        </item>
    </argument>
    <dataSource name="postcode_rule_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magedelight\Productavailability\Model\Postcoderule\DataProvider</argument>
            <argument name="name" xsi:type="string">postcode_rule_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">rule_id</argument>
            <argument name="requestFieldName" xsi:type="string">rule_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="submit_url" xsi:type="url" path="productavailability/postcoderule/save"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
    </dataSource>
    <fieldset name="general">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="label" xsi:type="string"/>
            </item>
        </argument>
        <!-- <field name="rule_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">postcode_rule</item>
                    <item name="dataScope" xsi:type="string">rule_id</item>
                </item>
            </argument>
        </field> -->
        <field name="title">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Title</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">postcode_rule</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="dataScope" xsi:type="string">title</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="assing_all_zipcodes">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magedelight\Productavailability\Model\Source\AssignAllZipcodes</item>
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Assign all zipcodes</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="dataType" xsi:type="string">number</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="source" xsi:type="string">postcode_rule</item>
                    <item name="dataScope" xsi:type="string">assing_all_zipcodes</item>
                    <item name="component" xsi:type="string">Magedelight_Productavailability/js/form/element/select</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                    <item name="additionalClasses" xsi:type="string">assing_all_zipcodes</item>
                </item>
            </argument>
        </field>
    </fieldset>
    
    <fieldset name="assing_zipcodes">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="label" xsi:type="string" translate="true">Assign Zipcode(s)</item>
                <item name="sortOrder" xsi:type="number">40</item>
                <item name="visibleValue" xsi:type="string">0</item>
                <item name="additionalClasses" xsi:type="string">assing_zipcodes</item>
            </item>
        </argument>
        <container name="assing_zipcodes_container">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
            <htmlContent name="html_content">
                <argument name="block" xsi:type="object">Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\AssignPostcodes</argument>
            </htmlContent>
        </container>
    </fieldset>
    
    <fieldset name="import_products">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="label" xsi:type="string" translate="true">Assign Product(s) to this Postcode Rule</item>
                <item name="sortOrder" xsi:type="number">50</item>
            </item>
        </argument>
        <container name="import_products_container">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
            <htmlContent name="html_content">
                <argument name="block" xsi:type="object">Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\ImportProducts</argument>
            </htmlContent>
        </container>
    </fieldset>
</form>