define([
    'jquery',
    'underscore',
    'uiRegistry',
    'Magento_Ui/js/form/element/select',
    'Magento_Ui/js/modal/modal'
], function ($,_, uiRegistry, select, modal) {
    'use strict';

    return select.extend({

        initialize: function (){
            var assing_zipcodes = uiRegistry.get('index = assing_zipcodes');
            var assign_all_zipcode = this._super().initialValue;
            setTimeout(function(){
                if(assign_all_zipcode == 1){
                    $('.assing_zipcodes').hide();
                }
            }, 1000);
            return this;
        },

        /**
         * On value change handler.
         *
         * @param {String} value
         */
        onUpdate: function (value) {
            var assing_zipcodes = uiRegistry.get('index = assing_zipcodes');
            
            if (assing_zipcodes.visibleValue == value) {
                $('.assing_zipcodes').show();
            } else {
                $('.assing_zipcodes').hide();
            }
            return this._super();
        },
    });
});