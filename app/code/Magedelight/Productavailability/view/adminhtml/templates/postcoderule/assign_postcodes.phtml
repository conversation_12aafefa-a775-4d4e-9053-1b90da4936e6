<?php
/** @var \Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\AssignPostcodes $block */
$blockGrid = $block->getBlockGrid();

/** @var \Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Tab\Postcode $blockGrid */
$gridJsObjectName = $blockGrid->getJsObjectName();
?>


<?= $block->getGridHtml() ?>
<input type="hidden" name="sel_postcodes" id="in_postcoderule_postcodes" 
data-form-part="productavailability_postcoderule_form" value="" />
<script type="text/x-magento-init">
    {
        "*": {
            "Magedelight_Productavailability/postcoderule/assign-postcodes": {
                "selectedPostcodes": <?= /* @noEscape */ $block->getPostcodesJson() ?>,
                "gridJsObjectName": <?= /* @noEscape */ '"' . $gridJsObjectName . '"' ?: '{}' ?>
            }
        }
    }
</script>

<!-- @todo remove when "UI components" will support such initialization -->
<script>
    require('mage/apply/main').apply();
</script>
