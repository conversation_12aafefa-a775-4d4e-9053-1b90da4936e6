/**
 * Magedelight
 * Copyright (C) 2018 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_Productavailability
 * @copyright Copyright (c) 2018 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */

define([
    'jquery',
    'mage/cookies'
], function ($) {
    $.widget('magedelight.postcode', {

        options: {
        },

        _create: function () {
            var self = this;

            var ajaxCallUrl = self.options.ajaxUrl;
            var productId = self.options.productId;

            $(document).on('submit', '#form-product-postcode', function (event) {
                event.preventDefault();
                callAjax(ajaxCallUrl, productId);
            });

            $(document).ready(function () {

                /*if(postcodeCookie){
                    $('.postcode-history').empty();
                    $.each(postcodeCookie, function (index,postcdeValue)
                    {
                        $('.postcode-history').append('<li>'+postcdeValue+'</li>');
                    });

                }*/
                
                $("#product-updatecart-button").attr("disabled", true);
                
                var is_postcode = $("#postcode").attr('value');

                if (is_postcode) {
                    callAjax(ajaxCallUrl, productId);
                }

                $('#postcode').keydown(function (e) {
                    if (e.keyCode == 27 || e.keyCode == 13) {
                        $('.customer-history').css('display', 'none');
                    }
                });

                $('#postcode').click(function (event) {
                    $('.postcode-response').css('display','none');
                    $('.customer-history').css('display','block');
                });

                $("body").click(function (e) {
                    if (!$(e.target).closest('.postcode').length) {
                        $('.customer-history').css('display','none');
                    }
                });

                $(document).on('click', '.postcode-history li', function (event) {
                    var postcode = $(this).attr('postcode');
                    if (postcode) {
                        $('.postcode').attr('value',postcode);
                        callAjax(ajaxCallUrl, productId);
                    }
                });

                $(document).on('click', '.saved-address li', function (event) {
                    var postcode = $(this).attr('postcode');
                    if (postcode) {
                        $('.postcode').attr('value',postcode);
                        callAjax(ajaxCallUrl, productId);
                    }
                });

            });

            function callAjax(ajaxCallUrl,productId)
            {

                var postcode = $("#postcode").val();
                $('.customer-history').css('display','none');
                $('.postcode-response').css('display','block');

                $.ajax({
                    url : ajaxCallUrl,
                    type: 'POST',
                    showLoader: true,
                    dataType: 'json',
                    data: { postcode: postcode,productId:productId },
                    success : function (data) {
                        var postcodeCookie = $.cookie('_postcode_info')
                        postcodeCookie = jQuery.parseJSON(postcodeCookie);//Get the cookies

                        if (postcodeCookie) {
                            $('.postcode-history').empty();
                            var addHeading =  '<li class="title">Recent History</li>';
                            var liHTML = $('.postcode-history').append(addHeading);
                            $.each(postcodeCookie, function (index,postcdeValue) {
                            
                                liHTML.append('<li postcode="'+postcdeValue+'">'+postcdeValue+'</li>');
                                
                            });
                        }

                        if (data['postcode_error']) {
                            $("#product-addtocart-button").attr("disabled", true);
                            $("#product-updatecart-button").attr("disabled", true);
                            $("#qty").attr("disabled", true);
                        } else {
                            $("#product-addtocart-button").attr("disabled", false);
                            $("#product-updatecart-button").attr("disabled", false);
                            $("#qty").attr("disabled", false);
                        }
                        $('.shipping-info').html(data['shipping_info']);
                        $('.is-cod').html(data['is_cod']);
                        $('.postcode-error').html(data['postcode_error']);
                    },
                    error : function (request, error) {
                        $('.postcode_error').html('Error');
                    }
                });
            }
        },
    });
    return $.magedelight.postcode;
});