// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */


//
//  Common
//  _____________________________________________

@icon-location-pointer: "\e900";
@icon-not-allowed-symbol: "\e901";
@icon-payment-method: "\e902";
@icon-calendar: "\e903";

@font-face {
  font-family: 'MD-Icon';
  src:  url('../Magedelight_Productavailability/fonts/MD-Icon/MD-Icon.eot?874j09');
  src:  url('../Magedelight_Productavailability/fonts/MD-Icon/MD-Icon.eot?874j09#iefix') format('embedded-opentype'),
    url('../Magedelight_Productavailability/fonts/MD-Icon/MD-Icon.ttf?874j09') format('truetype'),
    url('../Magedelight_Productavailability/fonts/MD-Icon/MD-Icon.woff?874j09') format('woff'),
    url('../Magedelight_Productavailability/fonts/MD-Icon/MD-Icon.svg?874j09#MD-Icon') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  font-family: 'MD-Icon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
& when (@media-common = true) {
    .md-postcode {
        float: left;
        width: 100%;
        margin: 10px 0 0 0;
        position: relative;
        input:focus:not([disabled]), 
        textarea:focus:not([disabled]), 
        select:focus:not([disabled]),
        textarea,
        input,
        select {
            &:focus {
                box-shadow: none;
                outline: 0;
            }
        }
        .form-product-postcode {
            float: left;
            width: 100%;
            .postcode-box {
                float: left;
                width: 280px;
                max-width: 100%;
                position: relative;
                .postcode-inner {
                    float: left;
                    width: 100%;
                    .control {
                        position: relative;
                        &:before {
                            content: @icon-location-pointer;
                            font-family: 'MD-Icon';
                            margin: 6px 0 0 10px;
                            position: absolute;
                        }
                        .input-text {
                            padding-left: 30px;
                        }
                    }
                }
                .postcode-check-btn {
                    position: absolute;
                    right: 0;
                    top: 0;
                }
                .postcode-response {
                    clear: both;
                    box-shadow: 0 1px 6px 0 #dedede;
                    width: 280px;
                    box-sizing: border-box;
                    font-size: 13px;
                    line-height: 17px;
                    .is-shipping,
                    .is-cod,
                    .shipping-info,
                    .postcode-error {
                        padding: 8px 12px 8px 32px;
                        position: relative;
                        &:before {
                            font-family: 'MD-Icon';
                            margin: 6px 0 0 10px;
                            position: absolute;
                            left: 0;
                            top: 2px;
                        }
                    }
                    .postcode-error {
                        background: #ffebeb;
                        color: #de6565;
                        &:before {
                            content: @icon-not-allowed-symbol;
                        }
                    }
                    .shipping-info {
                        background: #e9fbe9;
                        color: #47b547;
                        &:before {
                            content: @icon-calendar;
                        }
                    }
                    .is-cod {
                        background: #fff7e7;
                        color: #ab8540;
                        &:before {
                            content: @icon-payment-method;
                            margin-top: 10px;
                            .lib-font-size(19px);
                        }
                    }
                    .is-shipping:empty,
                    .is-cod:empty,
                    .shipping-info:empty,
                    .postcode-error:empty {
                        padding: 0;
                        &:before {
                            display: none;
                        }
                    }
                }
            }
        }
        .customer-history {
            ul {
                margin: 0;
                padding: 0;
                list-style: none;
                float: left;
                clear: both;
                &.saved-login {
                    box-shadow: 0 1px 6px 0 #dedede;
                    width: 280px;
                    box-sizing: border-box;
                    border-bottom-left-radius: 3px;
                    border-bottom-right-radius: 3px;
                    padding: 9px 15px 8px 15px;
                    .lib-font-size(13px);
                    background: #f7f7f7;
                    &:before {
                        content: "";
                        width: 0;
                        height: 0;
                        border-style: solid;
                        border-width: 5px 5px 0 5px;
                        border-color: @color-gray20 transparent transparent;
                        position: absolute;
                        bottom: -5px;
                        left: 19px;
                    }
                    li {
                        margin: 0;
                        a {
                            color: @color-orange-red1;
                        }
                    }
                }
                &.postcode-history {
                    box-shadow: 0 1px 6px 0 #dedede;
                    width: 280px;
                    box-sizing: border-box;
                    padding: 0px 0;
                    border-bottom-left-radius: 3px;
                    border-bottom-right-radius: 3px;
                    li {
                        padding: 6px 12px 5px 12px;
                        margin: 0;
                        cursor: pointer;
                        border-bottom: 1px solid @color-gray-darken0;
                        &.title {
                            font-weight: bold;
                            background: #f7f7f7;
                            font-size: 12px;
                            text-transform: uppercase;
                            padding: 10px 12px 7px 12px;
                            label {
                                padding: 0;
                            }
                        }
                        &:last-child {
                            border-bottom: 0;
                        }
                    }
                }
            }
        }
    }
}