<?xml version="1.0"?>
<!--
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
-->
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="product.info.main">
            <block class="Magedelight\Productavailability\Block\Postcode" name="magedelight.product.postcode.simple" ifconfig="productavailability/general/enable" template="Magedelight_Productavailability::product/view/postcode.phtml" before="product.info" />
        </referenceContainer>
        <!--<referenceContainer name="product.info.main">
            <block class="Magento\Catalog\Block\Product\View" name="magedelight.product.postcode.additional" ifconfig="productavailability/general/enable" template="Magedelight_Productavailability::product/view/postcode.phtml" before="product.info"/>
        </referenceContainer>-->
    </body>
</page>
