<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
?>
<?php

$ajaxCallUrl = $block->getUrl('productavailability/postcodecheck/index');
$productId = $block->getCurrentProduct()->getId();
$isInStock = $block->getStockQtyByProductId($block->getCurrentProduct());
$AllShippingAddress = $block->getAllShippingAddress();
$currentPostcode = $block->getCurrentPostcode();
$postcodeHistory = $block->getPostcodeHistory();


if ($block->showPostcodeHtmlBlock()):
    ?>
<div class="md-postcode">
    <form class="form-product-postcode" method="post" id="form-product-postcode" data-mage-init='{"validation":{}}'>
        <div class="postcode-box">
            <div class="postcode-inner">
                <div class="control">
                    <input type="text"
                        <?php if ($isInStock == 0):?>
                            disabled="disabled"
                        <?php endif; ?>
                        <?php if ($isInStock == 1):?>
                            value="<?= /* @noEscape */ $currentPostcode; ?>"
                        <?php endif; ?>
                           placeholder="Enter Delivery Postcode" name="postcode" id="postcode" title="Postcode" 
                           class="input-text postcode" autocomplete="off" data-validate="{'required':true}">
                </div>
            </div>
            <div class="postcode-check-btn">
                <button class="action primary" <?php if ($isInStock == 0):?>
                        disabled="disabled"
                        <?php endif; ?>type="submit" title="Check" id="button-postcode-check">
                    <span>Check</span>
                </button>
            </div>
            <div class="postcode-response">
                <div class="postcode-error"></div>
                <div class="is-shipping"></div>
                <div class="shipping-info"></div>
                <div class="is-cod"></div>
            </div>
        </div>
    </form>
    <div class="customer-history" id="customer-history" style="display: none">
        <ul class="postcode-history">
        <?php if (!empty($postcodeHistory)):?>
                <li class="title">Recent History</li>
                <?php foreach ($postcodeHistory as $recentPostcode):?>
                    <li postcode="<?= /* @noEscape */ $recentPostcode; ?>"><?= /* @noEscape */ $recentPostcode; ?></li>
                <?php endforeach; ?>

        <?php endif;?>
        </ul>
        
        <ul class="saved-login saved-address">
            <?php if (!$block->isCustomerLogin()) {?>
                <li class="see-address-title">
                    <a href="<?= $block->escapeUrl($block->getUrl('customer/account/login')); ?>">login</a>
                    <span> to see your saved addresses</span>
                </li>
            <?php } else { ?>
                <li class="address-title">Saved Addresses</li>
                <?php
                if ($AllShippingAddress) {
                    foreach ($AllShippingAddress as $address) { ?>
                        <li class="saved-postcode" postcode="<?= /* @noEscape */ $address->getPostcode(); ?>">
                            <?= /* @noEscape */ $address->getPostcode(); ?>
                            <span><?= /* @noEscape */ $address->getStreet()[0]."..."; ?></span>
                        </li>
                    <?php }
                }
                ?>
            <?php } ?>
        </ul>
    </div>
</div>
<script type="text/x-magento-init">
{
    "body": {
    "Magedelight_Productavailability/js/postcode": {
        "ajaxUrl":"<?= /* @noEscape */ $ajaxCallUrl; ?>",
        "productId":"<?= /* @noEscape */ $productId; ?>"
        }
     }
}
</script>
<?php endif; ?>