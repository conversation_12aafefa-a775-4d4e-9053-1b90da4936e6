<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Api\Data;

interface PostcodeRuleSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Postcode Rule list.
     *
     * @return \Magedelight\Productavailability\Api\Data\PostcodeRuleInterface[]
     */
    public function getItems();

    /**
     * Set title list.
     *
     * @param \Magedelight\Productavailability\Api\Data\PostcodeRuleInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
