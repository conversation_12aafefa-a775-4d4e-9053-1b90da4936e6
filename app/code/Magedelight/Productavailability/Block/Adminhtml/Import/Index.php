<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Block\Adminhtml\Import;

use Magento\Backend\Block\Widget;
use Magento\Backend\Block\Template\Context;

class Index extends Widget
{
    /**
     * @var string
     */
    protected $_template = 'import.phtml';

    /**
     * @param Context $context
     * @param array $data
     */
    public function __construct(Context $context, array $data = [])
    {
        parent::__construct($context, $data);
        $this->setUseContainer(true);
    }
}
