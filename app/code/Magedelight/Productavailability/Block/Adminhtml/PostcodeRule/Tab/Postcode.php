<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Tab;

use Magento\Backend\Block\Widget\Grid;
use Magento\Backend\Block\Widget\Grid\Column;
use Magento\Backend\Block\Widget\Grid\Extended;
use Magedelight\Productavailability\Model\Postcode\Source\Status;
use Magento\Framework\App\ObjectManager;

class Postcode extends \Magento\Backend\Block\Widget\Grid\Extended
{

    /**
     * @var \Magento\Framework\Registry
     */
    private $_coreRegistry = null;

    /**
     * @var \Magedelight\Productavailability\Model\PostcodeFactory
     */
    private $_postcodeFactory;

    /**
     * @var \Magedelight\Productavailability\Model\PostcodeRuleRepository
     */
    private $postcodeRuleRepository;

    /**
     * @var Status
     */
    private $status;

    /**
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Backend\Helper\Data $backendHelper
     * @param \Magedelight\Productavailability\Model\PostcodeFactory $postcodeFactory
     * @param \Magedelight\Productavailability\Model\PostcodeRuleRepository $postcodeRuleRepository
     * @param \Magento\Framework\Registry $coreRegistry
     * @param array $data
     * @param Status|null $status
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Backend\Helper\Data $backendHelper,
        \Magedelight\Productavailability\Model\PostcodeFactory $postcodeFactory,
        \Magedelight\Productavailability\Model\PostcodeRuleRepository $postcodeRuleRepository,
        \Magento\Framework\Registry $coreRegistry,
        array $data = [],
        Status $status = null
    ) {
        $this->_postcodeFactory = $postcodeFactory;
        $this->postcodeRuleRepository = $postcodeRuleRepository;
        $this->_coreRegistry = $coreRegistry;
        $this->status = $status ?: ObjectManager::getInstance()->get(Status::class);
        parent::__construct($context, $backendHelper, $data);
    }

    /**
     * Constructor
     *
     * @return void
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('productavailability_postcoderule_postcodes');
        $this->setDefaultSort('postcode_id');
        $this->setUseAjax(true);
    }

    /**
     * Get Postcode Rule Function
     *
     * @return array|null
     */
    public function getPostcodeRule()
    {
        $registry = $this->_coreRegistry->registry('productavailability_postcoderule');
        $postcodes = [];
        if ($registry && null !== $registry->getAssignedPostcodes()) {
            $postcodes = array_flip($registry->getAssignedPostcodes());
        }
        return $postcodes;
    }

    /**
     * Add Column Filter to Collection Function
     *
     * @param Column $column
     * @return $this
     */
    protected function _addColumnFilterToCollection($column)
    {
        // Set custom filter for in postcoderule flag
        if ($column->getId() == 'in_postcoderule') {
            $productIds = $this->_getSelectedPostcodes();
            if (empty($productIds)) {
                $productIds = 0;
            }
            if ($column->getFilter()->getValue()) {
                $this->getCollection()->addFieldToFilter('postcode_id', ['in' => $productIds]);
            } elseif (!empty($productIds)) {
                $this->getCollection()->addFieldToFilter('postcode_id', ['nin' => $productIds]);
            }
        } else {
            parent::_addColumnFilterToCollection($column);
        }
        return $this;
    }

    /**
     * Prepare Collection Function
     *
     * @return Grid
     */
    protected function _prepareCollection()
    {
        if ($this->getPostcodeRule()) {
            $this->setDefaultFilter(['in_postcoderule' => 1]);
        }

        $collection = $this->_postcodeFactory->create()->getCollection();
        $this->setCollection($collection);
        return parent::_prepareCollection();
    }

    /**
     * Prepare Column Function
     *
     * @return Extended
     */
    protected function _prepareColumns()
    {
        $this->addColumn(
            'in_postcoderule',
            [
                'type' => 'checkbox',
                'name' => 'in_postcoderule',
                'values' => $this->_getSelectedPostcodes(),
                'index' => 'postcode_id',
                'header_css_class' => 'col-select col-massaction',
                'column_css_class' => 'col-select col-massaction'
            ]
        );

        /* $this->addColumn(
            'postcode_id',
            [
            'header' => __('ID'),
            'sortable' => true,
            'index' => 'postcode_id',
            'header_css_class' => 'col-id',
            'column_css_class' => 'col-id'
                ]
        ); */

        $this->addColumn('postcode', ['header' => __('Postcode / Zipcode'), 'index' => 'postcode']);
        
        $this->addColumn('city', ['header' => __('City/District'), 'index' => 'city']);
        
        $this->addColumn('state', ['header' => __('State'), 'index' => 'state',
        'renderer'  => \Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Tab\Renderer\Region::class]);
        
        $this->addColumn('country', ['header' => __('Country'), 'index' => 'country']);

        $this->addColumn('status', ['header' => __('Status'), 'index' => 'status', 'type' => 'options',
        'options' => $this->status->getOptionArray()]);

        return parent::_prepareColumns();
    }

    /**
     * Get Grid URL Function
     *
     * @return string
     */
    public function getGridUrl()
    {
        return $this->getUrl('productavailability/*/grid', ['_current' => true]);
    }

    /**
     * Get Selected Post Codes Function
     *
     * @return array
     */
    protected function _getSelectedPostcodes()
    {
        $postcodes = $this->getRequest()->getPost('selected_postcodes');

        if ($postcodes === null) {
            $postcodes = $this->getPostcodeRule();
            if (count($postcodes)) {
                return array_keys($postcodes);
            }
        }
        return $postcodes;
    }
}
