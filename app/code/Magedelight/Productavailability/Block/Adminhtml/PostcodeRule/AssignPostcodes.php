<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Block\Adminhtml\PostcodeRule;

class AssignPostcodes extends \Magento\Backend\Block\Template
{
     /**
     * @var string
     */
    protected $_nameInLayout = '';
    
    /**
     * Block template
     *
     * @var string
     */
    protected $_template = 'Magedelight_Productavailability::postcoderule/assign_postcodes.phtml';

    /**
     * @var \Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Tab\Postcode
     */
    protected $blockGrid;

    /**
     * @var \Magento\Framework\Registry
     */
    protected $registry;

    /**
     * @var \Magento\Framework\Json\EncoderInterface
     */
    protected $jsonEncoder;

    /**
     * AssignPostcodes constructor.
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Json\EncoderInterface $jsonEncoder
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Json\EncoderInterface $jsonEncoder,
        array $data = []
    ) {
        $this->registry = $registry;
        $this->jsonEncoder = $jsonEncoder;
        parent::__construct($context, $data);
    }

    /**
     * Retrieve instance of grid block
     *
     * @return \Magento\Framework\View\Element\BlockInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getBlockGrid()
    {
        if (null === $this->blockGrid) {
            $this->blockGrid = $this->getLayout()->createBlock(
                \Magedelight\Productavailability\Block\Adminhtml\PostcodeRule\Tab\Postcode::class,
                'postcoderule.postcode.grid'
            );
        }
        return $this->blockGrid;
    }

    /**
     * Return HTML of grid block
     *
     * @return string
     */
    public function getGridHtml()
    {
        return $this->getBlockGrid()->toHtml();
    }

    /**
     * Get Post Code Json
     *
     * @return string
     */
    public function getPostcodesJson()
    {
        $postcodes = $this->getPostcodeRule();
        if (!empty($postcodes)) {
            return $this->jsonEncoder->encode($postcodes);
        }
        return '{}';
    }

    /**
     * Retrieve current postcode rule instance
     *
     * @return array|null
     */
    public function getPostcodeRule()
    {
        $registry = $this->registry->registry('productavailability_postcoderule');
        $postcodes = [];
        if ($registry && null !== $registry->getAssignedPostcodes()) {
            $postcodes = array_flip($registry->getAssignedPostcodes());
        }
        return $postcodes;
    }
}
