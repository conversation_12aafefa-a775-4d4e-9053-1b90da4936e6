<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Helper;

use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\Stdlib\CookieManagerInterface;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    public const XML_PATH_POSTCODE_ACTIVE = 'productavailability/general/enable';
    public const XML_PATH_SHIPPING_UNAVAILABILITY_MESSAGE = 'productavailability/general/shipping_error_message';
    public const XML_PATH_ESTIMATED_DELIVERY_MESSAGE = 'productavailability/general/shipping_delivery_message';
    public const COD_CHECKER_ENABLE = 'productavailability/general/cod_checker_enable';
    public const COD_AVAILABILITY_MESSAGE = 'productavailability/general/cod_sucess_message';
    public const COD_UNAVAILABILITY_MESSAGE = 'productavailability/general/cod_error_message';
    public const SEND_NOTIFY_EMAIL_TO_CUSTOMER = 'productavailability/productalert/allow_postcode';
    public const FIELD_SEPERATOR = 'productavailability/general/field_seperator';

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $storeConfig;

    /**
     * @var \Magento\Framework\ObjectManagerInterface
     */
    private $_objectManager;

    /**
     * @var CookieManagerInterface
     */
    private $cookieManager;

    /**
     * @var \Magento\Framework\View\LayoutInterface
     */
    private $_layout;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $_storeManager;

    /**
     * Data constructor.
     *
     * @param Context $context
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param CookieManagerInterface $cookieManager
     * @param \Magento\Framework\View\LayoutInterface $layout
     * @param \Magento\Framework\ObjectManagerInterface $objectmanager
     */
    public function __construct(
        Context $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        CookieManagerInterface $cookieManager,
        \Magento\Framework\View\LayoutInterface $layout,
        \Magento\Framework\ObjectManagerInterface $objectmanager
    ) {
    
        $this->storeConfig = $context->getScopeConfig();
        $this->cookieManager = $cookieManager;
        $this->_layout = $layout;
        $this->_objectManager = $objectmanager;
        $this->_storeManager = $storeManager;
        parent::__construct($context);
    }

    /**
     * Check Extension is Enabled or not.
     */
    public function isEnabled()
    {
        return $this->getConfig('productavailability/general/enable');
    }

    /**
     * Get Configurations
     *
     * @param string $config_path
     */
    public function getConfig($config_path)
    {
        return $this->_storeManager->getStore()->getConfig(
            $config_path,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check Extension is Enabled or not.
     *
     * @param string|int $storeId
     */
    public function isModuleEnable($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_POSTCODE_ACTIVE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
    
    /**
     * Check COD is Enabled or not.
     *
     * @param string|int $storeId
     */
    public function isCODCheckingEnable($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::COD_CHECKER_ENABLE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get Shipping Error Message
     *
     * @param string|int $storeId
     */
    public function getShippingErrorMessage($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_SHIPPING_UNAVAILABILITY_MESSAGE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get Delivery Message
     *
     * @param string|int $storeId
     */
    public function getDeliveryMessage($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::XML_PATH_ESTIMATED_DELIVERY_MESSAGE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get COD Success Message
     *
     * @param string|int $storeId
     */
    public function getCODSucessMessage($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::COD_AVAILABILITY_MESSAGE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get COD Error Message
     *
     * @param string|int $storeId
     */
    public function getCODErroMessage($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::COD_UNAVAILABILITY_MESSAGE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Send Email to Customer
     *
     * @param string|int $storeId
     */
    public function sendEmailToCustomer($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::SEND_NOTIFY_EMAIL_TO_CUSTOMER,
            ScopeInterface::SCOPE_WEBSITES,
            $this->getWebsiteId($storeId)
        );
    }

    /**
     * GetFieldSeperator
     *
     * @param string|int $storeId
     */
    public function getFieldSeperator($storeId = 0)
    {
        return $this->storeConfig->getValue(
            self::FIELD_SEPERATOR,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Create block instance
     *
     * @param string|\Magento\Framework\View\Element\AbstractBlock $block
     * @return \Magento\Framework\View\Element\AbstractBlock
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function createBlock($block)
    {
        if (is_string($block)) {
            if (class_exists($block)) {
                $block = $this->_layout->createBlock($block);
            }
        }
        if (!$block instanceof \Magento\Framework\View\Element\AbstractBlock) {
            throw new \Magento\Framework\Exception\LocalizedException(__('Invalid block type: %1', $block));
        }
        return $block;
    }

    /**
     * Get Cookie Manager
     */
    public function getCookieManager()
    {
        return $this->_objectManager->create(\Magento\Framework\Stdlib\CookieManagerInterface::class);
    }

    /**
     * Get Store Id
     *
     * @param string|int $storeId
     */
    public function getStoreId()
    {
        return $this->_storeManager->getStore()->getStoreId();
    }
    
    /**
     * Get Website Id
     *
     * @param string|int $storeId
     */
    public function getWebsiteId($storeId)
    {
        return $this->_storeManager->getStore($storeId)->getWebsiteId();
    }
}
