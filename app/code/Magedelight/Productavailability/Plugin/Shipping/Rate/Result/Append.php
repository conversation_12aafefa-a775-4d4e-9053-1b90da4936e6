<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Plugin\Shipping\Rate\Result;

use Magedelight\Productavailability\Helper\ShippingHelper;

class Append
{
    /**
     * @var ShippingHelper
     */
    private $shippingHelper;

    /**
     * @param ShippingHelper $shippingHelper
     */
    public function __construct(ShippingHelper $shippingHelper)
    {
        $this->shippingHelper = $shippingHelper;
    }
    
    /**
     * Validate each shipping method before append.
     *
     * @param \Magento\Shipping\Model\Rate\Result $subject
     * @param \Magento\Quote\Model\Quote\Address\RateResult\AbstractResult|\Magento\Shipping\Model\Rate\Result $result
     * @return array
     */
    public function beforeAppend($subject, $result)
    {
        if (!$this->shippingHelper->isModuleEnable()) {
            return [$result];
        }
        
        if (!$result instanceof \Magento\Quote\Model\Quote\Address\RateResult\Method) {
            return [$result];
        }
        
        $productAssigned = $this->shippingHelper->isProductAvailable();
        
        if (!$productAssigned) {
            $result->setIsDisabled(true);
        }
        
        if ($this->shippingHelper->isMethodRestricted($result)) {
            $result->setIsDisabled(true);
        }
        
        return [$result];
    }
}
