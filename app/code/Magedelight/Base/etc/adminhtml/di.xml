<?xml version="1.0"?>
<!-- 
/**
 * Magedelight
 * Copyright (C) 2019 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_Base
 * @copyright Copyright (c) 2019 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */ 
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Backend\Model\View\Result\Page">
        <plugin name="magedelight_base_modify_setactivemenu" type="Magedelight\Base\Plugin\Magento\Backend\Model\View\Result\Page" />
    </type>

    <type name="Magento\Backend\Model\Menu\Item">
	    <plugin name="md_common_menu_item_newtab" type="Magedelight\Base\Plugin\Magento\Backend\Model\Menu\Item" />
	</type>
</config>