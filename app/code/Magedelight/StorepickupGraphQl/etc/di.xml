<?xml version="1.0" ?>

<!--
/**
* Magedelight
* Copyright (C) 2019 Magedelight <<EMAIL>>
*
* @category Magedelight
* @package Magedelight_StorepickupGraphQl
* @copyright Copyright (c) 2019 Mage Delight (http://www.magedelight.com/)
* @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
* <AUTHOR> <<EMAIL>>
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">    
    <type name="Magento\Framework\GraphQl\Query\Resolver\Argument\FieldEntityAttributesPool">
        <arguments>
            <argument name="attributesInstances" xsi:type="array">
                <item name="pickup_stores" xsi:type="object">
                    \Magedelight\StorepickupGraphQl\Model\Resolver\FilterArgument
                </item>
            </argument>
        </arguments>
    </type>
</config>
