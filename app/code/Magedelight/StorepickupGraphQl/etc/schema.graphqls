type Query {
    storePickup(
        filter: storePickupFilterInput @doc(description: "Identifies which store pickup fields to search for and return."),
        pageSize: Int = 5 @doc(description: "How many items should show on the page"),
        currentPage: Int = 1 @doc(description: "Allows to ussing paging it start with 1"),
    ):storePickupOutput @resolver(class: "\\Magedelight\\StorepickupGraphQl\\Model\\Resolver\\StorePickup") @doc(description: "The Impelemention to resolve Store Pickup")

    getStoreHolidayInformation(storelocator_id: String!): StoreHolidayInfo @resolver (class: "\\Magedelight\\StorepickupGraphQl\\Model\\Resolver\\StoreHolidayInfo") @doc(description:"Returns holiday information about store")

    getStoreTimeInformation(storelocator_id: String!, date: String!): StoreTimeInfo @resolver (class: "\\Magedelight\\StorepickupGraphQl\\Model\\Resolver\\StoreTimeInfo") @doc(description:"Returns Store time information")
    storeHoliday(
            filter: storeHolidayFilterInput @doc(description: "Identifies which store holiday fields to search for and return."),
            pageSize: Int = 5 @doc(description: "How many items should show on the page"),
            currentPage: Int = 1 @doc(description: "Allows to ussing paging it start with 1"),
     ):storeHolidayOutput @resolver(class: "\\Magedelight\\StorepickupGraphQl\\Model\\Resolver\\StoreHoliday") @doc(description: "The Impelemention to resolve Store Pickup")

}

type Mutation {
    createStorePickup (input: CreateStoreInput!): CreateStoreOutput @resolver(class: "\\Magedelight\\StorepickupGraphQl\\Model\\Resolver\\CreatStorePickUp") @doc(description:"Create store pickup")

    deleteStorePickup(storelocator_id: Int!): DeleteStoreOutput @resolver(class: "\\Magedelight\\StorepickupGraphQl\\Model\\Resolver\\DeleteStorePickup") @doc(description:"Delete a store pickup")
    
    createStoreHoliday (input: CreateStoreHolidayInput!): CreateStoreHolidayOutput @resolver(class: "\\Magedelight\\StorepickupGraphQl\\Model\\Resolver\\CreatStoreHoliday") @doc(description:"Create store holiday")
    deleteStoreHoliday(holiday_id: Int!): DeleteStoreHolidayOutput @resolver(class: "\\Magedelight\\StorepickupGraphQl\\Model\\Resolver\\DeleteStoreHoliday") @doc(description:"Delete a store  holiday")
}

input storePickupFilterInput @doc(description: "storePickupFilterInput specifies the fields to search") {
    storelocator_id: FilterTypeInput  @doc(description: "storelocator_id")
    storename: FilterTypeInput  @doc(description: "storename")
    is_active: FilterTypeInput  @doc(description: "is_active")
    url_key: FilterTypeInput  @doc(description: "url_key")
    or: storePickupFilterInput @doc(description: "The keyword required to perform a logical OR comparison")
}

input storeHolidayFilterInput @doc(description: "storeHolidayFilterInput specifies the fields to search") {
    holiday_id: FilterTypeInput  @doc(description: "holiday_id")
    holiday_name: FilterTypeInput  @doc(description: "holiday_name")
    is_active: FilterTypeInput  @doc(description: "is_active")
    or: storePickupFilterInput @doc(description: "The keyword required to perform a logical OR comparison")
}

type storePickupOutput {
    total_count:  Int @doc(description: "total_count")
    items: [PickupStores] @doc(description: "items")
}

type storeHolidayOutput {
    total_count:  Int @doc(description: "total_count")
    items: [HolidayStores] @doc(description: "items")
}

type PickupStores  {
    storelocator_id: Int @doc(description: "storelocator_id"),
    storename: String @doc(description: "storename"),
    storeemail: String @doc(description: "storeemail"),
    is_active: Int @doc(description: "is_active"),
    url_key: String @doc(description: "url_key"),
    website_url: String @doc(description: "website_url"),
    facebook_url: String @doc(description: "facebook_url"),
    twitter_url: String @doc(description: "twitter_url"),
    description: String @doc(description: "description"),
    address: String @doc(description: "address"),
    country_id: String @doc(description: "country_id"),
    region: String @doc(description: "region"),
    region_id: Int @doc(description: "region_id"),
    city: String @doc(description: "city"),
    zipcode: String @doc(description: "zipcode"),
    telephone: String @doc(description: "telephone"),
    longitude: String @doc(description: "longitude"),
    latitude: String @doc(description: "latitude"),
    storeimage: String @doc(description: "storeimage"),
    storetime: String @doc(description: "storetime"),
    meta_title: String @doc(description: "meta_title"),
    meta_keywords: String @doc(description: "meta_keywords"),
    meta_description: String @doc(description: "meta_description"),
    conditions: String @doc(description: "conditions"),
}

type HolidayStores  {
    holiday_id: Int @doc(description: "holiday_id")
    holiday_name: String @doc(description: "holiday_name")
    holiday_applied_stores: String @doc(description: "storeemail")
    holiday_date_from: String @doc(description: "holiday_date_from")
    holiday_date_to: String @doc(description: "holiday_date_to")
    holiday_comment: String @doc(description: "	holiday_comment")
    is_repetitive: Int @doc(description: "is_repetitive")
    is_active: Int @doc(description: "is_active")
    all_store: String @doc(description: "all_store")
}

type DeleteStoreOutput {
    result:Boolean
}

type DeleteStoreHolidayOutput {
    result:Boolean
}

input CreateStoreInput {
    storelocator_id: Int @doc(description: "storelocator_id")
    storename: String @doc(description: "storename")
    storeemail: String @doc(description: "storeemail")
    is_active: Int @doc(description: "is_active")
    url_key: String @doc(description: "url_key")
    address: String @doc(description: "address")
    country_id: String @doc(description: "country_id")
    region: String @doc(description: "region")
    region_id: Int @doc(description: "region_id")
    city: String @doc(description: "city")
    zipcode: String @doc(description: "zipcode")
    telephone: String @doc(description: "telephone")
}

input CreateStoreHolidayInput {
    holiday_id: Int @doc(description: "holiday_id")
    holiday_name: String @doc(description: "holiday_name")
    holiday_applied_stores: String @doc(description: "storeemail")
    holiday_date_from: String @doc(description: "holiday_date_from")
    holiday_date_to: String @doc(description: "holiday_date_to")
    holiday_comment: String @doc(description: "	holiday_comment")
    is_repetitive: Int @doc(description: "is_repetitive")
    is_active: Int @doc(description: "is_active")
    all_store: String @doc(description: "all_store")
}

type CreateStoreOutput {
    store_pick_up: PickupStores @doc(description: "items")
}

type CreateStoreHolidayOutput {
    store_holiday: HolidayStores @doc(description: "items")
}

type StoreHolidayInfo {
    dates: holidayDates,
    days: String,
    details: [holidayDetails]
}

type holidayDates {
    repetitive: [String] @doc(description: "repititive dates"),
    normal: [String] @doc(description: "normal dates"),
    shipping_off_day: [String] @doc(description: "shipping off day dates")
}

type holidayDetails {
    holiday_id: Int @doc(description: "holiday id"),
    holiday_name: String @doc(description: "holiday name"),
    holiday_applied_stores: String @doc(description: "holiday applied stores"),
    holiday_date_from: String @doc(description: "holiday date from"),
    holiday_date_to: String @doc(description: "holiday date to"),
    holiday_comment: String @doc(description: "holiday comment"),
    is_repetitive: Int @doc(description: "yearly repitive"),
    is_active: Int @doc(description: "is active holiday"),
    all_store: Int @doc(description: "all store"),
}

type StoreTimeInfo {
    storetime: [String] @doc(description: "storetime")
}
