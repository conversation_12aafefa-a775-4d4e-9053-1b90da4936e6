<?php


namespace Magedelight\StorepickupGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\Resolver\ContextInterface;
use Magento\Framework\GraphQl\Query\Resolver\Value;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magedelight\StorepickupGraphQl\Model\CreatStoreHolidayService;

class CreatStoreHoliday implements ResolverInterface
{
    /**
     * @var CreatStoreHolidayService
     */
    protected $creatStoreHolidayService;

    /**
     * CreatStorePickUp constructor.
     * @param CreatStoreHolidayService $creatStoreHolidayService
     */
    public function __construct(
        CreatStoreHolidayService $creatStoreHolidayService
    ) {
        $this->creatStoreHolidayService = $creatStoreHolidayService;
    }

    /**
     * @param Field $field
     * @param ContextInterface $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return Value|mixed|void
     * @throws GraphQlInputException
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (empty($args['input']) || !is_array($args['input'])) {
            throw new GraphQlInputException(__('"input" value should be specified'));
        }
        $storeData = $this->creatStoreHolidayService->execute($args['input']);

        return ['store_holiday' => array(
            'holiday_id' => $storeData->getHolidayId(),
            'holiday_name' => $storeData->getHolidayname(),
            'holiday_applied_stores' => $storeData->getHolidayAppliedStores(),
            'holiday_date_from' => $storeData->getHolidayDateFrom(),
            'holiday_date_to' => $storeData->getHolidayDateTo(),
            'holiday_comment' => $storeData->getHolidayComment(),
            'is_repetitive' => $storeData->getIsRepetitive(),
            'is_active' => $storeData->getIsActive(),
            'all_store' => $storeData->getAllStore())
            ];
    }
}
