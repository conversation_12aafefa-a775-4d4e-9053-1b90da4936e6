<?xml version="1.0" encoding="UTF-8"?>

<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="sales_model_service_quote_submit_before">
        <observer name="magedelight_storepickup" instance="Magedelight\Storepickup\Observer\SaveDeliveryDateToOrderObserver"/>
    </event>
    <event name="checkout_onepage_controller_success_action">
        <observer name="checkout_controller_success_action" instance="Magedelight\Storepickup\Observer\AfterPlaceOrder"  />
    </event>
    <event name="order_cancel_after">
        <observer name="sales_order_cancel_after" instance="Magedelight\Storepickup\Observer\SendMailAfterCancelOrder"  />
    </event>
    <event name="sales_order_save_after">
        <observer name="sales_order_history_save_before" instance="Magedelight\Storepickup\Observer\SetDeliveryPin"  />
    </event>
    <event name="layout_load_before">
        <observer name="magedelight_layout_load_before" instance="Magedelight\Storepickup\Observer\LayoutLoadObserver" />
    </event>
</config>
