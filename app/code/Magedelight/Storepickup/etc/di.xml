<?xml version="1.0" ?>

<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magedelight\Storepickup\Api\StorelocatorRepositoryInterface" type="Magedelight\Storepickup\Model\StorelocatorRepository"/>
    <preference for="Magedelight\Storepickup\Api\Data\StorelocatorInterface" type="Magedelight\Storepickup\Model\Data\Storelocator"/>
    <preference for="Magedelight\Storepickup\Api\Data\StorelocatorSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
    <preference for="Magedelight\Storepickup\Api\StoreInformationManagementInterface" type="Magedelight\Storepickup\Model\StoreInformationManagement"/>

    <!--Holiday -->
    <preference for="Magedelight\Storepickup\Api\StoreholidayRepositoryInterface" type="Magedelight\Storepickup\Model\StoreholidayRepository"/>
    <preference for="Magedelight\Storepickup\Api\Data\StoreholidayInterface" type="Magedelight\Storepickup\Model\Data\Storeholiday"/>
    <preference for="Magedelight\Storepickup\Api\Data\StoreholidaySearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>

    <virtualType name="Magedelight\Storepickup\Model\ResourceModel\Storelocator\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_storelocator</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Storepickup\Model\ResourceModel\Storelocator\Collection</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="managestores_storelocator_listing_data_source" xsi:type="string">Magedelight\Storepickup\Model\ResourceModel\Storelocator\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <!-- Holiday -->
    <virtualType name="Magedelight\Storepickup\Model\ResourceModel\Storeholiday\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">magedelight_store_holiday</argument>
            <argument name="resourceModel" xsi:type="string">Magedelight\Storepickup\Model\ResourceModel\Storeholiday\Collection</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="managestores_storeholiday_listing_data_source" xsi:type="string">Magedelight\Storepickup\Model\ResourceModel\Storeholiday\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <!-- Store image upload configuration -->
    <virtualType name="Magedelight\Storepickup\StoreImageUploader" type="Magedelight\Storepickup\Model\Uploader">
        <arguments>
            <argument name="baseTmpPath" xsi:type="const">Magedelight\Storepickup\Model\Uploader::IMAGE_TMP_PATH</argument>
            <argument name="basePath" xsi:type="const">Magedelight\Storepickup\Model\Uploader::IMAGE_PATH</argument>
            <argument name="allowedExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Magedelight\Storepickup\Model\UploaderPool">
        <arguments>
            <argument name="uploaders" xsi:type="array">
                <item name="image" xsi:type="string">Magedelight\Storepickup\StoreImageUploader</item>
            </argument>
        </arguments>
    </type>
    <type name="Magedelight\Storepickup\Controller\Adminhtml\Storelocator\Upload">
        <arguments>
            <argument name="uploader" xsi:type="object">Magedelight\Storepickup\StoreImageUploader</argument>
        </arguments>
    </type>
    <type name="Magedelight\Storepickup\Ui\Component\Listing\Column\Image">
        <arguments>
            <argument name="imageModel" xsi:type="object">Magedelight\Storepickup\StoreImageUploader</argument>
        </arguments>
    </type>

    <!-- for save store pickup extension attribute data -->
    <type name="Magento\Checkout\Model\ShippingInformationManagement">
        <plugin name="magedelight_storepickup_shippinginformationmanagement" type="Magedelight\Storepickup\Plugin\Checkout\Model\ShippingInformationManagementPlugin"/>
    </type>


    <virtualType name="Magento\Sales\Model\ResourceModel\Order\Grid" type="Magento\Sales\Model\ResourceModel\Grid">
        <arguments>
            <argument name="columns" xsi:type="array">
                <item name="pickup_store" xsi:type="string">sales_order.pickup_store</item>
                <item name="pickup_date" xsi:type="string">sales_order.pickup_date</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Magento\Sales\Model\Order">
        <plugin name="add-pickup-details" type="Magedelight\Storepickup\Plugin\OrderShippingInformationPlugin" sortOrder="10"/>
    </type>
    
</config>
