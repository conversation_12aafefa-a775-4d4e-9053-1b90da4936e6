<?xml version="1.0" ?>

<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
	<default>
		<magedelight_storepickup>
			<general>
				<enable>1</enable>
				<checkout_pickup_layout>along_with_shipping</checkout_pickup_layout>
				<allowguestcustomer>1</allowguestcustomer>
				<allowproductallstore>0</allowproductallstore>
				<show_product_availability>0</show_product_availability>
			</general>
			<timesloat>
				<enable_pickup_date>1</enable_pickup_date>
				<timesloatenable>1</timesloatenable>
				<timeratio>30</timeratio>
			</timesloat>
			<deliverypin>
				<enable_delivery_pin>0</enable_delivery_pin>
			</deliverypin>
		</magedelight_storepickup>
		<carriers>
			<storepickup>
				<model>Magedelight\Storepickup\Model\Carrier\Storepickup</model>
				<active>1</active>
				<title>Storepickup</title>
				<name>StorePickup</name>
				<price>0.00</price>
				<specificerrmsg>This shipping method is not available. To use this shipping method, please contact us.</specificerrmsg>
				<sallowspecific>0</sallowspecific>
			</storepickup>
		</carriers>
	</default>
</config>
