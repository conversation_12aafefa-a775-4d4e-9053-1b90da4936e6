<?xml version="1.0" encoding="UTF-8"?>

<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="core_layout_render_element">
        <observer name="md_store_info_add_to_order_view" instance="Magedelight\Storepickup\Observer\AdminOrderViewBlock" />
    </event>
    <event name="checkout_submit_all_after">
        <observer name="magedelight_schedule_save_order_backend" instance="Magedelight\Storepickup\Observer\SaveOrderEdit" />
    </event>
</config>
