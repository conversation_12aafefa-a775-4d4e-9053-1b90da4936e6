<?xml version="1.0" ?>

<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="magedelight" sortOrder="999" translate="label">
            <label>magedelight</label>
        </tab>
        <section id="magedelight_storepickup" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
            <class>separator-top md_section_storepickup</class>
            <label>Store Pickup</label>
            <tab>magedelight</tab>
            <resource>Magedelight_Storepickup::config_storepickup</resource>
            <group id="general" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
                <label>General</label>
                <field id="enable" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="select">
                    <label>Enable Storepickup</label>
                    <comment/>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="checkout_pickup_layout" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="15" translate="label" type="select">
                    <label>Display Pickup on Checkout As</label>
                    <source_model>Magedelight\Storepickup\Model\Config\Source\StoreLayoutCheckout</source_model>
                </field>
                <field id="allowguestcustomer" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Access To Guest Customer</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[If set to NO customer will no longer place order using store pickup.]]></comment>
                </field>
                <field id="allowproductallstore" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1" translate="label">
                    <label>Check product availability at store on checkout page</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="show_product_availability" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
                    <label>Show Stores On Product Page</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                    <comment><![CDATA[If set to YES customer can see available stores on product page..]]></comment>
                </field>
                <field id="store_layout_on_product" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="45" translate="label" type="select">
                    <label>Stores Layout On Product Page</label>
                    <source_model>Magedelight\Storepickup\Model\Config\Source\StoreLayout</source_model>
                    <depends>
                        <field id="show_product_availability">1</field>
                    </depends>
                </field>
            </group>
            <group id="timesloat" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Date And Time Settings</label>
                <field id="enable_pickup_date" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Pickup Date</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable Pickup Date on Frontend</comment>
                </field>
                <field id="timesloatenable" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Time Slot</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable_pickup_date">1</field>
                    </depends>
                </field>
                <field id="timeratio" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Time Interval</label>
                    <source_model>Magedelight\Storepickup\Model\Source\Timeinterval</source_model>
                    <depends>
                        <field id="enable_pickup_date">1</field>
                        <field id="timesloatenable">1</field>
                    </depends>
                </field>
            </group>
            <group id="deliverypin" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Delivery Pin Settings</label>
                <field id="enable_delivery_pin" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Delivery Pin</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable Delivery Pin</comment>
                </field>
                <field id="display_delivery_pin" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Delivery PIN In</label>
                    <source_model>Magedelight\Storepickup\Model\Source\DisplayDeliveryPin</source_model>
                    <depends>
                        <field id="enable_delivery_pin">1</field>
                    </depends>
                </field>
                <field id="generate_auto_shipment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Generate Auto-shipment on PIN validation</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable_delivery_pin">1</field>
                    </depends>
                </field>
                <field id="allow_force_shipment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow force shipment generation</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable_delivery_pin">1</field>
                    </depends>
                </field>
            </group>
            <group id="storeimportexport" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="0" showInStore="0">
                <comment><![CDATA[<ul class="message message-notification"><li>Keep <strong>Storelocator Id</strong> blank for insert new store where as mentioned <strong>Storelocator Id</strong> will update record.</li> <li>For <strong>Status</strong> write <strong>0</strong> for <strong>Disable</strong> & <strong>1</strong> for <strong>Enable</strong></li><li>In <strong>Store Id</strong> column add coma separated value Like <strong>0, 1, 2</strong> </li><li>Enter multiple <strong>telephone</strong> numbers with colon <strong>:</strong> separated</li></ul>]]></comment>
                <label>Import and Export</label>
                <field id="export" translate="label" type="Magedelight\Storepickup\Block\Adminhtml\Export\ExportStore" sortOrder="1" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Export</label>
                </field>
                <field id="import" translate="label" type="Magedelight\Storepickup\Block\Adminhtml\Export\ImportStore" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Import</label>
                    <backend_model>Magedelight\Storepickup\Model\Config\Backend\ImportStore</backend_model>
                    <comment>Upload Only .CSV File</comment>
                </field>
            </group>
        </section>
        <section id="carriers" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="1000" translate="label">
            <group id="storepickup" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
                <label>Magedelight Storepickup</label>
                <field id="active" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="select">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="text">
                    <label>Title</label>
                </field>
                <field id="name" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="text">
                    <label>Method Name</label>
                </field>
                <field id="type" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Type</label>
                    <source_model>Magento\OfflineShipping\Model\Config\Source\Flatrate</source_model>
                </field>
                <field id="price" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label" type="text">
                    <label>Price</label>
                    <validate>validate-number validate-zero-or-greater</validate>
                </field>
                <field id="handling_type" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Calculate Handling Fee</label>
                    <source_model>Magento\Shipping\Model\Source\HandlingType</source_model>
                </field>
                <field id="handling_fee" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Handling Fee</label>
                    <validate>validate-number validate-zero-or-greater</validate>
                </field>
                <field id="offdays" translate="label" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Shipping off Days</label>
                    <comment>Shipping off Days from current Date.</comment>
                </field>
                <field id="specificerrmsg" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type="textarea">
                    <label>Displayed Error Message</label>
                </field>
                <field id="sallowspecific" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="100" translate="label" type="select">
                    <label>Ship to Applicable Countries</label>
                    <frontend_class>shipping-applicable-country</frontend_class>
                    <source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
                </field>
                <field id="specificcountry" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="110" translate="label" type="multiselect">
                    <label>Ship to Specific Countries</label>
                    <can_be_empty>1</can_be_empty>
                    <source_model>Magento\Directory\Model\Config\Source\Country</source_model>
                </field>
                <field id="sort_order" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="120" translate="label" type="text">
                    <label>Sort Order</label>
                </field>
            </group>
        </section>
    </system>
</config>
