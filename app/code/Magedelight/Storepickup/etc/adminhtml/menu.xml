<?xml version="1.0"?>

<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Kris<PERSON> TechnoLabs. All Rights reserved.
*/
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Magedelight_Storepickup::storepickup_root"
             title="Store Pickup"
             module="Magedelight_Storepickup"
             sortOrder="50"
             resource="Magedelight_Storepickup::root"
             toolTip="magedelight_base" />

        <add id="Magedelight_Storepickup::storepickup_root_commonlyvisible"
             title="Store Pickup"
             module="Magedelight_Storepickup"
             sortOrder="50"
             parent="Magedelight_Base::md_modules"
             resource="Magedelight_Storepickup::root" />

        <add id="Magedelight_Storepickup::managestore_root"
             title="Manage Stores"
             module="Magedelight_Storepickup"
             sortOrder="600"
             action="managestores/storelocator/"
             parent="Magedelight_Storepickup::storepickup_root"
             resource="Magedelight_Storepickup::managestore_root" />

        <add id="Magedelight_Storepickup::storeholiday_root"
             title="Manage Holiday"
             module="Magedelight_Storepickup"
             sortOrder="600"
             action="managestores/storeholiday/"
             parent="Magedelight_Storepickup::storepickup_root"
             resource="Magedelight_Storepickup::storeholiday_root" />

        <add id="Magedelight_Storepickup::config_storepickup"
             title="Configuration"
             module="Magedelight_Storepickup"
             sortOrder="600"
             action="adminhtml/system_config/edit/section/magedelight_storepickup"
             parent="Magedelight_Storepickup::storepickup_root"
             resource="Magedelight_Storepickup::config_root" />

        <add id="Magedelight_Storepickup::useful_links"
             title="Useful Links"
             module="Magedelight_Storepickup"
             sortOrder="999"
             parent="Magedelight_Storepickup::storepickup_root"
             resource="Magedelight_Storepickup::root" />

        <add id="Magedelight_Storepickup::documentation"
             title="Documentation"
             module="Magedelight_Storepickup"
             sortOrder="10"
             target="_blank"
             parent="Magedelight_Storepickup::useful_links"
             resource="Magedelight_Storepickup::root" />
    </menu>
</config>
