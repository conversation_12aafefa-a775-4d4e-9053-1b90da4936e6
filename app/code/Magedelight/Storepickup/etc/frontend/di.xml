<?xml version="1.0"?>
<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Model\CompositeConfigProvider">
        <arguments>
            <argument name="configProviders" xsi:type="array">
                <item name="checkout_custom_shipping_block" xsi:type="object">Magedelight\Storepickup\Model\StorepickupConfigProvider</item>
            </argument>
        </arguments>
    </type>
    <preference for="Magento\Sales\Block\Order\Info" type="Magedelight\Storepickup\Block\Order\Info" />
</config>
