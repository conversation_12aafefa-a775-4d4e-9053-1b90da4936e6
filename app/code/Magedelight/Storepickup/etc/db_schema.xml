<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
  <table name="magedelight_storelocator" resource="default" engine="innodb" comment="Magedelight Storelocator Table">
    <column xsi:type="int" name="storelocator_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Storelocator ID"/>
    <column xsi:type="varchar" name="storename" nullable="false" length="255" comment="Stores Name"/>
    <column xsi:type="varchar" name="url_key" nullable="false" length="255" comment="URL Key"/>
    <column xsi:type="mediumtext" name="description" nullable="true" comment="Store Description"/>
    <column xsi:type="varchar" name="website_url" nullable="true" length="250" comment="Store Website Url"/>
    <column xsi:type="varchar" name="facebook_url" nullable="true" length="250" comment="Store Facebook Url"/>
    <column xsi:type="varchar" name="twitter_url" nullable="true" length="250" comment="Store Twitter Url"/>
    <column xsi:type="varchar" name="address" nullable="true" length="250" comment="Address"/>
    <column xsi:type="varchar" name="city" nullable="true" length="20" comment="Stores City"/>
    <column xsi:type="varchar" name="region" nullable="true"  comment="Stores Region"/>
    <column xsi:type="varchar" name="country_id" nullable="true"  comment="Stores Country"/>
    <column xsi:type="varchar" name="zipcode" nullable="true" length="250" comment="Zipcode"/>
    <column xsi:type="varchar" name="longitude" nullable="true" length="20" comment="Longitude"/>
    <column xsi:type="varchar" name="latitude" nullable="true" length="20" comment="Latitude"/>
    <column xsi:type="varchar" name="telephone" nullable="true" length="250" comment="Telephone"/>
    <column xsi:type="mediumtext" name="storeimage" nullable="true" comment="Store Image"/>
    <column xsi:type="mediumtext" name="storetime" nullable="true" comment="Time Schedule"/>
    <column xsi:type="varchar" name="meta_title" nullable="true" length="250" comment="Meta Title"/>
    <column xsi:type="mediumtext" name="meta_keywords" nullable="true" comment="Meta Keywords"/>
    <column xsi:type="mediumtext" name="meta_description" nullable="true" comment="Meta Description"/>
    <column xsi:type="int" name="is_active" padding="11" unsigned="false" nullable="false" identity="false" default="1" comment="Is Active"/>
    <column xsi:type="int" name="region_id" padding="11" unsigned="false" nullable="true" identity="false" comment="Region ID"/>
    <column xsi:type="text" name="conditions_serialized" nullable="false" comment="Conditions for where to display"/>
    <column xsi:type="varchar" name="storeemail" nullable="false"  comment="Stores Email"/>
    <column xsi:type="varchar" name="msi_source" nullable="true" length="255" comment="MSI Source"/>
    <column xsi:type="boolean" name="is_pickup_location" default="0" comment="Is Pickup Location"/>
    <constraint xsi:type="primary" referenceId="PRIMARY">
      <column name="storelocator_id"/>
    </constraint>
    <constraint xsi:type="unique" referenceId="MAGEDELIGHT_STORELOCATOR_URL_KEY">
      <column name="url_key"/>
    </constraint>
    <index referenceId="MAGEDELIGHT_STORELOCATOR_STORENAME" indexType="fulltext">
      <column name="storename"/>
    </index>
  </table>
  <table name="magedelight_store_holiday" resource="default" engine="innodb" comment="Magedelight Store Holiday Table">
    <column xsi:type="int" name="holiday_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Holiday ID"/>
    <column xsi:type="varchar" name="holiday_name" nullable="false" length="255" comment="Holiday Name"/>
    <column xsi:type="varchar" name="holiday_applied_stores" nullable="false" length="255" comment="Holiday Applied Stores"/>
    <column xsi:type="date" name="holiday_date_from" comment="From Date"/>
    <column xsi:type="date" name="holiday_date_to" comment="TO Date"/>
    <column xsi:type="mediumtext" name="holiday_comment" nullable="false" comment="Holiday Comment"/>
    <column xsi:type="int" name="is_repetitive" padding="11" unsigned="false" nullable="false" identity="false" comment="Yearly Repetative"/>
    <column xsi:type="int" name="is_active" padding="11" unsigned="false" nullable="false" identity="false" default="1" comment="Is Active"/>
    <column xsi:type="int" name="all_store" padding="11" unsigned="false" nullable="false" identity="false" comment="Allow Specific Stores?"/>
    <constraint xsi:type="primary" referenceId="PRIMARY">
      <column name="holiday_id"/>
    </constraint>
    <index referenceId="MAGEDELIGHT_STORE_HOLIDAY_HOLIDAY_ID" indexType="btree">
      <column name="holiday_id"/>
    </index>
  </table>
  <table name="quote" resource="default">
    <column xsi:type="text" name="pickup_store" nullable="false" comment="Pickup Store Name"/>
    <column xsi:type="datetime" name="pickup_date" on_update="false" nullable="true" comment="Pickup Date"/>
    <column xsi:type="text" name="pickup_store_email" nullable="false" comment="Store Email"/>
    <column xsi:type="text" name="delivery_pin" nullable="false" comment="Delivery Pin"/>
  </table>
  <table name="sales_order" resource="default">
    <column xsi:type="text" name="pickup_store" nullable="false" comment="Pickup Store Name"/>
    <column xsi:type="datetime" name="pickup_date" on_update="false" nullable="true" comment="Pickup Date"/>
    <column xsi:type="text" name="pickup_store_email" nullable="false" comment="Store Email"/>
    <column xsi:type="text" name="delivery_pin" nullable="false" comment="Delivery Pin"/>
    <column xsi:type="boolean" name="is_verified_pin" nullable="false" comment="Is Verified Pin"/>
  </table>
  <table name="sales_order_grid" resource="default">
    <column xsi:type="text" name="pickup_store" nullable="false" comment="Pickup Store Name"/>
    <column xsi:type="datetime" name="pickup_date" on_update="false" nullable="true" comment="Pickup Date"/>
  </table>
  <table name="magedelight_storelocator_store" resource="default" engine="innodb" comment="Magedelight Storelocator Field Store Table">
    <column xsi:type="int" name="storelocator_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Storelocator ID"/>
    <column xsi:type="smallint" name="store_ids" padding="5" unsigned="true" nullable="false" identity="false" comment="Storelocator storeID"/>
    <constraint xsi:type="primary" referenceId="PRIMARY">
      <column name="storelocator_id"/>
      <column name="store_ids"/>
    </constraint>
    <constraint xsi:type="foreign" referenceId="FK_2B734E600A5E74CA3D35445F87F8D3A2" table="magedelight_storelocator_store" column="storelocator_id" referenceTable="magedelight_storelocator" referenceColumn="storelocator_id" onDelete="CASCADE"/>
    <constraint xsi:type="foreign" referenceId="MAGEDELIGHT_STORELOCATOR_STORE_STORE_IDS_STORE_STORE_ID" table="magedelight_storelocator_store" column="store_ids" referenceTable="store" referenceColumn="store_id" onDelete="CASCADE"/>
  </table>
</schema>
