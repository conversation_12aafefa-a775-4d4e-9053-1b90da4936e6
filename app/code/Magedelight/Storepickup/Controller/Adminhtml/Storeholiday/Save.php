<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Controller\Adminhtml\Storeholiday;

use Magento\Framework\Exception\LocalizedException;
use Magedelight\Storepickup\Model\StoreholidayFactory;
use Magedelight\Storepickup\Model\ResourceModel\Storelocator\CollectionFactory as StoreCollectionFactory;

class Save extends \Magento\Backend\App\Action
{

    /**
     * @var \Magento\Framework\App\Request\DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var $storeholidayFactory
     */
    protected $storeholidayFactory;

    /**
     * @var StoreCollectionFactory
     */
    protected $storeCollectionFactory;

    /**
     * @var \Magedelight\Storepickup\Model\ResourceModel\Storeholiday
     */
    protected $storeholidayResource;

    /**
     * Save constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor
     * @param StoreholidayFactory $storeholidayFactory
     * @param StoreCollectionFactory $storeCollectionFactory
     * @param \Magedelight\Storepickup\Model\ResourceModel\Storeholiday $storeHolidayResource
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor,
        StoreholidayFactory $storeholidayFactory,
        StoreCollectionFactory $storeCollectionFactory,
        \Magedelight\Storepickup\Model\ResourceModel\Storeholiday $storeHolidayResource
    ) {
        $this->dataPersistor = $dataPersistor;
        $this->storeholidayFactory = $storeholidayFactory;
        $this->storeCollectionFactory = $storeCollectionFactory;
        $this->storeholidayResource=$storeHolidayResource;
        parent::__construct($context);
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->getRequest()->getPostValue();

        if ($data) {
            $id = $this->getRequest()->getParam('holiday_id');

            $model = $this->storeholidayFactory->create();
            if ($id) {
                $this->storeholidayResource->load($model, $id);
                if (!$model->getId() && $id) {
                    $this->messageManager->addErrorMessage(__('This holiday no longer exists.'));
                    return $resultRedirect->setPath('*/*/');
                }
            }

            try {
                $popData = $this->prepareData($data);
                $model->setData($popData);
                $this->storeholidayResource->save($model);

                $this->messageManager->addSuccessMessage(__('You saved the holiday.'));
                $this->dataPersistor->clear('Magedelight_Storepickup_holiday');

                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['holiday_id' => $model->getId()]);
                }
                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage(
                    $e,
                    __('Something went wrong while saving the holiday.')
                );
            }

            $this->dataPersistor->set('Magedelight_Storepickup_storeholiday', $data);
            return $resultRedirect->setPath(
                '*/*/edit',
                ['holiday_id' => $this->getRequest()->getParam('holiday_id')]
            );
        }
        return $resultRedirect->setPath('*/*/');
    }

    /**
     * Data prepare
     *
     * @param mixed $data
     * @return mixed
     */
    protected function prepareData($data)
    {
        if ($data['all_store'] == 0) {
            $data["holiday_applied_stores"] = implode(',', $this->getAllStoreArray());
        } else {
            $data["holiday_applied_stores"] = implode(',', $data['holiday_applied_stores']);
        }

        return $data;
    }

    /**
     * Get all store
     *
     * @return array
     */
    public function getAllStoreArray()
    {
        $storelocator = $this->storeCollectionFactory->create();
        $_options = [];
        foreach ($storelocator as $option) {
            $_options[] =  $option['storelocator_id'];
        }
        return $_options;
    }
}
