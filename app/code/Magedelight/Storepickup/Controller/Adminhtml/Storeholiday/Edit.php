<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Controller\Adminhtml\Storeholiday;

use Magedelight\Storepickup\Model\Storeholiday;

class Edit extends \Magedelight\Storepickup\Controller\Adminhtml\Storeholiday
{
    public const ADMIN_RESOURCE = 'Magedelight_Storepickup::storeholiday_root';

    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var Storeholiday
     */
    protected $storeholiday;

    /**
     * @var \Magedelight\Storepickup\Model\ResourceModel\Storeholiday
     */
    protected $storeholidayResource;

    /**
     * Edit constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param Storeholiday $storeholiday
     * @param \Magedelight\Storepickup\Model\ResourceModel\Storeholiday $storeHolidayResource
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        Storeholiday $storeholiday,
        \Magedelight\Storepickup\Model\ResourceModel\Storeholiday $storeHolidayResource
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->storeholiday = $storeholiday;
        $this->storeholidayResource=$storeHolidayResource;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Edit action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $id = $this->getRequest()->getParam('holiday_id');
        $model = $this->storeholiday;

        if ($id) {
            $this->storeholidayResource->load($model, $id);
            if (!$model->getId()) {
                $this->messageManager->addErrorMessage(__('This holiday no longer exists.'));
                /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
        }
        $this->_coreRegistry->register('Magedelight_Storepickup_storeholiday', $model);

        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            $id ? __('Edit Holiday') : __('New Holiday'),
            $id ? __('Edit Holiday') : __('New Holiday')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Storeholidays'));
        $resultPage->getConfig()->getTitle()
            ->prepend($model->getId() ? __('Edit %1', $model->getHolidayName()) : __('New Holiday'));
        return $resultPage;
    }
}
