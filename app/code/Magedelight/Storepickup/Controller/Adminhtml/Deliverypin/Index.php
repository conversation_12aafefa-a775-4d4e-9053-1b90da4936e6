<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Controller\Adminhtml\DeliveryPin;

use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Controller\Result\JsonFactory;

class Index extends \Magento\Backend\App\Action
{
    /**
     * @var JsonFactory
     */
    protected $resultPageFactory;

    /**
     * Constructor
     *
     * @param Context $context
     * @param JsonFactory $resultPageFactory
     */
    public function __construct(
        Context $context,
        JsonFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context);
    }

    /**
     * Index Delivery Pin
     *
     * @return \Magento\Framework\App\ResponseInterface|ResultInterface|string
     */
    public function execute()
    {

        if ($this->getRequest()->isAjax()) {
            $pin= hash('sha256', $this->getRequest()->getParam('pin', false));
            $resultJson = $this->resultPageFactory->create();
            return $resultJson->setData(['pin' => $pin]);
        }
    }
}
