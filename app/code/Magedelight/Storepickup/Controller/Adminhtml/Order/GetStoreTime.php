<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
namespace Magedelight\Storepickup\Controller\Adminhtml\Order;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\App\RequestInterface;

class GetStoreTime extends Action
{
    /**
     * @var JsonFactory
     */
    protected $resultJsonFactory;

    /**
     * @var \Magedelight\Storepickup\Api\StoreInformationManagementInterface
     */
    private $storeInformationManagementInterface;

    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @param Context $context
     * @param JsonFactory $resultJsonFactory
     * @param \Magedelight\Storepickup\Api\StoreInformationManagementInterface $storeInformationManagementInterface
     * @param RequestInterface $request
     */
    public function __construct(Context $context,
        JsonFactory $resultJsonFactory,
        \Magedelight\Storepickup\Api\StoreInformationManagementInterface $storeInformationManagementInterface,
        RequestInterface $request)
    {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->storeInformationManagementInterface = $storeInformationManagementInterface;
        $this->request = $request;
    }

    /**
     * Execute action
     *
     * @return mixed
     */
    public function execute()
    {
        $resultJson = $this->resultJsonFactory->create();
        $storeVal = $this->request->getParam('storeVal');
        $dateVal = $this->request->getParam('dateVal');
        $response = $this->storeInformationManagementInterface->getStoreTimeInformation($dateVal, $storeVal);
        return $resultJson->setData($response);
    }
}
