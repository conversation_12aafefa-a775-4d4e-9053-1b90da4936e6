<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Controller\Adminhtml\Storelocator;

use Magedelight\Storepickup\Model\StorelocatorFactory;

class Duplicate extends \Magedelight\Storepickup\Controller\Adminhtml\Storelocator
{
    public const ADMIN_RESOURCE = 'Magedelight_Storepickup::managestore_root';

    /**
     * @var StorelocatorFactory
     */
    protected $storelocatorFactory;

    /**
     * @var \Magedelight\Storepickup\Model\ResourceModel\Storelocator
     */
    protected $storeLocatorResource;

    /**
     * Duplicate constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param StorelocatorFactory $storelocatorFactory
     * @param \Magedelight\Storepickup\Model\ResourceModel\Storelocator $storeLocatorResource
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        StorelocatorFactory $storelocatorFactory,
        \Magedelight\Storepickup\Model\ResourceModel\Storelocator $storeLocatorResource
    ) {
        parent::__construct($context, $coreRegistry);
        $this->storelocatorFactory = $storelocatorFactory;
        $this->storeLocatorResource=$storeLocatorResource;
    }

    /**
     * Duplicate action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $id = $this->getRequest()->getParam('storelocator_id');
        if ($id) {
            try {
                $model = $this->storelocatorFactory->create();
                $this->storeLocatorResource->load($model, $id);

                $model->setId(null);
                $model->setIsActive(0);
                $model->setStoreIds([0]);
                $model->setUrlKey($id. '-' . uniqid());
                $this->storeLocatorResource->save($model);

                $this->messageManager->addSuccessMessage(__('You duplicate the store.'));
                return $resultRedirect->setPath('*/*/edit', ['storelocator_id' => $model->getStorelocatorId()]);
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
                return $resultRedirect->setPath('*/*/edit', ['storelocator_id' => $id]);
            }
        }
        $this->messageManager->addErrorMessage(__('We can\'t find a store to duplicate.'));
        return $resultRedirect->setPath('*/*/');
    }
}
