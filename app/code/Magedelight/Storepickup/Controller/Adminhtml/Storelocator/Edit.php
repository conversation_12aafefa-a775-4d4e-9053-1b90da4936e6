<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Controller\Adminhtml\Storelocator;

use Magedelight\Storepickup\Api\Data\StorelocatorInterfaceFactory;
use Magedelight\Storepickup\Model\Storelocator as StorelocatorModel;
use Magedelight\Storepickup\Model\RegistryConstants;
use Magento\Backend\Model\Session as pageSession;

class Edit extends \Magedelight\Storepickup\Controller\Adminhtml\Storelocator
{
    public const ADMIN_RESOURCE = 'Magedelight_Storepickup::managestore_root';

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var \Magento\Framework\App\Request\DataPersistorInterface
     */
    private $dataPersistor;

    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;
    /**
     * @var Magento\Backend\Model\Session
     **/
    protected $pageSession;

    /**
     * @var StorelocatorModel
     */
    protected $storelocatorModel;

    /**
     * @var \Magedelight\Storepickup\Model\ResourceModel\Storelocator
     */
    protected $storeLocatorResource;

    /**
     * Edit constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor
     * @param StorelocatorModel $storelocatorModel
     * @param pageSession $pageSession
     * @param \Magedelight\Storepickup\Model\ResourceModel\Storelocator $storeLocatorResource
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor,
        StorelocatorModel $storelocatorModel,
        pageSession $pageSession,
        \Magedelight\Storepickup\Model\ResourceModel\Storelocator $storeLocatorResource
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->dataPersistor = $dataPersistor;
        $this->logger = $logger;
        $this->storelocatorModel = $storelocatorModel;
        $this->pageSession = $pageSession;
        $this->storeLocatorResource=$storeLocatorResource;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Initialize current rule and set it in the registry.
     *
     * @return int
     */
    protected function _initRule()
    {
        $labelId = $this->getRequest()->getParam('storelocator_id');
        $this->_coreRegistry->register(RegistryConstants::CURRENT_STORE_RULE, $labelId);

        return $labelId;
    }

    /**
     * Edit action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $id = $this->getRequest()->getParam('storelocator_id');
        $model = $this->storelocatorModel;
        $this->_coreRegistry->register(RegistryConstants::CURRENT_STORE_RULE, $model);

        $resultPage = $this->resultPageFactory->create();

        if ($id) {
            $this->storeLocatorResource->load($model, $id);

            if (!$model->getId()) {
                $this->messageManager->addErrorMessage(__('This store no longer exists.'));
                /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
            $model->getConditions()->setFormName('Magedelight_Storepickup_form');
            $model->getConditions()->setJsFormObject(
                $model->getConditionsFieldSetId($model->getConditions()->getFormName())
            );
        }

        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $data = $this->pageSession->getPageData(true);
        if (!empty($data)) {
            $model->addData($data);
        }
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            $id ? __('Edit Store') : __('New Store'),
            $id ? __('Edit Store') : __('New Store')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Stores'));
        $resultPage->getConfig()->getTitle()
            ->prepend($model->getId() ? __('%1', $model->getStorename()) : __('New Store'));
        return $resultPage;
    }
}
