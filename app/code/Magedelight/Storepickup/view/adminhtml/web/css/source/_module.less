
.admin__menu .item-storepickup-root-commonlyvisible > a:before,
.admin__menu .item-storepickup-root.parent.level-0 .submenu > .submenu-title:before,
.config-nav-block .md_section_storepickup a:before {
    content: '';
    background-image: url('@{baseDir}Magedelight_Storepickup/images/store-pickup-logo.svg');
    background-repeat: no-repeat;
    background-size: 40px;
    background-position-x:center;
    background-position-y: -6px;
    height: 32px;
    display: inline-block;
    vertical-align: middle;
    padding: 0;
    width: 32px;
    margin-right: 5px;
}

.admin__menu .item-storepickup-root.parent.level-0 .submenu > .submenu-title:before {
    background-position-y: -6px;
}
.config-nav-block .md_section_storepickup a:before {
    background-position-y: -44px;
    margin: -5px 0 0;
}

.admin__fieldset > .admin__control-storetime-container > .admin__field-control {
    width: 98%;
}
.storeaddress-container {
    &.admin__control-fields,
    &[class*="admin__control-grouped"] {
        > .admin__field {
            &:first-child {
                > .admin__field-label {
                    display: none;
                }
            }
        }
    }
}
#pickup-date-block {
    margin-top: 15px;
    .store-time {
        margin-top: 15px;
    }
}
