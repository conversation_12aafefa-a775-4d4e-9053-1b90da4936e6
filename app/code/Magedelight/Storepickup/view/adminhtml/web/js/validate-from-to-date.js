/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/

require([
    'Magento_Ui/js/lib/validation/validator',
    'jquery',
    'uiRegistry',
    'mage/translate'
], function(validator, $, uiRegistry){

    validator.addRule(
        'validate-from-to-date',
        function (value) {
            var fromDate = uiRegistry.get('index = holiday_date_from');

            if(fromDate.value() > value){
                return false;
            }
            return true;
        }
        ,$.mage.__('To date should be greater then From date.')
    );
});
