<?xml version="1.0" ?>
<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">magedelight_storeholiday_form.storeholiday_form_data_source</item>
        </item>
        <item name="label" translate="true" xsi:type="string">General Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button class="Magedelight\Storepickup\Block\Adminhtml\Storeholiday\Edit\BackButton" name="back"/>
            <button class="Magedelight\Storepickup\Block\Adminhtml\Storeholiday\Edit\DeleteButton" name="delete"/>
            <button class="Magedelight\Storepickup\Block\Adminhtml\Storeholiday\Edit\SaveButton" name="save"/>
            <button class="Magedelight\Storepickup\Block\Adminhtml\Storeholiday\Edit\SaveAndContinueButton" name="save_and_continue"/>
        </buttons>
        <namespace>magedelight_storeholiday_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>magedelight_storeholiday_form.storeholiday_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="storeholiday_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="*/*/save"/>
        </settings>
        <dataProvider class="Magedelight\Storepickup\Model\Storeholiday\DataProvider" name="storeholiday_form_data_source">
            <settings>
                <requestFieldName>holiday_id</requestFieldName>
                <primaryFieldName>holiday_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="holidayinfo">
        <settings>
            <label>Holiday Information</label>
        </settings>
        <field formElement="checkbox" name="is_active" sortOrder="10">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Storeholiday</item>
                    <item name="default" xsi:type="number">0</item>
                </item>
            </argument>
            <settings>
                <dataType>boolean</dataType>
                <label translate="true">Status</label>
                <dataScope>is_active</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="number">0</map>
                            <map name="true" xsi:type="number">1</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
        <field formElement="input" name="holiday_name" sortOrder="20">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Storeholiday</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Holiday Name</label>
                <dataScope>holiday_name</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="all_store" component="Magedelight_Storepickup/js/form/element/select-store" sortOrder="30" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Storeholiday</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Allow Specific Stores?</label>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Magedelight\Storepickup\Model\Storeholiday\Source\YesNoStore"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="holiday_applied_stores" formElement="multiselect">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Storeholiday</item>
                    <item name="visibleValue" xsi:type="string">1</item>
                    <item name="visible" xsi:type="boolean">false</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>int</dataType>
                <label translate="true">Allow Stores</label>
                <dataScope>holiday_applied_stores</dataScope>
            </settings>
            <formElements>
                <multiselect>
                    <settings>
                        <options class="Magedelight\Storepickup\Model\Storeholiday\Source\StoreList"/>
                    </settings>
                </multiselect>
            </formElements>
        </field>
        <field name="holiday_date_from" formElement="date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Storeholiday</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-date" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">From</label>
                <dataScope>holiday_date_from</dataScope>
            </settings>
        </field>
        <field name="holiday_date_to" formElement="date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Storeholiday</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-date" xsi:type="boolean">true</rule>
                    <rule name="validate-from-to-date" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">To</label>
                <dataScope>holiday_date_to</dataScope>
            </settings>
        </field>
        <field formElement="checkbox" name="is_repetitive" sortOrder="80">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Storeholiday</item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="notice" xsi:type="string" translate="true">If Yes then selected same date will be disabled for next year.</item>
                </item>
            </argument>
            <settings>
                <dataType>boolean</dataType>
                <label translate="true">Yearly Repetitive</label>
                <dataScope>is_repetitive</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">false</rule>
                </validation>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="number">0</map>
                            <map name="true" xsi:type="number">1</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
        <field name="holiday_comment" formElement="wysiwyg" sortOrder="90">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Storeholiday</item>
                </item>
            </argument>
            <settings>
                <additionalClasses>
                    <class name="admin__field-wide">true</class>
                </additionalClasses>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">false</rule>
                </validation>
                <label translate="true">Comment</label>
                <dataScope>holiday_comment</dataScope>
            </settings>
            <formElements>
                <wysiwyg>
                    <settings>
                        <wysiwyg>true</wysiwyg>
                    </settings>
                </wysiwyg>
            </formElements>
        </field>
    </fieldset>
</form>
