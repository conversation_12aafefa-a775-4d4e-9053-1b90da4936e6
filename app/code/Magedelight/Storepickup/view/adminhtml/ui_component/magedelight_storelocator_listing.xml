<?xml version="1.0" ?>
<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">magedelight_storelocator_listing.managestores_storelocator_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>magedelight_storelocator_columns</spinner>
        <deps>
            <dep>magedelight_storelocator_listing.managestores_storelocator_listing_data_source</dep>
        </deps>
        <buttons>
            <button name="import">
                <url path="*/*/import"/>
                <class>primary</class>
                <label translate="true">Import MSI Store</label>
            </button>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">Add new Store</label>
            </button>
        </buttons>
    </settings>
    <dataSource component="Magento_Ui/js/grid/provider" name="managestores_storelocator_listing_data_source">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">storelocator_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
<!--        <aclResource>Magedelight_Storepickup::Storelocator</aclResource>-->
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="managestores_storelocator_listing_data_source">
            <settings>
                <requestFieldName>storelocator_id</requestFieldName>
                <primaryFieldName>storelocator_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">magedelight_storelocator_listing.magedelight_storelocator_listing.magedelight_storelocator_columns.ids</item>
                    <item name="indexField" xsi:type="string">storelocator_id</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/tree-massactions</item>
                </item>
            </argument>
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete selected items?</message>
                        <title translate="true">Delete items</title>
                    </confirm>
                    <url path="managestores/storelocator/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
            <action name="disable">
                <settings>
                    <url path="managestores/storelocator/massStatus">
                        <param name="is_active">0</param>
                    </url>
                    <type>disable</type>
                    <label translate="true">Disable</label>
                </settings>
            </action>
            <action name="enable">
                <settings>
                    <url path="managestores/storelocator/massStatus">
                        <param name="is_active">1</param>
                    </url>
                    <type>enable</type>
                    <label translate="true">Enable</label>
                </settings>
            </action>
        </massaction>
    </listingToolbar>
    <columns name="magedelight_storelocator_columns">
        <settings>
            <editorConfig>
                <param name="selectProvider" xsi:type="string">magedelight_storelocator_listing.magedelight_storelocator_listing.magedelight_storelocator_columns.ids</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="indexField" xsi:type="string">storelocator_id</param>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" path="managestores/Storelocator/inlineEdit" xsi:type="url"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">magedelight_storelocator_listing.magedelight_storelocator_listing.magedelight_storelocator_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>storelocator_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="storelocator_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
                <sortable>true</sortable>
            </settings>
        </column>
        <column name="storeimage" component="Magento_Ui/js/grid/columns/thumbnail" class="Magedelight\Storepickup\Ui\Component\Listing\Column\Image">
            <settings>
                <altField>name</altField>
                <hasPreview>1</hasPreview>
                <label translate="true">Store Image</label>
                <sortable>false</sortable>
            </settings>
        </column>
        <column name="storename" sortOrder="30">
            <settings>
                <filter>text</filter>
                <label translate="true">Store Name</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
                <sortable>true</sortable>
            </settings>
        </column>
        <column name="url_key" sortOrder="40">
            <settings>
                <filter>text</filter>
                <label translate="true">URL Key</label>
                <sortable>true</sortable>
            </settings>
        </column>
        <column name="longitude" sortOrder="60" class="Magedelight\Storepickup\Ui\Component\Listing\Column\Longitudenew">
            <settings>
                <filter>text</filter>
                <label translate="true">Longitude</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
                <sortable>true</sortable>
            </settings>
        </column>
        <column name="latitude" sortOrder="70" class="Magedelight\Storepickup\Ui\Component\Listing\Column\Longitudenew">
            <settings>
                <filter>text</filter>
                <label translate="true">Latitude</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
                <sortable>true</sortable>
            </settings>
        </column>
        <column name="country_id" component="Magento_Ui/js/grid/columns/select" sortOrder="100">
            <settings>
                <filter>select</filter>
                <options class="Magedelight\Storepickup\Ui\Component\Listing\Column\Countries"/>
                <dataType>select</dataType>
                <label translate="true">Country</label>
            </settings>
        </column>
        <column name="is_active" component="Magento_Ui/js/grid/columns/select" sortOrder="110">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <options class="Magedelight\Storepickup\Ui\Component\Listing\Column\isActive"/>
                <label translate="true">Status</label>
                <sortable>true</sortable>
            </settings>
        </column>
        <actionsColumn class="Magedelight\Storepickup\Ui\Component\Listing\Column\StorelocatorActions" name="actions" sortOrder="120">
            <settings>
                <indexField>storelocator_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>107</resizeDefaultWidth>
            </settings>
        </actionsColumn>
    </columns>
</listing>
