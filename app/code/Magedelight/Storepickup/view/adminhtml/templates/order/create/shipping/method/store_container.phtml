<?php 
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
?>

<?php
    $_shippingRateGroups = $block->getShippingRates();
    $config = $this->helper('Magedelight\Storepickup\Helper\Storelocator');
    $viewModel = $block->getViewModel();
    $storeArray = $viewModel->getStoreInformation();
?>
<?php if ($_shippingRateGroups && $config->isModuleEnable()) : ?>
    <?php
        $style = ($block->getActiveMethodRate() && $block->getActiveMethodRate()->getCode()=='storepickup_storepickup') ? 'style="display:block;"' : 'style="display:none;';
        $storeClass = ($block->getActiveMethodRate() && $block->getActiveMethodRate()->getCode()=='storepickup_storepickup') ? 'select admin__control-select required' : 'select admin__control-select';
        $dateAndTimeClass = ($block->getActiveMethodRate() && $block->getActiveMethodRate()->getCode()=='storepickup_storepickup') ? 'field required' : 'field';
    ?>
    <div id="order-shipping-method-storepickup" <?= /* @escapeNotVerified */ $style; ?>>
        <div class="admin__order-shipment-methods">
            <div class="admin__order-shipment-method-storepickup storepickup-shipping-form"  id="storepickup-method-form" <?= /* @escapeNotVerified */ $style; ?>>
                <div class="admin__field field field-store-list">
                    <label class="label admin__field-label">
                        <span><?php /* @escapeNotVerified */ echo __('Select Store') ?></span>
                    </label>
                    <div class="admin__field-control control">
                        <input type="hidden" id="baseHolidayUrl" value="<?= /* @escapeNotVerified */ $block->getUrl('managestores/order/getholiday')?>" />
                        <input type="hidden" id="baseTimeUrl" value="<?= /* @escapeNotVerified */ $block->getUrl('managestores/order/getstoretime')?>" />
                        <input type="hidden" id="storeDateEnable" value="<?= /* @escapeNotVerified */ $viewModel->enablePickupDate()?>" />
                        <input type="hidden" id="storeTimeEnable" value="<?= /* @escapeNotVerified */ $viewModel->isStoreTimeEnable()?>" />
                        <select id="storepickup_storename" name="storepickup_storename" class="<?php echo $this->escapeHtml($storeClass); ?>">
                            <option value="">Select Store</option>
                            <?php if(!empty($storeArray)):?>
                                <?php foreach ($storeArray as $store):?>
                                    <option value="<?= /* @escapeNotVerified */$store['storelocator_id']?>"><?= /* @escapeNotVerified */$store['storename'];?></option>
                                <?php endforeach;?>
                            <?php endif;?>
                        </select>
                        <div id="pickup-date-block" style="display: none;">
                            <?php if ($viewModel->enablePickupDate()):?>
                                <label class="pickup-date-label">
                                    <span id="pickup-date-label"><?php echo $this->escapeHtml('Pickup Date')?></span>
                                </label>
                                <input class="<?php echo $this->escapeHtml($dateAndTimeClass); ?>"
                                       type="text"
                                       id="pickup-date"
                                       name="storepickup_pickup_date"
                                       placeholder="Select Pickup Date"
                                       readonly="true"><br>
                            <?php endif;?>
                            <?php if ($viewModel->isStoreTimeEnable()):?>
                                <div class="store-time" style="display: none;">
                                    <label class="store-time-label" ><span id="store-time"><?php echo $this->escapeHtml('Select Time')?></span></label>
                                    <select id="store_time" name="storepickup_store_time"
                                            class="<?php echo $this->escapeHtml($dateAndTimeClass); ?>">
                                    </select>
                                </div>
                            <?php endif;?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php $scriptString = <<<script
    require(["jquery",
    'mage/translate',
    'underscore',
    'Magento_Ui/js/modal/modal',
    'mage/url',
    'mage/storage',
    'mage/calendar'], function($,translate,_,modal,urlBuilder,storage){
        $(document.body).on('change', "#storepickup_storename", function (event) {
                $("#pickup-date-block").hide();
                if($("#storeDateEnable").val() == 1){
                    getHoliday(this.value);
                }
        });

        function getHoliday(id) {
            var responseHoliday = {};
            var url = $("#baseHolidayUrl").val();
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                data: {
                    id:id
                },
                showLoader: true,
                success: function (response) {
                    responseHoliday['normalHoliday'] = response[0]['dates']['normal'];
                    responseHoliday['shippingHoliday'] = response[0]['dates']['shipping_off_day'];
                    responseHoliday['repetitiveHoliday'] = response[0]['dates']['repetitive'];
                    responseHoliday['closeDay'] = response[0]['days'];

                    pickupDate(responseHoliday);
                    
                },
                error: function (xhr, status, error) {
                    console.log('Error:', error);
                }
            });
        }

        function pickupDate(responseData) {
            var self = this;
            var holidayDays = responseData;
            var noday = '';
            var format = 'yy-mm-dd';

            function unavailable(date){

                var day = $.datepicker.formatDate('mm-dd-yy',date);
                if(Array.isArray(holidayDays['closeDay']) && holidayDays['closeDay'].length > 0){
                    if(holidayDays['closeDay'].includes(date.getDay())) {
                        return [false,''];
                    }
                }

                if(Array.isArray(holidayDays['normalHoliday']) && holidayDays['normalHoliday'].length > 0){
                    if(holidayDays['normalHoliday'].includes(day)) {
                        return [false,''];
                    }
                }

                if(Array.isArray(holidayDays['shippingHoliday']) && holidayDays['shippingHoliday'].length > 0){
                    if(holidayDays['shippingHoliday'].includes(day)) {
                        return [false,''];
                    }
                }

                if(Array.isArray(holidayDays['repetitiveHoliday']) && holidayDays['repetitiveHoliday'].length > 0){
                    if(holidayDays['repetitiveHoliday'].includes($.datepicker.formatDate('m-d',date))) {
                        return [false,''];
                    }
                }

                return [true,''];
            }

            var options = {
                minDate: 0,
                dateFormat:format,
                hideIfNoPrevNext: true,
                beforeShowDay: function(date) {
                    return unavailable(date);
                }
            };

            $('#pickup-date').datepicker(options);
            $("#pickup-date-block").show();
            return this;
        }

        $(document.body).on('change',"#pickup-date", function (event) {
            if($("#storeTimeEnable").val() == 1){
                storeTimeSloteObj(this.value,$('#storepickup_storename').val());
                $('.store-time').show();
                }else{
                $('.store-time').hide();
            }
        });

        function storeTimeSloteObj(dateVal, storeVal){
            var url = $("#baseTimeUrl").val();
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                data: {
                    storeVal:storeVal,
                    dateVal:dateVal
                },
                showLoader: true,
                success: function (response) {
                    $("#store_time").empty();
                    var selectOption = $('<option></option>').attr('value',"").text(translate("Select a Time Slot"));
                    $('#store_time').append(selectOption);
                    $.each(response, function(index, item) {
                        var newOption = $('<option></option>').attr('value', item).text(item);
                        $('#store_time').append(newOption);
                    });                  
                },
                error: function (xhr, status, error) {
                    console.log('Error:', error);
                }
            });
        }

    });
    script;
    ?>
    <?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>
<?php endif; ?>