<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Sales\Block\Adminhtml\Order\View\Info;
use Magento\Framework\Escaper;

//phpcs:disable Squiz.ControlStructures.ControlSignature.NewlineAfterOpenBrace
//phpcs:disable Squiz.WhiteSpace.ScopeClosingBrace.ContentBefore
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Files.LineLength.MaxExceeded
// phpcs:disable Magento2.Security.LanguageConstruct.DirectOutput
// phpcs:disable
?>

<?php
/**
 * @var Info $block
 * @var Escaper $escaper
 */
$order = $block->getOrder();

?>
<section class="admin__page-section">
    <div class="admin__page-section-title">
        <span class="title"><?= $escaper->escapeHtml(__('Items to Ship')) ?></span>
    </div>
    <div class="admin__table-wrapper">
        <table class="data-table admin__table-primary order-shipment-table">
            <thead>
                <tr class="headings">
                    <th class="col-product"><span><?= $escaper->escapeHtml(__('Product')) ?></span></th>
                    <th class="col-ordered-qty"><span><?= $escaper->escapeHtml(__('Qty')) ?></span></th>
                    <th class="col-qty<?php if ($block->isShipmentRegular()): ?> last<?php endif; ?>">
                        <span><?= $escaper->escapeHtml(__('Qty to Ship')) ?></span>
                    </th>
                    <?php if (!$block->canShipPartiallyItem()): ?>
                    <th class="col-ship last"><span><?= $escaper->escapeHtml(__('Ship')) ?></span></th>
                    <?php endif; ?>
                </tr>
            </thead>
            <?php $_items = $block->getShipment()->getAllItems() ?>
            <?php $_i = 0; foreach ($_items as $_item):
                if ($_item->getOrderItem()->getParentItem()):
                    continue;
                endif;
                $_i++ ?>
                <tbody class="<?= $_i%2 ? 'odd' : 'even' ?>">
                    <?= $block->getItemHtml($_item) ?>
                    <?= $block->getItemExtraInfoHtml($_item->getOrderItem()) ?>
                </tbody>
            <?php endforeach; ?>
        </table>
    </div>
</section>

<section class="admin__page-section">
    <div class="admin__page-section-title">
        <span class="title"><?= $escaper->escapeHtml(__('Shipment Total')) ?></span>
    </div>
    <div class="admin__page-section-content order-comments-history">
        <div class="admin__page-section-item">
            <div class="admin__page-section-item-title">
                <span class="title"><?= $escaper->escapeHtml(__('Shipment Comments')) ?></span>
            </div>
            <div class="admin__page-section-item-content">
                <div id="order-history_form" class="admin__field">
                    <label class="admin__field-label"
                           for="shipment_comment_text">
                        <span><?= $escaper->escapeHtml(__('Comment Text')) ?></span></label>
                    <div class="admin__field-control">
                        <textarea id="shipment_comment_text"
                                  class="admin__control-textarea"
                                  name="shipment[comment_text]"
                                  rows="3"
                                  cols="5"><?= $escaper->escapeHtml($block->getShipment()->getCommentText()) ?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="admin__page-section-item order-totals order-totals-actions">
        <div class="admin__page-section-item-title">
            <span class="title"><?= $escaper->escapeHtml(__('Shipment Options')) ?></span>
        </div>
        <div class="admin__page-section-item-content">
            <?php if ($block->canCreateShippingLabel()): ?>
                <div class="field choice admin__field admin__field-option field-create">
                    <input id="create_shipping_label"
                           class="admin__control-checkbox"
                           name="shipment[create_shipping_label]"
                           value="1"
                           type="checkbox"
                           onclick="toggleCreateLabelCheckbox();"/>
                    <label class="admin__field-label"
                           for="create_shipping_label">
                        <span><?= $escaper->escapeHtml(__('Create Shipping Label')) ?></span></label>
                </div>
            <?php endif ?>

            <div class="field choice admin__field admin__field-option field-append">
                <input id="notify_customer"
                       class="admin__control-checkbox"
                       name="shipment[comment_customer_notify]"
                       value="1"
                       type="checkbox"/>
                <label class="admin__field-label"
                       for="notify_customer">
                    <span><?=$escaper->escapeHtml(__('Append Comments')) ?></span></label>
            </div>

            <?php if ($block->canSendShipmentEmail()): ?>
                <div class="field choice admin__field admin__field-option field-email">
                    <input id="send_email"
                           class="admin__control-checkbox"
                           name="shipment[send_email]"
                           value="1"
                           type="checkbox"/>
                    <label class="admin__field-label"
                           for="send_email">
                        <span><?= $escaper->escapeHtml(__('Email Copy of Shipment')) ?></span></label>
                </div>
            <?php endif; ?>
            <?= $block->getChildHtml('submit_before') ?>
            <div class="order-history-comments-actions actions">
                <?php
                    $deliveryStatus = $this->helper('Magedelight\Storepickup\Helper\Storelocator')->getConfig('magedelight_storepickup/deliverypin/enable_delivery_pin');
                    $generateAutoShipment = $this->helper('Magedelight\Storepickup\Helper\Storelocator')->getConfig('magedelight_storepickup/deliverypin/generate_auto_shipment');
                    $forceShipment = true;
                ?>
                <?php if ($this->helper('Magedelight\Storepickup\Helper\Storelocator')->getConfig('magedelight_storepickup/general/enable')): ?>
                    <?php if ($deliveryStatus && $block->getOrder()->getShippingMethod() == 'storepickup_storepickup'): ?>
                        <?php if ($block->getOrder()->getIsVerifiedPin() == 0): ?>
                            <?php $forceShipment = $this->helper('Magedelight\Storepickup\Helper\Storelocator')->getConfig('magedelight_storepickup/deliverypin/allow_force_shipment'); ?>
                            <?php if ($block->getOrder()->getStatus() == 'ready_for_pickup'): ?>
                            <button id="verify-delivery-pin" title="<?= $escaper->escapeHtml(__('Verify Delivery Pin')) ?>" type="button" class="action-default scalable" onclick="validateDeliveryPin();" data-ui-id="order-items-delivery-button">
                                <span><?= $escaper->escapeHtml(__('Verify Delivery Pin')) ?></span>
                            </button>
                                <?php if ($forceShipment): ?>
                                <strong><?= $escaper->escapeHtml(__(' OR ')); ?></strong>
                            <?php endif; ?>
                        <?php else: ?>
                            <strong><?= $escaper->escapeHtml(__(' Order is not yet Ready for Pickup ')); ?></strong>
                        <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>

                <?= $block->getChildHtml('submit_button') ?>
                <?= $block->getChildHtml('submit_after') ?>
            </div>
        </div>
    </div>
    <div id="popup-modal" style="display:none;">
        <hr /><br /><br />
        <b><?= $escaper->escapeHtml(__('Enter Pin :')) ?></b>
        <input type="text" name="delivery-pin" id="delivery-pin" />

        <input type="hidden" name="order-pin" id="order-pin" value="<?=/* @noEscape */ hash("sha256", $block->getOrder()->getDeliveryPin()); ?>" />
        <span style="color:red" id="pin-error"></span>
        <span style="color:green" id="pin-success"></span>
    </div>
</section>
<script>
require([
    "jquery",
    "Magento_Ui/js/modal/alert",
    "Magento_Ui/js/modal/modal",
    "prototype"
], function(jQuery, alert, modal){

//<![CDATA[
var sendEmailCheckbox = $('send_email');

var forceShipment = '<?=/* @noEscape */  $forceShipment ?>';
var generateAutoShipment = '<?=/* @noEscape */ $generateAutoShipment ?>';

if(forceShipment == false) {
    disableElements('submit-button');
}

if (sendEmailCheckbox) {
    var notifyCustomerCheckbox = $('notify_customer');
    var shipmentCommentText = $('shipment_comment_text');
    Event.observe(sendEmailCheckbox, 'change', bindSendEmail);
    bindSendEmail();
}
function bindSendEmail() {
    if (sendEmailCheckbox.checked == true) {
        notifyCustomerCheckbox.disabled = false;
    }
    else {
        notifyCustomerCheckbox.disabled = true;
    }
}
window.toggleCreateLabelCheckbox = function() {
    var checkbox = $('create_shipping_label');
    var submitButton = checkbox.up('.order-totals').select('.submit-button span')[0];
    if (checkbox.checked) {
        submitButton.innerText += '...';
    } else {
        submitButton.innerText = submitButton.innerText.replace(/\.\.\.$/, '');
    }
}
window.submitShipment = function(btn) {
    if (!validQtyItems()) {
        alert({
            content: '<?=/* @noEscape */  $escaper->escapeJs($escaper->escapeHtml(__('Invalid value(s) for Qty to Ship'))) ?>'
        });
        return;
    }
    var checkbox = $(btn).up('.order-totals').select('#create_shipping_label')[0];
    if (checkbox && checkbox.checked) {
        packaging.showWindow();
    } else {
        disableElements('submit-button');
        // Temporary solution will be replaced after refactoring order functionality
        jQuery('#edit_form').on('invalid-form.validate', function() {
            enableElements('submit-button');
            jQuery('#edit_form').off('invalid-form.validate');
        });
        jQuery('#edit_form').triggerHandler('save');
    }
}
window.validQtyItems = function() {
    var valid = true;
    $$('.qty-item').each(function(item) {
        var val = parseFloat(item.value);
        if (isNaN(val) || val < 0) {
            valid = false;
        }
    });
    return valid;
}

window.bindSendEmail = bindSendEmail;

window.shipmentCommentText = shipmentCommentText;
window.notifyCustomerCheckbox = notifyCustomerCheckbox;
window.sendEmailCheckbox = sendEmailCheckbox;
//]]>

window.validateDeliveryPin = function() {
    var options = {
        type: 'popup',
        responsive: true,
        innerScroll: true,
        title: '<?=/* @noEscape */  $escaper->escapeJs($escaper->escapeHtml(__('Verify Delivery Pin'))) ?>',
        buttons: [{
            text: '<?=/* @noEscape */  $escaper->escapeJs($escaper->escapeHtml(__('Verify Pin'))) ?>',
            class: 'verify-pin-btn-cls',
            //id: 'verify-pin-btn',
            click: function () {
                var deliveryPin = jQuery('#delivery-pin').val();
                if(deliveryPin.trim() == "")
                {
                    jQuery('#pin-error').html('<?= $escaper->escapeJs($escaper->escapeHtml(__('Please Enter Pin'))) ?>');
                } else {
                    jQuery('button.verify-pin-btn-cls').attr('disabled','disabled');
                    var param = 'pin='+jQuery('#delivery-pin').val();
                    var closePop = false;
                    jQuery.ajax({
                        url: "<?=/* @noEscape */ $block->getUrl('managestores/deliverypin'); ?>",
                        data: param,
                        type: "POST",
                        success: function (data) {
                            if(jQuery('#order-pin').val() == data.pin) {
                                updateOrderComment();
                                if(generateAutoShipment == false) {
                                    jQuery('#pin-success').html('<?= $escaper->escapeJs($escaper->escapeHtml(__('Verified!'))) ?>');
                                    enableElements('submit-button');
                                    jQuery("#popup-modal").modal("closeModal");
                                    jQuery("#verify-delivery-pin").remove();
                                } else {
                                    jQuery('#edit_form').triggerHandler('save');
                                }
                            } else {
                                jQuery('#pin-error').html('<?= $escaper->escapeJs($escaper->escapeHtml(__('Incorrect Pin'))) ?>');
                                jQuery('button.verify-pin-btn-cls').removeAttr('disabled');
                            }
                        }
                    });
                }
            }
        }]
    };

    var popup = modal(options, jQuery('#popup-modal'));
    jQuery("#popup-modal").modal("openModal");
    jQuery('#pin-error').html('');

}

window.updateOrderComment = function () {
    var param = 'orderId=<?=/* @noEscape */ $block->getOrder()->getId() ?>';
    jQuery.ajax({
        url: "<?=/* @noEscape */ $block->getUrl('managestores/deliverycomment'); ?>",
        data: param,
        type: "POST",
        success: function (data) {

        }
    });
}

});
</script>
