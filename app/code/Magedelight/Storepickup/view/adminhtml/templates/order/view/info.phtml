<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// phpcs:disable Magento2.Templates.ThisInTemplate
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Files.LineLength.MaxExceeded
// phpcs:disable Magento2.Security.LanguageConstruct.DirectOutput
// phpcs:disable

?>
<?php /** @var $block \Magento\Shipping\Block\Adminhtml\View */ ?>
<?php $order = $block->getOrder() ?>
<?php if ($order->getIsVirtual()):
    return '';
endif; ?>

<?php /* Shipping Method */ ?>
<div class="admin__page-section-item order-shipping-method">
    <div class="admin__page-section-item-title">
        <span class="title"><?= /* @noEscape */ $block->escapeHtml(__('Shipping &amp; Handling Information')) ?></span>
    </div>
    <div class="admin__page-section-item-content">
        <?php  if ($order->getTracksCollection()->count()): ?>
            <p><a href="#" id="linkId" onclick="popWin('<?=/* @noEscape */ $block->escapeHtmlAttr($block->escapeJs($block->escapeUrl($this->helper(Magento\Shipping\Helper\Data::class)->getTrackingPopupUrlBySalesModel($order)))) ?>','trackorder','width=800,height=600,resizable=yes,scrollbars=yes')" title="<?= $block->escapeHtmlAttr(__('Track Order')) ?>"><?= $block->escapeHtml(__('Track Order')) ?></a></p>
        <?php endif; ?>
        <?php if ($order->getShippingDescription()): ?>
            <strong><?=/* @noEscape */ $order->getShippingDescription() ?></strong>

            <?php if ($this->helper(Magento\Tax\Helper\Data::class)->displayShippingPriceIncludingTax()): ?>
                <?php $_excl = $block->displayShippingPriceInclTax($order); ?>
            <?php else: ?>
                <?php $_excl = $block->displayPriceAttribute('shipping_amount', false, ' '); ?>
            <?php endif; ?>
            <?php $_incl = $block->displayShippingPriceInclTax($order); ?>

            <?= /** @noEscape */ $_excl ?>
            <?php if ($this->helper(Magento\Tax\Helper\Data::class)->displayShippingBothPrices()
                && $_incl != $_excl): ?>
                (<?= $block->escapeHtml(__('Incl. Tax')) ?> <?= /** @noEscape */ $_incl ?>)
            <?php endif; ?>
        <?php else: ?>
            <?= $block->escapeHtml(__('No shipping information available')) ?>
        <?php endif; ?>
    </div>
</div>
