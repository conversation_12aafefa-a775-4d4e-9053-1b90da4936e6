/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>noLabs. All Rights reserved.
*/

define(['jquery', 'Magento_Checkout/js/view/form/element/email'], function (
    $,
    Component
) {
    'use strict';

    return Component.extend({
        defaults: {
            template:
                'Magedelight_Storepickup/form/element/email',
            links: {
                email:
                    'checkout.steps.shipping-step.shippingAddress.customer-email:email'
            }
        }
    });
});
