/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>abs. All Rights reserved.
*/
define(
    [
        'jquery',
        'uiRegistry',
        'uiComponent',
        'ko',
        'Magedelight_Storepickup/js/action/set-pickup-date',
        'Magedelight_Storepickup/js/action/get-store-time',
        'mageUtils',
        'Magento_Checkout/js/action/set-shipping-information',
        'Magento_Checkout/js/model/step-navigator',
        'Magento_Customer/js/model/customer',
        'Magedelight_Storepickup/js/model/pickup-locations-service',
        'Magento_Checkout/js/model/quote',
        'mage/calendar'
    ], function (
        $,
        registry,
        Component,
        ko,
        setPickupDateAction,
        getStoreTimeAction,
        utils,
        setShippingInformationAction,
        stepNavigator,
        customer,
        pickupLocationsService,
        quote
    ) {
    'use strict';
        var self;
        var config = {
            'pickupStore': '[name="pickup_store"]',
            'pickupStoreDate': '[name="pickup_date"]'
        };
        
        return Component.extend({
            defaults: {
                template: 'Magedelight_Storepickup/store-pickup-date',
                loginFormSelector:
                '#store-pickup form[data-role=email-with-possible-login]',
                storePickupSlotFormSelector:
                '#store-pickup form[data-role=md-store-pickup-slot]',
                storePickupFormSelector:
                '#store-pickup form[data-role=md-store-pickup]',
            },
            canVisibleDateBlock: ko.observable(false),
            holidayDays: ko.observableArray(),
            storeTimeOptionValue: '',
            canVisibleTimeBlock: ko.observable(false),
            storeTimeSloteObj: ko.observableArray(),
            rate: {
                'carrier_code': 'storepickup',
                'method_code': 'storepickup'
            },
            pickupLayout : window.checkoutConfig.pickupLayout,
            
            /**
             * Initialize
             */
            initialize: function () {
                self = this;
                this._super();
                this.pickupDate();
                this.getHoliday();
            },

            /**
             * Display next button based on pickup layout
             */
            canVisibleNextButton: function () {
                return this.pickupLayout === 'tabview';
            },

            /**
             * Display next button based on pickup layout
             */
            canVisibleDateAndTime: function () {
                return ko.pureComputed(function () {
                    return _.isMatch(quote.shippingMethod(), this.rate);
                }       , this);
            },

            /**
             * Create Date Picker
             *
             * @param responseData
             * @returns {exports}
             */
            pickupDate: function (responseData) {
                self.holidayDays(responseData);
                var noday = '';
                var format = 'yy-mm-dd';

                function unavailable(date) {
                    var day = $.datepicker.formatDate('mm-dd-yy',date);

                    if(Array.isArray(self.holidayDays().closeDay) && self.holidayDays().closeDay.length > 0) {
                        if(self.holidayDays().closeDay.includes(date.getDay())) {
                            return [false,''];
                        }
                    }

                    if(Array.isArray(self.holidayDays().normalHoliday) && self.holidayDays().normalHoliday.length > 0) {
                        if(self.holidayDays().normalHoliday.includes(day)) {
                            return [false,''];
                        }
                    }

                    if(Array.isArray(self.holidayDays().shippingHoliday) && self.holidayDays().shippingHoliday.length > 0) {
                        if(self.holidayDays().shippingHoliday.includes(day)) {
                            return [false,''];
                        }
                    }

                    if(Array.isArray(self.holidayDays().repetitiveHoliday) && self.holidayDays().repetitiveHoliday.length > 0) {
                        if(self.holidayDays().repetitiveHoliday.includes($.datepicker.formatDate('m-d',date))) {
                            return [false,''];
                        }
                    }
                    return [true,''];
                }

                ko.bindingHandlers.datepicker = {
                    init: function (element, valueAccessor, allBindingsAccessor) {
                        var $el = $(element);
                        //initialize datepicker
                        if(noday) {
                            // noinspection JSDuplicatedDeclaration
                            var options = {
                                minDate: 0,
                                dateFormat:format,
                                hideIfNoPrevNext: true,
                            };
                        } else {
                            // noinspection JSDuplicatedDeclaration
                            var options = {
                                minDate: 0,
                                dateFormat:format,
                                hideIfNoPrevNext: true,
                                beforeShowDay: function(date) {
                                    return unavailable(date);
                                }
                            };
                        }

                        $el.datepicker(options);

                        var writable = valueAccessor();
                        if (!ko.isObservable(writable)) {
                            var propWriters = allBindingsAccessor()._ko_property_writers;
                            if (propWriters && propWriters.datepicker) {
                                writable = propWriters.datepicker;
                            } else {
                                return;
                            }
                        }
                        writable($(element).datepicker("getDate"));
                    },
                    update: function (element, valueAccessor) {
                        var widget = $(element).data("DatePicker");
                        if (widget) {
                            var date = ko.utils.unwrapObservable(valueAccessor());
                            widget.date(date);
                        }
                    }
                };
                return this;
            },

            /**
             * Get Holiday Information
             *
             */
            getHoliday: function () {
                var responseHoliday = {};
                $(document).on('change',config.pickupStore,function(){
                    if($(config.pickupStore).val() !== "" && window.checkoutConfig.IsPickupDateEnabel === true){
                        self.canVisibleDateBlock(true);
                        $(config.pickupStoreDate).val('');
                        self.canVisibleTimeBlock(false);
                        var value = $(config.pickupStore).val();
                        setPickupDateAction(value).done(
                            function (response) {
                                responseHoliday['normalHoliday'] = response[0]['dates']['normal'];
                                responseHoliday['shippingHoliday'] = response[0]['dates']['shipping_off_day'];
                                responseHoliday['repetitiveHoliday'] = response[0]['dates']['repetitive'];
                                responseHoliday['closeDay'] = response[0]['days'];
                                self.pickupDate(responseHoliday);
                            }
                        );
                    }
                });
            },

            /**
             * Get Store Time
             *
             * @returns {*|Array}
             */
            getStoreTimeOption: function() {
                return _.map(self.storeTimeSloteObj(), function(value, key) {
                    return {
                        'value': value,
                        'label': value
                    }
                });
            },

            /**
             * Change Store Time
             *
             */
            changeStoreTime: function () {
                var dateVal = $(config.pickupStoreDate).val();
                var storeVal = $(config.pickupStore).val();
                if(dateVal !== "" && window.checkoutConfig.isTimeslotenable === true){
                    self.canVisibleTimeBlock(true);
                    getStoreTimeAction(dateVal, storeVal).done(
                        function (response) {
                            self.storeTimeSloteObj(response);
                        }
                    );
                } else {
                    self.canVisibleTimeBlock(false);
                }
            },

            /**
             * Set shipping information handler
             */
            setStorePickupInformation: function () {
                if (this.validateStorePickupInformation()) {
                    this.selectPickupLocation();
                    setShippingInformationAction().done(function () {
                        stepNavigator.next();
                    });
                }
            },

            /**
             * @param {Object} location
             * @returns void
             */
            selectPickupLocation: function () {
                pickupLocationsService.selectForShipping();
            },

            /**
             * @returns {Boolean}
             */
            validateStorePickupInformation: function () {
                var emailValidationResult,
                    loginFormSelector = this.loginFormSelector;

                if (!customer.isLoggedIn()) {
                    $(loginFormSelector).validation();
                    emailValidationResult = $(loginFormSelector + ' input[name=username]').valid() ? true : false;

                    if (!emailValidationResult) {
                        $(this.loginFormSelector + ' input[name=username]').trigger('focus');

                        return false;
                    }
                }

                var storeValidation,
                    storePickupFormSelector = this.storePickupFormSelector;
                    $(storePickupFormSelector).validation();
                    storeValidation = $(storePickupFormSelector + ' select[name=pickup_store]').valid() ? true : false;
                    if (!storeValidation) {
                        $(this.storePickupFormSelector + ' select[name=pickup_store]').trigger('focus');

                        return false;
                    }

                var storeDateValidation,
                    storePickupSlotFormSelector = this.storePickupSlotFormSelector;
                    $(storePickupSlotFormSelector).validation();
                    storeDateValidation = $(storePickupSlotFormSelector + ' input[name=pickup_date]').valid() ? true : false;
                    if (!storeDateValidation) {
                        $(this.storePickupSlotFormSelector + ' input[name=pickup_date]').trigger('focus');

                        return false;
                    }

                var storeTimeValidation,
                    storePickupSlotFormSelector = this.storePickupSlotFormSelector;
                    $(storePickupSlotFormSelector).validation();
                    storeTimeValidation = $(storePickupSlotFormSelector + ' select[name=store_time]').valid() ? true : false;
                    if (!storeTimeValidation) {
                        $(this.storePickupSlotFormSelector + ' select[name=store_time]').trigger('focus');

                        return false;
                    }

                return true;
            }
        });
    }
);
