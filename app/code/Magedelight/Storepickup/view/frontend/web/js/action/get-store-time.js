/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>abs. All Rights reserved.
*/

define(
    [
        'jquery',
        'Magento_Checkout/js/model/url-builder',
        'mage/storage',
        'Magento_Checkout/js/model/error-processor',
        'Magento_Checkout/js/model/full-screen-loader'
    ],
    function ($,
              urlBuilder,
              storage,
              errorProcessor,
              fullScreenLoader
    ) {
    'use strict';
        return function (dateVal, storeVal) {
            var url = '/magedelight_storepickup/guest-carts/:storeloctorId/get-store-time-information';
            var urlParams = {
                storeloctorVal: storeVal,
                storeDateVal: dateVal

            };
            var serviceUrl = urlBuilder.createUrl(url, urlParams),
                payload = {
                    storeVal, dateVal
                };

            if(payload) {
                fullScreenLoader.startLoader();

                return storage.post(
                    serviceUrl,
                    JSON.stringify(payload)
                ).done(
                    function (response) {
                        fullScreenLoader.stopLoader();
                    }
                ).fail(
                    function (response) {
                        errorProcessor.process(response);
                        fullScreenLoader.stopLoader();
                    }
                );
            }else{
                //jQuery("#show-address").html('');
            }
        }
    }
);
