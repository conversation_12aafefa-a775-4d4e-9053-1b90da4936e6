/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>abs. All Rights reserved.
*/

define([
    'mage/storage',
    'Magento_Checkout/js/model/url-builder',
    'Magento_Checkout/js/model/error-processor',
    'Magento_Checkout/js/model/full-screen-loader'
], function (storage, urlBuilder, errorProcessor, fullScreenLoader) {
    'use strict';

    return function() {
        var url = '/magedelight_storepickup/guest-carts/get-store-information';
        var urlParams = {};
        var serviceUrl = urlBuilder.createUrl(url, urlParams),
            payload = {};

        return storage.get(
            serviceUrl,
            JSON.stringify(payload),
            false
        ).done(
            function (response) {
                fullScreenLoader.stopLoader();
            }
        ).fail(
            function (response) {
                errorProcessor.process(response);
            }
        );
    };
});
