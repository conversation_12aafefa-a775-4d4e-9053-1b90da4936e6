/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>abs. All Rights reserved.
*/

define([
    'jquery',
    'knockout',
    'mage/storage',
    'Magento_Customer/js/customer-data',
    'Magento_Checkout/js/checkout-data',
    'Magento_Checkout/js/model/address-converter',
    'Magento_Checkout/js/action/select-shipping-address',
    'underscore',
    'mage/translate',
    'mage/url',
    'Magedelight_Storepickup/js/model/pickup-address-converter',
    'uiRegistry'
], function (
    $,
    ko,
    storage,
    customerData,
    checkoutData,
    addressConverter,
    selectShippingAddressAction,
    _,
    $t,
    url,
    pickupAddressConverter,
    registry
) {
    'use strict';

    var websiteCode = window.checkoutConfig.websiteCode,
        countryData = customerData.get('directory-data');

    return {
        locationsCache: [],

        /**
         * Select location for shipping.
         *
         * @param {Object} location
         * @returns void
         */
        selectForShipping: function () {
            var addtionalBlock = window.checkoutConfig.isMdOscEnabled ?
                    "checkout.steps.shippingMethods.shippingAdditional.additional_block" :
                    window.checkoutConfig.pickupLayout === 'tabview' ? 
                    "checkout.steps.md-store-pickup.md-store-selector.additional_block" : "checkout.steps.shipping-step.shippingAddress.shippingAdditional.additional_block";

                    
            var addobj = registry.get(addtionalBlock).changeStoreInfoObj();
            var storeAddress = addobj[0].address;
            var telephoneno = null;

            if (addobj[0].telephone) {
                telephoneno = addobj[0].telephone.toString().replace(":", ",");
            }

            var regionId = null;
            if (addobj[0].region) {
                regionId = addobj[0].region_id;
            }else{
                if (addobj[0].region_id) {
                    regionId = addobj[0].region_id;
                }
            }
                   

            var address = $.extend(
                {},
                addressConverter.formAddressDataToQuoteAddress({
                    firstname: addobj[0].storename,
                    lastname: addobj[0].storename,
                    street: [storeAddress],
                    city: addobj[0].city,
                    postcode: addobj[0].zipcode,
                    'country_id': addobj[0].country_id,
                    telephone: telephoneno,
                    'region_id': regionId,
                    'save_in_address_book': 0
                }));

            address = pickupAddressConverter.formatAddressToPickupAddress(address);
            selectShippingAddressAction(address);
            checkoutData.setSelectedShippingAddress(address.getKey());
            checkoutData.setSelectedPickupAddress(
                addressConverter.quoteAddressToFormAddressData(address)
            );
        },

        /**
         * Formats address returned by REST endpoint to match checkout address field naming.
         *
         * @param {Object} address - Address object returned by REST endpoint.
         */
        formatAddress: function (address) {
            return {
                name: address.name,
                description: address.description,
                latitude: address.latitude,
                longitude: address.longitude,
                street: [address.street],
                city: address.city,
                postcode: address.postcode,
                'country_id': address['country_id'],
                country: this.getCountryName(address['country_id']),
                telephone: address.phone,
                'region_id': address['region_id'],
                region: this.getRegionName(
                    address['country_id'],
                    address['region_id']
                ),
                /*'pickup_location_code': address['pickup_location_code']*/
            };
        },

        /**
         * Get country name by id.
         *
         * @param {*} countryId
         * @return {String}
         */
        getCountryName: function (countryId) {
            return countryData()[countryId] !== undefined ?
                countryData()[countryId].name
                : ''; //eslint-disable-line
        },

        /**
         * Returns region name based on given country and region identifiers.
         *
         * @param {String} countryId - Country identifier.
         * @param {String} regionId - Region identifier.
         */
        getRegionName: function (countryId, regionId) {
            var regions = countryData()[countryId] ?
                countryData()[countryId].regions
                : null;

            return regions && regions[regionId] ? regions[regionId].name : '';
        },

        /**
         * Process response errors.
         *
         * @param {Object} response
         * @returns void
         */
        processError: function (response) {
            var expr = /([%])\w+/g,
                error;

            if (response.status === 401) {
                //eslint-disable-line eqeqeq
                window.location.replace(url.build('customer/account/login/'));

                return;
            }

            try {
                error = JSON.parse(response.responseText);
            } catch (exception) {
                error = $t(
                    'Something went wrong with your request. Please try again later.'
                );
            }

            if (error.hasOwnProperty('parameters')) {
                error = error.message.replace(expr, function (varName) {
                    varName = varName.substr(1);

                    if (error.parameters.hasOwnProperty(varName)) {
                        return error.parameters[varName];
                    }

                    return error.parameters.shift();
                });
            }
        },

        /**
         * Returns selected pick up address from local storage
         *
         * @returns {Object|null}
         */
        getSelectedPickupAddress: function () {
            var shippingAddress,
                pickUpAddress;

            if (checkoutData.getSelectedPickupAddress()) {
                shippingAddress = addressConverter.formAddressDataToQuoteAddress(
                    checkoutData.getSelectedPickupAddress()
                );
                pickUpAddress = pickupAddressConverter.formatAddressToPickupAddress(
                    shippingAddress
                );

                return pickUpAddress;
            }

            return null;
        }
    };
});
