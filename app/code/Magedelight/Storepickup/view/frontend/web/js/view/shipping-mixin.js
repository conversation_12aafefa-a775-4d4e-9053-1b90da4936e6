/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>abs. All Rights reserved.
*/

define([
    'jquery',
    'uiRegistry',
    'Magento_Checkout/js/model/quote',
    'Magento_Customer/js/model/customer',
    'Magedelight_Storepickup/js/model/validate-store-pickup',
    'Magedelight_Storepickup/js/model/pickup-locations-service',
    'ko',
    'Magento_Checkout/js/model/step-navigator',
    'Magento_Checkout/js/action/set-shipping-information',
], function ($, registry, quote, customer, validateStorePickup,pickupLocationsService,ko,stepNavigator,setShippingInformationAction) {
    'use strict';

    return function (Shipping) {
        return Shipping.extend({
            isStorePickupSelected: false,
            loginFormSelector:
                'form[data-role=email-with-possible-login]',
            storePickupSlotFormSelector:
                'form[data-role=md-store-pickup-slot]',
            storePickupFormSelector:
                'form[data-role=md-store-pickup]',
            rate: {
                'carrier_code': 'storepickup',
                'method_code': 'storepickup'
            },

            /**
             * Validate guest email
             */
            validateGuestEmail: function () {
                var loginFormSelector = 'form[data-role=email-with-possible-login]';

                $(loginFormSelector).validation();

                return $(loginFormSelector + ' input[type=email]').valid();
            },

            validateShippingInformation: function () {
                var result = this._super(),
                    shippingMethod = quote.shippingMethod();

                if (!customer.isLoggedIn() && !this.validateGuestEmail()) {
                    return result;
                }

                if (shippingMethod && shippingMethod['carrier_code'] === 'storepickup') {
                    if (!validateStorePickup.validate()) {
                        return false;
                    }
                }

                return result;
            },

            /**
             * Set shipping information handler
             */
            setShippingInformation: function () {

                this.isStorePickupSelected = ko.pureComputed(function () {
                    return _.isMatch(quote.shippingMethod(), this.rate);
                }, this);

                if (window.checkoutConfig.pickupLayout === 'along_with_shipping' && this.isStorePickupSelected()) {
                    if (this.validateStorePickupInformation()) {
                        this.selectPickupLocation();
                        setShippingInformationAction().done(function () {
                            stepNavigator.next();
                        });
                    }
                }else{
                    this._super();
                }
            },

            /**
             * @param {Object} location
             * @returns void
             */
            selectPickupLocation: function () {
                pickupLocationsService.selectForShipping();
            },

            /**
             * @returns {Boolean}
             */
            validateStorePickupInformation: function () {
                var emailValidationResult,
                    loginFormSelector = this.loginFormSelector;

                if (!customer.isLoggedIn()) {
                    $(loginFormSelector).validation();
                    emailValidationResult = Boolean($(loginFormSelector + ' input[name=username]').valid());
                    
                    if (!emailValidationResult) {
                        $(loginFormSelector + ' input[name=username]').trigger('focus');

                        return false;
                    }
                }


                var storeValidation,
                    storePickupFormSelector = this.storePickupFormSelector;
                    $(storePickupFormSelector).validation();
                    storeValidation = $(storePickupFormSelector + ' select[name=pickup_store]').valid() ? true : false;
                    if (!storeValidation) {
                        $(this.storePickupFormSelector + ' select[name=pickup_store]').trigger('focus');

                        return false;
                    }

                var storeDateValidation,
                    storePickupSlotFormSelector = this.storePickupSlotFormSelector;
                    $(storePickupSlotFormSelector).validation();
                    storeDateValidation = $(storePickupSlotFormSelector + ' input[name=pickup_date]').valid() ? true : false;
                    if (!storeDateValidation) {
                        $(this.storePickupSlotFormSelector + ' input[name=pickup_date]').trigger('focus');

                        return false;
                    }

                var storeTimeValidation,
                    storePickupSlotFormSelector = this.storePickupSlotFormSelector;
                    $(storePickupSlotFormSelector).validation();
                    storeTimeValidation = $(storePickupSlotFormSelector + ' select[name=store_time]').valid() ? true : false;
                    if (!storeTimeValidation) {
                        $(this.storePickupSlotFormSelector + ' select[name=store_time]').trigger('focus');

                        return false;
                    }

                return true;
            }
        });
    };
});
