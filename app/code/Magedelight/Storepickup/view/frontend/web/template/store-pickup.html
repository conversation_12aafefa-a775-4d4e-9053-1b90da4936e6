<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
*/
-->
<li id="store-pickup"
    if="isVisible"
    css="'selected-shipping': !isStorePickupSelected(), 'selected-store-pickup': isStorePickupSelected()"
>
    <!-- ko if: (methodAvailable) -->
        <render args="deliveryMethodSelectorTemplate"></render>
    <!-- /ko -->
    <if args="isStorePickupSelected">
        <each args="getRegion('md-store-selector')">
            <div template=" {name: getTemplate()}"></div>
        </each>
    </if>
</li>
