<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/
-->
<form id="md-store-pickup-slot" class="form-continue" data-role="md-store-pickup-slot" data-bind="visible: canVisibleDateAndTime()">
    <div id="pickup-date-block" data-bind="visible: canVisibleDateBlock">
        <label class="pickup-date-label">
            <span id="pickup-date-label" data-bind="i18n: 'Pickup Date'"></span>
        </label>
        <input class="field required"
            data-validate="{required:true}"
            type="text"
            id="pickup-date"
            data-bind="datepicker: true, event: {change: changeStoreTime()}"
            name="pickup_date"
            placeholder="Select Pickup Date"
            readonly="true">
    </div>
    <div class="store-time" data-bind="visible: canVisibleTimeBlock">
        <label class="store-time-label" ><span id="store-time" data-bind="i18n: 'Select Time'"></span></label>
        <select name="store_time"
                class="field required"
                data-validate="{required:true}"
                data-bind="
                          options: getStoreTimeOption(),
                          optionsValue: 'value',
                          optionsText: 'label',
                          optionsCaption: $t('Select a Time Slot'),
                          value: storeTimeOptionValue">
        </select>
    </div>
    <div class="primary" data-bind="visible: canVisibleNextButton()">
        <button data-role="opc-continue"
        type="submit"
        class="button action continue primary"
        data-bind="click: setStorePickupInformation"
        >
            <span>
                <translate args="'Next'"></translate>
            </span>
        </button>
    </div>
</form>
