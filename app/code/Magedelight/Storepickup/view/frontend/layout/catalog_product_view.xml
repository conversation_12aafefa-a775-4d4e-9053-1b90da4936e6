<?xml version="1.0"?>
<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
*/
-->

<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <referenceContainer name="product.info.extrahint" >
        <block name="product.view.store" template="Magedelight_Storepickup::product/view/store.phtml" ifconfig="magedelight_storepickup/general/enable" after="-">
            <arguments>
                <argument name="view_model" xsi:type="object">Magedelight\Storepickup\ViewModel\StoreList</argument>
            </arguments>
        </block>
    </referenceContainer>
</page>
