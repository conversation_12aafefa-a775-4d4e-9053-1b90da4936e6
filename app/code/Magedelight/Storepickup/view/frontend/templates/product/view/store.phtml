<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
$enabled = $block->getViewModel()->showProductAvailability();
$stores = $block->getViewModel()->getAllValidateStoreIds();
$popupenable = 0;
if ($enabled && count($stores)):
    ?>
<div class="store-information">
    <div id="store-information-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 40 56">
            <g id="ic_location-delivery-box" transform="translate(-12 -4)">
                <path id="Path_44845" data-name="Path 44845" d="M38.22,17H35v2h3.72Z" fill="#fff"/>
                <path id="Path_44846" data-name="Path 44846" d="M39,21H35v3a1,1,0,0,1-1,1H30a1,1,0,0,1-1-1V21H25V31H39Zm-2,9H34a1,1,0,0,1,0-2h3a1,1,0,0,1,0,2Z" fill="#fff"/>
                <path id="Path_44847" data-name="Path 44847" d="M31,17h2v2H31Z" fill="#fff"/>
                <path id="Path_44848" data-name="Path 44848" d="M29,17H25.78l-.5,2H29Z" fill="#fff"/>
                <path id="Path_44849" data-name="Path 44849" d="M32,54.59C35.61,50.95,52,33.88,52,24a20,20,0,1,0-40,0C12,33.88,28.39,50.95,32,54.59ZM32,8A16,16,0,1,1,16,24,16,16,0,0,1,32,8Z" fill="#fff"/>
                <path id="Path_44850" data-name="Path 44850" d="M32,38A14,14,0,1,0,18,24,14,14,0,0,0,32,38ZM23,20a1.569,1.569,0,0,1,.03-.24l1-4A1,1,0,0,1,25,15H39a1,1,0,0,1,.97.76l1,4A1.569,1.569,0,0,1,41,20V32a1,1,0,0,1-1,1H24a1,1,0,0,1-1-1Z" fill="#fff"/>
                <path id="Path_44851" data-name="Path 44851" d="M32,60c12.29,0,19-2.64,19-4,0-1-4.22-3.19-13.91-3.83-2.4,2.6-4.15,4.31-4.39,4.55a1.015,1.015,0,0,1-1.4,0c-.24-.24-1.99-1.95-4.39-4.55C17.22,52.81,13,55,13,56,13,57.36,19.71,60,32,60Z" fill="#fff"/>
                <path id="Path_44852" data-name="Path 44852" d="M31,21h2v2H31Z" fill="#fff"/>
            </g>
        </svg>
        <span><?=/* @noEscape */ __('View Pickup Points') ?></span>
    </div>
    <?php if($block->getViewModel()->getPdpPickupLayout()==\Magedelight\Storepickup\Model\Config\Source\StoreLayout::PICKUP_LAYOUT_WITH_PAGE):?>
        <div id="store-information-list">
            <div class="title"><?=/* @noEscape */ __('Pickup is available in %1 stores', count($stores)) ?></div>
            <div class="stores">
                <?php $i = 0;?>
                <?php foreach ($stores as $store):?>
                    <?php if ($i==6):?>
                        <div class="hide" style="display: none;">
                    <?php endif;?>
                    <div class="store-column">
                        <div class="store-inner-column">
                            <div class="store-info">
                                <svg id="ic_Pin_gradient" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="14.195" height="18.927" viewBox="0 0 14.195 18.927">
                                    <defs>
                                        <linearGradient id="linear-gradient" x1="0.089" y1="0.085" x2="0.818" y2="0.89" gradientUnits="objectBoundingBox">
                                        <stop offset="0" stop-color="#ff4d4d"/>
                                        <stop offset="1" stop-color="#3e4b9e"/>
                                        </linearGradient>
                                    </defs>
                                    <path id="Path_44853" data-name="Path 44853" d="M15.1,0A7.107,7.107,0,0,0,8,7.1c0,5.1,6.612,11.477,6.893,11.746a.293.293,0,0,0,.408,0c.281-.269,6.893-6.651,6.893-11.746A7.107,7.107,0,0,0,15.1,0Zm0,10.35A3.253,3.253,0,1,1,18.35,7.1,3.253,3.253,0,0,1,15.1,10.35Z" transform="translate(-8)" fill="url(#linear-gradient)"/>
                                </svg>
                                <div class="store-name">
                                    <h3><?= /* @noEscape */__($store->getStorename()) ?></h3>
                                </div>
                                <div class="store-address">
                                    <p>
                                        <?php
                                            $address = $block->getViewModel()->filterOutputHtml($store->getAddress()).', '.$store->getCity();

                                            if ($store->getRegion()!="") {
                                                $address.= ', '.$store->getRegion();
                                            }

                                            $address.= ', '.$store->getZipcode();

                                            echo $this->escapeHtml(__($address));
                                        ?>      
                                    </p>
                                    <p><?=/* @noEscape */__($block->getViewModel()->getCountryByCode($store->getCountryId())) ?></p>
                                </div>
                                <?php if ($store->getPhoneFrontendStatus()): ?>
                                <div class="store-contact">
                                    <p><?=/* @noEscape */ __($store->getTelephone()) ?></p>
                                </div>
                                <?php endif; ?>
                                <?php if ($store->getIsPickupLocation()): ?>
                                <div class="pickup">
                                    <p>
                                        <svg id="ic_tick" xmlns="http://www.w3.org/2000/svg" width="15.675" height="15.675" viewBox="0 0 15.675 15.675">
                                            <path id="Path_44854" data-name="Path 44854" d="M7.838,0a7.838,7.838,0,1,0,7.838,7.838A7.845,7.845,0,0,0,7.838,0Z" fill="#4bae4f" fill-rule="evenodd"/>
                                            <path id="Path_44855" data-name="Path 44855" d="M109.083,143.55c-1-1-2-2.011-3-3.013a.288.288,0,0,1,0-.4l1.154-1.154a.288.288,0,0,1,.4,0l1.65,1.65,4.243-4.246a.292.292,0,0,1,.407,0l1.157,1.157a.283.283,0,0,1,0,.4l-5.612,5.606A.283.283,0,0,1,109.083,143.55Z" transform="translate(-102.755 -132.127)" fill="#fff"/>
                                        </svg>
                                        <?=/* @noEscape */ __("Pickup available") ?>
                                    </p>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="store-stock">
                                <?php if ($store->getData('msi_source')):?>
                                    <?php $stockData = $block->getViewModel()->getMSIStockQty();?>
                                    <?php if (isset($stockData[$store->getData('msi_source')]) && $stockData[$store->getData('msi_source')] != "" ):?>
                                        <div class="msi-store-stock">
                                            <span><?= /* @noEscape */ $stockData[$store->getData('msi_source')]?></span>
                                            <span><?=/* @noEscape */ __("pcs") ?></span>
                                        </div>
                                        <?php if ($stockData[$store->getData('msi_source')] > 0):?>
                                            <span><?=/* @noEscape */ __("In stock") ?></span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php $i++;?>
                <?php endforeach; ?>
                <?php if ($i >= 6):?>
                    </div>
                <?php endif;?>    
            </div>
            <?php if (count($stores)>6):?>
                <div class="so-more">
                    <a href="javascript:void(0)"> <?=/* @noEscape */ __('See All') ?>
                        <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 9L12 15L18 9" stroke="#0077b6" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style=""/>
                        </svg>
                    </a>
                </div>
            <?php endif;?>  
        </div>
    <?php else:?>
        <?php $popupenable=1; ?>
        <div id="store-information-list" style="display:none;">
            <div class="stores">
                <?php foreach ($stores as $store):?>
                    <div class="store-column">
                        <div class="store-inner-column">
                            <div class="store-info">
                                <svg id="ic_Pin_gradient" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="14.195" height="18.927" viewBox="0 0 14.195 18.927">
                                    <defs>
                                        <linearGradient id="linear-gradient" x1="0.089" y1="0.085" x2="0.818" y2="0.89" gradientUnits="objectBoundingBox">
                                        <stop offset="0" stop-color="#ff4d4d"/>
                                        <stop offset="1" stop-color="#3e4b9e"/>
                                        </linearGradient>
                                    </defs>
                                    <path id="Path_44853" data-name="Path 44853" d="M15.1,0A7.107,7.107,0,0,0,8,7.1c0,5.1,6.612,11.477,6.893,11.746a.293.293,0,0,0,.408,0c.281-.269,6.893-6.651,6.893-11.746A7.107,7.107,0,0,0,15.1,0Zm0,10.35A3.253,3.253,0,1,1,18.35,7.1,3.253,3.253,0,0,1,15.1,10.35Z" transform="translate(-8)" fill="url(#linear-gradient)"/>
                                </svg>
                                <div class="store-name">
                                    <h3><?= /* @noEscape */__($store->getStorename()) ?></h3>
                                </div>
                                <div class="store-address">
                                    <p>
                                        <?php
                                            $address = $block->getViewModel()->filterOutputHtml($store->getAddress()).', '.$store->getCity();

                                            if ($store->getRegion()!="") {
                                                $address.= ', '.$store->getRegion();
                                            }

                                            $address.= ', '.$store->getZipcode();

                                            echo $this->escapeHtml(__($address));
                                        ?>      
                                    </p>
                                    <p><?=/* @noEscape */__($block->getViewModel()->getCountryByCode($store->getCountryId())) ?></p>
                                </div>
                                <?php if ($store->getPhoneFrontendStatus()): ?>
                                <div class="store-contact">
                                    <p><?=/* @noEscape */ __($store->getTelephone()) ?></p>
                                </div>
                                <?php endif; ?>
                                <?php if ($store->getIsPickupLocation()): ?>
                                <div class="pickup">
                                    <p>
                                        <svg id="ic_tick" xmlns="http://www.w3.org/2000/svg" width="15.675" height="15.675" viewBox="0 0 15.675 15.675">
                                            <path id="Path_44854" data-name="Path 44854" d="M7.838,0a7.838,7.838,0,1,0,7.838,7.838A7.845,7.845,0,0,0,7.838,0Z" fill="#4bae4f" fill-rule="evenodd"/>
                                            <path id="Path_44855" data-name="Path 44855" d="M109.083,143.55c-1-1-2-2.011-3-3.013a.288.288,0,0,1,0-.4l1.154-1.154a.288.288,0,0,1,.4,0l1.65,1.65,4.243-4.246a.292.292,0,0,1,.407,0l1.157,1.157a.283.283,0,0,1,0,.4l-5.612,5.606A.283.283,0,0,1,109.083,143.55Z" transform="translate(-102.755 -132.127)" fill="#fff"/>
                                        </svg>
                                        <?=/* @noEscape */ __("Pickup available") ?>
                                    </p>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="store-stock">
                                <?php if ($store->getData('msi_source')):?>
                                    <?php $stockData = $block->getViewModel()->getMSIStockQty();?>
                                    <?php if (isset($stockData[$store->getData('msi_source')]) && $stockData[$store->getData('msi_source')] != "" ):?>
                                        <div class="msi-store-stock">
                                            <span><?= /* @noEscape */ $stockData[$store->getData('msi_source')]?></span>
                                            <span><?=/* @noEscape */ __("pcs") ?></span>
                                        </div>
                                        <?php if ($stockData[$store->getData('msi_source')] > 0):?>
                                            <span><?=/* @noEscape */ __("In stock") ?></span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>  
            </div> 
        </div>
    <?php endif;?>
</div>
<?php endif ?>
<script>
    var popupenable = "<?= /* @noEscape */ $popupenable ?>";
</script>
<script type="text/x-magento-init">
{
    "*": {
        "Magedelight_Storepickup/js/product/view/list" : {}
    }
}
</script>
