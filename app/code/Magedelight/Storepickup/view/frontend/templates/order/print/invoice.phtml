<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
?>
<?php
$_order = $block->getOrder();
$mdHelper = $this->helper(\Magedelight\Storepickup\Helper\Storelocator::class);
$displayDeliveryPin = $mdHelper->getConfig(
    'magedelight_storepickup/deliverypin/display_delivery_pin',
    $mdHelper->getCurrentStoreId()
);
$enableDeliveryPin = $mdHelper->getConfig(
    'magedelight_storepickup/deliverypin/enable_delivery_pin',
    $mdHelper->getCurrentStoreId()
);
?>
<?php $_invoice = $block->getInvoice() ?>
<?php if ($_invoice): ?>
    <?php $_invoices = [$_invoice]; ?>
<?php else: ?>
    <?php $_invoices = $_order->getInvoiceCollection() ?>
<?php endif; ?>
<?php foreach ($_invoices as $_invoice): ?>
<div class="order-details-items invoice">
    <div class="order-title">
        <strong><?= $escaper->escapeHtml(__('Invoice #')) ?><?= (int) $_invoice->getIncrementId() ?></strong>
    </div>
    <div class="table-wrapper table-order-items invoice">
        <table class="data table table-order-items invoice" id="my-invoice-table-<?= (int) $_invoice->getId() ?>">
            <caption class="table-caption"><?= $escaper->escapeHtml(__('Items Invoiced')) ?></caption>
            <thead>
            <tr>
                <th class="col name"><?= $escaper->escapeHtml(__('Product Name')) ?></th>
                <th class="col sku"><?= $escaper->escapeHtml(__('SKU')) ?></th>
                <th class="col price"><?= $escaper->escapeHtml(__('Price')) ?></th>
                <th class="col qty"><?= $escaper->escapeHtml(__('Qty Invoiced')) ?></th>
                <th class="col subtotal"><?= $escaper->escapeHtml(__('Subtotal')) ?></th>
            </tr>
            </thead>
            <?php $_items = $_invoice->getItemsCollection(); ?>
            <?php foreach ($_items as $_item): ?>
                <?php if (!$_item->getOrderItem()->getParentItem()): ?>
                    <tbody>
                        <?= $block->getItemHtml($_item) ?>
                    </tbody>
                <?php endif; ?>
            <?php endforeach; ?>
            <tfoot>
                <?= $block->getInvoiceTotalsHtml($_invoice) ?>
            </tfoot>
        </table>
    </div>
    <div class="block block-order-details-view">
        <div class="block-title">
            <strong><?= $escaper->escapeHtml(__('Order Information')) ?></strong>
        </div>
        <div class="block-content">
        <?php if (!$_order->getIsVirtual()): ?>
            <div class="box box-order-shipping-address">
                <div class="box-title">
                    <strong><?= $escaper->escapeHtml(__('Shipping Address')) ?></strong>
                </div>
                <div class="box-content">
                    <?php $_shipping = $_invoice->getShippingAddress() ?>
                    <address><?= /* @noEscape */ $block->formatAddress($_shipping, 'html') ?></address>
                </div>
            </div>

            <div class="box box-order-shipping-method">
                <div class="box-title">
                    <strong><?= $escaper->escapeHtml(__('Shipping Method')) ?></strong>
                </div>
                <div class="box-content">
                    <?php if ($_order->getShippingDescription()): ?>
                        <?= /* @noEscape */$_order->getShippingDescription() ?>
                        <?php if ($_order->getShippingMethod() == 'storepickup_storepickup'): ?>
                            <?php if ($enableDeliveryPin && ($displayDeliveryPin == 'account' || $displayDeliveryPin == 'both')):?>
                                <?=/* @noEscape */ __("Delivery Pin: ". $_order->getDeliveryPin()); ?>
                        <?php endif; ?>
                        <?php endif; ?>

                    <?php else: ?>
                        <?= $escaper->escapeHtml(__('No shipping information available')) ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
            <div class="box box-order-billing-address">
                <div class="box-title">
                    <strong><?= $escaper->escapeHtml(__('Billing Address')) ?></strong>
                </div>
                <div class="box-content">
                    <?php $_billing = $_invoice->getbillingAddress() ?>
                    <address><?=/* @noEscape */ $block->formatAddress($_order->getBillingAddress(), 'html') ?></address>
                </div>
            </div>

            <div class="box box-order-billing-method">
                <div class="box-title">
                    <strong><?= $escaper->escapeHtml(__('Payment Method')) ?></strong>
                </div>
                <div class="box-content">
                    <?= $block->getPaymentInfoHtml() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>
