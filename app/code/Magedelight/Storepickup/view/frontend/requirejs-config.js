/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
*/

var config = {
    config: {
        mixins: {
            'Magento_Checkout/js/view/shipping': {
                'Magedelight_Storepickup/js/view/shipping-mixin': true
            },
            'Magento_Checkout/js/model/shipping-save-processor/payload-extender': {
                'Magedelight_Storepickup/js/model/shipping-save-processor/store-pickup-payload-extender-mixin': true
            }
        }
    }
};
