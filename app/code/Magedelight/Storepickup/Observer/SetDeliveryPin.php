<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Observer;

use Magedelight\Storepickup\Api\StorelocatorRepositoryInterface;
use Magedelight\Storepickup\Model\StorelocatorFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order\Address\Renderer;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;
use \Magento\Framework\Mail\Template\TransportBuilder;

class SetDeliveryPin implements ObserverInterface
{
    /**
     * @var TransportBuilder
     */
    protected $transportBuilder;

    /**
     * @var LoggerInterface
     */
    protected $_logLoggerInterface;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var OrderRepositoryInterface
     */
    protected $orderRepository;
    /**
     * @var \Magento\Framework\Event\ManagerInterface
     */
    protected $eventManager;
    /**
     * @var Renderer
     */
    protected $addressRenderer;
    /**
     * @var \Magento\Payment\Helper\Data
     */
    private $paymentHelper;
    /**
     * @var \Magento\Sales\Model\Order $order
     */
    protected $order;

    /**
     * @var StorelocatorFactory
     */
    protected $storelocatorFactory;

    /**
     * @var StorelocatorRepositoryInterface
     */
    protected $storelocatorRepository;

    /**
     * @var \Magento\Sales\Model\Order\Email\Container\CreditmemoIdentity
     */
    protected $identityContainer;

    /**
     * SendMailAfterCancelOrder constructor.
     * @param TransportBuilder $transportBuilder
     * @param LoggerInterface $logLoggerInterface
     * @param OrderRepositoryInterface $OrderRepositoryInterface
     * @param \Magento\Sales\Model\Order $order
     * @param StorelocatorFactory $storelocatorFactory
     * @param ScopeConfigInterface $scopeConfig
     * @param ManagerInterface $eventManager
     * @param Renderer $addressRenderer
     * @param \Magento\Payment\Helper\Data $paymentHelper
     * @param \Magento\Sales\Model\Order\Email\Container\CreditmemoIdentity $identityContainer
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     */
    public function __construct(
        TransportBuilder $transportBuilder,
        LoggerInterface $logLoggerInterface,
        OrderRepositoryInterface $OrderRepositoryInterface,
        \Magento\Sales\Model\Order $order,
        StorelocatorFactory $storelocatorFactory,
        ScopeConfigInterface $scopeConfig,
        ManagerInterface $eventManager,
        Renderer $addressRenderer,
        \Magento\Payment\Helper\Data $paymentHelper,
        \Magento\Sales\Model\Order\Email\Container\CreditmemoIdentity $identityContainer,
        StorelocatorRepositoryInterface $storelocatorRepository
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->_logLoggerInterface = $logLoggerInterface;
        $this->orderRepository = $OrderRepositoryInterface;
        $this->order = $order;
        $this->storelocatorFactory = $storelocatorFactory;
        $this->scopeConfig = $scopeConfig;
        $this->eventManager = $eventManager;
        $this->addressRenderer = $addressRenderer;
        $this->paymentHelper = $paymentHelper;
        $this->identityContainer = $identityContainer;
        $this->storelocatorRepository = $storelocatorRepository;
    }

    /**
     * Set delivery pin action
     *
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $order = $observer->getEvent()->getOrder();
        if ($order instanceof \Magento\Framework\Model\AbstractModel) {
            try {
                $adminemail = $this->scopeConfig->getValue(
                    'trans_email/ident_general/email',
                    ScopeInterface::SCOPE_STORE
                );

                $adminname = $this->scopeConfig->getValue(
                    'trans_email/ident_general/name',
                    ScopeInterface::SCOPE_STORE
                );

                $displayDeliveryPin = $this->scopeConfig->getValue(
                    'magedelight_storepickup/deliverypin/display_delivery_pin',
                    ScopeInterface::SCOPE_STORE
                );

                $enableDeliveryPin = $this->scopeConfig->getValue(
                    'magedelight_storepickup/deliverypin/enable_delivery_pin',
                    ScopeInterface::SCOPE_STORE
                );

                if ($order->getShippingMethod() == 'storepickup_storepickup') {
                    $storeEmail = $order->getCustomerEmail();

                    if ($order->getStatus() == 'ready_for_pickup') {
                        $myvar = $order;
                        $emailTempVariables['order'] = $myvar;
                        $displayPin = ($displayDeliveryPin == 'email' ||
                            $displayDeliveryPin == 'both' ||
                            $order->getCustomerIsGuest()) ? 1 : 0;

                        $enablePin = $enableDeliveryPin == false ? 0 : 1;
                        $transport = [
                            'order' => $order,
                            'billing' => $order->getBillingAddress(),
                            'shipping' => $order->getShippingAddress(),
                            'payment_html' => $this->getPaymentHtml($order),
                            'store' => $order->getStore(),
                            'formattedShippingAddress' => $this->getFormattedShippingAddress($order),
                            'formattedBillingAddress' => $this->getFormattedBillingAddress($order),
                            'display_delivery_pin' => $displayPin,
                            'enable_delivery_pin' => $enablePin,
                        ];

                        $transport = new \Magento\Framework\DataObject($transport);

                        $this->eventManager->dispatch(
                            'email_order_set_template_vars_before',
                            ['sender' => $this, 'transport' => $transport]
                        );

                        $senderName = $adminname;
                        $recieveremail = $storeEmail;
                        $senderEmail = $adminemail;

                        $postObject = new \Magento\Framework\DataObject();
                        $postObject->setData($emailTempVariables);

                        $sender = ['name' => $senderName, 'email' => $senderEmail];

                        $transport = $this->transportBuilder->setTemplateIdentifier(
                            'sales_order_ready_for_pickup_template'
                        )
                            ->setTemplateOptions(
                                ['area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                                'store' => $order->getStoreId()]
                            )
                            ->setTemplateVars($transport->getData())
                            ->setFromByScope($sender)
                            ->addTo($recieveremail)
                            ->setReplyTo($senderEmail)
                            ->getTransport();
                        $transport->sendMessage();
                    }
                }
            } catch (\Exception $e) {
                $this->_logLoggerInterface->debug($e->getMessage());
            }
        }
    }

    /**
     * Get Shipping address
     *
     * @param Order $order
     * @return string|null
     */
    protected function getFormattedShippingAddress($order)
    {
        return $order->getIsVirtual()
        ? null
        : $this->addressRenderer->format($order->getShippingAddress(), 'html');
    }

    /**
     * Get Billing address
     *
     * @param Order $order
     * @return string|null
     */
    protected function getFormattedBillingAddress($order)
    {
        return $this->addressRenderer->format($order->getBillingAddress(), 'html');
    }

    /**
     * Get payment
     *
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return string
     * @throws \Exception
     */
    private function getPaymentHtml(\Magento\Sales\Api\Data\OrderInterface $order)
    {
        return $this->paymentHelper->getInfoBlockHtml(
            $order->getPayment(),
            $this->identityContainer->getStore()->getStoreId()
        );
    }
}
