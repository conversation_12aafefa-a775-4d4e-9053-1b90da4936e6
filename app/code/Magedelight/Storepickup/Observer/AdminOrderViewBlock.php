<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Observer;

use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\View\Element\TemplateFactory;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magedelight\Storepickup\Api\StorelocatorRepositoryInterface;
use Magento\Sales\Model\Order\Address;

class AdminOrderViewBlock implements ObserverInterface
{
    /**
     * @var TemplateFactory
     */
    protected $templateFactory;

    /**
     * @var TimezoneInterface
     */
    private $timezone;

    /**
     * @var StorelocatorRepositoryInterface
     */
    protected $storelocatorRepository;

    /**
     * @var Address\Renderer
     */
    protected $addressRenderer;

    /**
     * AdminOrderViewBlock constructor.
     * @param TemplateFactory $templateFactory
     * @param TimezoneInterface $timezone
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     * @param Address\Renderer $addressRenderer
     */
    public function __construct(
        TemplateFactory $templateFactory,
        TimezoneInterface $timezone,
        StorelocatorRepositoryInterface $storelocatorRepository,
        \Magento\Sales\Model\Order\Address\Renderer $addressRenderer
    ) {
        $this->templateFactory = $templateFactory;
        $this->timezone = $timezone;
        $this->storelocatorRepository = $storelocatorRepository;
        $this->addressRenderer = $addressRenderer;
    }

    /**
     * Admin action
     *
     * @param EventObserver $observer
     * @return $this
     */
    public function execute(EventObserver $observer)
    {
        $element = $observer->getElementName();
        if ($element == 'order_shipping_view') {
            $orderViewBlock = $observer->getLayout()->getBlock($element);
            $order = $orderViewBlock->getOrder();

            if ($order->getPickupDate() != '0000-00-00') {
                $formattedDate = $order->getPickupDate();
            } else {
                $formattedDate = __('N/A');
            }

            /** @var \Magento\Framework\View\Element\Template $storeInfoBlock */
            $storeInfoBlock = $this->templateFactory->create();
            $storeInfoBlock->setStoreName($order->getPickupStore());
            $storeInfoBlock->setStoreAddress($this->getFormattedAddress($order->getShippingAddress()));
            $storeInfoBlock->setPickupDate($formattedDate);

            $storeInfoBlock->setTemplate('Magedelight_Storepickup::order/view/tab/storeinfo.phtml');
            $html = $observer->getTransport()->getOutput() . $storeInfoBlock->toHtml();
            $observer->getTransport()->setOutput($html);
        }
        return $this;
    }

    /**
     * Returns string with formatted address
     *
     * @param Address $address
     * @return null|string
     */
    public function getFormattedAddress(Address $address)
    {
        return $this->addressRenderer->format($address, 'html');
    }
}
