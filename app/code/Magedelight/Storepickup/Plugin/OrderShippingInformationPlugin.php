<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Plugin;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Magento\Store\Model\ScopeInterface;

class OrderShippingInformationPlugin
{
    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var State
     */
    protected $state;

    /**
     * @var \Magento\Cms\Model\Template\FilterProvider
     */
    protected $filterProvider;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Magento\Shipping\Model\Config
     */
    protected $shipconfig;

    /**
     * @var int
     */
    protected $flag = 0;

    /**
     * OrderShippingInformationPlugin constructor.
     * @param ScopeConfigInterface $scopeConfig
     * @param State $state
     * @param \Magento\Cms\Model\Template\FilterProvider $filterProvider
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Shipping\Model\Config $shipconfig
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        State $state,
        \Magento\Cms\Model\Template\FilterProvider $filterProvider,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Shipping\Model\Config $shipconfig
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->state = $state;
        $this->filterProvider = $filterProvider;
        $this->storeManager = $storeManager;
        $this->shipconfig = $shipconfig;
    }

    /**
     * After get shipping
     *
     * @param \Magento\Sales\Model\Order $subject
     * @param string $result
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function aftergetShippingDescription(\Magento\Sales\Model\Order $subject, $result)
    {
        $pickupdate = $subject->getPickupDate();

        $shipingMethod = $subject->getShippingMethod();
        $areaCode = $this->state->getAreaCode();
        $displayDeliveryPin = $this->scopeConfig->getValue(
            'magedelight_storepickup/deliverypin/display_delivery_pin',
            ScopeInterface::SCOPE_STORE
        );

        $enableDeliveryPin = $this->scopeConfig->getValue(
            'magedelight_storepickup/deliverypin/enable_delivery_pin',
            ScopeInterface::SCOPE_STORE
        );
        if ($areaCode != "frontend") {
            $result = strip_tags($result);
        } else {
            $result = "<address>" . strip_tags($result);
        }

        if (empty($pickupdate)) {
            $result = str_replace(', Pickup-Date:', '', $result);
        } else {
            if ($areaCode != "frontend") {
                $result = $result . ' Pickup-Date: ' . $pickupdate;
            } else {
                $result = $result . ',<br/> Pickup Date - ' . $pickupdate;
            }
        }
        if ($shipingMethod == 'storepickup_storepickup') {
            if ($enableDeliveryPin && ($displayDeliveryPin == 'email' || $displayDeliveryPin == 'both')) {
                if ($areaCode != "frontend") {
                    if ($subject->getDeliveryPin() != "") {
                        $result = $result . $this->getDeliveryPinTxt($subject->getDeliveryPin());
                    }
                }
            }
        }
        if ($areaCode != "frontend") {
            $result = $result;
        } else {
            $result = $result . "</address>";
        }
        $storeId = $this->storeManager->getStore()->getId();
        return $this->filterProvider->getBlockFilter()->setStoreId($storeId)->filter($result);
    }

    /**
     * Get delivery pin
     *
     * @param mixed $deliveryPin
     * @return string
     */
    private function getDeliveryPinTxt($deliveryPin)
    {
        return ' Delivery Pin: ' . $deliveryPin;
    }
}
