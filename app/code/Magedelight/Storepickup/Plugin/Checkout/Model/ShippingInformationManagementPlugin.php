<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Plugin\Checkout\Model;

use Magento\Checkout\Api\Data\ShippingInformationInterface;
use Magento\Checkout\Model\ShippingInformationManagement;
use Magento\Quote\Model\ShippingAddressManagementInterface;
use Magento\Quote\Model\QuoteRepository;
use Magedelight\Storepickup\Model\Carrier\Storepickup;
/**
 * Class ShippingInformationManagement for save store pickup extension attribute data
 */
class ShippingInformationManagementPlugin
{
    /**
     * @var QuoteRepository
     */
    private $quoteRepository;

    /**
     * @var ShippingAddressManagementInterface
     */
    private $shippingAddressManagement;


    public function __construct(
        QuoteRepository $quoteRepository,
        ShippingAddressManagementInterface $shippingAddressManagement
    ) {
        $this->quoteRepository = $quoteRepository;
        $this->shippingAddressManagement = $shippingAddressManagement;
    }

    /**
     * Save pickup data
     *
     * @param ShippingInformationManagement $subject
     * @param \Magento\Checkout\Api\Data\PaymentDetailsInterface $paymentDetails
     * @param string|int $cartId
     * @param ShippingInformationInterface $addressInformation
     *
     * @return \Magento\Checkout\Api\Data\PaymentDetailsInterface
     */
    public function afterSaveAddressInformation(
        ShippingInformationManagement $subject,
        $paymentDetails,
        $cartId,
        ShippingInformationInterface $addressInformation
    ) {
        if ($addressInformation->getShippingCarrierCode() !== Storepickup::SHIPPING_METHOD_CODE) {
            return $paymentDetails;
        }

        /** @var Quote $quoteEntity */
        $quoteEntity = $this->quoteRepository->get($cartId);

        if ($store = (string)$addressInformation->getExtensionAttributes()->getPickupStore()) {
            $quoteEntity->setPickupStore($store);
        }

        if ($date = (string)$addressInformation->getExtensionAttributes()->getPickupDate()) {
            $quoteEntity->setPickupDate($date);
        }

        if ($emailValue = (string)$addressInformation->getExtensionAttributes()->getPickupStoreEmail()) {
            $quoteEntity->setPickupStoreEmail($emailValue);
        }

        $this->quoteRepository->save($quoteEntity);

        return $paymentDetails;
    }
}
