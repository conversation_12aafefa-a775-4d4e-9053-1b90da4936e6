<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\ViewModel\Adminhtml\Order;

use Magento\Framework\View\Element\Block\ArgumentInterface;

class Storepickup implements ArgumentInterface
{

    /**
     * @var \Magedelight\Storepickup\Api\StoreInformationManagementInterface 
     */
    protected $storeInformationManagement;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface 
     */
    protected $storeManagerInterface;

    /**
     * @var \Magedelight\Storepickup\Helper\Storelocator 
     */
    protected $storelocatorHelper;

    /**
     * StoreList constructor.
     * @param \Magedelight\Storepickup\Api\StoreInformationManagementInterface $storeInformationManagement
     * @param \Magento\Store\Model\StoreManagerInterface $storeManagerInterface
     * @param \Magedelight\Storepickup\Helper\Storelocator $storelocatorHelper
     */
    public function __construct(
        \Magedelight\Storepickup\Api\StoreInformationManagementInterface $storeInformationManagement,
        \Magento\Store\Model\StoreManagerInterface $storeManagerInterface,
        \Magedelight\Storepickup\Helper\Storelocator $storelocatorHelper
    ) {
        $this->storeInformationManagement = $storeInformationManagement;
        $this->storeManagerInterface = $storeManagerInterface;
        $this->storelocatorHelper = $storelocatorHelper;
    }

    /**
     * Get Store information
     *
     * @return array|StorelocatorRepositoryInterface[]
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStoreInformation()
    {
        return $this->storeInformationManagement->getStoreInformation();
    }

    /**
     * Get Base Url
     *
     * @return string
     */
    public function getBaseUrl()
    {
        return $this->storeManagerInterface->getStore()->getBaseUrl();
    }

    /**
     * Check pickup date enable
     *
     * @return boolean
     */
    public function enablePickupDate()
    {
        return ($this->storelocatorHelper->isStoreDateEnable()) ? $this->storelocatorHelper->isStoreDateEnable() : 0;
    }

    /**
     * Check store time enable
     *
     * @return boolean
     */
    public function isStoreTimeEnable()
    {
        return ($this->storelocatorHelper->isStoreTimeEnable()) ? $this->storelocatorHelper->isStoreTimeEnable() : 0;
    }
}
