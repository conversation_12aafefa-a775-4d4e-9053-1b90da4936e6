<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model;

use Magedelight\Storepickup\Api\StoreholidayRepositoryInterface;
use Magedelight\Storepickup\Api\Data\StoreholidaySearchResultsInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Reflection\DataObjectProcessor;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magedelight\Storepickup\Model\ResourceModel\Storeholiday as ResourceStoreholiday;
use Magedelight\Storepickup\Model\ResourceModel\Storeholiday\CollectionFactory as StoreholidayCollectionFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Api\ExtensionAttribute\JoinProcessorInterface;
use Magento\Framework\Api\ExtensibleDataObjectConverter;

class StoreholidayRepository implements StoreholidayRepositoryInterface
{

    /**
     * @var ResourceStoreholiday
     */
    protected $resource;

    /**
     * @var StoreholidayFactory
     */
    protected $storeholidayFactory;

    /**
     * @var StoreholidayCollectionFactory
     */
    protected $storeholidayCollectionFactory;

    /**
     * @var StoreholidaySearchResultsInterfaceFactory
     */
    protected $searchResultsFactory;

    /**
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @var DataObjectProcessor
     */
    protected $dataObjectProcessor;

    /**
     * @var JoinProcessorInterface
     */
    protected $extensionAttributesJoinProcessor;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var CollectionProcessorInterface
     */
    private $collectionProcessor;

    /**
     * @var ExtensibleDataObjectConverter
     */
    protected $extensibleDataObjectConverter;

    /**
     * StoreholidayRepository constructor.
     * @param ResourceStoreholiday $resource
     * @param StoreholidayFactory $storeholidayFactory
     * @param StoreholidayCollectionFactory $storeholidayCollectionFactory
     * @param StoreholidaySearchResultsInterfaceFactory $searchResultsFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param DataObjectProcessor $dataObjectProcessor
     * @param StoreManagerInterface $storeManager
     * @param CollectionProcessorInterface $collectionProcessor
     * @param JoinProcessorInterface $extensionAttributesJoinProcessor
     * @param ExtensibleDataObjectConverter $extensibleDataObjectConverter
     */
    public function __construct(
        ResourceStoreholiday $resource,
        StoreholidayFactory $storeholidayFactory,
        StoreholidayCollectionFactory $storeholidayCollectionFactory,
        StoreholidaySearchResultsInterfaceFactory $searchResultsFactory,
        DataObjectHelper $dataObjectHelper,
        DataObjectProcessor $dataObjectProcessor,
        StoreManagerInterface $storeManager,
        CollectionProcessorInterface $collectionProcessor,
        JoinProcessorInterface $extensionAttributesJoinProcessor,
        ExtensibleDataObjectConverter $extensibleDataObjectConverter
    ) {
        $this->resource = $resource;
        $this->storeholidayFactory = $storeholidayFactory;
        $this->storeholidayCollectionFactory = $storeholidayCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->dataObjectProcessor = $dataObjectProcessor;
        $this->storeManager = $storeManager;
        $this->collectionProcessor = $collectionProcessor;
        $this->extensionAttributesJoinProcessor = $extensionAttributesJoinProcessor;
        $this->extensibleDataObjectConverter = $extensibleDataObjectConverter;
    }

    /**
     * @inheritdoc
     */
    public function save(
        \Magedelight\Storepickup\Api\Data\StoreholidayInterface $storeholiday
    ) {
        $storeholidayData = $this->extensibleDataObjectConverter->toNestedArray(
            $storeholiday,
            [],
            \Magedelight\Storepickup\Api\Data\StoreholidayInterface::class
        );

        $storeholidayModel = $this->storeholidayFactory->create()->setData($storeholidayData);

        try {
            $this->resource->save($storeholidayModel);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the storeholiday: %1',
                $exception->getMessage()
            ));
        }
        return $storeholidayModel->getDataModel();
    }

    /**
     * @inheritdoc
     */
    public function getById($storeholidayId)
    {
        $storeholiday = $this->storeholidayFactory->create();
        $this->resource->load($storeholiday, $storeholidayId);
        if (!$storeholiday->getId()) {
            throw new NoSuchEntityException(__('Storeholiday with id "%1" does not exist.', $storeholidayId));
        }
        return $storeholiday->getDataModel();
    }

    /**
     * @inheritdoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->storeholidayCollectionFactory->create();

        $this->extensionAttributesJoinProcessor->process(
            $collection,
            \Magedelight\Storepickup\Api\Data\StoreholidayInterface::class
        );

        $this->collectionProcessor->process($criteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model->getDataModel();
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritdoc
     */
    public function delete(
        \Magedelight\Storepickup\Api\Data\StoreholidayInterface $storeholiday
    ) {
        try {
            $storeholidayModel = $this->storeholidayFactory->create();
            $this->resource->load($storeholidayModel, $storeholiday->getHolidayId());
            $this->resource->delete($storeholidayModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Storeholiday: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritdoc
     */
    public function deleteById($storeholidayId)
    {
        return $this->delete($this->getById($storeholidayId));
    }
}
