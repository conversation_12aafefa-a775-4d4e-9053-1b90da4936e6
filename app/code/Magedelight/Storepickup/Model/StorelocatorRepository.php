<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model;

use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\Search\FilterGroup;
use Magento\Framework\Api\SortOrder;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\StateException;
use Magento\Framework\Exception\ValidatorException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magedelight\Storepickup\Api\StorelocatorRepositoryInterface;
use Magedelight\Storepickup\Api\Data\StorelocatorInterface;
use Magedelight\Storepickup\Model\StorelocatorFactory;
use Magedelight\Storepickup\Api\Data\StorelocatorSearchResultsInterfaceFactory;
use Magedelight\Storepickup\Model\ResourceModel\Storelocator as ResourceData;
use Magedelight\Storepickup\Model\ResourceModel\Storelocator\Collection;
use Magedelight\Storepickup\Model\ResourceModel\Storelocator\CollectionFactory as StorelocatorCollectionFactory;
use Magento\Framework\Api\ExtensibleDataObjectConverter;
use Magedelight\Storepickup\Api\Data\StorelocatorInterfaceFactory;

class StorelocatorRepository implements StorelocatorRepositoryInterface
{
    /**
     * @var array
     */
    protected $instances = [];

    /**
     * @var ResourceData
     */
    protected $resource;

    /**
     * @var StorelocatorCollectionFactory
     */
    protected $ruleCollectionFactory;

    /**
     * @var StorelocatorSearchResultsInterfaceFactory
     */
    protected $searchResultsFactory;

    /**
     * @var StorelocatorFactory
     */
    protected $storelocatorFactory;

    /**
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var StorelocatorInterfaceFactory
     */
    protected $storelocatorInterfaceFactory;

    /**
     * @var ExtensibleDataObjectConverter
     */
    protected $extensibleDataObjectConverter;

    /**
     * StorelocatorRepository constructor.
     * @param ResourceData $resource
     * @param StorelocatorCollectionFactory $ruleCollectionFactory
     * @param StorelocatorSearchResultsInterfaceFactory $ruleSearchResultsInterfaceFactory
     * @param \Magedelight\Storepickup\Model\StorelocatorFactory $storelocatorFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param ExtensibleDataObjectConverter $extensibleDataObjectConverter
     * @param StorelocatorInterfaceFactory $storelocatorInterfaceFactory
     * @param CollectionProcessorInterface|null $collectionProcessor
     */
    public function __construct(
        ResourceData $resource,
        StorelocatorCollectionFactory $ruleCollectionFactory,
        StorelocatorSearchResultsInterfaceFactory $ruleSearchResultsInterfaceFactory,
        StorelocatorFactory $storelocatorFactory,
        DataObjectHelper $dataObjectHelper,
        ExtensibleDataObjectConverter $extensibleDataObjectConverter,
        StorelocatorInterfaceFactory $storelocatorInterfaceFactory,
        CollectionProcessorInterface $collectionProcessor = null
    ) {
        $this->resource = $resource;
        $this->ruleCollectionFactory = $ruleCollectionFactory;
        $this->searchResultsFactory = $ruleSearchResultsInterfaceFactory;
        $this->storelocatorFactory = $storelocatorFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->extensibleDataObjectConverter = $extensibleDataObjectConverter;
        $this->storelocatorInterfaceFactory = $storelocatorInterfaceFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritdoc
     */
    public function save(
        \Magedelight\Storepickup\Api\Data\StorelocatorInterface $storelocator
    ) {
        $storelocatorData = $this->extensibleDataObjectConverter->toNestedArray(
            $storelocator,
            [],
            \Magedelight\Storepickup\Api\Data\StorelocatorInterface::class
        );

        if (empty($storelocatorData['url_key'])) {
            $urlKey = preg_replace('/\s+/', ' ', strtolower($storelocatorData['storename'] ?? '') ?? '');
            $urlKey = str_replace(" ", '-', $urlKey);
            $storelocatorData['url_key'] = $urlKey;
        }

        if (isset($storelocatorData['storelocator_id']) && !$storelocatorData['storelocator_id']) {
            unset($storelocatorData['storelocator_id']);
        }
        $storelocatorModel = $this->storelocatorFactory->create()->setData($storelocatorData);

        try {
            $this->resource->save($storelocatorModel);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the data: %1',
                $exception->getMessage()
            ));
        }
        return $storelocatorModel->getDataModel();
    }

    /**
     * Get data record
     *
     * @param int $storelocatorId
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function getById($storelocatorId)
    {
        if (!isset($this->instances[$storelocatorId])) {
            /** @var \Magedelight\Storepickup\Api\Data\StorelocatorInterface|\Magento\Framework\Model\AbstractModel $data */
            $data = $this->storelocatorFactory->create();
            $this->resource->load($data, $storelocatorId);
            if (!$data->getId()) {
                throw new NoSuchEntityException(__('Requested data doesn\'t exist'));
            }
            $this->instances[$storelocatorId] = $data;
        }
        return $this->instances[$storelocatorId];
    }

    /**
     * Get list
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorSearchResultsInterface
     * @throws \Magento\Framework\Exception\InputException
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        /** @var \Magedelight\Storepickup\Api\Data\StorelocatorSearchResultsInterface $searchResults */
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);

        /** @var \Magedelight\Storepickup\Model\ResourceModel\Storelocator\Collection $collection */
        $collection = $this->ruleCollectionFactory->create();

        //Add filters from root filter group to the collection
        /** @var FilterGroup $group */
        foreach ($searchCriteria->getFilterGroups() as $group) {
            $this->addFilterGroupToCollection($group, $collection);
        }
        $sortOrders = $searchCriteria->getSortOrders();
        /** @var SortOrder $sortOrder */
        if ($sortOrders) {
            foreach ($searchCriteria->getSortOrders() as $sortOrder) {
                $field = $sortOrder->getField();
                $collection->addOrder(
                    $field,
                    ($sortOrder->getDirection() == SortOrder::SORT_ASC) ? 'ASC' : 'DESC'
                );
            }
        } else {
            // set a default sorting order since this method is used constantly in many
            // different blocks
            $field = 'storelocator_id';
            $collection->addOrder($field, 'ASC');
        }
        $collection->setCurPage($searchCriteria->getCurrentPage());
        $collection->setPageSize($searchCriteria->getPageSize());
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults->setItems($collection->getItems());
    }

    /**
     * Delete
     *
     * @param mixed $data
     * @return bool
     * @throws CouldNotSaveException
     * @throws StateException
     */
    public function delete($data)
    {
        /** @var \Magedelight\Storepickup\Api\Data\StorelocatorInterface|\Magento\Framework\Model\AbstractModel $data */
        $id = $data->getId();
        try {
            unset($this->instances[$id]);
            $this->resource->delete($data);
        } catch (ValidatorException $e) {
            throw new CouldNotSaveException(__($e->getMessage()));
        } catch (\Exception $e) {
            throw new StateException(
                __('Unable to remove data %1', $id)
            );
        }
        unset($this->instances[$id]);
        return true;
    }

    /**
     * Delete by id
     *
     * @param string $storelocatorId
     * @return bool
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     * @throws StateException
     */
    public function deleteById($storelocatorId)
    {
        $data = $this->getById($storelocatorId);
        return $this->delete($data);
    }

    /**
     * Helper function that adds a FilterGroup to the collection.
     *
     * @param FilterGroup $filterGroup
     * @param Collection $collection
     * @return $this
     * @throws \Magento\Framework\Exception\InputException
     */
    protected function addFilterGroupToCollection(FilterGroup $filterGroup, Collection $collection)
    {
        $fields = [];
        $conditions = [];
        foreach ($filterGroup->getFilters() as $filter) {
            $condition = $filter->getConditionType() ? $filter->getConditionType() : 'eq';
            $fields[] = $filter->getField();
            $conditions[] = [$condition => $filter->getValue()];
        }
        if ($fields) {
            $collection->addFieldToFilter($fields, $conditions);
        }
        return $this;
    }
}
