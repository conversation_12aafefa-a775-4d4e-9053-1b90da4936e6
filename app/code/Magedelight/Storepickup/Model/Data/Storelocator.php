<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model\Data;

use Magedelight\Storepickup\Api\Data\StorelocatorInterface;
use Magento\Framework\Api\AbstractExtensibleObject;

class Storelocator extends \Magento\Framework\Model\AbstractExtensibleModel implements StorelocatorInterface
{
    /**
     * Get storelocator_id
     *
     * @return string|null
     */
    public function getStorelocatorId()
    {
        return $this->getData(self::STORELOCATOR_ID);
    }

    /**
     * Set storelocator_id
     *
     * @param string $storelocatorId
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setStorelocatorId($storelocatorId)
    {
        return $this->setData(self::STORELOCATOR_ID, $storelocatorId);
    }

    /**
     * Get storename
     *
     * @return string|null
     */
    public function getStorename()
    {
        return $this->getData(self::STORENAME);
    }

    /**
     * Set storename
     *
     * @param string $storename
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setStorename($storename)
    {
        return $this->setData(self::STORENAME, $storename);
    }

    /**
     * Get storeemail
     *
     * @return string|null
     */
    public function getStoreemail()
    {
        return $this->getData(self::STOREEMAIL);
    }

    /**
     * Set storeemail
     *
     * @param string $storeemail
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setStoreemail($storeemail)
    {
        return $this->setData(self::STOREEMAIL, $storeemail);
    }

    /**
     * Get is_active
     *
     * @return string|null
     */
    public function getIsActive()
    {
        return $this->getData(self::IS_ACTIVE);
    }

    /**
     * Set is_active
     *
     * @param string $isActive
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setIsActive($isActive)
    {
        return $this->setData(self::IS_ACTIVE, $isActive);
    }

    /**
     * Get url_key
     *
     * @return string|null
     */
    public function getUrlKey()
    {
        return $this->getData(self::URL_KEY);
    }

    /**
     * Set url_key
     *
     * @param string $urlKey
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setUrlKey($urlKey)
    {
        return $this->setData(self::URL_KEY, $urlKey);
    }

    /**
     * Get Website url
     *
     * @return string|null
     */
    public function getWebsiteUrl()
    {
        return $this->getData(self::WEBSITE_URL);
    }

    /**
     * Set Website url
     *
     * @param string $websiteUrl
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setWebsiteUrl($websiteUrl)
    {
        return $this->setData(self::WEBSITE_URL, $websiteUrl);
    }

    /**
     * Get facebook_url
     *
     * @return string|null
     */
    public function getFacebookUrl()
    {
        return $this->getData(self::FACEBOOK_URL);
    }

    /**
     * Set facebook_url
     *
     * @param string $facebookUrl
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setFacebookUrl($facebookUrl)
    {
        return $this->setData(self::FACEBOOK_URL, $facebookUrl);
    }

    /**
     * Get twitter_url
     *
     * @return string|null
     */
    public function getTwitterUrl()
    {
        return $this->getData(self::TWITTER_URL);
    }

    /**
     * Set twitter_url
     *
     * @param string $twitterUrl
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setTwitterUrl($twitterUrl)
    {
        return $this->setData(self::TWITTER_URL, $twitterUrl);
    }

    /**
     * Get description
     *
     * @return string|null
     */
    public function getDescription()
    {
        return $this->getData(self::DESCRIPTION);
    }

    /**
     * Set description
     *
     * @param string $description
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setDescription($description)
    {
        return $this->setData(self::DESCRIPTION, $description);
    }

    /**
     * Get address
     *
     * @return string|null
     */
    public function getAddress()
    {
        return $this->getData(self::ADDRESS);
    }

    /**
     * Set address
     *
     * @param string $address
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setAddress($address)
    {
        return $this->setData(self::ADDRESS, $address);
    }

    /**
     * Get country_id
     *
     * @return string|null
     */
    public function getCountryId()
    {
        return $this->getData(self::COUNTRY_ID);
    }

    /**
     * Set country_id
     *
     * @param string $countryId
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setCountryId($countryId)
    {
        return $this->setData(self::COUNTRY_ID, $countryId);
    }

    /**
     * Get region
     *
     * @return string|null
     */
    public function getRegion()
    {
        return $this->getData(self::REGION);
    }

    /**
     * Set region
     *
     * @param string $region
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setRegion($region)
    {
        return $this->setData(self::REGION, $region);
    }

    /**
     * Get region_id
     *
     * @return string|null
     */
    public function getRegionId()
    {
        return $this->getData(self::REGION_ID);
    }

    /**
     * Set region_id
     *
     * @param string $regionId
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setRegionId($regionId)
    {
        return $this->setData(self::REGION_ID, $regionId);
    }

    /**
     * Get city
     *
     * @return string|null
     */
    public function getCity()
    {
        return $this->getData(self::CITY);
    }

    /**
     * Set city
     *
     * @param string $city
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setCity($city)
    {
        return $this->setData(self::CITY, $city);
    }

    /**
     * Get zipcode
     *
     * @return string|null
     */
    public function getZipcode()
    {
        return $this->getData(self::ZIPCODE);
    }

    /**
     * Set zipcode
     *
     * @param string $zipcode
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setZipcode($zipcode)
    {
        return $this->setData(self::ZIPCODE, $zipcode);
    }

    /**
     * Get phone_frontend_status
     *
     * @return string|null
     */
    public function getPhoneFrontendStatus()
    {
        return $this->getData(self::PHONE_FRONTEND_STATUS);
    }

    /**
     * Set phone_frontend_status
     *
     * @param string $phoneFrontendStatus
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setPhoneFrontendStatus($phoneFrontendStatus)
    {
        return $this->setData(self::PHONE_FRONTEND_STATUS, $phoneFrontendStatus);
    }

    /**
     * Get telephone
     *
     * @return string|null
     */
    public function getTelephone()
    {
        return $this->getData(self::TELEPHONE);
    }

    /**
     * Set telephone
     *
     * @param string $telephone
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setTelephone($telephone)
    {
        return $this->setData(self::TELEPHONE, $telephone);
    }

    /**
     * Get longitude
     *
     * @return string|null
     */
    public function getLongitude()
    {
        return $this->getData(self::LONGITUDE);
    }

    /**
     * Set longitude
     *
     * @param string $longitude
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setLongitude($longitude)
    {
        return $this->setData(self::LONGITUDE, $longitude);
    }

    /**
     * Get latitude
     *
     * @return string|null
     */
    public function getLatitude()
    {
        return $this->getData(self::LATITUDE);
    }

    /**
     * Set latitude
     *
     * @param string $latitude
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setLatitude($latitude)
    {
        return $this->setData(self::LATITUDE, $latitude);
    }

    /**
     * Get storeimage
     *
     * @return string|null
     */
    public function getStoreimage()
    {
        return $this->getData(self::STOREIMAGE);
    }

    /**
     * Set storeimage
     *
     * @param string $storeimage
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setStoreimage($storeimage)
    {
        return $this->setData(self::STOREIMAGE, $storeimage);
    }

    /**
     * Get meta_title
     *
     * @return string|null
     */
    public function getMetaTitle()
    {
        return $this->getData(self::META_TITLE);
    }

    /**
     * Set meta_title
     *
     * @param string $metaTitle
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setMetaTitle($metaTitle)
    {
        return $this->setData(self::META_TITLE, $metaTitle);
    }

    /**
     * Get meta_keywords
     *
     * @return string|null
     */
    public function getMetaKeywords()
    {
        return $this->getData(self::META_KEYWORDS);
    }

    /**
     * Set meta_keywords
     *
     * @param string $metaKeywords
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setMetaKeywords($metaKeywords)
    {
        return $this->setData(self::META_KEYWORDS, $metaKeywords);
    }

    /**
     * Get meta_description
     *
     * @return string|null
     */
    public function getMetaDescription()
    {
        return $this->getData(self::META_DESCRIPTION);
    }

    /**
     * Set meta_description
     *
     * @param string $metaDescription
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setMetaDescription($metaDescription)
    {
        return $this->setData(self::META_DESCRIPTION, $metaDescription);
    }

    /**
     * Get storetime
     *
     * @return string[]
     */
    public function getStoretime()
    {
        return $this->getData(self::STORETIME);
    }

    /**
     * Set storetime
     *
     * @param string[] $storetime
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setStoretime($storetime)
    {
        return $this->setData(self::STORETIME, $storetime);
    }

    /**
     * @inheritdoc
     */
    public function getConditions()
    {
        return $this->getData(self::KEY_CONDITION);
    }

    /**
     * @inheritdoc
     */
    public function setConditions($conditions)
    {
        return $this->setData(self::KEY_CONDITION, $conditions);
    }

    /**
     * Get is_pickup_location
     *
     * @return boolean
     */
    public function getIsPickupLocation()
    {
        return $this->getData(self::IS_PICKUPLOCATION);
    }

    /**
     * Set is_pickup_location
     *
     * @param boolean $isPickupLocation
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setIsPickupLocation($isPickupLocation)
    {
        return $this->setData(self::IS_PICKUPLOCATION, $isPickupLocation);
    }

     /**
     * Get MSI Source.
     *
     * @return string
     */
    public function getMsiSource()
    {
        return $this->getData(self::MSI_SOURCE);
    }

    /**
     * Set MSI Source
     *
     * @param string $source
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface
     */
    public function setMsiSource($source)
    {
        return $this->setData(self::MSI_SOURCE, $source);
    }
}
