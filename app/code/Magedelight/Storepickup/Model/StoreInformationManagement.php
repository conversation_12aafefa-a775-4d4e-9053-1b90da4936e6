<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model;

use Magedelight\Storepickup\Model\Source\Country;
use Magedelight\Storepickup\Model\Source\Region;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\Storepickup\Api\StorelocatorRepositoryInterface;
use Magedelight\Storepickup\Api\Data\StorelocatorInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magedelight\Storepickup\Helper\Storelocator as storeHelper;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Checkout\Model\Cart;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\State;
use Magento\Backend\Model\Session\Quote as BackendModelSession;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Module\Manager as ModuleManager;

class StoreInformationManagement implements \Magedelight\Storepickup\Api\StoreInformationManagementInterface
{
    public const XML_PATH_EMAIL_RECIPIENT = 'contact/email/recipient_email';
    /**
     * @var StorelocatorFactory
     */
    protected $storelocatorFactory;

    /**
     * @var $storeManager
     */
    protected $storeManager;

    /**
     * @var StorelocatorRepository
     */
    protected $storelocatorRepository;

    /**
     * @var Country
     */
    protected $countryOptions;

    /**
     * @var Region
     */
    protected $regionOptions;

    /**
     * @var StorelocatorInterfaceFactory
     */
    protected $storelocatorDataFactory;

    /**
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @var StoreholidayFactory
     */
    protected $storeholidayFactory;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var Json
     */
    protected $serialize;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * @var Cart
     */
    protected $cart;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var storeHelper
     */
    protected $storeHelper;
    /**
     * @var ResourceModel\Storelocator\CollectionFactory
     */
    protected $storelocatorCollection;

    /**
     * @var ResourceModel\Storeholiday\CollectionFactory
     */
    protected $storeHolidayCollection;

    /**
     * @var \Magento\Directory\Model\Region
     */
    protected $region;

    /**
     * @var \Magento\Directory\Model\ResourceModel\Region
     */
    protected $regionResource;

    /**
     * @var ModuleManager
     */
    private $moduleManager;

    /**
     * @var State
     */
    protected $state;

     /**
     * @var BackendModelSession
     */
    protected $backendModelSession;

    /**
     * @var \Magento\Catalog\Model\Product
     */
    protected $product;

    /**
     * StoreInformationManagement constructor.
     * @param StorelocatorFactory $storelocatorFactory
     * @param ResourceModel\Storelocator\CollectionFactory $storelocatorCollection
     * @param StoreManagerInterface $storeManager
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     * @param Country $countryOptions
     * @param Region $regionOptions
     * @param StorelocatorInterfaceFactory $storelocatorDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param StoreholidayFactory $storeholidayFactory
     * @param ResourceModel\Storeholiday\CollectionFactory $storeHolidayCollection
     * @param ScopeConfigInterface $scopeConfig
     * @param Json $serialize
     * @param DateTime $dateTime
     * @param \Magento\Checkout\Model\Cart $cart
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param storeHelper $storeHelper
     * @param \Magento\Directory\Model\Region $region
     * @param \Magento\Directory\Model\ResourceModel\Region $regionResource
     */
    public function __construct(
        StorelocatorFactory $storelocatorFactory,
        \Magedelight\Storepickup\Model\ResourceModel\Storelocator\CollectionFactory $storelocatorCollection,
        StoreManagerInterface $storeManager,
        StorelocatorRepositoryInterface $storelocatorRepository,
        Country $countryOptions,
        Region $regionOptions,
        StorelocatorInterfaceFactory $storelocatorDataFactory,
        DataObjectHelper $dataObjectHelper,
        StoreholidayFactory $storeholidayFactory,
        \Magedelight\Storepickup\Model\ResourceModel\Storeholiday\CollectionFactory $storeHolidayCollection,
        ScopeConfigInterface $scopeConfig,
        Json $serialize,
        DateTime $dateTime,
        \Magento\Checkout\Model\Cart $cart,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        storeHelper $storeHelper,
        \Magento\Directory\Model\RegionFactory $region,
        \Magento\Directory\Model\ResourceModel\Region $regionResource,
        State $state,
        BackendModelSession $backendModelSession,
        \Magento\Catalog\Model\Product $product,
        ModuleManager $moduleManager
    ) {
        $this->storelocatorFactory = $storelocatorFactory;
        $this->storelocatorCollection=$storelocatorCollection;
        $this->storeManager = $storeManager;
        $this->storelocatorRepository = $storelocatorRepository;
        $this->countryOptions = $countryOptions;
        $this->regionOptions = $regionOptions;
        $this->storelocatorDataFactory = $storelocatorDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->storeholidayFactory = $storeholidayFactory;
        $this->storeHolidayCollection=$storeHolidayCollection;
        $this->scopeConfig = $scopeConfig;
        $this->serialize = $serialize;
        $this->dateTime = $dateTime;
        $this->cart = $cart;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->storeHelper = $storeHelper;
        $this->region=$region;
        $this->regionResource=$regionResource;
        $this->backendModelSession = $backendModelSession;
        $this->state = $state;
        $this->product = $product;
        $this->moduleManager = $moduleManager;
        
    }

    /**
     * Get Store information
     *
     * @return array|StorelocatorRepositoryInterface[]
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStoreInformation()
    {
        $storeDetails = $this->storelocatorCollection->create()
            ->addFieldToFilter('is_active', 1)
            ->addStoreFilter($this->storeManager->getStore()->getId());

        $storeDataArray = [];

        if ($this->storeHelper->isCheckProductOnCheckout()) {
            $quoteItems = $this->getProductFromQuote();
            $itemId = [];
            $msiSourceCode = [];

            foreach ($quoteItems as $item) {
                //$itemId[] = $item->getProductId();
                if($this->isMsiEnabled()){
                    $sourceItems = $this->getSourcesItems($item->getSku());
                    foreach ($sourceItems as $sourceItem) {
                        $msiSourceCode[] = $sourceItem->getData('source_code');
                    }
                }
            }
            if(!empty($msiSourceCode)){
                $msiSourceCode = array_unique($msiSourceCode);
            }

            foreach ($storeDetails as $store) {

                $msiSource = $store->getData('msi_source');
                $isPickupLocation = $store->getData('is_pickup_location');

                if ($msiSource !== '') {
                    if (!$isPickupLocation || !in_array($msiSource, $msiSourceCode)) {
                        continue;
                    }
                }


                //echo $this->checkRuleActions($store);

                if(!$this->checkRuleActions($store)){
                    continue;
                }
                
                /* @var $store \Magedelight\Storepickup\Model\Storelocator */
                $allProducts = $store->getAllValidateProductIds();
                $countProductIds = count(array_intersect($itemId, $allProducts));
                $itemCount = count($itemId);
                if ($countProductIds == $itemCount) {
                    $storeDataArray[] = $store->getData();
                }
            }
        } else {
            foreach ($storeDetails as $storeData) {
                $storeDataArray[] = $storeData->getData();
            }
        }

        return $storeDataArray;
    }

    /**
     * @param  $rule
     *
     * @return boolean
     */
    public function checkRuleActions($store)
    {
        $validate = true;
        $quoteItems = $this->getProductFromQuote();
        foreach ($quoteItems as $item) {
            $product = $this->product->load($item->getProductId());
            if ($store->getConditions()->validate($product)) {
                continue;
            }else{
                return false;
            }
        }
        return $validate;
    }

    /**
     * Get product quote
     *
     * @return \Magento\Quote\Model\Quote\Item[]
     */
    public function getProductFromQuote()
    {
        if($this->state->getAreaCode() == 'adminhtml'){
            return $this->backendModelSession->getQuote()->getAllItems();
        }else{
            return $this->cart->getQuote()->getAllItems();
        }
    }

    /**
     * @inheritdoc
     */
    public function getStoreInformationById($storeloctorId)
    {
        /** @var \Magedelight\Storepickup\Api\Data\StorelocatorInterface $storelocatorCollection */
        $storelocatorCollection = $this->storelocatorCollection->create()
            ->addFieldToFilter('is_active', 1, 'eq')
            ->addFieldToFilter('storelocator_id', $storeloctorId)
            ->addStoreFilter($this->storeManager->getStore()->getId());

        $storeInfo = $storelocatorCollection->getData();

        if ($storeInfo) {
            $countryname = $this->getCountryName($storeInfo[0]['country_id']);
            $storeInfo[0]['countryname'] = $countryname;

            $storeInfo[0]['statename'] ='';
            if ($storeInfo[0]["region"]===null) {
                if (isset($storeInfo[0]["region_id"]) && $storeInfo[0]["region_id"] != 0) {
                    $storeInfo[0]['statename'] = $this->getRegionName($storeInfo[0]["region_id"]);
                    $storeInfo[0]['region_code'] = $this->getRegionCode($storeInfo[0]["region_id"]);
                }
            } else {
                $storeInfo[0]['statename'] = $storeInfo[0]['region'];
            }

            $storeInfo[0]['address'] = str_replace('\n', ', ', $storeInfo[0]['address']);

            if ($storeInfo[0]['telephone']) {
                $storeInfo[0]['telephone'] = explode(':', $storeInfo[0]['telephone']);
            }
        }

        return $storeInfo;
    }

    /**
     * @inheritdoc
     */
    public function getStoreHolidayInformation($storeloctorId)
    {
        $holidayModel = $this->storeholidayFactory->create();
        $holildayCollection = $this->storeHolidayCollection->create();
        $holildayCollection->addFieldToFilter('is_active', '1');
        $_holildayCollection = $this->getStoreFilter($holildayCollection, $storeloctorId);

        $holidayData['dates'] = $this->getStoreHolidays($_holildayCollection);
        $holidayData['days'] = $this->getStoreOffdays($storeloctorId);
        $holidayData['details'] = $this->getStoreFilter($holildayCollection, $storeloctorId);

        $holidayObj[] = $holidayData;

        return $holidayObj;
    }

    /**
     * @inheritdoc
     */
    public function getStoreTimeInformation($dateVal, $storeVal)
    {
        $storeDate = $dateVal;
        $storeID = $storeVal;

        $timeStamp = $this->dateTime->gmtTimestamp($storeDate);
        $selectedday = $this->dateTime->date('l', $timeStamp);
        $timeSloats = [];

        $_timeInterval = $this->storeHelper->isTimeInterval();

        $storelocatorCollection = $this->storelocatorCollection->create();
        $storelocatorCollection->addFieldToFilter('storelocator_id', $storeID);
        $storelocatorCollection->addFieldToSelect('storetime')
            ->addStoreFilter($this->storeManager->getStore()->getId());

        $storelocatorData = $storelocatorCollection->getData();
        if (!empty($storelocatorData[0]['storetime'])) {
            $storetime = $this->serialize->unserialize($storelocatorData[0]['storetime']);
        } else {
            $daysIndexs = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            foreach ($daysIndexs as $daysIndex) {
                $storetime[] = [
                    'days' => $daysIndex,
                    'day_status' => 1,
                    'open_hour' => 0,
                    'open_minute' => 0,
                    'close_hour' => 23,
                    'close_minute' => 0
                ];
            }
        }

        if (!empty($storetime)) {
            foreach ($storetime as $key => $day) {
                if ($day['days'] == $selectedday) {
                    $timeSloats[$key] = $this->getTimeSloats($storeDate, $day, $_timeInterval);
                }
            }
        }
        if ($timeSloats) {
            // phpcs:disable
            return call_user_func_array("array_merge", $timeSloats);
        }
    }

    /**
     * Get country name
     *
     * @param string $countryval
     * @return string
     */
    public function getCountryName($countryval)
    {
        $countryArray = $this->countryOptions->getOptions();
        return $countryArray[$countryval];
    }

    /**
     * Get region name
     *
     * @param mixed $region_id
     * @return string
     */
    public function getRegionName($region_id)
    {
        $regionArray = $this->regionOptions->getOptions();
        return $regionArray[$region_id];
    }

    /**
     * Get code
     *
     * @param mixed $region_id
     * @return string
     */
    public function getRegionCode($region_id)
    {
        $region=$this->region->create();
        $this->regionResource->load($region, $region_id);
        return $region->getCode();
    }

    /**
     * Get store filter
     *
     * @param mixed $holildayCollection
     * @param mixed $storeloctorId
     * @return mixed
     */
    public function getStoreFilter($holildayCollection, $storeloctorId)
    {
        $_holildayCollection = $holildayCollection->getData();
        /* holiday_applied_stores || 0 */
        $elementkey = 0;
        $i=0;
        foreach ($holildayCollection as $collection) {
            $appliedStore = $collection->getHolidayAppliedStores();
            $appliedStore = explode(',', $appliedStore);

            if (in_array('0', $appliedStore, true) || in_array($storeloctorId, $appliedStore, true)) {
                $i++;
            } else {
                unset($_holildayCollection[$elementkey]);
            }
            $elementkey++;
        }
        return $_holildayCollection;
    }

    /**
     * Get store holiday
     *
     * @param mixed $_holildayCollection
     * @return array|bool
     */
    public function getStoreHolidays($_holildayCollection)
    {
        $holidayDays = [];
        $holidayDays['repetitive'] = [];
        $holidayDays['normal'] = [];
        $holidayDays['shipping_off_day'] = [];

        foreach ($_holildayCollection as $key => $store) {
            $fromDate = date_create($store['holiday_date_from']);
            $toDate = date_create($store['holiday_date_to']);
            $diff = date_diff($fromDate, $toDate);

            if ($diff->format("%a") == 0) {

                $date = date("m-d-Y", strtotime($store['holiday_date_from'] . "+0 days"));
                $holidayDays['normal'][] = $date;

                if ($store['is_repetitive'] == 1) {
                    $repetitiveDate = $holidayDays['normal'];
                    foreach ($repetitiveDate as &$value) {
                        $tmpDate = explode('-', $value);
                        $value = $tmpDate[2].'-'.$tmpDate[0].'-'.$tmpDate[1];
                        $holidayDays['repetitive'][] = date('n-j', strtotime($value.' +1 year'));
                    }
                }

            } elseif ($diff->format("%a") > 0) {
                $diffpart=$diff->format("%a");
                for ($i = 0; $i <= $diffpart; $i++) {
                    $date = date("m-d-Y", strtotime($store['holiday_date_from'] . "+" . $i . "days"));
                    $holidayDays['normal'][] = $date;
                }

                if ($store['is_repetitive'] == 1) {
                    $repetitiveDate = $holidayDays['normal'];
                    foreach ($repetitiveDate as &$value) {
                        $tmpDate = explode('-', $value);
                        $value = $tmpDate[2].'-'.$tmpDate[0].'-'.$tmpDate[1];
                        $holidayDays['repetitive'][] = date('n-j', strtotime($value.' +1 year'));
                    }
                }
            }
        }
        /* Shipping Off Days  */
        $offDays = $this->storeHelper->getPickupOffDays();
        if ($offDays >= 1) {
            for ($i=0; $i < $offDays; $i++) {
                $date = date("m-d-Y", strtotime(date("Y/m/d") . "+" . $i . "days"));
                $holidayDays['shipping_off_day'][] = $date;
            }
        }

        if (empty($holidayDays)) {
            return true;
        }
        return $holidayDays;
    }

    /**
     * Get store days
     *
     * @param int $storeloctorId
     * @return array|bool
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStoreOffdays($storeloctorId)
    {
        $storeCollection = $this->storelocatorCollection->create();
        $storeCollection->addFieldToFilter('storelocator_id', $storeloctorId);
        $storeCollection->addFieldToSelect('storetime')
            ->addStoreFilter($this->storeManager->getStore()->getId());
        $storeData = $storeCollection->getData();
        if (!empty($storeData[0]['storetime'])) {
            $storetime = $this->serialize->unserialize($storeData[0]['storetime']);
        }

        $daysIndex = [
            'Sunday' => 0,
            'Monday' => 1,
            'Tuesday' => 2,
            'Wednesday' => 3,
            'Thursday' => 4,
            'Friday' => 5,
            'Saturday' => 6
        ];

        if (!empty($storetime)) {
            foreach ($storetime as $day) {
                if ($day['day_status'] == '0') {
                    $days[] = $daysIndex[$day['days']];
                }
            }
        }

        if (empty($days)) {
            return true;
        }
        return $days;
    }

    /**
     * Get time sloat
     *
     * @param Date $storeDate
     * @param mixed $day
     * @param mixed $_timeInterval
     * @return bool
     * @throws \Exception
     */
    public function getTimeSloats($storeDate, $day, $_timeInterval)
    {
        $tmpstoreDate = $storeDate;
        $storeDate = explode("-", $storeDate);

        $d = mktime($day['open_hour'], $day['open_minute'], 00, $storeDate[1], $storeDate[2], $storeDate[0]);
        $stTime = date("Y-m-d h:i:sa", $d);

        $storeDate = $tmpstoreDate;
        if ($day['close_hour'] == 00) {
            $date = $storeDate;
            $date1 = str_replace('-', '/', $date);
            $storeDate = date('Y-m-d', strtotime($date1 . "+1 days"));
        }

        $storeDate = explode("-", $storeDate);
        $d = mktime($day['close_hour'], $day['close_minute'], 00, $storeDate[1], $storeDate[2], $storeDate[0]);
        $enTime = date("Y-m-d h:i:sa", $d);

        $duration = $_timeInterval;
        $break = 0;

        $start = new \DateTime($stTime);
        $end = new \DateTime($enTime);
        $interval = new \DateInterval("PT" . $duration . "M");
        $breakInterval = new \DateInterval("PT" . $break . "M");

        for ($intStart = $start; $intStart < $end; $intStart->add($interval)->add($breakInterval)) {
            $endPeriod = clone $intStart;
            $endPeriod->add($interval);
            if ($endPeriod > $end) {
                $endPeriod = $end;
            }
            $periods[] = $intStart->format('H:i');
        }

        if (empty($periods)) {
            return true;
        }
        return $periods;
    }

    /**
     * Get items
     *
     * @return \Magedelight\Storepickup\Api\Data\StorelocatorInterface[]
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getItems()
    {
        $searchCriteria = $this->searchCriteriaBuilder->create();
        $searchResult = $this->storelocatorRepository->getList($searchCriteria);
        return $searchResult->getItems();
    }

     /**
     * Get Source items
     *
     * @param string $sku
     * @return mixed
     */
    public function getSourcesItems($sku)
    {
        $searchCriteria = $this->searchCriteriaBuilder->addFilter('sku', $sku)->create();
        $sourceItemRepository = ObjectManager::getInstance()->get('\Magento\InventoryApi\Api\SourceItemRepositoryInterface');
        $sourceItemData = $sourceItemRepository->getList($searchCriteria);
        return $sourceItemData->getItems();
    }

    public function isMsiEnabled()
    {
        return $this->moduleManager->isEnabled('Magento_Inventory');
    }
}
