<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model;

use Magedelight\Storepickup\Helper\Storelocator as storeHelper;
use Magedelight\Storepickup\Model\ResourceModel\Storelocator\CollectionFactory;
use Magento\Checkout\Model\ConfigProviderInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Module\Manager as ModuleManager;

class StorepickupConfigProvider implements ConfigProviderInterface
{
    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfiguration;

    /**
     * @var $collectionFactory
     */
    protected $collectionFactory;

    /**
     * @var storeHelper
     */
    protected $storeHelper;

    /**
     * @var ModuleManager
     */
    protected $moduleManager;

    /**
     * StorepickupConfigProvider constructor.
     * @param ScopeConfigInterface $scopeConfiguration
     * @param CollectionFactory $collectionFactory
     * @param storeHelper $storeHelper
     * @param ModuleManager $moduleManager
     */
    public function __construct(
        ScopeConfigInterface $scopeConfiguration,
        CollectionFactory $collectionFactory,
        storeHelper $storeHelper,
        ModuleManager $moduleManager
    ) {
        $this->scopeConfiguration = $scopeConfiguration;
        $this->collectionFactory = $collectionFactory;
        $this->storeHelper = $storeHelper;
        $this->moduleManager = $moduleManager;
    }

    /**
     * @inheritdoc
     */
    public function getConfig()
    {
        $config = [
            'show_hide_store_container' => $this->storeHelper->isStorePickupMethodActive(),
            'IsPickupDateEnabel' => $this->storeHelper->isStoreDateEnable(),
            'isTimeslotenable' => $this->storeHelper->isStoreTimeEnable(),
            'isMdOscEnabled' => $this->moduleManager->isEnabled('Magedelight_OneStepCheckout'),
            'isModuleEnable' => $this->storeHelper->isModuleEnable(),
            'pickupLayout' => $this->storeHelper->getPickupLayout()
        ];

        return $config;
    }
}
