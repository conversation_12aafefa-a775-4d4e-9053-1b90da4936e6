<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model\Storelocator;

use Magento\Framework\App\Request\DataPersistorInterface;
use Magedelight\Storepickup\Model\ResourceModel\Storelocator\CollectionFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Module\ModuleListInterface;
use Magento\Framework\ObjectManagerInterface;
use Magedelight\Storelocator\Model\StorelocatorImages;

class DataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{

    /**
     * @var Data
     */
    protected $loadedData;

    /**
     * @var ObjectManagerInterface
     */
    private $objectManager;
    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var \Magedelight\Storepickup\Model\ResourceModel\Storelocator\Collection
     */
    protected $collection;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;
    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * @var ModuleListInterface
     */
    protected $moduleList;

    /**
     * @var StorelocatorImages
     */
    protected $storelocatorImages;

    /**
     * Constructor
     *
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param StoreManagerInterface $storeManager
     * @param SerializerInterface $serializer
     * @param ModuleListInterface $moduleList
     * @param ObjectManagerInterface $objectManager
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        DataPersistorInterface $dataPersistor,
        StoreManagerInterface $storeManager,
        SerializerInterface $serializer,
        ModuleListInterface $moduleList,
        ObjectManagerInterface $objectManager,
        StorelocatorImages $storelocatorImages,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        $this->storeManager = $storeManager;
        $this->serializer = $serializer;
        $this->objectManager = $objectManager;
        $this->moduleList = $moduleList;
        $this->storelocatorImages = $storelocatorImages;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * Get data
     *
     * @return Data
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getData()
    {
        $baseurl =  $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }
        $items = $this->collection->getItems();
        $telephoneArray = [];
        foreach ($items as $model) {
            $_data = $model->getData();

            $_data['images'] = $this->storelocatorImages->getImages($model->getId());
            /*** Get image code ***/
            if ($_data['storeimage']):
                $img = [];
                $img[0]['name'] = $_data['storeimage'];
                $img[0]['url'] = $baseurl.\Magedelight\Storepickup\Model\Uploader::IMAGE_PATH.$_data['storeimage'];
                $_data['storeimage'] = $img;
            endif;
            if ($_data['address']):
                $add = explode('\n', $_data['address']);
                $_data['address'] = $add;
            endif;
            if (isset($_data['telephone'])) {
                if ($_data['telephone']) {
                    $telephone = explode(':', $_data['telephone']);
                    foreach ($telephone as $key => $tele) {
                        $telephoneArray[$key]['telephone'] = $tele;
                    }
                }
            }
            $_data['telephone'] = $telephoneArray;
            if ($_data['storetime']):
                $storetime = $storetime = $this->serializer->unserialize($_data['storetime']);
                $_data['storetime'] = $storetime;
            endif;
            $model->setData($_data);
            $this->loadedData[$model->getId()] = $model->getData();
        }
        $data = $this->dataPersistor->get('Magedelight_Storepickup_store');

        if (!empty($data)) {
            $model = $this->collection->getNewEmptyItem();
            $model->setData($data);
            $this->loadedData[$model->getId()] = $model->getData();
            $this->dataPersistor->clear('Magedelight_Storepickup_store');
        }

        return $this->loadedData;
    }

    /**
     * Check if Another_Module is enabled
     *
     * @return bool
     */
    private function isAnotherModuleEnabled()
    {
        $moduleInfo = $this->moduleList->getOne('Magedelight_Storelocator');
        return isset($moduleInfo['setup_version']) && $moduleInfo['setup_version'];
    }

    /**
     * Get meta.
     *
     * @return array
     * @throws LocalizedException
     */
    public function getMeta()
    {
        $meta = parent::getMeta();
        if ($this->isAnotherModuleEnabled()) {
            $storelocatorDataProvider = $this->objectManager
                ->create(\Magedelight\Storelocator\Model\Storelocator\DataProvider::class);
            $storelocatorData = $storelocatorDataProvider->getMeta();
            $meta = $storelocatorData;
        }
        return $meta;
    }
}
