<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model\Storelocator\Source;

use Magento\Framework\Data\OptionSourceInterface;

class Days implements OptionSourceInterface
{

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            ['label' => __('Monday'), 'value' => 'Monday'],
            ['label' => __('Tuesday'), 'value' => 'Tuesday'],
            ['label' => __('Wednesday'), 'value' => 'Wednesday'],
            ['label' => __('Thursday'), 'value' => 'Thursday'],
            ['label' => __('Friday'), 'value' => 'Friday'],
            ['label' => __('Saturday'), 'value' => 'Saturday'],
            ['label' => __('Sunday'), 'value' => 'Sunday']
        ];
    }
}
