<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model\Storelocator\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Magento\Inventory\Model\ResourceModel\Source\CollectionFactory;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Module\Manager as ModuleManager;

class MSIStore implements OptionSourceInterface
{

     /**
     * @var ModuleManager
     */
    private $moduleManager;

    /**
     * @param ModuleManager $moduleManager
     */
    public function __construct(
        ModuleManager $moduleManager
    ) {
        $this->moduleManager = $moduleManager;
    }

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        $options[] = ['label' => __('Please Select'), 'value' => ''];

        if($this->isMsiEnabled()){
            $sourceCollection = ObjectManager::getInstance()->get('\Magento\Inventory\Model\ResourceModel\Source\CollectionFactory')->create();
            if ($sourceCollection) {
                foreach ($sourceCollection as $source) {
                    $options[] = ['label' => $source->getName(), 'value' => $source->getSourceCode()];
                }
            }
        }
        
        return $options;
    }

    /**
     * Is MsiEnabled
     *
     * @return boolean
     */
    public function isMsiEnabled()
    {
        return $this->moduleManager->isEnabled('Magento_Inventory');
    }
}
