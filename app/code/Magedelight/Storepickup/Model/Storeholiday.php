<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model;

use Magedelight\Storepickup\Api\Data\StoreholidayInterface;
use Magedelight\Storepickup\Api\Data\StoreholidayInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;

class Storeholiday extends \Magento\Framework\Model\AbstractModel
{

    /**
     * @var StoreholidayInterfaceFactory
     */
    protected $storeholidayDataFactory;

    /**
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @var string
     */
    protected $_eventPrefix = 'magedelight_storelocator_storeholiday';

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param StoreholidayInterfaceFactory $storeholidayDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param \Magedelight\Storepickup\Model\ResourceModel\Storeholiday $resource
     * @param \Magedelight\Storepickup\Model\ResourceModel\Storeholiday\Collection $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        StoreholidayInterfaceFactory $storeholidayDataFactory,
        DataObjectHelper $dataObjectHelper,
        \Magedelight\Storepickup\Model\ResourceModel\Storeholiday $resource,
        \Magedelight\Storepickup\Model\ResourceModel\Storeholiday\Collection $resourceCollection,
        array $data = []
    ) {
        $this->storeholidayDataFactory = $storeholidayDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Retrieve storeholiday model with storeholiday data
     *
     * @return StoreholidayInterface
     */
    public function getDataModel()
    {
        $storeholidayData = $this->getData();

        $storeholidayDataObject = $this->storeholidayDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $storeholidayDataObject,
            $storeholidayData,
            StoreholidayInterface::class
        );

        return $storeholidayDataObject;
    }
}
