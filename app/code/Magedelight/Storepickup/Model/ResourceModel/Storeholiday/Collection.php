<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model\ResourceModel\Storeholiday;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'holiday_id';

    /**
     * @var string
     */
    protected $_eventPrefix = 'magedelight_store_holiday_collection';

    /**
     * @var string
     */
    protected $_eventObject = 'store_holiday_collection';

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            \Magedelight\Storepickup\Model\Storeholiday::class,
            \Magedelight\Storepickup\Model\ResourceModel\Storeholiday::class
        );
    }
}
