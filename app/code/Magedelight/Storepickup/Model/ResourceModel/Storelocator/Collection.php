<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model\ResourceModel\Storelocator;

use Magento\Framework\DB\Select;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Magento\Framework\Data\Collection\Db\FetchStrategyInterface;
use Magento\Framework\Data\Collection\EntityFactoryInterface;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Magedelight\Storepickup\Model\Storelocator;
use Magedelight\Storepickup\Model\ResourceModel\Storelocator as StorelocatorResourceModel;

class Collection extends AbstractCollection
{

    /**
     * @var string
     */
    protected $_idFieldName = 'storelocator_id';
    /**
     * @var string
     */
    protected $_eventPrefix = 'magedelight_storelocator_collection';
    /**
     * @var string
     */
    protected $_eventObject = 'storelocator_collection';
    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;
    /**
     * @var array
     */
    protected $_joinedFields = [];

    /**
     * constructor
     *
     * @param EntityFactoryInterface $entityFactory
     * @param LoggerInterface $logger
     * @param FetchStrategyInterface $fetchStrategy
     * @param ManagerInterface $eventManager
     * @param StoreManagerInterface $storeManager
     * @param mixed $connection
     * @param AbstractDb $resource
     */
    public function __construct(
        EntityFactoryInterface $entityFactory,
        LoggerInterface $logger,
        FetchStrategyInterface $fetchStrategy,
        ManagerInterface $eventManager,
        StoreManagerInterface $storeManager,
        $connection = null,
        AbstractDb $resource = null
    ) {
        $this->storeManager = $storeManager;
        parent::__construct($entityFactory, $logger, $fetchStrategy, $eventManager, $connection, $resource);
    }

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(Storelocator::class, StorelocatorResourceModel::class);
        $this->_map['fields']['storelocator_id'] = 'main_table.storelocator_id';
        $this->_map['fields']['store_ids'] = 'store_table.store_ids';
    }

    /**
     * After collection load
     *
     * @return $this
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function _afterLoad()
    {
        $this->performAfterLoad('magedelight_storelocator_store', 'storelocator_id');
        return parent::_afterLoad();
    }

    /**
     * Add filter
     *
     * @param array|string $field
     * @param mixed $condition
     * @return AbstractCollection
     */
    public function addFieldToFilter($field, $condition = null)
    {
        if ($field === 'store_ids') {
            return $this->addStoreFilter($condition, false);
        }
        return parent::addFieldToFilter($field, $condition);
    }

    /**
     * Add store filter
     *
     * @param mixed $store
     * @param bool $withAdmin
     * @return $this
     */
    public function addStoreFilter($store, $withAdmin = true)
    {
        if (!$this->getFlag('store_filter_added')) {
            if ($store instanceof Store) {
                $store = [$store->getId()];
            }
            if (!is_array($store)) {
                $store = [$store];
            }
            if ($withAdmin) {
                $store[] = Store::DEFAULT_STORE_ID;
            }
            $this->addFilter('store_ids', ['in' => $store], 'public');
        }
        return $this;
    }

    /**
     * Join store relation table if there is store filter
     *
     * @return void
     * @SuppressWarnings(PHPMD.Ecg.Sql.SlowQuery)
     */
    protected function _renderFiltersBefore()
    {
        if ($this->getFilter('store_ids')) {
            $this->getSelect()->join(
                ['store_table' => $this->getTable('magedelight_storelocator_store')],
                'main_table.storelocator_id = store_table.storelocator_id',
                []
            )->group('main_table.storelocator_id');

        }
        parent::_renderFiltersBefore();
    }

    /**
     * Get select
     *
     * @return Select
     */
    public function getSelectCountSql()
    {
        $countSelect = parent::getSelectCountSql();
        $countSelect->reset(Select::GROUP);
        return $countSelect;
    }

    /**
     * Perform After load
     *
     * @param mixed $tableName
     * @param mixed $linkField
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function performAfterLoad($tableName, $linkField)
    {
        $linkedIds = $this->getColumnValues($linkField);
        if (count($linkedIds)) {
            $connection = $this->getConnection();
            $select = $connection->select()->from(['magedelight_storelocator_store' => $this->getTable($tableName)])
                ->where('magedelight_storelocator_store.' . $linkField . ' IN (?)', $linkedIds);
            $result = $connection->fetchAll($select);
            if ($result) {
                $storesData = [];
                foreach ($result as $storeData) {
                    $storesData[$storeData[$linkField]][] = $storeData['store_ids'];
                }

                foreach ($this as $item) {
                    $linkedId = $item->getData($linkField);
                    if (!isset($storesData[$linkedId])) {
                        continue;
                    }
                    $storeIdKey = array_search(Store::DEFAULT_STORE_ID, $storesData[$linkedId], true);
                    if ($storeIdKey !== false) {
                        $stores = $this->storeManager->getStores(false, true);
                        $storeId = current($stores)->getId();
                        $storeCode = key($stores);
                    } else {
                        $storeId = current($storesData[$linkedId]);
                        $storeCode = $this->storeManager->getStore($storeId)->getCode();
                    }
                    $item->setData('_first_store_id', $storeId);
                    $item->setData('store_code', $storeCode);
                    $item->setData('store_ids', $storesData[$linkedId]);
                }
            }
        }
    }

    /**
     * Filter collection to only active rules.
     *
     * @param Product $product
     * @param string|null $now
     * @param int $storeId
     * @return \Magedelight\Storepickup\Model\ResourceModel\Storelocator\Collection
     */
    public function setValidationFilter($product, $now = null, $storeId = null)
    {
        if (!$this->getFlag('storelocator_rule_validation_filter')) {
            $this->statusFilter();
            if ($product->getTypeId() == 'configurable') {
                $this->isForParentFilter();
            }
            $this->storeFilter($storeId);
            $this->dateFilter($now);
            $this->setOrder('priority', self::SORT_ORDER_ASC);
            $this->setFlag('storelocator_rule_validation_filter', true);
        }
        return $this;
    }

    /**
     * From date or to date filter
     *
     * @param Date $now
     * @return $this
     */
    public function dateFilter($now)
    {
        $this->getSelect()->where(
            'start_date is null or start_date <= ?',
            $now
        )->where(
            'end_date is null or end_date >= ?',
            $now
        );

        return $this;
    }

    /**
     * From status filter
     *
     * @return $this
     */
    public function statusFilter()
    {
        $this->addFieldToFilter('status', 1);
        return $this;
    }

    /**
     * From store filter
     *
     * @param int $storeId
     * @return $this
     */
    public function storeFilter($storeId)
    {

        $this->addFieldToFilter(
            ['store_ids', 'store_ids'],
            [
                ['finset' => $storeId],
                ['finset' => 0]
            ]
        );
        return $this;
    }
}
