<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;

class Timeinterval implements OptionSourceInterface
{

    /**
     * To Option array
     *
     * @param bool $isActiveOnlyFlag
     * @return array
     */
    public function toOptionArray($isActiveOnlyFlag = false)
    {
        $methods = [
            ['value' => '15', 'label' => __('15 Minute')],
            ['value' => '30', 'label' => __('30 Minute')],
            ['value' => '60', 'label' => __('1 Hour')],
        ];
        return $methods;
    }
}
