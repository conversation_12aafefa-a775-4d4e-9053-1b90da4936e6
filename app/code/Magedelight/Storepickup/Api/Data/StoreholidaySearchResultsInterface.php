<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Api\Data;

interface StoreholidaySearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Storeholiday list.
     *
     * @return \Magedelight\Storepickup\Api\Data\StoreholidayInterface[]
     */
    public function getItems();

    /**
     * Set holiday_id list.
     *
     * @param \Magedelight\Storepickup\Api\Data\StoreholidayInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
