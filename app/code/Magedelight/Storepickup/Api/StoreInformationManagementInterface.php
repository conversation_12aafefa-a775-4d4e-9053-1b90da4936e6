<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Api;

interface StoreInformationManagementInterface
{
    /**
     * Get store information
     *
     * @api
     * @param string[]
     * @return \Magedelight\Storepickup\Api\StorelocatorRepositoryInterface[]
     */
    public function getStoreInformation();

    /**
     * Get store information by id
     *
     * @api
     * @param string $storeloctorId
     * @return \Magedelight\Storepickup\Api\StorelocatorRepositoryInterface
     */
    public function getStoreInformationById($storeloctorId);

    /**
     * Get store holiday information
     *
     * @api
     * @param string $storeloctorId
     * @return \Magedelight\Storepickup\Api\StorelocatorRepositoryInterface
     */
    public function getStoreHolidayInformation($storeloctorId);

    /**
     * Get store holiday information
     *
     * @api
     * @param string $dateVal
     * @param string $storeVal
     * @return \Magedelight\Storepickup\Api\StorelocatorRepositoryInterface
     */
    public function getStoreTimeInformation($dateVal, $storeVal);
}
