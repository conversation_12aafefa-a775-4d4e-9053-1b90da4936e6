<?php
/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

/** @var \Krish\CustomWidget\Block\Widget\ProductSlider $block */

/** @var \Magento\Framework\Escaper $escaper */

use Magento\Framework\App\Action\Action;

$title = $block->getData('title');
$pageLink = $block->getData('page_link');
$pageTitle = $block->getData('page_title');
$showPrice = (bool)$block->getData('show_price');
$showAddToCart = (bool)$block->getData('show_add_to_cart');
$customCssClass = $block->getData('custom_css_class');
$widgetId = $block->getWidgetId();
$products = $block->getProductCollection();
?>

<?php if ($products && $products->getSize()): ?>
    <div class="block widget block-products-list grid krish-product-slider
    <?= $escaper->escapeHtmlAttr($customCssClass) ?>">
        <div class="section-heading">
            <div class="block-header">
                <?php if ($title): ?>
                    <div class="heading-title">
                        <strong><?= $escaper->escapeHtml($title) ?></strong>
                    </div>
                <?php endif; ?>
                <?php if ($pageTitle && $pageLink): ?>
                    <div class="block-link">
                        <a href="<?= $escaper->escapeUrl($pageLink) ?>" class="action view">
                            <span><?= $escaper->escapeHtml($pageTitle) ?></span>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="block-content">
            <div class="products wrapper grid products-grid grid-widget">
                <ol id="<?= $escaper->escapeHtmlAttr($widgetId) ?>"
                    class="products grid items product-items widget-product-grid slick-carousel">
                    <?php foreach ($products as $product): ?>
                        <li class="product-item">
                            <div class="product-item-info">
                                <?php $productImage = $block->getImage($product, 'category_page_grid'); ?>
                                <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
                                   class="product photo product-item-photo">
                                <span class="product-image-container">
                                    <span class="product-image-wrapper">
                                        <?php
                                        $productImageUrl = $block->getProductImageUrl($product);
                                        if (!$productImageUrl) {
                                            $productImageUrl = $block->getImage($product, 'category_page_grid');
                                        }
                                        ?>
                                        <img class="product-image-photo"
                                             src="<?= $escaper->escapeUrl($productImageUrl) ?>"
                                             alt="<?= $escaper->escapeHtmlAttr($product->getName()) ?>"
                                             loading="lazy">
                                    </span>
                                </span>
                                </a>
                                <div class="product-item-details">
                                    <?php //write if ($product->getAttributeText('brand')):?>
                                    <span class="brand-name">
                                        <?= $escaper->escapeHtml('brandname') ?>
                                    </span>
                                    <?php //endif;?>
                                    <strong class="product-item-name">
                                        <a class="product-item-link"
                                           href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
                                           title="<?= $escaper->escapeHtmlAttr($product->getName()) ?>">
                                            <?= $escaper->escapeHtml($product->getName()) ?>
                                        </a>
                                    </strong>
                                    <?php if ($showPrice): ?>
                                        <div class="price-box price-final_price">
                                            <?= $block->getProductPriceToHtml($product) ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($showAddToCart): ?>
                                        <div class="product-item-inner">
                                        <div class="product-item-actions">
                                            <div class="actions-primary">
                                                <?php if ($product->isSaleable()): ?>
                                                    <?php $postParams = $block->getAddToCartPostParams($product); ?>
                                                    <form data-role="tocart-form"
                                                          data-product-sku="<?= $escaper
                                                            ->escapeHtmlAttr($product->getSku()) ?>"
                                                          action="<?= $escaper->escapeUrl($postParams['action']) ?>"
                                                          method="post">
                                                        <input type="hidden" name="product"
                                                               value="<?= $escaper
                                                                ->escapeHtmlAttr($postParams['data']['product']) ?>">
                                                        <input type="hidden"
                                                               name="<?= $escaper
                                                                ->escapeHtmlAttr(Action::PARAM_NAME_URL_ENCODED) ?>"
                                                               value="<?= $escaper
                                                                ->escapeHtmlAttr($postParams['data'][Action::PARAM_NAME_URL_ENCODED]) ?>">
                                                        <?= $block->getBlockHtml('formkey') ?>
                                                        <button type="submit"
                                                                title="<?= $escaper
                                                                ->escapeHtmlAttr(__('Add to Cart')) ?>"
                                                                class="action tocart primary">
                                                            <span><?= $escaper->escapeHtml(__('Add to Cart')) ?></span>
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <?php if ($product->isAvailable()): ?>
                                                        <div class="stock available">
                                                            <span><?= $escaper->escapeHtml(__('In stock')) ?></span>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="stock unavailable">
                                                            <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ol>
            </div>
        </div>
    </div>
    <script>
        // require(['jquery', 'slick'], function ($) {
        //     $(document).ready(function () {
        //         $(".slick-carousel").slick({
        //             responsive: [
        //         {
        //             breakpoint: 1280,
        //             settings: {
        //                 slidesToShow: 4,
        //                 slidesToScroll: 1
        //             }
        //         },
        //         {
        //             breakpoint: 768,
        //             settings: {
        //                 slidesToShow: 3,
        //                 slidesToScroll: 1
        //             }
        //         },
        //         {
        //             breakpoint: 480,
        //             settings: {
        //                 slidesToShow: 2,
        //                 slidesToScroll: 1,
        //             }
        //         }
        //     ]
        //         });
        //     });
        // });
    </script>
<?php endif; ?>
