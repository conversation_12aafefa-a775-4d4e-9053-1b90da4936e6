& when (@media-common =true) {
    .homepage-section-featured-products {
        .block {
            margin-bottom: 0;
        }

        .slick-prev,
        .slick-next {
            border: 1px solid #70707080;
            background: white;
            border-radius: 999px;
            height: 40px;
            width: 40px;
        }

        .slick-prev {
            left: 0;

            &::before {
                content: "\e905";
                .lib-font-size(16);
                .lib-line-height(16);
                font-family: @icons__font-name;
                color: @color-black;
                padding-right: 0;
            }
        }
        
        .block-header{
            .lib-vendor-prefix-display();
            justify-content: space-between;
            .lib-vendor-prefix-flex-direction();
        }
        
        .action.view{
            color: #000000;
            .lib-font-size(14);
            text-decoration: none;
            position: relative;
            display: inline-block;
            font-weight: 600; 
            .lib-vendor-prefix-display();
            .lib-vendor-box-align(center);

            &::after{
                content: "\e624";
                .lib-font-size(16);
                .lib-line-height(16);
                font-family: @icons__font-name;
                color: @color-black;
                padding-left: 5px;
            }
        }

        .slick-next {
            right: 0;

            &::before {
                content: "\e904";
                .lib-font-size(16);
                .lib-line-height(16);
                font-family: @icons__font-name;
                color: @color-black;
                padding-left: 0;
            }
        }

        .slick-list {
            padding-bottom: 70px;
            // padding-right: 40px;
            // padding-left: 40px;
        }

        .product-items {
            // margin-left:-40px;
            // margin-right: -40px;
        }

        // .slick-slide{
        //     margin-left: 10px;

        //     &.slick-current{
        //         margin-left: 0;
        //     }
        // }
        .product-image-container {
            width: 340px;
            height: auto;
            aspect-ratio: 340 / 380;
        }

        .product-image-wrapper {
            height: 100%;
            width: 100%;
        }

        .brand-name {
            .lib-font-size(16);
            .lib-css(font-weight, @font-weight__bold);
            margin-top: 16px;
            text-transform: uppercase;
            .lib-vendor-prefix-display(block);
        }

        .product-item-name {
            .product-item-link {
                color: #4F4E52;
                .lib-font-size(16);
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                height: 45px;
            }
        }

        .product-item .tocart{
            width: 100%;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m){
    .homepage-section-featured-products{
        .block-header{
            flex-direction: row;
            align-items: center;
        }

        .action.view{
            .lib-font-size(16);
        }
    }
}