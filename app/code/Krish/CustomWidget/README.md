# Krish_CustomWidget

A Magento 2 module that provides a configurable product slider widget using Owl Carousel.

## Features

- Display products in a responsive carousel slider
- Configure product selection criteria (newest, bestselling, featured, on sale)
- Customize slider settings (autoplay, navigation, dots, items per slide)
- Responsive design for all devices
- Easy to use widget interface
- 'Is Featured' product attribute for selecting featured products

## Installation

### Manual Installation

1. Download the module files
2. Create the directory `app/code/Krish/CustomWidget` in your Magento installation
3. Extract the module files to the directory
4. Run the following Magento CLI commands:

```bash
bin/magento module:enable Krish_CustomWidget
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:static-content:deploy -f
bin/magento cache:clean
```

### Composer Installation

```bash
composer require krish/module-custom-widget
bin/magento module:enable Krish_CustomWidget
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:static-content:deploy -f
bin/magento cache:clean
```

## Usage

### Adding the Widget to a CMS Page or Block

1. Go to **Content > Pages** or **Content > Blocks**
2. Edit the page or block where you want to add the slider
3. Click on the **Insert Widget** button in the editor
4. Select **Krish Product Slider** from the widget type dropdown
5. Configure the widget options:
   - **Title**: The title to display above the slider
   - **Number of Products to Display**: How many products to show in the slider
   - **Product Type**: Select from Newest, Best Selling, Featured, or On Sale
   - **Show Price**: Whether to display product prices
   - **Show Add to Cart Button**: Whether to display the Add to Cart button
   - **Autoplay**: Enable/disable automatic sliding
   - **Autoplay Timeout**: Time between slides in milliseconds
   - **Show Navigation Arrows**: Enable/disable navigation arrows
   - **Show Dots**: Enable/disable navigation dots
   - **Items Per Slide**: Number of products to show per slide
   - **Custom CSS Class**: Additional CSS class for custom styling
6. Click **Insert Widget** to add it to the content
7. Save the page or block

### Adding the Widget to a Layout XML File

```xml
<referenceContainer name="content">
    <block class="Krish\CustomWidget\Block\Widget\ProductSlider" name="product.slider.widget">
        <arguments>
            <argument name="title" xsi:type="string">Featured Products</argument>
            <argument name="products_count" xsi:type="number">10</argument>
            <argument name="product_type" xsi:type="string">featured</argument>
            <argument name="show_price" xsi:type="number">1</argument>
            <argument name="show_add_to_cart" xsi:type="number">1</argument>
            <argument name="autoplay" xsi:type="number">1</argument>
            <argument name="autoplay_timeout" xsi:type="number">5000</argument>
            <argument name="show_nav" xsi:type="number">1</argument>
            <argument name="show_dots" xsi:type="number">1</argument>
            <argument name="custom_css_class" xsi:type="string">custom-slider</argument>
        </arguments>
    </block>
</referenceContainer>
```

## Customization

### Custom Styling

You can add custom CSS styles by:

1. Adding a custom CSS class in the widget configuration
2. Creating a custom CSS file in your theme:
   - `app/design/frontend/YourVendor/YourTheme/web/css/source/_extend.less`

Example custom styles:

```css
.custom-slider {
    .product-item {
        border: 1px solid #eee;
        border-radius: 5px;

        &:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    }

    .owl-nav {
        .owl-prev, .owl-next {
            background-color: #f8f8f8;
            color: #333;
        }
    }
}
```

### Using the 'Is Featured' Attribute

The module includes a custom 'Is Featured' product attribute that allows you to mark products as featured and display them in the slider:

1. Go to **Catalog > Products** in the admin panel
2. Edit a product you want to feature
3. In the **Product Details** section, set the **Is Featured** attribute to **Yes**
4. Save the product
5. When configuring the product slider widget, select **Featured Products** as the product type

### Custom Product Selection

To create a custom product selection criteria:

1. Extend the `ProductSlider` block class
2. Override the `getProductCollection` method
3. Add your custom product selection logic

## Support

For issues or feature requests, please contact Krish <NAME_EMAIL> or visit our website at [https://www.krishtechnolabs.com/](https://www.krishtechnolabs.com/).

## License

This module is licensed under the Open Software License (OSL 3.0).
