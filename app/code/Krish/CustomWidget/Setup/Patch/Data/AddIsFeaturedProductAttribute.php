<?php

/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\CustomWidget\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Model\Entity\Attribute\Source\Boolean;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchRevertableInterface;
use Psr\Log\LoggerInterface;

/**
 * Data patch to add 'is_featured' product attribute
 */
class AddIsFeaturedProductAttribute implements DataPatchInterface, PatchRevertableInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup Module data setup
     * @param EavSetupFactory $eavSetupFactory EAV setup factory
     * @param LoggerInterface $logger Logger interface
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Add 'is_featured' product attribute
     *
     * @return $this
     * @throws LocalizedException
     */
    public function apply(): self
    {
        try {
            $this->moduleDataSetup->getConnection()->startSetup();

            /** @var EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

            $eavSetup->addAttribute(
                Product::ENTITY,
                'is_featured',
                [
                    'type' => 'int',
                    'label' => 'Is Featured',
                    'input' => 'boolean',
                    'source' => Boolean::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'user_defined' => false,
                    'default' => '0',
                    'searchable' => false,
                    'filterable' => true,
                    'comparable' => false,
                    'visible_on_front' => false,
                    'used_in_product_listing' => true,
                    'unique' => false,
                    'apply_to' => '',
                    'group' => 'Product Details',
                    'is_used_in_grid' => true,
                    'is_visible_in_grid' => true,
                    'is_filterable_in_grid' => true,
                    'position' => 100
                ]
            );

            $this->moduleDataSetup->getConnection()->endSetup();

            $this->logger->info('Successfully added is_featured product attribute');

            return $this;
        } catch (\Exception $e) {
            $this->logger->critical('Error adding is_featured product attribute: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            throw new LocalizedException(__('Failed to add is_featured product attribute: %1', $e->getMessage()));
        }
    }

    /**
     * Remove 'is_featured' product attribute
     *
     * @return void
     */
    public function revert(): void
    {
        try {
            $this->moduleDataSetup->getConnection()->startSetup();

            /** @var EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
            $eavSetup->removeAttribute(Product::ENTITY, 'is_featured');

            $this->moduleDataSetup->getConnection()->endSetup();

            $this->logger->info('Successfully removed is_featured product attribute');
        } catch (\Exception $e) {
            $this->logger->critical('Error removing is_featured product attribute: ' . $e->getMessage(), [
                'exception' => $e
            ]);
        }
    }

    /**
     * Get aliases
     *
     * @return array<string>
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * Get dependencies
     *
     * @return array<string>
     */
    public static function getDependencies(): array
    {
        return [];
    }
}
