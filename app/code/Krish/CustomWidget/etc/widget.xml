<?xml version="1.0"?>
<!--
/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
-->
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget id="krish_product_slider" class="Krish\CustomWidget\Block\Widget\ProductSlider">
        <label translate="true">Krish Product Slider</label>
        <description translate="true">Displays products in a carousel slider</description>
        <parameters>
            <parameter name="title" xsi:type="text" visible="true" sort_order="10" required="false">
                <label translate="true">Title</label>
            </parameter>
            <parameter name="products_count" xsi:type="text" visible="true" sort_order="20" required="true">
                <label translate="true">Number of Products to Display</label>
                <value>10</value>
            </parameter>
            <parameter name="product_type" xsi:type="select" visible="true" sort_order="30" required="true">
                <label translate="true">Product Type</label>
                <options>
                    <option name="newest" value="newest">
                        <label translate="true">Newest Products</label>
                    </option>
                    <option name="bestsellers" value="bestsellers">
                        <label translate="true">Best Selling</label>
                    </option>
                    <option name="featured" value="featured">
                        <label translate="true">Featured Products</label>
                    </option>
                    <option name="on_sale" value="on_sale">
                        <label translate="true">On Sale</label>
                    </option>
                </options>
            </parameter>
            <parameter name="show_price" xsi:type="select" visible="true" sort_order="40" required="true">
                <label translate="true">Show Price</label>
                <options>
                    <option name="yes" value="1">
                        <label translate="true">Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label translate="true">No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="show_add_to_cart" xsi:type="select" visible="true" sort_order="50" required="true">
                <label translate="true">Show Add to Cart Button</label>
                <options>
                    <option name="yes" value="1">
                        <label translate="true">Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label translate="true">No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="autoplay" xsi:type="select" visible="true" sort_order="60" required="true">
                <label translate="true">Autoplay</label>
                <options>
                    <option name="yes" value="1">
                        <label translate="true">Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label translate="true">No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="autoplay_timeout" xsi:type="text" visible="true" sort_order="70" required="false">
                <label translate="true">Autoplay Timeout (ms)</label>
                <value>5000</value>
            </parameter>
            <parameter name="show_nav" xsi:type="select" visible="true" sort_order="80" required="true">
                <label translate="true">Show Navigation Arrows</label>
                <options>
                    <option name="yes" value="1">
                        <label translate="true">Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label translate="true">No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="show_dots" xsi:type="select" visible="true" sort_order="90" required="true">
                <label translate="true">Show Dots</label>
                <options>
                    <option name="yes" value="1">
                        <label translate="true">Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label translate="true">No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="custom_css_class" xsi:type="text" visible="true" sort_order="110" required="false">
                <label translate="true">Custom CSS Class</label>
            </parameter>
            <parameter name="page_link" xsi:type="text" visible="true" sort_order="115" required="false">
                <label translate="true">Page Link URL</label>
                <description translate="true">URL to link to a specific page</description>
                <value>#</value>
            </parameter>
            <parameter name="page_title" xsi:type="text" visible="true" sort_order="116" required="false">
                <label translate="true">Page Link Text</label>
                <description translate="true">Text to display for the page link</description>
                <value>View All</value>
            </parameter>
            <parameter name="template" xsi:type="select" visible="true" required="true" sort_order="120">
                <label translate="true">Template</label>
                <options>
                    <option name="default" value="Krish_CustomWidget::widget/product_slider.phtml">
                        <label translate="true">Default Template</label>
                    </option>
                </options>
            </parameter>
        </parameters>
    </widget>
</widgets>
