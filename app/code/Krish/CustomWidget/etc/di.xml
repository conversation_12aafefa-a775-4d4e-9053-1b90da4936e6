<?xml version="1.0"?>
<!--
/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Add is_featured attribute to product listing -->
    <type name="Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\Eav">
        <arguments>
            <argument name="attributesToEliminate" xsi:type="array">
                <item name="is_featured" xsi:type="string">is_featured</item>
            </argument>
        </arguments>
    </type>

    <!-- Add is_featured to product grid -->
    <virtualType name="Magento\Catalog\Ui\DataProvider\Product\Listing\DataProvider">
        <arguments>
            <argument name="addFieldStrategies" xsi:type="array">
                <item name="is_featured" xsi:type="object">Magento\Catalog\Ui\DataProvider\Product\Listing\Modifier\AttributeList</item>
            </argument>
            <argument name="addFilterStrategies" xsi:type="array">
                <item name="is_featured" xsi:type="object">Magento\Catalog\Ui\DataProvider\Product\Listing\Modifier\AttributeList</item>
            </argument>
        </arguments>
    </virtualType>
</config>
