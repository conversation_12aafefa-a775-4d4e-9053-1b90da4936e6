<?php

/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\CustomWidget\Model;

use Magento\Framework\Pricing\Helper\Data as PricingHelper;
use Psr\Log\LoggerInterface;

/**
 * Configuration model for custom widgets
 */
class Config
{
    /**
     * @param PricingHelper $priceHelper Price helper for currency formatting
     * @param LoggerInterface $logger Logger interface for error logging
     */
    public function __construct(
        private readonly PricingHelper $priceHelper,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Get formatted price
     *
     * @param float $price Price to format
     * @return string Formatted price
     */
    public function getFormattedPrice(float $price): string
    {
        try {
            return $this->priceHelper->currency($price, true, false);
        } catch (\Exception $e) {
            $this->logger->error('Error formatting price: ' . $e->getMessage(), [
                'price' => $price,
                'exception' => $e
            ]);
            return (string)$price;
        }
    }

    /**
     * Get slider configuration
     *
     * @param int $itemsPerSlide Number of items to show per slide
     * @param bool $autoplay Whether to autoplay the slider
     * @param int $autoplayTimeout Autoplay timeout in milliseconds
     * @param bool $showNav Whether to show navigation arrows
     * @param bool $showDots Whether to show navigation dots
     * @return array<string, mixed> Slider configuration
     */
    public function getSliderConfig(
        int $itemsPerSlide,
        bool $autoplay,
        int $autoplayTimeout,
        bool $showNav,
        bool $showDots
    ): array {
        try {
            return [
                'infinite' => true,
                'slidesToShow' => $itemsPerSlide,
                'slidesToScroll' => 1,
                'autoplay' => $autoplay,
                'autoplaySpeed' => $autoplayTimeout,
                'arrows' => $showNav,
                'dots' => $showDots,
                'responsive' => [
                    [
                        'breakpoint' => 1024,
                        'settings' => [
                            'slidesToShow' => $itemsPerSlide,
                            'slidesToScroll' => 1
                        ]
                    ],
                    [
                        'breakpoint' => 768,
                        'settings' => [
                            'slidesToShow' => 2,
                            'slidesToScroll' => 1
                        ]
                    ],
                    [
                        'breakpoint' => 480,
                        'settings' => [
                            'slidesToShow' => 1,
                            'slidesToScroll' => 1
                        ]
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $this->logger->error('Error generating slider config: ' . $e->getMessage(), [
                'itemsPerSlide' => $itemsPerSlide,
                'autoplay' => $autoplay,
                'autoplayTimeout' => $autoplayTimeout,
                'showNav' => $showNav,
                'showDots' => $showDots,
                'exception' => $e
            ]);

            // Return a default configuration in case of error
            return [
                'infinite' => true,
                'slidesToShow' => 4,
                'slidesToScroll' => 1,
                'autoplay' => false,
                'arrows' => true,
                'dots' => true
            ];
        }
    }
}
