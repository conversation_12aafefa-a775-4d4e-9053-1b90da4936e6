<?php

/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\CustomWidget\Model;

use Magento\Catalog\Model\Product\Visibility;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Sales\Model\ResourceModel\Report\Bestsellers\CollectionFactory as BestSellersCollectionFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Product Collection Provider for widgets
 *
 * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
 * @SuppressWarnings(PHPMD.LongVariable)
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class ProductCollectionProvider
{
    /**
     * @param CollectionFactory $colFactory Product collection factory
     * @param Visibility $prodVisibility Product visibility model
     * @param StoreManagerInterface $storeManager Store manager
     * @param BestSellersCollectionFactory $bestsellerFactory Bestseller collection factory
     * @param DateTime $dateTime Date/time utility
     * @param LoggerInterface $logger Logger interface
     */
    public function __construct(
        private readonly CollectionFactory $colFactory,
        private readonly Visibility $prodVisibility,
        private readonly StoreManagerInterface $storeManager,
        private readonly BestSellersCollectionFactory $bestsellerFactory,
        private readonly DateTime $dateTime,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Get product collection based on type
     *
     * @param string $productType Type of products to retrieve
     * @param int $count Number of products to retrieve
     * @return Collection
     */
    public function getProductCollection(string $productType, int $count): Collection
    {
        try {
            return match ($productType) {
                'newest' => $this->getNewestProducts($count),
                'bestsellers' => $this->getBestSellingProducts($count),
                'featured' => $this->getFeaturedProducts($count),
                'on_sale' => $this->getOnSaleProducts($count),
                default => $this->getNewestProducts($count),
            };
        } catch (\Exception $e) {
            $this->logger->error('Error getting product collection: ' . $e->getMessage(), [
                'productType' => $productType,
                'count' => $count,
                'exception' => $e
            ]);
            // Return newest products as fallback
            return $this->getNewestProducts($count);
        }
    }

    /**
     * Get newest products
     *
     * @param int $count Number of products to retrieve
     * @return Collection
     */
    public function getNewestProducts(int $count): Collection
    {
        try {
            $collection = $this->colFactory->create();
            $collection->setVisibility($this->prodVisibility->getVisibleInCatalogIds());

            $collection->addAttributeToSelect('*')
                ->addStoreFilter()
                ->addAttributeToSort('created_at', 'desc')
                ->setPageSize($count);

            return $collection;
        } catch (\Exception $e) {
            $this->logger->error('Error getting newest products: ' . $e->getMessage(), [
                'count' => $count,
                'exception' => $e
            ]);
            // Return empty collection as fallback
            return $this->colFactory->create();
        }
    }

    /**
     * Get best selling products
     *
     * @param int $count Number of products to retrieve
     * @return Collection
     */
    public function getBestSellingProducts(int $count): Collection
    {
        try {
            $storeId = $this->storeManager->getStore()->getId();

            // Get bestseller product ids
            $bestSellers = $this->bestsellerFactory->create()
                ->setPeriod('month')
                ->setStoreId($storeId)
                ->setPageSize($count);

            $productIds = [];
            foreach ($bestSellers as $item) {
                $productIds[] = $item->getProductId();
            }

            // If no bestsellers found, return newest products
            if (empty($productIds)) {
                $this->logger->info('No bestselling products found, returning newest products');
                return $this->getNewestProducts($count);
            }

            // Get product collection by ids
            $collection = $this->colFactory->create();
            $collection->setVisibility($this->prodVisibility->getVisibleInCatalogIds());

            $collection->addAttributeToSelect('*')
                ->addStoreFilter()
                ->addIdFilter($productIds)
                ->setPageSize($count);

            return $collection;
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Store entity not found: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            return $this->getNewestProducts($count);
        } catch (\Exception $e) {
            $this->logger->error('Error getting bestselling products: ' . $e->getMessage(), [
                'count' => $count,
                'exception' => $e
            ]);
            return $this->getNewestProducts($count);
        }
    }

    /**
     * Get featured products (products with is_featured = 1 attribute)
     *
     * @param int $count Number of products to retrieve
     * @return Collection
     */
    public function getFeaturedProducts(int $count): Collection
    {
        try {
            $collection = $this->colFactory->create();
            $collection->setVisibility($this->prodVisibility->getVisibleInCatalogIds());

            $collection->addAttributeToSelect('*')
                ->addStoreFilter()
                ->addAttributeToFilter([
                    ['attribute' => 'is_featured', 'eq' => 1]
                ], '', 'left')
                ->setPageSize($count);

            // If no featured products found, return newest products
            if ($collection->count() == 0) {
                $this->logger->info('No featured products found, returning newest products');
                return $this->getNewestProducts($count);
            }

            return $collection;
        } catch (\Exception $e) {
            $this->logger->error('Error getting featured products: ' . $e->getMessage(), [
                'count' => $count,
                'exception' => $e
            ]);
            return $this->getNewestProducts($count);
        }
    }

    /**
     * Get on sale products (products with special price)
     *
     * @param int $count Number of products to retrieve
     * @return Collection
     */
    public function getOnSaleProducts(int $count): Collection
    {
        try {
            $todayDate = $this->dateTime->gmtDate();

            $collection = $this->colFactory->create();
            $collection->setVisibility($this->prodVisibility->getVisibleInCatalogIds());

            $collection->addAttributeToSelect('*')
                ->addStoreFilter()
                ->addAttributeToFilter([
                    // Special price is set
                    [
                        'attribute' => 'special_price',
                        'notnull' => true,
                    ],
                ], '', 'left')
                ->addAttributeToFilter([
                    // Special price is effective
                    [
                        'attribute' => 'special_from_date',
                        ['null' => true],
                        ['lteq' => $todayDate],
                    ],
                ], '', 'left')
                ->addAttributeToFilter([
                    [
                        'attribute' => 'special_to_date',
                        ['null' => true],
                        ['gteq' => $todayDate],
                    ],
                ], '', 'left')
                ->setPageSize($count);

            return $collection;
        } catch (\Exception $e) {
            $this->logger->error('Error getting on sale products: ' . $e->getMessage(), [
                'count' => $count,
                'exception' => $e
            ]);
            return $this->getNewestProducts($count);
        }
    }
}
