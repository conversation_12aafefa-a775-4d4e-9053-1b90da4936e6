<?php
/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
namespace Krish\CustomWidget\Model;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Helper\Image;
use Magento\Catalog\Model\Product;

class ProductService
{
    /**
     * @var ProductRepositoryInterface
     */
    private ProductCollectionProvider $colProvider;

    /**
     * @var Config
     */
    private Image $imageHelper;

    /**
     * @param ProductCollectionProvider $colProvider
     * @param Image $imageHelper
     */
    public function __construct(
        ProductCollectionProvider $colProvider,
        Image $imageHelper
    ) {
        $this->colProvider = $colProvider;
        $this->imageHelper = $imageHelper;
    }

    /**
     * Get product image url
     *
     * @param Product $product
     * @return string
     */
    public function getProductImageUrl(Product $product)
    {
        return $this->imageHelper->init($product, 'product_page_image_small')
            ->setImageFile($product->getSmallImage())
            ->resize(340, 450)
            ->getUrl();
    }

    /**
     * Get product collection based on product type and count
     *
     * @param $productType
     * @param $count
     *
     * @return \Magento\Catalog\Model\ResourceModel\Product\Collection
     */
    public function getProductCollection($productType, $count)
    {
        return $this->colProvider->getProductCollection(
            $productType,
            $count
        );
    }
}
