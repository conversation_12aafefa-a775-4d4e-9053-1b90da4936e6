<?php
/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
declare(strict_types=1);

namespace Krish\CustomWidget\Block\Widget;

use Krish\CustomWidget\Model\Config;
use Krish\CustomWidget\Model\ProductService;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Block\Product\Context;
use Magento\Catalog\Block\Product\ListProduct;
use Magento\Catalog\Helper\Output as OutputHelper;
use Magento\Catalog\Model\Layer\Resolver;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\Data\Helper\PostHelper;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Pricing\Render;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Url\Helper\Data;
use Magento\Widget\Block\BlockInterface;
use Psr\Log\LoggerInterface;

/**
 * Product Slider Widget Block
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
 * @SuppressWarnings(PHPMD.LongVariable)
 */
class ProductSlider extends ListProduct implements BlockInterface
{
    /**
     * Default template path
     *
     * @var string
     */
    protected $_template = "widget/product_slider.phtml";

    /**
     * @var Config
     */
    private readonly Config $config;

    /**
     * @var Json
     */
    private readonly Json $jsonSerializer;

    /**
     * @var LoggerInterface
     */
    private readonly LoggerInterface $logger;

    /**
     * @var ProductService
     */
    private ProductService $productService;

    /**
     * @param Context $context
     * @param PostHelper $postDataHelper
     * @param Resolver $layerResolver
     * @param CategoryRepositoryInterface $categoryRepository
     * @param Data $urlHelper
     * @param Config $config
     * @param ProductService $productService
     * @param Json $jsonSerializer
     * @param LoggerInterface $logger
     * @param array $data
     * @param OutputHelper|null $outputHelper
     *
     * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     * @SuppressWarnings(PHPMD.LongVariable)
     */
    public function __construct(
        Context $context,
        PostHelper $postDataHelper,
        Resolver $layerResolver,
        CategoryRepositoryInterface $categoryRepository,
        Data $urlHelper,
        Config $config,
        ProductService $productService,
        Json $jsonSerializer,
        LoggerInterface $logger,
        array $data = [],
        ?OutputHelper $outputHelper = null
    ) {
        parent::__construct(
            $context,
            $postDataHelper,
            $layerResolver,
            $categoryRepository,
            $urlHelper,
            $data,
            $outputHelper
        );
        $this->config = $config;
        $this->productService = $productService;
        $this->jsonSerializer = $jsonSerializer;
        $this->logger = $logger;
    }

    /**
     * Get product image url
     *
     * @param Product $product Product entity
     * @return string
     */
    public function getProductImageUrl(Product $product): string
    {
        try {
            return $this->productService->getProductImageUrl($product);
        } catch (\Exception $e) {
            $this->logger->error('Error getting product image URL: ' . $e->getMessage(), [
                'product_id' => $product->getId(),
                'exception' => $e
            ]);
            return '';
        }
    }

    /**
     * Get product price html
     *
     * @param Product $product Product entity
     * @return string
     */
    public function getProductPriceToHtml(Product $product): string
    {
        try {
            return $this->getLayout()
                ->createBlock(Render::class)
                ->render(
                    'final_price',
                    $product,
                    [
                        'display_minimal_price' => true,
                        'zone' => Render::ZONE_ITEM_LIST,
                        'price_id' => 'old-price-' . $product->getId() . '-widget-product-grid',
                    ]
                );
        } catch (\Exception $e) {
            $this->logger->error('Error rendering product price: ' . $e->getMessage(), [
                'product_id' => $product->getId(),
                'exception' => $e
            ]);
            return '';
        }
    }

    /**
     * Get formatted price
     *
     * @param float $price Price to format
     * @return string
     */
    public function getFormattedPrice(float $price): string
    {
        try {
            return $this->config->getFormattedPrice($price);
        } catch (\Exception $e) {
            $this->logger->error('Error formatting price: ' . $e->getMessage(), [
                'price' => $price,
                'exception' => $e
            ]);
            return (string)$price;
        }
    }

    /**
     * Get slider configuration as JSON
     *
     * @return string
     */
    public function getSliderConfigJson(): string
    {
        try {
            $itemsPerSlide = 5;
            $autoplay = (bool)$this->getData('autoplay');
            $autoplayTimeout = (int)$this->getData('autoplay_timeout') ?: 5000;
            $showNav = (bool)$this->getData('show_nav');
            $showDots = (bool)$this->getData('show_dots');

            $config = $this->config->getSliderConfig(
                $itemsPerSlide,
                $autoplay,
                $autoplayTimeout,
                $showNav,
                $showDots
            );

            return $this->jsonSerializer->serialize($config);
        } catch (\Exception $e) {
            $this->logger->error('Error generating slider config JSON: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            // Return empty JSON object as fallback
            return '{}';
        }
    }

    /**
     * Get widget unique ID
     *
     * @return string
     */
    public function getWidgetId(): string
    {
        return 'krish-product-slider-' . uniqid();
    }

    /**
     * Get a product collection based on widget configuration
     *
     * @return Collection
     * @throws LocalizedException
     */
    public function getProductCollection(): Collection
    {
        try {
            $productType = $this->getData('product_type');
            $productsCount = (int)$this->getData('products_count');
            return $this->productService->getProductCollection($productType, $productsCount);
        } catch (\Exception $e) {
            $this->logger->error('Error getting product collection: ' . $e->getMessage(), [
            'product_type' => $this->getData('product_type'),
            'products_count' => $this->getData('products_count'),
            'exception' => $e
            ]);
            throw new LocalizedException(__('Unable to load product collection.'));
        }
    }
}
