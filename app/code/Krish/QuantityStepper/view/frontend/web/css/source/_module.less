//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    // Initially hide quantity inputs until they're initialized
    .input-text.qty:not([data-stepper-initialized]) {
        opacity: 0;
        transition: opacity 0.3s;
    }

    .input-text.qty[data-stepper-initialized] {
        opacity: 1;
    }

    .qty-stepper-container {
        display: flex;
        align-items: center;
        max-width: 150px;

        .qty-btn {
            width: 30px;
            height: 36px;
            padding: 0;
            border: 1px solid #c2c2c2;
            background: #f0f0f0;
            font-size: 16px;
            line-height: 1;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                background: #e0e0e0;
            }

            &.qty-decrease {
                border-radius: 3px 0 0 3px;
            }

            &.qty-increase {
                border-radius: 0 3px 3px 0;
            }
        }

        .input-text.qty {
            text-align: center;
            margin: 0;
            border-radius: 0;
            border-left: 0;
            border-right: 0;
            height: 36px;
            width: 50px;
        }

        // Hide browser's default number input arrows
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type=number] {
            -moz-appearance: textfield;
        }
    }
}

//
// Mobile styles
// _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .qty-stepper-container {
        max-width: 120px;

        .qty-btn {
            width: 25px;
            height: 32px;
        }

        .input-text.qty {
            height: 32px;
            width: 40px;
        }
    }
}

//
// Desktop styles
// _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .qty-stepper-container {
        .qty-btn {
            transition: background-color 0.2s;
        }
    }
}
