define([
    'jquery',
    'jquery/ui',
    'jquery-ui-modules/widget',
    'domReady!'
], function ($) {
    'use strict';

    $.widget('krish.quantityStepper', {
        options: {
            selector: '.input-text.qty',
            decrementButton: '<button type="button" class="qty-btn qty-decrease" title="Decrease Quantity">-</button>',
            incrementButton: '<button type="button" class="qty-btn qty-increase" title="Increase Quantity">+</button>'
        },

        /**
         * Widget initialization
         * @private
         */
        _create: function () {
            this._initializeAll();
            this._bindGlobalEvents();
        },

        /**
         * Initialize all quantity inputs
         * @private
         */
        _initializeAll: function () {
            var self = this;
            $(this.options.selector).each(function() {
                self._initializeStepper($(this));
            });
        },

        /**
         * Initialize a single quantity stepper
         * @private
         */
        _initializeStepper: function (element) {
            // Skip if already initialized
            if (element.attr('data-stepper-initialized')) {
                return;
            }

            // Add decrement button before the input
            $(this.options.decrementButton).insertBefore(element);

            // Add increment button after the input
            $(this.options.incrementButton).insertAfter(element);

            // Add wrapper for styling if needed
            element.parent().addClass('qty-stepper-container');

            // Mark as initialized
            element.attr('data-stepper-initialized', true);

            // Bind events for this specific stepper
            this._bindStepperEvents(element);
        },

        /**
         * Bind events for a specific stepper
         * @private
         */
        _bindStepperEvents: function (element) {
            var self = this;

            // Decrement button click
            element.parent().on('click', '.qty-decrease', function (e) {
                e.preventDefault();
                self._decrementQty(element);
            });

            // Increment button click
            element.parent().on('click', '.qty-increase', function (e) {
                e.preventDefault();
                self._incrementQty(element);
            });
        },

        /**
         * Bind global events to handle dynamic content
         * @private
         */
        _bindGlobalEvents: function () {
            var self = this;

            // Listen for content updates
            $(document).on('contentUpdated ajaxComplete', function() {
                setTimeout(function() {
                    self._initializeAll();
                }, 0);
            });

            // Special handling for quick view and other popups
            $(document).on('afterRender', function() {
                setTimeout(function() {
                    self._initializeAll();
                }, 0);
            });
        },

        /**
         * Decrement quantity
         * @private
         */
        _decrementQty: function (element) {
            var currentVal = parseFloat(element.val()) || 0,
                minValue = parseFloat(element.attr('min')) || 0,
                step = parseFloat(element.attr('step')) || 1;

            if (currentVal > minValue) {
                element.val(currentVal - step);
                element.trigger('change');
            }
        },

        /**
         * Increment quantity
         * @private
         */
        _incrementQty: function (element) {
            var currentVal = parseFloat(element.val()) || 0,
                maxValue = parseFloat(element.attr('max')) || 10000,
                step = parseFloat(element.attr('step')) || 1;

            if (currentVal < maxValue) {
                element.val(currentVal + step);
                element.trigger('change');
            }
        }
    });

    return $.krish.quantityStepper;

});
