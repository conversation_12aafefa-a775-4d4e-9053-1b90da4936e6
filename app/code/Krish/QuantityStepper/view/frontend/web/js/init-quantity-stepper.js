define([
    'jquery',
    'jquery-ui-modules/widget',
    'quantityStepper'
], function ($) {
    'use strict';

    return function () {
        // Initialize on page load
        $('.input-text.qty').each(function () {
            if (!$(this).parent().hasClass('qty-stepper-container')) {
                $(this).quantityStepper();
            }
        });

        // Initialize on content updates (for AJAX loaded content)
        $(document).on('contentUpdated', function () {
            $('.input-text.qty').each(function () {
                if (!$(this).parent().hasClass('qty-stepper-container')) {
                    $(this).quantityStepper();
                }
            });
        });
    };
});
