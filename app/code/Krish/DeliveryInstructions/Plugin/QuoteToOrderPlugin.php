<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_DeliveryInstructions
 *  <AUTHOR> Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\DeliveryInstructions\Plugin;

use Magento\Quote\Model\QuoteManagement;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Psr\Log\LoggerInterface;

/**
 * Plugin to transfer delivery instructions from quote to order
 */
class QuoteToOrderPlugin
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param LoggerInterface $logger
     */
    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    /**
     * Transfer delivery instructions from quote to order
     *
     * @param QuoteManagement $subject
     * @param OrderInterface $order
     * @param CartInterface $quote
     * @return OrderInterface
     */
    public function afterSubmit(
        QuoteManagement $subject,
        OrderInterface $order,
        CartInterface $quote
    ): OrderInterface {
        try {
            // Transfer delivery instruction
            if ($quote->getDeliveryInstruction()) {
                $order->setDeliveryInstruction($quote->getDeliveryInstruction());
            }

            // Transfer leave at door
            if ($quote->getLeaveAtDoor() !== null) {
                $order->setLeaveAtDoor($quote->getLeaveAtDoor());
            }

            // Transfer preferred delivery time
            if ($quote->getPreferredDeliveryTime()) {
                $order->setPreferredDeliveryTime($quote->getPreferredDeliveryTime());
            }

            // Save the order with delivery instructions
            $order->save();

        } catch (\Exception $e) {
            $this->logger->error(
                'Error transferring delivery instructions from quote to order: ' . $e->getMessage(),
                [
                    'quote_id' => $quote->getId(),
                    'order_id' => $order->getId(),
                    'exception' => $e
                ]
            );
        }

        return $order;
    }
}
