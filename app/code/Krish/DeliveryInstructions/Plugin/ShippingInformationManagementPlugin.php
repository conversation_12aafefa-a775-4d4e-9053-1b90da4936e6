<?php
/*
 * @category   <PERSON>h Technolabs Module Development
 * @package    Krish_ModuleName
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\DeliveryInstructions\Plugin;

use Magento\Checkout\Api\ShippingInformationManagementInterface;
use Magento\Checkout\Api\Data\ShippingInformationInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Psr\Log\LoggerInterface;

/**
 * Plugin to save delivery instructions from shipping information
 */
class ShippingInformationManagementPlugin
{
    /**
     * @var CartRepositoryInterface
     */
    protected $cartRepository;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param CartRepositoryInterface $cartRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        CartRepositoryInterface $cartRepository,
        LoggerInterface $logger
    ) {
        $this->cartRepository = $cartRepository;
        $this->logger = $logger;
    }

    /**
     * Save delivery instructions to quote before saving shipping information
     *
     * @param ShippingInformationManagementInterface $subject
     * @param int $cartId
     * @param ShippingInformationInterface $addressInformation
     * @return array
     */
    public function beforeSaveAddressInformation(
        ShippingInformationManagementInterface $subject,
        $cartId,
        ShippingInformationInterface $addressInformation
    ): array {
        try {
            $extensionAttributes = $addressInformation->getExtensionAttributes();

            if ($extensionAttributes) {
                $quote = $this->cartRepository->getActive($cartId);
                $dataChanged = false;

                // Save delivery instruction
                if ($extensionAttributes->getDeliveryInstruction()) {
                    $quote->setDeliveryInstruction($extensionAttributes->getDeliveryInstruction());
                    $dataChanged = true;
                }

                // Save leave at door
                if ($extensionAttributes->getLeaveAtDoor() !== null) {
                    $quote->setLeaveAtDoor($extensionAttributes->getLeaveAtDoor());
                    $dataChanged = true;
                }

                // Save preferred delivery time
                if ($extensionAttributes->getPreferredDeliveryTime()) {
                    $quote->setPreferredDeliveryTime($extensionAttributes->getPreferredDeliveryTime());
                    $dataChanged = true;
                }

                // Save the quote if any data changed
                if ($dataChanged) {
                    $this->cartRepository->save($quote);
                }
            }
        } catch (\Exception $e) {
            $this->logger->error(
                'Error saving delivery instructions to quote: ' . $e->getMessage(),
                [
                    'cart_id' => $cartId,
                    'exception' => $e
                ]
            );
        }

        return [$cartId, $addressInformation];
    }
}
