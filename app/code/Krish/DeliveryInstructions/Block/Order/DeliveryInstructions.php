<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_DeliveryInstructions
 *  <AUTHOR> Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\DeliveryInstructions\Block\Order;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Framework\Registry;
use Magento\Sales\Model\Order;

/**
 * Block for displaying delivery instructions in order view
 */
class DeliveryInstructions extends Template
{
    /**
     * @var Registry
     */
    protected $coreRegistry;

    /**
     * @param Context $context
     * @param Registry $coreRegistry
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $coreRegistry,
        array $data = []
    ) {
        $this->coreRegistry = $coreRegistry;
        parent::__construct($context, $data);
    }

    /**
     * Get current order
     *
     * @return Order|null
     */
    public function getOrder(): ?Order
    {
        return $this->coreRegistry->registry('current_order');
    }

    /**
     * Get delivery instruction
     *
     * @return string|null
     */
    public function getDeliveryInstruction(): ?string
    {
        $order = $this->getOrder();
        return $order ? $order->getDeliveryInstruction() : null;
    }

    /**
     * Get leave at door status
     *
     * @return bool
     * @suppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function getLeaveAtDoor(): bool
    {
        $order = $this->getOrder();
        return $order ? (bool)$order->getLeaveAtDoor() : false;
    }

    /**
     * Get preferred delivery time
     *
     * @return string|null
     */
    public function getPreferredDeliveryTime(): ?string
    {
        $order = $this->getOrder();
        return $order ? $order->getPreferredDeliveryTime() : null;
    }

    /**
     * Check if delivery instructions should be displayed
     *
     * @return bool
     */
    public function shouldDisplayDeliveryInstructions(): bool
    {
        return $this->getDeliveryInstruction() ||
               $this->getLeaveAtDoor() ||
               $this->getPreferredDeliveryTime();
    }

    /**
     * Format delivery time for display
     *
     * @param string|null $timeSlot
     * @return string
     */
    public function formatDeliveryTime(?string $timeSlot): string
    {
        if (!$timeSlot) {
            return '';
        }

        // If time slot is in format "09:00-12:00", format it nicely
        if (strpos($timeSlot, '-') !== false) {
            $times = explode('-', $timeSlot);
            if (count($times) === 2) {
                return trim($times[0]) . ' - ' . trim($times[1]);
            }
        }

        return $timeSlot;
    }
}
