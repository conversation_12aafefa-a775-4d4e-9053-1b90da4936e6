# Krish_DeliveryInstructions Module

## Overview
The Krish_DeliveryInstructions module adds a delivery instructions step to the Magento 2 checkout process, allowing customers to provide special delivery instructions, specify if they want packages left at the door, and select preferred delivery time slots.

## Features

### Checkout Integration
- Adds a new checkout step between shipping and payment
- Conditional display based on cart type (not shown for virtual products)
- Configurable to disable for specific shipping methods
- Responsive design compatible with Luma theme

### Form Fields
- **Delivery Instruction**: Textarea for special delivery notes
- **Leave at Door**: Checkbox option for contactless delivery
- **Preferred Delivery Time**: Dropdown with configurable time slots

### Data Management
- Uses Magento extension attributes for clean data handling
- Saves data to quote during checkout
- Transfers data to order upon placement
- Displays in customer account order view
- Shows in admin order management

### Backend Configuration
- Enable/disable module functionality
- Configure disabled shipping methods
- Set delivery instruction as required/optional
- Configure time slot settings (start time, end time, duration)
- Set maximum delivery days ahead

## Installation

1. Copy the module files to `app/code/Krish/DeliveryInstructions/`
2. Run setup commands:
   ```bash
   php bin/magento module:enable Krish_DeliveryInstructions
   php bin/magento setup:upgrade
   php bin/magento setup:di:compile
   php bin/magento setup:static-content:deploy
   php bin/magento cache:flush
   ```

## Configuration

Navigate to **Stores > Configuration > Krish Technolabs > Delivery Instructions**

### General Settings
- **Enable Delivery Instructions**: Enable/disable the module
- **Disabled for Shipping Methods**: Select shipping methods to exclude
- **Make Delivery Instruction Required**: Require delivery instruction input
- **Leave at Door Default Value**: Default checkbox state

### Time Slot Settings
- **Start Time**: Beginning of delivery time window
- **End Time**: End of delivery time window
- **Slot Duration (Hours)**: Duration of each time slot
- **Maximum Delivery Days**: Days ahead for scheduling

## Technical Details

### Database Schema
The module adds the following columns to quote and order tables:
- `delivery_instruction` (TEXT)
- `leave_at_door` (BOOLEAN)
- `preferred_delivery_time` (VARCHAR 50)

### Extension Attributes
- `Magento\Quote\Api\Data\CartInterface`
- `Magento\Checkout\Api\Data\ShippingInformationInterface`
- `Magento\Sales\Api\Data\OrderInterface`

### Key Components

#### Frontend JavaScript
- `delivery-instruction-step.js`: Main checkout step component
- `delivery-instruction-form.js`: Form handling component
- `delivery-instruction-data.js`: Data model for form values
- `delivery-instruction-payload-extender.js`: Extends shipping payload

#### Backend Classes
- `Helper\Data`: Configuration and utility methods
- `Model\ConfigProvider`: Checkout configuration provider
- `Plugin\ShippingInformationManagementPlugin`: Saves data during checkout
- `Plugin\QuoteToOrderPlugin`: Transfers data from quote to order
- `Observer\SaveDeliveryInstructionToOrder`: Order creation observer
- `Block\Order\DeliveryInstructions`: Order view display block

## Customization

### Adding Custom Time Slots
Modify the `Helper\Data::generateTimeSlots()` method to customize time slot generation logic.

### Styling
Override the CSS file at `view/frontend/web/css/delivery-instructions.css` in your theme.

### Templates
Customize the knockout templates:
- `delivery-instruction-step.html`
- `delivery-instruction-form.html`

### Order Display Templates
- Frontend: `view/frontend/templates/order/delivery_instructions.phtml`
- Admin: `view/adminhtml/templates/order/admin_delivery_instructions.phtml`

## Compatibility
- Magento 2.4.6+
- PHP 8.3
- Compatible with Luma theme
- Follows Magento coding standards (PSR-12)

## Dependencies
- Krish_Base (parent module)
- Magento_Checkout
- Magento_Quote
- Magento_Sales
- Magento_Shipping

## Support
For technical support and customization requests, contact Krish Technolabs Pvt Ltd.

## License
Open Software License (OSL 3.0)
