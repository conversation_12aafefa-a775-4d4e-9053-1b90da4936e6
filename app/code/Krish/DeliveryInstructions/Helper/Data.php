<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_DeliveryInstructions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\DeliveryInstructions\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

/**
 * Delivery Instructions helper
 * @suppressWarnings(CyclomaticComplexity)
 * @suppressWarnings(NPathComplexity)
 */
class Data extends AbstractHelper
{
    /**
     * Config paths
     */
    public const XML_PATH_ENABLED = 'krish_delivery_instructions/general/enabled';
    public const XML_PATH_DISABLED_SHIPPING_METHODS = 'krish_delivery_instructions/general/disabled_shipping_methods';
    public const XML_PATH_DELIVERY_INSTRUCTION_REQUIRED = 'krish_delivery_instructions/general/delivery_instruction_required';
    public const XML_PATH_LEAVE_AT_DOOR_DEFAULT = 'krish_delivery_instructions/general/leave_at_door_default';
    public const XML_PATH_START_TIME = 'krish_delivery_instructions/time_slots/start_time';
    public const XML_PATH_END_TIME = 'krish_delivery_instructions/time_slots/end_time';
    public const XML_PATH_SLOT_DURATION = 'krish_delivery_instructions/time_slots/slot_duration';
    public const XML_PATH_MAX_DELIVERY_DAYS = 'krish_delivery_instructions/time_slots/max_delivery_days';

    /**
     * @var TimezoneInterface
     */
    protected $timezone;

    /**
     * @param Context $context
     * @param TimezoneInterface $timezone
     */
    public function __construct(
        Context $context,
        TimezoneInterface $timezone
    ) {
        parent::__construct($context);
        $this->timezone = $timezone;
    }

    /**
     * Check if module is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled(?int $storeId = null): bool
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get disabled shipping methods
     *
     * @param int|null $storeId
     * @return array
     */
    public function getDisabledShippingMethods(?int $storeId = null): array
    {
        $methods = $this->scopeConfig->getValue(
            self::XML_PATH_DISABLED_SHIPPING_METHODS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );

        return $methods ? explode(',', $methods) : [];
    }

    /**
     * Check if delivery instruction is required
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isDeliveryInstructionRequired(?int $storeId = null): bool
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_DELIVERY_INSTRUCTION_REQUIRED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get leave at door default value
     *
     * @param int|null $storeId
     * @return bool
     * @suppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function getLeaveAtDoorDefault(?int $storeId = null): bool
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_LEAVE_AT_DOOR_DEFAULT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get time slot start time
     *
     * @param int|null $storeId
     * @return string
     */
    public function getStartTime(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_START_TIME,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get time slot end time
     *
     * @param int|null $storeId
     * @return string
     */
    public function getEndTime(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_END_TIME,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get slot duration in hours
     *
     * @param int|null $storeId
     * @return int
     */
    public function getSlotDuration(?int $storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_SLOT_DURATION,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get maximum delivery days
     *
     * @param int|null $storeId
     * @return int
     */
    public function getMaxDeliveryDays(?int $storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_MAX_DELIVERY_DAYS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Generate time slots
     *
     * @param int|null $storeId
     * @return array
     */
    public function generateTimeSlots(?int $storeId = null): array
    {
        $startTime = $this->getStartTime($storeId) ?: '09:00';
        $endTime = $this->getEndTime($storeId) ?: '18:00';
        $duration = $this->getSlotDuration($storeId) ?: 3;

        // Convert comma-separated time format to proper time format
        $startTime = $this->formatTimeString($startTime);
        $endTime = $this->formatTimeString($endTime);

        $slots = [];

        // Convert to timestamps
        $current = strtotime($startTime);
        $end = strtotime($endTime);

        // If time parsing failed, use defaults
        if ($current === false) {
            $current = strtotime('09:00:00');
        }
        if ($end === false) {
            $end = strtotime('18:00:00');
        }

        // Ensure duration is valid
        if ($duration <= 0) {
            $duration = 3;
        }

        while ($current < $end) {
            $slotStart = date('H:i', $current);
            $slotEnd = date('H:i', $current + ($duration * 3600));

            // Only add slot if it doesn't exceed end time
            if (($current + ($duration * 3600)) <= $end) {
                $slots[] = [
                    'value' => $slotStart . '-' . $slotEnd,
                    'label' => $slotStart . ' - ' . $slotEnd
                ];
            }

            $current += ($duration * 3600);
        }

        // If no slots generated, provide default slots
        if (empty($slots)) {
            $slots = [
                ['value' => '09:00-12:00', 'label' => '09:00 - 12:00'],
                ['value' => '12:00-15:00', 'label' => '12:00 - 15:00'],
                ['value' => '15:00-18:00', 'label' => '15:00 - 18:00']
            ];
        }

        return $slots;
    }

    /**
     * Format time string from comma-separated to proper time format
     *
     * @param string $timeString
     * @return string
     */
    private function formatTimeString(string $timeString): string
    {
        // Handle comma-separated format like "00,00,00" or "23,59,59"
        if (strpos($timeString, ',') !== false) {
            $parts = explode(',', $timeString);
            if (count($parts) >= 2) {
                $hours = str_pad(trim($parts[0]), 2, '0', STR_PAD_LEFT);
                $minutes = str_pad(trim($parts[1]), 2, '0', STR_PAD_LEFT);
                $seconds = isset($parts[2]) ? str_pad(trim($parts[2]), 2, '0', STR_PAD_LEFT) : '00';
                return $hours . ':' . $minutes . ':' . $seconds;
            }
        }

        // Handle already formatted time like "09:00" or "09:00:00"
        if (strpos($timeString, ':') !== false) {
            return $timeString;
        }

        // Fallback for invalid format
        return '09:00:00';
    }

    /**
     * Check if delivery instructions should be shown for shipping method
     *
     * @param string $shippingMethod
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabledForShippingMethod(string $shippingMethod, ?int $storeId = null): bool
    {
        if (!$this->isEnabled($storeId)) {
            return false;
        }

        $disabledMethods = $this->getDisabledShippingMethods($storeId);
        return !in_array($shippingMethod, $disabledMethods);
    }
}
