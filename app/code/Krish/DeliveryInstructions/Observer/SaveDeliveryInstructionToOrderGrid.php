<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_DeliveryInstructions
 *  <AUTHOR> Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\DeliveryInstructions\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

/**
 * Observer to save delivery instructions to order grid
 */
class SaveDeliveryInstructionToOrderGrid implements ObserverInterface
{
    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     */
    public function __construct(
        ResourceConnection $resourceConnection,
        LoggerInterface $logger
    ) {
        $this->resourceConnection = $resourceConnection;
        $this->logger = $logger;
    }

    /**
     * Save delivery instructions to order grid
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        try {
            /** @var \Magento\Sales\Model\Order $order */
            $order = $observer->getEvent()->getOrder();

            if (!$order || !$order->getId()) {
                return;
            }

            $connection = $this->resourceConnection->getConnection();
            $gridTable = $this->resourceConnection->getTableName('sales_order_grid');

            // Prepare data for grid update
            $gridData = [];
            
            if ($order->getDeliveryInstruction()) {
                $gridData['delivery_instruction'] = $order->getDeliveryInstruction();
            }
            
            if ($order->getLeaveAtDoor() !== null) {
                $gridData['leave_at_door'] = $order->getLeaveAtDoor();
            }
            
            if ($order->getPreferredDeliveryTime()) {
                $gridData['preferred_delivery_time'] = $order->getPreferredDeliveryTime();
            }

            // Update grid table if we have data to update
            if (!empty($gridData)) {
                $connection->update(
                    $gridTable,
                    $gridData,
                    ['entity_id = ?' => $order->getId()]
                );
            }

        } catch (\Exception $e) {
            $this->logger->error(
                'Error in SaveDeliveryInstructionToOrderGrid observer: ' . $e->getMessage(),
                [
                    'order_id' => $order ? $order->getId() : null,
                    'exception' => $e
                ]
            );
        }
    }
}
