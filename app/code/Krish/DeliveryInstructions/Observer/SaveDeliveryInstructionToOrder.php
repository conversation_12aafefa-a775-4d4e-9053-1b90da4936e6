<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_DeliveryInstructions
 *  <AUTHOR> Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\DeliveryInstructions\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Psr\Log\LoggerInterface;

/**
 * Observer to save delivery instructions to order
 */
class SaveDeliveryInstructionToOrder implements ObserverInterface
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param LoggerInterface $logger
     */
    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    /**
     * Save delivery instructions from quote to order
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        try {
            /** @var \Magento\Sales\Model\Order $order */
            $order = $observer->getEvent()->getOrder();
            /** @var \Magento\Quote\Model\Quote $quote */
            $quote = $observer->getEvent()->getQuote();

            if (!$order || !$quote) {
                return;
            }

            // Transfer delivery instruction
            if ($quote->getDeliveryInstruction()) {
                $order->setDeliveryInstruction($quote->getDeliveryInstruction());
            }

            // Transfer leave at door
            if ($quote->getLeaveAtDoor() !== null) {
                $order->setLeaveAtDoor($quote->getLeaveAtDoor());
            }

            // Transfer preferred delivery time
            if ($quote->getPreferredDeliveryTime()) {
                $order->setPreferredDeliveryTime($quote->getPreferredDeliveryTime());
            }

        } catch (\Exception $e) {
            $this->logger->error(
                'Error in SaveDeliveryInstructionToOrder observer: ' . $e->getMessage(),
                [
                    'quote_id' => $quote ? $quote->getId() : null,
                    'order_id' => $order ? $order->getId() : null,
                    'exception' => $e
                ]
            );
        }
    }
}
