<!--
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ModuleName
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<li id="delivery-instruction" class="checkout-delivery-instruction" data-bind="fadeVisible: isVisible">
    <div class="step-title" data-bind="i18n: 'Delivery Instructions'" data-role="title"></div>
    <div id="checkout-step-delivery-instruction" class="step-content" data-role="content">

        <div class="delivery-instruction-wrapper">
            <div class="delivery-instruction-header">
                <p class="delivery-instruction-description" data-bind="i18n: 'Please provide delivery instructions for your order'"></p>
            </div>

            <!-- ko foreach: getRegion('delivery-instruction-form') -->
                <!-- ko template: getTemplate() --><!-- /ko -->
            <!-- /ko -->

            <div class="actions-toolbar">
                <div class="primary">
                    <button data-role="opc-continue" type="submit" class="button action continue primary"
                            data-bind="click: navigateToNextStep">
                        <span data-bind="i18n: 'Next'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</li>
