<!--
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ModuleName
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<form id="delivery-instruction-form" class="form delivery-instruction-form" data-role="delivery-instruction-form">
    <fieldset class="fieldset delivery-instruction-fieldset">
        <!-- Delivery Instruction Textarea -->
        <div class="field delivery-instruction required" data-bind="css: {'_required': isDeliveryInstructionRequired()}">
            <label class="label" for="delivery-instruction">
                <span data-bind="i18n: 'Delivery Instruction'"></span>
            </label>
            <div class="control">
                <textarea id="delivery-instruction"
                         name="delivery_instruction"
                         class="input-text"
                         rows="4"
                         cols="50"
                         placeholder="Please provide any special delivery instructions..."
                         data-bind="value: deliveryInstruction,
                                   attr: {'aria-required': isDeliveryInstructionRequired()}">
                </textarea>
            </div>
        </div>

        <!-- Leave at Door Checkbox -->
        <div class="field leave-at-door">
            <label class="label" for="leave-at-door">
                <span data-bind="i18n: 'Leave at Door'"></span>
            </label>
            <div class="control">
                <input type="checkbox"
                       id="leave-at-door"
                       name="leave_at_door"
                       class="checkbox"
                       data-bind="checked: leaveAtDoor" />
            </div>
        </div>

        <!-- Preferred Delivery Time Dropdown -->
        <div class="field preferred-delivery-time">
            <label class="label" for="preferred-delivery-time">
                <span data-bind="i18n: 'Preferred Delivery Time'"></span>
            </label>
            <div class="control">
                <select id="preferred-delivery-time"
                        name="preferred_delivery_time"
                        class="select"
                        data-bind="value: preferredDeliveryTime">
                    <option value="" data-bind="i18n: 'Select Time Slot'"></option>
                    <!-- ko foreach: getTimeSlots() -->
                    <option data-bind="value: value, text: label"></option>
                    <!-- /ko -->
                </select>
            </div>
        </div>

    </fieldset>
</form>
