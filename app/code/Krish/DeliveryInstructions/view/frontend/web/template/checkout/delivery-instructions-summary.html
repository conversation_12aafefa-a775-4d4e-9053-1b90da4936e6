<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_DeliveryInstructions
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<!-- ko if: deliveryInstruction() || leaveAtDoor() || preferredDeliveryTime() -->
<div class="checkout-delivery-instructions-summary">
    <div class="delivery-instructions-title">
        <strong data-bind="i18n: 'Delivery Instructions'"></strong>
    </div>
    
    <div class="delivery-instructions-content">
        
        <!-- ko if: deliveryInstruction() -->
        <div class="delivery-instruction-item">
            <span class="label" data-bind="i18n: 'Instructions:'"></span>
            <span class="value" data-bind="text: deliveryInstruction"></span>
        </div>
        <!-- /ko -->

        <!-- ko if: leaveAtDoor() -->
        <div class="delivery-instruction-item">
            <span class="label" data-bind="i18n: 'Leave at Door:'"></span>
            <span class="value leave-at-door-yes" data-bind="i18n: 'Yes'"></span>
        </div>
        <!-- /ko -->

        <!-- ko if: preferredDeliveryTime() -->
        <div class="delivery-instruction-item">
            <span class="label" data-bind="i18n: 'Preferred Time:'"></span>
            <span class="value" data-bind="text: formattedDeliveryTime"></span>
        </div>
        <!-- /ko -->

    </div>
</div>
<!-- /ko -->

<style>
.checkout-delivery-instructions-summary {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    background-color: #fafafa;
}

.checkout-delivery-instructions-summary .delivery-instructions-title {
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
}

.checkout-delivery-instructions-summary .delivery-instructions-content {
    font-size: 13px;
}

.checkout-delivery-instructions-summary .delivery-instruction-item {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
}

.checkout-delivery-instructions-summary .delivery-instruction-item:last-child {
    margin-bottom: 0;
}

.checkout-delivery-instructions-summary .delivery-instruction-item .label {
    font-weight: 600;
    color: #666;
    min-width: 100px;
    margin-right: 10px;
}

.checkout-delivery-instructions-summary .delivery-instruction-item .value {
    color: #333;
    flex: 1;
    word-break: break-word;
}

.checkout-delivery-instructions-summary .delivery-instruction-item .leave-at-door-yes {
    color: #28a745;
    font-weight: 600;
}

@media (max-width: 768px) {
    .checkout-delivery-instructions-summary {
        margin: 10px 0;
        padding: 12px;
    }
    
    .checkout-delivery-instructions-summary .delivery-instruction-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .checkout-delivery-instructions-summary .delivery-instruction-item .label {
        margin-bottom: 4px;
        margin-right: 0;
        min-width: auto;
    }
}
</style>
