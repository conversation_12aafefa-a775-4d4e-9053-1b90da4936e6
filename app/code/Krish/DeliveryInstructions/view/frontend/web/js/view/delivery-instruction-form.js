/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_DeliveryInstructions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

define([
    'ko',
    'uiComponent',
    'Krish_DeliveryInstructions/js/model/delivery-instruction-data'
], function (
    ko,
    Component,
    deliveryInstructionData
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Krish_DeliveryInstructions/delivery-instruction-form'
        },

        /**
         * @returns {*}
         */
        initialize: function () {
            this._super();
            return this;
        },

        /**
         * Initialize observable properties
         *
         * @returns {exports}
         */
        initObservable: function () {
            this._super();

            this.deliveryInstruction = ko.observable(deliveryInstructionData.getDeliveryInstruction());
            this.leaveAtDoor = ko.observable(deliveryInstructionData.getLeaveAtDoor());
            this.preferredDeliveryTime = ko.observable(deliveryInstructionData.getPreferredDeliveryTime());

            // Subscribe to changes and update the data model
            this.deliveryInstruction.subscribe(function (value) {
                deliveryInstructionData.setDeliveryInstruction(value);
            });

            this.leaveAtDoor.subscribe(function (value) {
                deliveryInstructionData.setLeaveAtDoor(value);
            });

            this.preferredDeliveryTime.subscribe(function (value) {
                deliveryInstructionData.setPreferredDeliveryTime(value);
            });

            return this;
        },

        /**
         * Get time slots from configuration
         *
         * @returns {Array}
         */
        getTimeSlots: function () {
            if (window.checkoutConfig && window.checkoutConfig.deliveryInstructions) {
                return window.checkoutConfig.deliveryInstructions.timeSlots || [];
            }
            return [];
        },

        /**
         * Check if delivery instruction is required
         *
         * @returns {boolean}
         */
        isDeliveryInstructionRequired: function () {
            if (window.checkoutConfig && window.checkoutConfig.deliveryInstructions) {
                return window.checkoutConfig.deliveryInstructions.required || false;
            }
            return false;
        },

        /**
         * Get leave at door default value
         *
         * @returns {boolean}
         */
        getLeaveAtDoorDefault: function () {
            if (window.checkoutConfig && window.checkoutConfig.deliveryInstructions) {
                return window.checkoutConfig.deliveryInstructions.leaveAtDoorDefault || false;
            }
            return false;
        }
    });
});
