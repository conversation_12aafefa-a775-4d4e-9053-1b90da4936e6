/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_DeliveryInstructions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

define([
    'ko'
], function (ko) {
    'use strict';

    var deliveryInstruction = ko.observable('');
    var leaveAtDoor = ko.observable(false);
    var preferredDeliveryTime = ko.observable('');

    return {
        /**
         * Get delivery instruction
         *
         * @returns {string}
         */
        getDeliveryInstruction: function () {
            return deliveryInstruction();
        },

        /**
         * Set delivery instruction
         *
         * @param {string} value
         */
        setDeliveryInstruction: function (value) {
            deliveryInstruction(value);
        },

        /**
         * Get leave at door
         *
         * @returns {boolean}
         */
        getLeaveAtDoor: function () {
            return leaveAtDoor();
        },

        /**
         * Set leave at door
         *
         * @param {boolean} value
         */
        setLeaveAtDoor: function (value) {
            leaveAtDoor(value);
        },

        /**
         * Get preferred delivery time
         *
         * @returns {string}
         */
        getPreferredDeliveryTime: function () {
            return preferredDeliveryTime();
        },

        /**
         * Set preferred delivery time
         *
         * @param {string} value
         */
        setPreferredDeliveryTime: function (value) {
            preferredDeliveryTime(value);
        },

        /**
         * Get all delivery instruction data
         *
         * @returns {Object}
         */
        getData: function () {
            return {
                delivery_instruction: deliveryInstruction(),
                leave_at_door: leaveAtDoor(),
                preferred_delivery_time: preferredDeliveryTime()
            };
        },

        /**
         * Clear all delivery instruction data
         */
        clear: function () {
            deliveryInstruction('');
            leaveAtDoor(false);
            preferredDeliveryTime('');
        }
    };
});
