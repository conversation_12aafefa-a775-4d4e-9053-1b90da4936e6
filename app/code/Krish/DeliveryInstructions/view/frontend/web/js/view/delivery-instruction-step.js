/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_ModuleName
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

define([
    'ko',
    'uiComponent',
    'underscore',
    'jquery',
    'Magento_Checkout/js/model/step-navigator',
    'Magento_Checkout/js/model/quote',
    'Krish_DeliveryInstructions/js/model/delivery-instruction-data',
    'Krish_DeliveryInstructions/js/action/save-delivery-instructions'
], function (
    ko,
    Component,
    _,
    $,
    stepNavigator,
    quote,
    deliveryInstructionData,
    saveDeliveryInstructionsAction
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Krish_DeliveryInstructions/delivery-instruction-step',
            stepTitle: 'Delivery Instructions'
        },
        isVisible: ko.observable(true),

        /**
         * @returns {*}
         */
        initialize: function () {
            this._super();
            this.updateVisibility();
            // Register the step after initialization
            // Sort order: shipping=10, delivery-instruction=15, payment=20
            stepNavigator.registerStep(
                'delivery-instruction',
                null,
                this.stepTitle,
                this.isVisible,
                _.bind(this.navigate, this),
                15
            );

            return this;
        },

        updateVisibility: function (checkShipping = false) {
            this.isVisible(this.isStepVisible(checkShipping));
        },

        /**
         * Check if step should be visible
         *
         * @returns {boolean}
         */
        isStepVisible: function (checkShipping = false) {
            // Don't show for virtual products
            if (quote.isVirtual()) {
                return false;
            }

            // Check if module is enabled
            if (!window.checkoutConfig || !window.checkoutConfig.deliveryInstructions || !window.checkoutConfig.deliveryInstructions.enabled) {
                return false;
            }
            if(checkShipping) {
                // Must have shipping address and shipping method selected
                if (!self.hasShippingMethod()) {
                    return false;
                }

                var shippingMethod = window.checkoutConfig.selectedShippingMethod;

                // Check if current shipping method is disabled
                if (shippingMethod) {
                    var methodCode = shippingMethod.carrier_code + '_' + shippingMethod.method_code;
                    var disabledMethods = window.checkoutConfig.deliveryInstructions.disabledShippingMethods || [];

                    if (disabledMethods.indexOf(methodCode) !== -1) {
                        return false;
                    }
                }
            }

            return true;
        },

        /**
         * Navigate method.
         */
        navigate: function () {
            var self = this;

            if (!self.hasShippingMethod()) {
                this.isVisible(false);
                stepNavigator.setHash('shipping');
            } else {
                this.isVisible(self.isStepVisible(true));
            }
        },

        /**
         * @return {Boolean}
         */
        hasShippingMethod: function () {
            return window.checkoutConfig.selectedShippingMethod !== null;
        },

        /**
         * Navigate to next step
         */
        navigateToNextStep: function () {
            // Validate form before proceeding
            if (this.validateDeliveryInstructions()) {
                // Save delivery instructions to quote first
                this.saveDeliveryInstructions().done(function () {
                    stepNavigator.next();
                }).fail(function () {
                    console.error('Failed to save delivery instructions');
                });
            }
        },

        /**
         * Save delivery instructions to quote
         *
         * @returns {*}
         */
        saveDeliveryInstructions: function () {
            var self = this;
            var deliveryData = deliveryInstructionData.getData();

            return saveDeliveryInstructionsAction(deliveryData).done(function () {
                stepNavigator.isProcessed(true);
            });
        },

        /**
         * Validate delivery instructions form
         *
         * @returns {boolean}
         */
        validateDeliveryInstructions: function () {
            var isValid = true;

            // Check if delivery instruction is required
            if (window.checkoutConfig &&
                window.checkoutConfig.deliveryInstructions &&
                window.checkoutConfig.deliveryInstructions.required) {
                var instruction = deliveryInstructionData.getDeliveryInstruction();
                if (!instruction || instruction.trim() === '') {
                    isValid = false;
                    // Show error message
                    console.warn('Delivery instruction is required but not provided');
                }
            }

            return isValid;
        }
    });
});
