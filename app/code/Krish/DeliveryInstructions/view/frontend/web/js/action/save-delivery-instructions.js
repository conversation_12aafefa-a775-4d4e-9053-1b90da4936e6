/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_DeliveryInstructions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

define([
    'jquery',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/model/url-builder',
    'mage/storage',
    'Magento_Checkout/js/model/error-processor',
    'Magento_Customer/js/model/customer',
    'Magento_Checkout/js/model/full-screen-loader'
], function (
    $,
    quote,
    urlBuilder,
    storage,
    errorProcessor,
    customer,
    fullScreenLoader
) {
    'use strict';

    return function (deliveryData) {
        var serviceUrl,
            payload;

        // Build the service URL
        if (customer.isLoggedIn()) {
            serviceUrl = urlBuilder.createUrl('/carts/mine/shipping-information', {});
        } else {
            serviceUrl = urlBuilder.createUrl('/guest-carts/:cartId/shipping-information', {
                cartId: quote.getQuoteId()
            });
        }

        // Build the payload
        payload = {
            addressInformation: {
                shipping_address: quote.shippingAddress(),
                shipping_method_code: quote.shippingMethod().method_code,
                shipping_carrier_code: quote.shippingMethod().carrier_code,
                extension_attributes: {}
            }
        };

        // Add delivery instruction data to extension attributes
        if (deliveryData.delivery_instruction) {
            payload.addressInformation.extension_attributes.delivery_instruction = deliveryData.delivery_instruction;
        }
        
        if (deliveryData.leave_at_door !== null) {
            payload.addressInformation.extension_attributes.leave_at_door = deliveryData.leave_at_door;
        }
        
        if (deliveryData.preferred_delivery_time) {
            payload.addressInformation.extension_attributes.preferred_delivery_time = deliveryData.preferred_delivery_time;
        }

        fullScreenLoader.startLoader();

        return storage.post(
            serviceUrl,
            JSON.stringify(payload)
        ).done(function (response) {
            // Handle successful response
            fullScreenLoader.stopLoader();
        }).fail(function (response) {
            errorProcessor.process(response);
            fullScreenLoader.stopLoader();
        });
    };
});
