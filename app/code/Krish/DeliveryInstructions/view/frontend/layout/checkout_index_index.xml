<?xml version="1.0"?>
<!--
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ModuleName
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="steps" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <!-- Delivery Instructions Step -->
                                        <item name="delivery-instruction-step" xsi:type="array">
                                            <item name="component" xsi:type="string">Krish_DeliveryInstructions/js/view/delivery-instruction-step</item>
                                            <item name="sortOrder" xsi:type="string">15</item>
                                            <item name="deps" xsi:type="array">
                                                <item name="0" xsi:type="string">checkout.steps.shipping-step</item>
                                            </item>
                                            <item name="children" xsi:type="array">
                                                <!-- Delivery Instructions Form -->
                                                <item name="delivery-instruction-form" xsi:type="array">
                                                    <item name="component" xsi:type="string">Krish_DeliveryInstructions/js/view/delivery-instruction-form</item>
                                                    <item name="displayArea" xsi:type="string">delivery-instruction-form</item>
                                                    <item name="sortOrder" xsi:type="string">10</item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
