<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    
    <!-- Extension attributes for Quote -->
    <extension_attributes for="Magento\Quote\Api\Data\CartInterface">
        <attribute code="delivery_instruction" type="string" />
        <attribute code="leave_at_door" type="boolean" />
        <attribute code="preferred_delivery_time" type="string" />
    </extension_attributes>

    <!-- Extension attributes for Shipping Information Interface -->
    <extension_attributes for="Magento\Checkout\Api\Data\ShippingInformationInterface">
        <attribute code="delivery_instruction" type="string" />
        <attribute code="leave_at_door" type="boolean" />
        <attribute code="preferred_delivery_time" type="string" />
    </extension_attributes>

    <!-- Extension attributes for Order -->
    <extension_attributes for="Magento\Sales\Api\Data\OrderInterface">
        <attribute code="delivery_instruction" type="string" />
        <attribute code="leave_at_door" type="boolean" />
        <attribute code="preferred_delivery_time" type="string" />
    </extension_attributes>
</config>
