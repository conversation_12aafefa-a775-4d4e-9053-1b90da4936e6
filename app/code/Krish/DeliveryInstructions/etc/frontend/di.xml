<?xml version="1.0"?>
<!--
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ModuleName
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Add config provider to checkout -->
    <type name="Magento\Checkout\Model\CompositeConfigProvider">
        <arguments>
            <argument name="configProviders" xsi:type="array">
                <item name="delivery_instructions_config_provider" xsi:type="object">Krish\DeliveryInstructions\Model\ConfigProvider</item>
            </argument>
        </arguments>
    </type>
</config>
