<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_DeliveryInstructions
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    
    <!-- Observer to save delivery instructions to order -->
    <event name="sales_model_service_quote_submit_before">
        <observer name="krish_delivery_instructions_save_to_order" instance="Krish\DeliveryInstructions\Observer\SaveDeliveryInstructionToOrder" />
    </event>

    <!-- Observer to save delivery instructions to order grid -->
    <event name="sales_order_save_after">
        <observer name="krish_delivery_instructions_save_to_order_grid" instance="Krish\DeliveryInstructions\Observer\SaveDeliveryInstructionToOrderGrid" />
    </event>

</config>
