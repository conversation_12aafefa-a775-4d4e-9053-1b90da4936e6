<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    
    <!-- Add delivery instruction fields to quote table -->
    <table name="quote" resource="default">
        <column xsi:type="text" name="delivery_instruction" nullable="true" comment="Delivery Instruction"/>
        <column xsi:type="boolean" name="leave_at_door" nullable="true" default="0" comment="Leave At Door"/>
        <column xsi:type="varchar" name="preferred_delivery_time" nullable="true" length="50" comment="Preferred Delivery Time"/>
    </table>

    <!-- Add delivery instruction fields to sales_order table -->
    <table name="sales_order" resource="default">
        <column xsi:type="text" name="delivery_instruction" nullable="true" comment="Delivery Instruction"/>
        <column xsi:type="boolean" name="leave_at_door" nullable="true" default="0" comment="Leave At Door"/>
        <column xsi:type="varchar" name="preferred_delivery_time" nullable="true" length="50" comment="Preferred Delivery Time"/>
    </table>

    <!-- Add delivery instruction fields to sales_order_grid table for admin grid display -->
    <table name="sales_order_grid" resource="default">
        <column xsi:type="text" name="delivery_instruction" nullable="true" comment="Delivery Instruction"/>
        <column xsi:type="boolean" name="leave_at_door" nullable="true" default="0" comment="Leave At Door"/>
        <column xsi:type="varchar" name="preferred_delivery_time" nullable="true" length="50" comment="Preferred Delivery Time"/>
    </table>
</schema>
