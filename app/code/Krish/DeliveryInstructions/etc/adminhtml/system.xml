<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_DeliveryInstructions
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="krish_delivery_instructions" translate="label" type="text" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Delivery Instructions</label>
            <tab>krish</tab>
            <resource>Krish_DeliveryInstructions::config</resource>
            
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Delivery Instructions</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                
                <field id="disabled_shipping_methods" translate="label" type="multiselect" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Disabled for Shipping Methods</label>
                    <comment>Select shipping methods for which delivery instructions should be disabled</comment>
                    <source_model>Magento\Shipping\Model\Config\Source\Allmethods</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="delivery_instruction_required" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Make Delivery Instruction Required</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="leave_at_door_default" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Leave at Door Default Value</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
            
            <group id="time_slots" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Time Slot Settings</label>
                
                <field id="start_time" translate="label" type="time" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Start Time</label>
                    <comment>Delivery time slot start time</comment>
                </field>
                
                <field id="end_time" translate="label" type="time" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>End Time</label>
                    <comment>Delivery time slot end time</comment>
                </field>
                
                <field id="slot_duration" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Slot Duration (Hours)</label>
                    <comment>Duration of each time slot in hours</comment>
                    <validate>validate-number validate-greater-than-zero</validate>
                </field>
                
                <field id="max_delivery_days" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Maximum Delivery Days</label>
                    <comment>Maximum number of days ahead for delivery scheduling</comment>
                    <validate>validate-number validate-greater-than-zero</validate>
                </field>
            </group>
        </section>
    </system>
</config>
