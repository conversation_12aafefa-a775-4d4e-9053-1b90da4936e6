<?php
/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_ModuleName
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\DeliveryInstructions\Model;

use Magento\Checkout\Model\ConfigProviderInterface;
use Krish\DeliveryInstructions\Helper\Data;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Configuration provider for delivery instructions
 */
class ConfigProvider implements ConfigProviderInterface
{
    /**
     * @var Data
     */
    protected $helper;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @param Data $helper
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        Data $helper,
        StoreManagerInterface $storeManager
    ) {
        $this->helper = $helper;
        $this->storeManager = $storeManager;
    }

    /**
     * {@inheritdoc}
     */
    public function getConfig()
    {
        $storeId = $this->storeManager->getStore()->getId();
        return [
            'deliveryInstructions' => [
                'enabled' => $this->helper->isEnabled($storeId),
                'required' => $this->helper->isDeliveryInstructionRequired($storeId),
                'leaveAtDoorDefault' => $this->helper->getLeaveAtDoorDefault($storeId),
                'disabledShippingMethods' => $this->helper->getDisabledShippingMethods($storeId),
                'timeSlots' => $this->helper->generateTimeSlots($storeId),
                'maxDeliveryDays' => $this->helper->getMaxDeliveryDays($storeId)
            ]
        ];
    }
}
