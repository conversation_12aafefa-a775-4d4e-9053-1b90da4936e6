<?php
/*
 *
 * @category   Krish Technolabs Module Development
 * @package    Krish_ForceDisplayProduct
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ForceDisplayProduct\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Class Config
 */
class Config
{
    const XML_PATH_ENABLED = 'krish_force_display_product/general/enabled';
    const XML_PATH_OOS_TO_BOTTOM = 'krish_force_display_product/general/is_out_of_stock_bottom';

    /**
     * @var ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Check if module is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isModuleEnabled(?int $storeId = null): bool
    {
        return (bool) $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Move out of stock products in bottom
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isOutOfStockProductsMoveIntoBottom(?int $storeId = null): bool
    {
        return (bool) $this->scopeConfig->isSetFlag(
            self::XML_PATH_OOS_TO_BOTTOM,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
