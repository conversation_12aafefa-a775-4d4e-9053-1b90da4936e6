<?php
/*
 *
 * @category   Krish Technolabs Module Development
 * @package    Krish_ForceDisplayProduct
 * <AUTHOR> Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ForceDisplayProduct\Model;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\EntityManager\MetadataPool;

/**
 * Class AttributeFinder
 */
class AttributeFinder
{
    /**
     * @var ResourceConnection
     */
    private ResourceConnection $resource;

    /**
     * @var \Magento\Framework\DB\Adapter\AdapterInterface
     */
    private \Magento\Framework\DB\Adapter\AdapterInterface $connection;

    /**
     * @var \Magento\Framework\EntityManager\EntityMetadata|\Magento\Framework\EntityManager\EntityMetadataInterface
     */
    private \Magento\Framework\EntityManager\EntityMetadata|\Magento\Framework\EntityManager\EntityMetadataInterface $metadata;

    /**
     * @var AttributeRepositoryInterface
     */
    private AttributeRepositoryInterface $attributeRepository;

    /**
     * @param ResourceConnection $resource
     * @param MetadataPool $metadataPool
     * @param AttributeRepositoryInterface $attributeRepository
     * @throws \Exception
     */
    public function __construct(
        ResourceConnection $resource,
        MetadataPool $metadataPool,
        AttributeRepositoryInterface $attributeRepository
    ) {
        $this->resource = $resource;
        $this->connection = $resource->getConnection();
        $this->metadata = $metadataPool->getMetadata(ProductInterface::class);
        $this->attributeRepository = $attributeRepository;
    }

    /**
     * @param array $sku
     * @param string $attributeCode
     * @param int $storeId
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getAttributeValueBySkus(array $skus, string $attributeCode, int $storeId)
    {
        $linkField = $this->metadata->getLinkField();
        $attribute = $this->attributeRepository->get(Product::ENTITY, $attributeCode);
        $attributeId = (int) $attribute->getAttributeId();
        $attributeTable = $this->resource->getTableName('catalog_product_entity_' . $attribute->getBackendType());
        $productTable = $this->resource->getTableName('catalog_product_entity');

        $select = $this->connection->select()
            ->from(['cpe' => $productTable], ['sku'])
            ->join(
                ['default_val' => $attributeTable],
                'cpe.' . $linkField . ' = default_val.'.$linkField.' AND default_val.attribute_id = ' . $attributeId . ' AND default_val.store_id = 0',
                []
            )
            ->joinLeft(
                ['store_val' => $attributeTable],
                'cpe.' . $linkField . ' = store_val.'.$linkField.' AND store_val.attribute_id = ' . $attributeId . ' AND store_val.store_id = ' . $storeId,
                []
            )
            ->columns(['value' => new \Zend_Db_Expr('COALESCE(store_val.value, default_val.value)')])
            ->where('cpe.sku IN (?)', $skus);

        return $this->connection->fetchAssoc($select);
    }
}
