<?xml version="1.0"?>
<!--
  ~
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ForceDisplayProduct
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<requests xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Search/etc/search_request.xsd">
    <request query="quick_search_container" index="catalogsearch_fulltext">
        <queries>
            <query xsi:type="boolQuery" name="quick_search_container" boost="1">
                <queryReference clause="must" ref="is_force_display"/>
            </query>
            <query xsi:type="filteredQuery" name="is_force_display">
                <filterReference clause="must" ref="is_force_display_filter"/>
            </query>
        </queries>
        <filters>
            <filter xsi:type="termFilter" name="is_force_display_filter" field="is_force_display" value="$is_force_display$"/>
        </filters>
    </request>
    <request query="advanced_search_container" index="catalogsearch_fulltext">
        <queries>
            <query xsi:type="boolQuery" name="advanced_search_container" boost="1">
                <queryReference clause="must" ref="is_force_display"/>
            </query>
            <query xsi:type="filteredQuery" name="is_force_display">
                <filterReference clause="must" ref="is_force_display_filter"/>
            </query>
        </queries>
        <filters>
            <filter xsi:type="termFilter" name="is_force_display_filter" field="is_force_display" value="$is_force_display$"/>
        </filters>
    </request>
    <request query="catalog_view_container" index="catalogsearch_fulltext">
        <queries>
            <query xsi:type="boolQuery" name="catalog_view_container" boost="1">
                <queryReference clause="must" ref="is_force_display"/>
            </query>
            <query xsi:type="filteredQuery" name="is_force_display">
                <filterReference clause="must" ref="is_force_display_filter"/>
            </query>
        </queries>
        <filters>
            <filter xsi:type="termFilter" name="is_force_display_filter" field="is_force_display" value="$is_force_display$"/>
        </filters>
    </request>
</requests>
