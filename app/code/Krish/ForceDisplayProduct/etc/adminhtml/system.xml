<?xml version="1.0"?>
<!--
  ~
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ForceDisplayProduct
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="krish_force_display_product" translate="label" type="text" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Force Display Product</label>
            <tab>krish</tab>
            <resource>Krish_ForceDisplayProduct::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Configuration</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Module</label>
                    <frontend_class>on-off-trigger</frontend_class>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or disable the Force Display Product module</comment>
                </field>
                <field id="is_out_of_stock_bottom" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Move Out Of Stock Products In Bottom</label>
                    <frontend_class>on-off-trigger</frontend_class>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or disable the Force Display Product module</comment>
                </field>
            </group>
        </section>
    </system>
</config>
