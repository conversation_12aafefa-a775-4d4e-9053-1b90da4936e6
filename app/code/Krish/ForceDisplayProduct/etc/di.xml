<?xml version="1.0"?>
<!--
  ~
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ForceDisplayProduct
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Force Enabled OOS Product Display On Frontend -->
    <type name="Magento\Framework\App\Config\ScopeConfigInterface">
        <plugin name="forceEnabledShowOutOfStockProductsConfig" type="Krish\ForceDisplayProduct\Plugin\Framework\App\Config\ScopeConfigInterface"/>
    </type>
    <!-- Add is_force_display virtual field in the search engine -->
    <type name="Magento\Elasticsearch\ElasticAdapter\Model\Adapter\FieldMapper\ProductFieldMapper">
        <plugin name="addForceDisplayAttributeMapper" type="Krish\ForceDisplayProduct\Plugin\Elasticsearch\Model\Adapter\FieldMapper\FieldMapperPlugin"/>
    </type>
    <!-- Push is_force_display value dynamically in the search engine -->
    <type name="Magento\Elasticsearch\Model\Adapter\BatchDataMapper\ProductDataMapper">
        <plugin name="addForceDisplayProductDataMapper" type="Krish\ForceDisplayProduct\Plugin\Elasticsearch\Model\Adapter\BatchDataMapper\ProductDataMapperPlugin"/>
    </type>
    <!-- Filter is_force_display true on product collection -->
    <type name="Magento\Catalog\Model\Layer\ItemCollectionProviderInterface">
        <plugin name="addForceDisplayFilterItemCollectionProvider" type="Krish\ForceDisplayProduct\Plugin\Catalog\Model\Layer\ItemCollectionProvider" />
    </type>
    <!-- Move OOS products in to the bottom -->
    <type name="Magento\Catalog\Model\ResourceModel\Product\Collection">
        <plugin name="moveAllOutOfStockProductsInBottom" type="Krish\ForceDisplayProduct\Plugin\Catalog\Model\ResourceModel\Product\CollectionPlugin"/>
    </type>
</config>
