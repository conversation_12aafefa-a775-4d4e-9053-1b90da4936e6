<?php
/*
 * @category   <PERSON>h Technolabs Module Development
 * @package    Krish_ForceDisplayProduct
 * <AUTHOR> Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ForceDisplayProduct\Plugin\Elasticsearch\Model\Adapter\BatchDataMapper;

use Krish\ForceDisplayProduct\Model\AttributeFinder;
use Krish\ForceDisplayProduct\Model\Config;
use Magento\Elasticsearch\Model\Adapter\BatchDataMapper\ProductDataMapper;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\InventoryCatalogApi\Model\GetSkusByProductIdsInterface;

/**
 * Class ProductDataMapperPlugin
 */
class ProductDataMapperPlugin
{
    /**
     * @var GetSkusByProductIdsInterface
     */
    private GetSkusByProductIdsInterface $getSkusByProductIds;

    /**
     * @var Config
     */
    private Config $config;

    /**
     * @var AttributeFinder
     */
    private AttributeFinder $attributeFinder;

    /**
     * @param GetSkusByProductIdsInterface $getSkusByProductIds
     * @param Config $config
     * @param AttributeFinder $attributeFinder
     */
    public function __construct(
        GetSkusByProductIdsInterface $getSkusByProductIds,
        Config $config,
        AttributeFinder $attributeFinder,
    ) {
        $this->getSkusByProductIds = $getSkusByProductIds;
        $this->config = $config;
        $this->attributeFinder = $attributeFinder;
    }
    /**
     * Map more attributes
     *
     * @param ProductDataMapper $subject
     * @param array|mixed $documents
     * @param mixed $documentData
     * @param mixed $storeId
     * @return array
     * @throws NoSuchEntityException|\Magento\Framework\Exception\LocalizedException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     * @noinspection PhpUnusedParameterInspection
     */
    public function afterMap(
        ProductDataMapper $subject,
        array $documents,
        array $documentData,
        int $storeId
    ): array {
        if (!$this->config->isModuleEnabled()) {
            return $documents;
        }
        $skus = $this->getSkusByProductIds->execute(array_keys($documents));
        $forceDisplayValues = $this->attributeFinder->getAttributeValueBySkus(
            $skus,
            'force_display',
            $storeId
        );
        foreach ($documents as $productId => $document) {
            $sku = $skus[$productId];
            $document['is_force_display'] = 1;
            if (isset($document['is_out_of_stock']) && (int) $document['is_out_of_stock'] !== (int) 1) {
                $document['is_force_display'] = 0;
                if (isset($forceDisplayValues[$sku]) && isset($forceDisplayValues[$sku]['value'])) {
                    $document['is_force_display'] = (int) $forceDisplayValues[$sku]['value'];
                }
            }
            $documents[$productId] = $document;
        }

        return $documents;
    }
}
