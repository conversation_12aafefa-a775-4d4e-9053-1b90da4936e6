<?php /*
 * @category   <PERSON>h Technolabs Module Development
 * @package    Krish_ForceDisplayProduct
 * <AUTHOR> Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */ /** @noinspection PhpUnusedParameterInspection */
declare(strict_types=1);

namespace Krish\ForceDisplayProduct\Plugin\Elasticsearch\Model\Adapter\FieldMapper;

use Krish\ForceDisplayProduct\Model\Config;
use Magento\Elasticsearch\ElasticAdapter\Model\Adapter\FieldMapper\ProductFieldMapper;

/**
 * Class FieldMapperPlugin for es attributes mapping
 */
class FieldMapperPlugin
{
    /**
     * @var array
     */
    private $allowedFields = [
        'is_force_display' => 'integer'
    ];

    /**
     * @var Config
     */
    private Config $config;

    /**
     * @param Config $config
     */
    public function __construct(
        Config $config
    ) {
        $this->config = $config;
    }

    /**
     * Missing mapped attribute code
     *
     * @param ProductFieldMapper $subject
     * @param array $result
     * @param array $context
     * @return array
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetAllAttributesTypes(
        ProductFieldMapper $subject,
        array $result,
        array $context
    ): array {
        if (!$this->config->isModuleEnabled()) {
            return $result;
        }
        foreach ($this->allowedFields as $fieldName => $fieldType) {
            $result[$fieldName] = ['type' => $fieldType];
        }

        return $result;
    }
}
