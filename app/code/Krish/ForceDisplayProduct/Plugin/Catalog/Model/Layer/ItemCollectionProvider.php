<?php
/*
 *
 * @category   Krish Technolabs Module Development
 * @package    Krish_ForceDisplayProduct
 * <AUTHOR> Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ForceDisplayProduct\Plugin\Catalog\Model\Layer;

use Krish\ForceDisplayProduct\Model\Config;
use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\Layer\ItemCollectionProviderInterface;
use Magento\Catalog\Model\ResourceModel\Product\Collection;

/**
 * Class ItemCollectionProvider
 */
class ItemCollectionProvider
{
    /**
     * @var Config
     */
    private Config $config;

    /**
     * AdditionalFieldMapperPlugin constructor.
     * @param Config $config
     */
    public function __construct(
        Config $config
    ) {
        $this->config = $config;
    }

    /**
     * @param ItemCollectionProviderInterface $subject
     * @param Collection $collection
     * @param Category $category
     * @return Collection
     * @suppressWarnings(PHPMD.UnusedFormalParameter)
     * @noinspection PhpUnusedParameterInspection
     */
    public function afterGetCollection(
        ItemCollectionProviderInterface $subject,
        Collection $collection,
        Category $category
    ) {
        if ($this->config->isModuleEnabled()) {
            $collection->addFieldToFilter('is_force_display', (int) 1);
        }
        return $collection;
    }
}
