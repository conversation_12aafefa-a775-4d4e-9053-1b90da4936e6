<?php
/*
 *
 * @category   Krish Technolabs Module Development
 * @package    Krish_ForceDisplayProduct
 * <AUTHOR> Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ForceDisplayProduct\Plugin\Catalog\Model\ResourceModel\Product;

use Krish\ForceDisplayProduct\Model\Config;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\DB\Select;
use Magento\InventoryCatalogApi\Model\SortableBySaleabilityInterface;

/**
 * Collection plugin applying sort order
 */
class CollectionPlugin
{
    /**
     * @var Config
     */
    private Config $config;

    /**
     * @var SortableBySaleabilityInterface
     */
    private SortableBySaleabilityInterface $sortableBySaleabilityProvider;

    /**
     * @param Config $config
     */
    public function __construct(
        Config $config,
        SortableBySaleabilityInterface $sortableBySaleabilityProvider
    ) {
        $this->config = $config;
        $this->sortableBySaleabilityProvider = $sortableBySaleabilityProvider;
    }

    /**
     * Setting order and determine flags
     *
     * @param Collection $subject
     * @param mixed $attribute
     * @param string $dir
     * @return array
     * */
    public function beforeSetOrder(
        Collection $subject,
        mixed $attribute,
        string $dir = Select::SQL_DESC
    ): array {
        $this->applyOutOfStockSortOrders($subject);
        return [$attribute, $dir];
    }

    /**
     * Apply sort orders
     *
     * @param Collection $collection
     * @return void
     */
    private function applyOutOfStockSortOrders(Collection $collection): void
    {
        if ($this->config->isModuleEnabled()) {
            $collection->setFlag('is_processing_data', true);
            if (!$collection->getFlag('oos_moved_bottom')) {
                $collection->setFlag('oos_moved_bottom', true);
                if ($this->config->isOutOfStockProductsMoveIntoBottom() && $this->sortableBySaleabilityProvider->isSortableBySaleability()) {
                    $collection->setOrder(SortableBySaleabilityInterface::IS_OUT_OF_STOCK, Select::SQL_DESC);
                }
            }
            $collection->setFlag('is_processing_data', false);
        }
    }

    /**
     * Determine and set order if necessary
     *
     * @param Collection $subject
     * @param mixed $attribute
     * @param string $dir
     * @return array
     */
    public function beforeAddOrder(
        Collection $subject,
        mixed $attribute,
        string $dir = Select::SQL_DESC
    ): array {
        if (!$subject->getFlag('is_processing_data')) {
            $this->applyOutOfStockSortOrders($subject);
        }
        return [$attribute, $dir];
    }
}
