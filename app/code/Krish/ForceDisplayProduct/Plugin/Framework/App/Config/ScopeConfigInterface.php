<?php
/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_ForceDisplayProduct
 * <AUTHOR> Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ForceDisplayProduct\Plugin\Framework\App\Config;

use Krish\ForceDisplayProduct\Model\Config;
use Magento\CatalogInventory\Model\Configuration;
use Magento\Framework\App\Config\ScopeConfigInterface as NativeScopeConfigInterface;

/**
 * Class ScopeConfigInterface
 */
class ScopeConfigInterface
{
    /**
     * @var Config
     */
    private Config $config;

    /**
     * @param Config $config
     */
    public function __construct(
        Config $config
    ) {
        $this->config = $config;
    }

    /**
     * Force display out of stock products
     * @param NativeScopeConfigInterface $subject
     * @param $results
     * @param $path
     * @param $scopeType
     * @param $scopeCode
     * @return mixed
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     * @noinspection PhpUnusedParameterInspection
     */
    public function afterGetValue(
        NativeScopeConfigInterface $subject,
        $results,
        $path,
        $scopeType = 'default',
        $scopeCode = null
    ) {
        if ($path == Configuration::XML_PATH_SHOW_OUT_OF_STOCK && $this->config->isModuleEnabled()) {
            $results = true;
        }
        return $results;
    }
}
