<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Api\Data;

/**
 * Corporate Account Request Interface
 */
interface CorporateAccountRequestInterface
{
    public const ENTITY_ID = 'entity_id';
    public const CUSTOMER_ID = 'customer_id';
    public const COMPANY_NAME = 'company_name';
    public const COMPANY_TYPE = 'company_type';
    public const GST_NUMBER = 'gst_number';
    public const BUSINESS_EMAIL = 'business_email';
    public const BUSINESS_PHONE = 'business_phone';
    public const UPLOADED_DOCUMENT = 'uploaded_document';
    public const STATUS = 'status';
    public const APPROVED_BY = 'approved_by';
    public const CREATED_AT = 'created_at';
    public const UPDATED_AT = 'updated_at';
    public const PROCESSED_AT = 'processed_at';

    // Status constants
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_DECLINED = 'declined';

    /**
     * Get Entity ID
     *
     * @return int|null
     */
    public function getEntityId();

    /**
     * Set Entity ID
     *
     * @param int $entityId
     * @return $this
     */
    public function setEntityId($entityId);

    /**
     * Get Customer ID
     *
     * @return int
     */
    public function getCustomerId();

    /**
     * Set Customer ID
     *
     * @param int $customerId
     * @return $this
     */
    public function setCustomerId($customerId);

    /**
     * Get Company Name
     *
     * @return string
     */
    public function getCompanyName();

    /**
     * Set Company Name
     *
     * @param string $companyName
     * @return $this
     */
    public function setCompanyName($companyName);

    /**
     * Get Company Type
     *
     * @return string
     */
    public function getCompanyType();

    /**
     * Set Company Type
     *
     * @param string $companyType
     * @return $this
     */
    public function setCompanyType($companyType);

    /**
     * Get GST Number
     *
     * @return string|null
     */
    public function getGstNumber();

    /**
     * Set GST Number
     *
     * @param string|null $gstNumber
     * @return $this
     */
    public function setGstNumber($gstNumber);

    /**
     * Get Business Email
     *
     * @return string
     */
    public function getBusinessEmail();

    /**
     * Set Business Email
     *
     * @param string $businessEmail
     * @return $this
     */
    public function setBusinessEmail($businessEmail);

    /**
     * Get Business Phone
     *
     * @return string
     */
    public function getBusinessPhone();

    /**
     * Set Business Phone
     *
     * @param string $businessPhone
     * @return $this
     */
    public function setBusinessPhone($businessPhone);

    /**
     * Get Uploaded Document
     *
     * @return string|null
     */
    public function getUploadedDocument();

    /**
     * Set Uploaded Document
     *
     * @param string|null $uploadedDocument
     * @return $this
     */
    public function setUploadedDocument($uploadedDocument);

    /**
     * Get Status
     *
     * @return string
     */
    public function getStatus();

    /**
     * Set Status
     *
     * @param string $status
     * @return $this
     */
    public function setStatus($status);



    /**
     * Get Approved By
     *
     * @return int|null
     */
    public function getApprovedBy();

    /**
     * Set Approved By
     *
     * @param int|null $approvedBy
     * @return $this
     */
    public function setApprovedBy($approvedBy);

    /**
     * Get Created At
     *
     * @return string
     */
    public function getCreatedAt();

    /**
     * Set Created At
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt($createdAt);

    /**
     * Get Updated At
     *
     * @return string
     */
    public function getUpdatedAt();

    /**
     * Set Updated At
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt($updatedAt);

    /**
     * Get Processed At
     *
     * @return string|null
     */
    public function getProcessedAt();

    /**
     * Set Processed At
     *
     * @param string|null $processedAt
     * @return $this
     */
    public function setProcessedAt($processedAt);
}
