<?php
/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_CorporateAccount
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Api;

use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Corporate Account Request Repository Interface
 */
interface CorporateAccountRequestRepositoryInterface
{
    /**
     * Save Corporate Account Request
     *
     * @param CorporateAccountRequestInterface $corporateAccountRequest
     * @return CorporateAccountRequestInterface
     * @throws LocalizedException
     */
    public function save(CorporateAccountRequestInterface $corporateAccountRequest);

    /**
     * Get Corporate Account Request by ID
     *
     * @param int $entityId
     * @return CorporateAccountRequestInterface
     * @throws NoSuchEntityException
     */
    public function getById($entityId);

    /**
     * Get Corporate Account Request by Customer ID
     *
     * @param int $customerId
     * @return CorporateAccountRequestInterface
     * @throws NoSuchEntityException
     */
    public function getByCustomerId($customerId);

    /**
     * Get list of Corporate Account Requests
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return \Magento\Framework\Api\SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria);

    /**
     * Delete Corporate Account Request
     *
     * @param CorporateAccountRequestInterface $corporateAccountRequest
     * @return bool
     * @throws LocalizedException
     */
    public function delete(CorporateAccountRequestInterface $corporateAccountRequest);

    /**
     * Delete Corporate Account Request by ID
     *
     * @param int $entityId
     * @return bool
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById($entityId);

    /**
     * Approve Corporate Account Request
     *
     * @param int $entityId
     * @param int $adminUserId
     * @return CorporateAccountRequestInterface
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function approve($entityId, $adminUserId = null);

    /**
     * Decline Corporate Account Request
     *
     * @param int $entityId
     * @param int $adminUserId
     * @return CorporateAccountRequestInterface
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function decline($entityId, $adminUserId = null);
}
