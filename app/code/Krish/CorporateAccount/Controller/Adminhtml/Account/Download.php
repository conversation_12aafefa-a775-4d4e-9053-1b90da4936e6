<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Controller\Adminhtml\Account;

use Krish\CorporateAccount\Api\CorporateAccountRequestRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem;

/**
 * Download Corporate Account Document Controller
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class Download extends Action implements HttpGetActionInterface
{
    /**
     * Authorization level of a basic admin session
     */
    public const ADMIN_RESOURCE = 'Krish_CorporateAccount::corporate_account_view';

    /**
     * @var CorporateAccountRequestRepositoryInterface
     */
    private $corporateAccountRequestRepository;

    /**
     * @var FileFactory
     */
    private $fileFactory;

    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * Constructor
     *
     * @param Context $context
     * @param CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository
     * @param FileFactory $fileFactory
     * @param Filesystem $filesystem
     */
    public function __construct(
        Context $context,
        CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository,
        FileFactory $fileFactory,
        Filesystem $filesystem
    ) {
        parent::__construct($context);
        $this->corporateAccountRequestRepository = $corporateAccountRequestRepository;
        $this->fileFactory = $fileFactory;
        $this->filesystem = $filesystem;
    }

    /**
     * Execute action
     *
     * @return ResultInterface
     */
    public function execute()
    {
        $id = $this->getRequest()->getParam('id');

        if (!$id) {
            $this->messageManager->addErrorMessage(__('Corporate account request ID is required.'));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('*/*/index');
        }

        try {
            $corporateRequest = $this->corporateAccountRequestRepository->getById($id);
            $documentPath = $corporateRequest->getUploadedDocument();

            if (!$documentPath) {
                $this->messageManager->addErrorMessage(__('No document found for this request.'));
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/view', ['id' => $id]);
            }

            $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
            $filePath = $mediaDirectory->getAbsolutePath($documentPath);

            if (!$mediaDirectory->isFile($documentPath)) {
                $this->messageManager->addErrorMessage(__('Document file not found.'));
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/view', ['id' => $id]);
            }

            $fileName = basename($documentPath);
            $contentType = $this->getContentType($fileName);

            return $this->fileFactory->create(
                $fileName,
                [
                    'type' => 'filename',
                    'value' => $filePath
                ],
                DirectoryList::MEDIA,
                $contentType
            );

        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__('Corporate account request not found.'));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('*/*/index');
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while downloading the document.'));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('*/*/index');
        }
    }

    /**
     * Get content type based on file extension
     *
     * @param string $fileName
     * @return string
     */
    private function getContentType($fileName)
    {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        switch ($extension) {
            case 'pdf':
                return 'application/pdf';
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'png':
                return 'image/png';
            default:
                return 'application/octet-stream';
        }
    }
}
