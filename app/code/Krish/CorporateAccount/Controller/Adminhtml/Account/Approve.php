<?php
/*
 * @category   <PERSON>h Technolabs Module Development
 * @package    Krish_CorporateAccount
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Controller\Adminhtml\Account;

use Krish\CorporateAccount\Api\CorporateAccountRequestRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Corporate Account Approve Controller
 */
class Approve extends Action implements HttpGetActionInterface
{
    /**
     * Authorization level of a basic admin session
     */
    public const ADMIN_RESOURCE = 'Krish_CorporateAccount::corporate_account_approve';

    /**
     * @var CorporateAccountRequestRepositoryInterface
     */
    private $corporateAccountRequestRepository;

    /**
     * @var Session
     */
    private $authSession;

    /**
     * @var ManagerInterface
     */
    private $eventManager;

    /**
     * Constructor
     *
     * @param Context $context
     * @param CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository
     * @param Session $authSession
     * @param ManagerInterface $eventManager
     */
    public function __construct(
        Context $context,
        CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository,
        Session $authSession,
        ManagerInterface $eventManager
    ) {
        parent::__construct($context);
        $this->corporateAccountRequestRepository = $corporateAccountRequestRepository;
        $this->authSession = $authSession;
        $this->eventManager = $eventManager;
    }

    /**
     * Execute action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setPath('corporate/account/index');

        $id = $this->getRequest()->getParam('id');
        if (!$id) {
            $this->messageManager->addErrorMessage(__('Invalid request ID.'));
            return $resultRedirect;
        }

        try {
            $adminUserId = $this->authSession->getUser()->getId();

            $corporateRequest = $this->corporateAccountRequestRepository->approve($id, $adminUserId);

            // Trigger approval email event
            $this->eventManager->dispatch('krish_corporate_account_request_approved', [
                'corporate_request' => $corporateRequest
            ]);

            $this->messageManager->addSuccessMessage(
                __('Corporate account request has been approved successfully.')
            );

        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__('Corporate account request not found.'));
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(
                __('Something went wrong while approving the request.')
            );
        }

        return $resultRedirect;
    }
}
