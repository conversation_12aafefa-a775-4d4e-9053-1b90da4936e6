<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Controller\Adminhtml\Account;

use Krish\CorporateAccount\Api\CorporateAccountRequestRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Result\PageFactory;

/**
 * View Corporate Account Request Controller
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class View extends Action implements HttpGetActionInterface
{
    /**
     * Authorization level of a basic admin session
     */
    public const ADMIN_RESOURCE = 'Krish_CorporateAccount::corporate_account_view';

    /**
     * @var PageFactory
     */
    private $resultPageFactory;

    /**
     * @var CorporateAccountRequestRepositoryInterface
     */
    private $corporateAccountRequestRepository;

    /**
     * Constructor
     *
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->corporateAccountRequestRepository = $corporateAccountRequestRepository;
    }

    /**
     * Execute action
     *
     * @return ResultInterface
     */
    public function execute()
    {
        $id = $this->getRequest()->getParam('id');

        if (!$id) {
            $this->messageManager->addErrorMessage(__('Corporate account request ID is required.'));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('*/*/index');
        }

        try {
            $corporateRequest = $this->corporateAccountRequestRepository->getById($id);

            // Create result page
            $resultPage = $this->resultPageFactory->create();
            $resultPage->setActiveMenu('Krish_CorporateAccount::corporate_account');
            $resultPage->getConfig()->getTitle()->prepend(__('Corporate Account Requests'));
            $resultPage->getConfig()->getTitle()->prepend(
                __('Request #%1 - %2', $corporateRequest->getEntityId(), $corporateRequest->getCompanyName())
            );

            return $resultPage;

        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__('Corporate account request not found.'));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('*/*/index');
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while loading the request.'));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('*/*/index');
        }
    }
}
