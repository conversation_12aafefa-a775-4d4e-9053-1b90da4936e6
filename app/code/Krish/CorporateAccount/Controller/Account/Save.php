<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Controller\Account;

use Krish\CorporateAccount\Api\CorporateAccountRequestRepositoryInterface;
use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterfaceFactory;
use Krish\CorporateAccount\Model\FileUploader;
use Krish\CorporateAccount\Model\EmailService;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterfaceFactory;
use Magento\Customer\Model\CustomerExtractor;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Message\ManagerInterface as MessageManagerInterface;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Corporate Account Save Controller
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @SuppressWarnings(PHPMD.TooManyFields)
 * @SuppressWarnings(PHPMD.ExcessiveClassComplexity)
 * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
 */
class Save implements HttpPostActionInterface
{
    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var RedirectFactory
     */
    private $resultRedirectFactory;

    /**
     * @var MessageManagerInterface
     */
    private $messageManager;

    /**
     * @var Validator
     */
    private $formKeyValidator;

    /**
     * @var Session
     */
    private $customerSession;

    /**
     * @var AccountManagementInterface
     */
    private $accountManagement;

    /**
     * @var CustomerExtractor
     */
    private $customerExtractor;

    /**
     * @var CorporateAccountRequestRepositoryInterface
     */
    private $corporateAccountRequestRepository;

    /**
     * @var CorporateAccountRequestInterfaceFactory
     */
    private $corporateAccountRequestFactory;

    /**
     * @var FileUploader
     */
    private $fileUploader;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var EventManagerInterface
     */
    private $eventManager;

    /**
     * Constructor
     *
     * @param RequestInterface $request
     * @param RedirectFactory $resultRedirectFactory
     * @param MessageManagerInterface $messageManager
     * @param Validator $formKeyValidator
     * @param Session $customerSession
     * @param AccountManagementInterface $accountManagement
     * @param CustomerExtractor $customerExtractor
     * @param CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository
     * @param CorporateAccountRequestInterfaceFactory $corporateAccountRequestFactory
     * @param FileUploader $fileUploader
     * @param StoreManagerInterface $storeManager
     * @param EventManagerInterface $eventManager
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        RequestInterface $request,
        RedirectFactory $resultRedirectFactory,
        MessageManagerInterface $messageManager,
        Validator $formKeyValidator,
        Session $customerSession,
        AccountManagementInterface $accountManagement,
        CustomerExtractor $customerExtractor,
        CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository,
        CorporateAccountRequestInterfaceFactory $corporateAccountRequestFactory,
        FileUploader $fileUploader,
        StoreManagerInterface $storeManager,
        EventManagerInterface $eventManager
    ) {
        $this->request = $request;
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->messageManager = $messageManager;
        $this->formKeyValidator = $formKeyValidator;
        $this->customerSession = $customerSession;
        $this->accountManagement = $accountManagement;
        $this->customerExtractor = $customerExtractor;
        $this->corporateAccountRequestRepository = $corporateAccountRequestRepository;
        $this->corporateAccountRequestFactory = $corporateAccountRequestFactory;
        $this->fileUploader = $fileUploader;
        $this->storeManager = $storeManager;
        $this->eventManager = $eventManager;
    }

    /**
     * Execute action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();

        // Redirect logged-in customers
        if ($this->customerSession->isLoggedIn()) {
            $resultRedirect->setPath('customer/account');
            return $resultRedirect;
        }

        // Validate form key
        if (!$this->formKeyValidator->validate($this->request)) {
            $this->messageManager->addErrorMessage(__('Invalid form key. Please try again.'));
            $resultRedirect->setPath('corporate/account/register');
            return $resultRedirect;
        }

        if (!$this->request->isPost()) {
            $resultRedirect->setPath('corporate/account/register');
            return $resultRedirect;
        }

        try {
            // Extract customer data and create customer account
            $customer = $this->customerExtractor->extract('customer_account_create', $this->request);
            $customer->setStoreId($this->storeManager->getStore()->getId());

            $password = $this->request->getParam('password');
            $confirmation = $this->request->getParam('password_confirmation');

            if ($password !== $confirmation) {
                throw new LocalizedException(__('Please make sure your passwords match.'));
            }

            // Create customer account
            $customer = $this->accountManagement->createAccount($customer, $password);

            // Handle file upload
            $uploadedDocument = null;
            try {
                $uploadedDocument = $this->fileUploader->uploadFile('corporate_document');
            } catch (\Exception $e) {
                // File upload is optional, continue without it
            }

            // Create corporate account request
            $corporateRequest = $this->corporateAccountRequestFactory->create();
            $corporateRequest->setCustomerId($customer->getId());
            $corporateRequest->setCompanyName($this->request->getParam('company_name'));
            $corporateRequest->setCompanyType($this->request->getParam('company_type'));
            $corporateRequest->setGstNumber($this->request->getParam('gst_number'));
            $corporateRequest->setBusinessEmail($this->request->getParam('business_email'));
            $corporateRequest->setBusinessPhone($this->request->getParam('business_phone'));
            $corporateRequest->setUploadedDocument($uploadedDocument);
            $corporateRequest->setStatus(CorporateAccountRequestInterface::STATUS_PENDING);

            $this->corporateAccountRequestRepository->save($corporateRequest);

            // Trigger email events
            $this->eventManager->dispatch('krish_corporate_account_request_submitted', [
                'corporate_request' => $corporateRequest
            ]);

            $this->messageManager->addSuccessMessage(
                __('Your corporate account request has been submitted successfully. You will receive an email notification once it is reviewed.')
            );

            $resultRedirect->setPath('customer/account/login');

        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $resultRedirect->setPath('corporate/account/register');
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(
                __('Something went wrong while saving your request. Please try again.')
            );
            $resultRedirect->setPath('corporate/account/register');
        }

        return $resultRedirect;
    }
}
