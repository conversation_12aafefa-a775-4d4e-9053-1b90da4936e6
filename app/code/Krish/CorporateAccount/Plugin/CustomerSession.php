<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Plugin;

use Krish\CorporateAccount\Helper\Data as CorporateHelper;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Customer Session Plugin
 */
class CustomerSession
{
    /**
     * @var CorporateHelper
     */
    private $corporateHelper;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Constructor
     *
     * @param CorporateHelper $corporateHelper
     * @param LoggerInterface $logger
     */
    public function __construct(
        CorporateHelper $corporateHelper,
        LoggerInterface $logger
    ) {
        $this->corporateHelper = $corporateHelper;
        $this->logger = $logger;
    }

    /**
     * Plugin to check corporate account status before setting customer data as logged in
     *
     * @param Session $subject
     * @param CustomerInterface $customer
     * @return array
     * @throws LocalizedException
     */
    public function beforeSetCustomerDataAsLoggedIn(Session $subject, CustomerInterface $customer)
    {
        try {
            // Check if module is enabled
            if (!$this->corporateHelper->isEnabled()) {
                return [$customer];
            }

            // Check if customer has pending corporate request
            if ($this->corporateHelper->hasCustomerPendingRequest($customer->getId())) {
                $this->logger->info('Corporate account session blocked for pending request', [
                    'customer_id' => $customer->getId(),
                    'customer_email' => $customer->getEmail()
                ]);
                
                throw new LocalizedException(
                    __(
                        'Your corporate account request is pending approval. You will receive an email notification once it is reviewed.'
                    )
                );
            }

        } catch (LocalizedException $e) {
            // Re-throw localized exceptions
            throw $e;
        } catch (\Exception $e) {
            // Log other exceptions but don't block login
            $this->logger->error('Error during corporate session check', [
                'customer_id' => $customer->getId(),
                'error' => $e->getMessage()
            ]);
        }

        return [$customer];
    }
}
