<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Plugin;

use Krish\CorporateAccount\Helper\Data as CorporateHelper;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Authentication;
use Magento\Framework\Exception\AuthenticationException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Customer Authentication Plugin
 */
class CustomerAuthentication
{
    /**
     * @var CorporateHelper
     */
    private $corporateHelper;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Constructor
     *
     * @param CorporateHelper $corporateHelper
     * @param CustomerRepositoryInterface $customerRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        CorporateHelper $corporateHelper,
        CustomerRepositoryInterface $customerRepository,
        LoggerInterface $logger
    ) {
        $this->corporateHelper = $corporateHelper;
        $this->customerRepository = $customerRepository;
        $this->logger = $logger;
    }

    /**
     * Plugin to check corporate account status before authentication
     *
     * @param Authentication $subject
     * @param int $customerId
     * @param string $password
     * @return array
     * @throws AuthenticationException
     */
    public function beforeAuthenticate(Authentication $subject, $customerId, $password)
    {
        try {
            // Check if module is enabled
            if (!$this->corporateHelper->isEnabled()) {
                return [$customerId, $password];
            }

            // Get customer
            $customer = $this->customerRepository->getById($customerId);
            
            // Check if customer has pending corporate request
            if ($this->corporateHelper->hasCustomerPendingRequest($customerId)) {
                $this->logger->info('Corporate account login blocked for pending request', [
                    'customer_id' => $customerId,
                    'customer_email' => $customer->getEmail()
                ]);
                
                throw new AuthenticationException(
                    __(
                        'Your corporate account request is pending approval. You will receive an email notification once it is reviewed.'
                    )
                );
            }

        } catch (NoSuchEntityException $e) {
            // Customer not found, let normal authentication handle it
            $this->logger->warning('Customer not found during corporate authentication check', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
        } catch (AuthenticationException $e) {
            // Re-throw authentication exceptions
            throw $e;
        } catch (\Exception $e) {
            // Log other exceptions but don't block authentication
            $this->logger->error('Error during corporate authentication check', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
        }

        return [$customerId, $password];
    }
}
