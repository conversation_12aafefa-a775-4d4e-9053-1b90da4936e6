<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Ui\Component\Listing\Column\Status;

use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Magento\Framework\Data\OptionSourceInterface;

/**
 * Status Options for Corporate Account Listing
 */
class Options implements OptionSourceInterface
{
    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            [
                'value' => CorporateAccountRequestInterface::STATUS_PENDING,
                'label' => __('Pending')
            ],
            [
                'value' => CorporateAccountRequestInterface::STATUS_APPROVED,
                'label' => __('Approved')
            ],
            [
                'value' => CorporateAccountRequestInterface::STATUS_DECLINED,
                'label' => __('Declined')
            ]
        ];
    }
}
