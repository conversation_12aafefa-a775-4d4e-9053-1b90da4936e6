<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Ui\Component\Listing\Column;

use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

/**
 * Actions Column for Corporate Account Listing
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class Actions extends Column
{
    /**
     * @var UrlInterface
     */
    private $urlBuilder;

    /**
     * Constructor
     *
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param UrlInterface $urlBuilder
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        UrlInterface $urlBuilder,
        array $components = [],
        array $data = []
    ) {
        $this->urlBuilder = $urlBuilder;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                $name = $this->getData('name');
                if (isset($item['entity_id'])) {
                    // View Corporate Request Details
                    $item[$name]['view'] = [
                        'href' => $this->urlBuilder->getUrl(
                            'corporate/account/view',
                            ['id' => $item['entity_id']]
                        ),
                        'label' => __('View Details'),
                        'hidden' => false,
                    ];

                    // View Customer Profile
                    $item[$name]['view_customer'] = [
                        'href' => $this->urlBuilder->getUrl(
                            'customer/index/edit',
                            ['id' => $item['customer_id']]
                        ),
                        'label' => __('View Customer'),
                        'hidden' => false,
                    ];

                    // Allow approval for pending or declined requests
                    if (in_array($item['status'], [
                        CorporateAccountRequestInterface::STATUS_PENDING,
                        CorporateAccountRequestInterface::STATUS_DECLINED
                    ])) {
                        $item[$name]['approve'] = [
                            'href' => $this->urlBuilder->getUrl(
                                'corporate/account/approve',
                                ['id' => $item['entity_id']]
                            ),
                            'label' => __('Approve'),
                            'confirm' => [
                                'title' => __('Approve Request'),
                                'message' => __('Are you sure you want to approve this corporate account request?')
                            ],
                            'hidden' => false,
                        ];
                    }

                    // Allow decline for pending or approved requests
                    if (in_array($item['status'], [
                        CorporateAccountRequestInterface::STATUS_PENDING,
                        CorporateAccountRequestInterface::STATUS_APPROVED
                    ])) {
                        $item[$name]['decline'] = [
                            'href' => $this->urlBuilder->getUrl(
                                'corporate/account/decline',
                                ['id' => $item['entity_id']]
                            ),
                            'label' => __('Decline'),
                            'confirm' => [
                                'title' => __('Decline Request'),
                                'message' => __('Are you sure you want to decline this corporate account request?')
                            ],
                            'hidden' => false,
                        ];
                    }

                    $item[$name]['delete'] = [
                        'href' => $this->urlBuilder->getUrl(
                            'corporate/account/delete',
                            ['id' => $item['entity_id']]
                        ),
                        'label' => __('Delete'),
                        'confirm' => [
                            'title' => __('Delete Request'),
                            'message' => __('Are you sure you want to delete this corporate account request?')
                        ],
                        'hidden' => false,
                    ];
                }
            }
        }

        return $dataSource;
    }
}
