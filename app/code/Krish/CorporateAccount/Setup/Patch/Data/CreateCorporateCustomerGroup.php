<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Setup\Patch\Data;

use Magento\Customer\Api\Data\GroupInterfaceFactory;
use Magento\Customer\Api\GroupManagementInterface;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Tax\Model\ClassModel;
use Magento\Tax\Model\ClassModelFactory;

/**
 * Create Corporate Customer Group Data Patch
 */
class CreateCorporateCustomerGroup implements DataPatchInterface
{
    /**
     * Corporate customer group code
     */
    public const CORPORATE_GROUP_CODE = 'Corporate Accounts';

    /**
     * @var GroupInterfaceFactory
     */
    private $groupFactory;

    /**
     * @var GroupRepositoryInterface
     */
    private $groupRepository;

    /**
     * @var GroupManagementInterface
     */
    private $groupManagement;

    /**
     * @var ClassModelFactory
     */
    private $classModelFactory;

    /**
     * Constructor
     *
     * @param GroupInterfaceFactory $groupFactory
     * @param GroupRepositoryInterface $groupRepository
     * @param GroupManagementInterface $groupManagement
     * @param ClassModelFactory $classModelFactory
     */
    public function __construct(
        GroupInterfaceFactory $groupFactory,
        GroupRepositoryInterface $groupRepository,
        GroupManagementInterface $groupManagement,
        ClassModelFactory $classModelFactory
    ) {
        $this->groupFactory = $groupFactory;
        $this->groupRepository = $groupRepository;
        $this->groupManagement = $groupManagement;
        $this->classModelFactory = $classModelFactory;
    }

    /**
     * Apply patch
     *
     * @return void
     */
    public function apply()
    {
        try {
            // Check if group already exists
            $existingGroups = $this->groupManagement->getLoggedInGroups();

            foreach ($existingGroups as $group) {
                if ($group->getCode() === self::CORPORATE_GROUP_CODE) {
                    // Group already exists, skip creation
                    return;
                }
            }

            // Get default tax class ID
            $taxClass = $this->classModelFactory->create();
            $taxClass->load(ClassModel::TAX_CLASS_TYPE_CUSTOMER, 'class_type');
            $taxClassId = $taxClass->getId() ?: 3; // Default to 3 if not found

            // Create new customer group
            $customerGroup = $this->groupFactory->create();
            $customerGroup->setCode(self::CORPORATE_GROUP_CODE);
            $customerGroup->setTaxClassId($taxClassId);

            $this->groupRepository->save($customerGroup);

        } catch (\Exception $e) {
            // Log error but don't fail the installation
            error_log('Failed to create Corporate customer group: ' . $e->getMessage());
        }
    }

    /**
     * Get dependencies
     *
     * @return array
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * Get aliases
     *
     * @return array
     */
    public function getAliases()
    {
        return [];
    }
}
