<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Helper;

use Krish\CorporateAccount\Api\CorporateAccountRequestRepositoryInterface;
use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\ScopeInterface;

/**
 * Corporate Account Helper
 *
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 * @SuppressWarnings(PHPMD.ExcessiveClassComplexity)
 */
class Data extends AbstractHelper
{
    /**
     * Configuration paths
     */
    public const XML_PATH_ENABLED = 'krish_corporate_account/general/enabled';
    public const XML_PATH_REQUIRE_APPROVAL = 'krish_corporate_account/general/require_approval';
    public const XML_PATH_AUTO_ASSIGN_GROUP = 'krish_corporate_account/general/auto_assign_group';
    public const XML_PATH_CORPORATE_GROUP_ID = 'krish_corporate_account/general/corporate_group_id';

    // Email Configuration
    public const XML_PATH_EMAIL_ENABLED = 'krish_corporate_account/email/enabled';
    public const XML_PATH_EMAIL_SENDER_IDENTITY = 'krish_corporate_account/email/sender_identity';
    public const XML_PATH_ADMIN_EMAIL = 'krish_corporate_account/email/admin_email';
    public const XML_PATH_EMAIL_COPY_TO = 'krish_corporate_account/email/copy_to';
    public const XML_PATH_EMAIL_COPY_METHOD = 'krish_corporate_account/email/copy_method';

    // Email Templates
    public const XML_PATH_EMAIL_TEMPLATE_SUBMITTED = 'krish_corporate_account/email_templates/template_submitted';
    public const XML_PATH_EMAIL_TEMPLATE_APPROVED = 'krish_corporate_account/email_templates/template_approved';
    public const XML_PATH_EMAIL_TEMPLATE_DECLINED = 'krish_corporate_account/email_templates/template_declined';
    public const XML_PATH_EMAIL_TEMPLATE_ADMIN_NOTIFICATION = 'krish_corporate_account/email_templates/template_admin_notification';

    // File Upload Configuration
    public const XML_PATH_FILE_UPLOAD_ENABLED = 'krish_corporate_account/file_upload/enabled';
    public const XML_PATH_FILE_UPLOAD_ALLOWED_EXTENSIONS = 'krish_corporate_account/file_upload/allowed_extensions';
    public const XML_PATH_FILE_UPLOAD_MAX_SIZE = 'krish_corporate_account/file_upload/max_file_size';

    /**
     * @var CorporateAccountRequestRepositoryInterface
     */
    private $corporateAccountRequestRepository;

    /**
     * @var GroupRepositoryInterface
     */
    private $groupRepository;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * Constructor
     *
     * @param Context $context
     * @param CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository
     * @param GroupRepositoryInterface $groupRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        Context $context,
        CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository,
        GroupRepositoryInterface $groupRepository,
        CustomerRepositoryInterface $customerRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
        parent::__construct($context);
        $this->corporateAccountRequestRepository = $corporateAccountRequestRepository;
        $this->groupRepository = $groupRepository;
        $this->customerRepository = $customerRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }

    /**
     * Check if module is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if approval is required
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isApprovalRequired($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_REQUIRE_APPROVAL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if auto assign to group is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isAutoAssignGroupEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_AUTO_ASSIGN_GROUP,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get corporate customer group ID
     *
     * @param int|null $storeId
     * @return int|null
     */
    public function getCorporateGroupId($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CORPORATE_GROUP_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if email notifications are enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEmailEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_EMAIL_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get email sender identity
     *
     * @param int|null $storeId
     * @return string
     */
    public function getEmailSenderIdentity($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_SENDER_IDENTITY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get admin notification email
     *
     * @param int|null $storeId
     * @return string
     */
    public function getAdminEmail($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_ADMIN_EMAIL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get email copy to addresses
     *
     * @param int|null $storeId
     * @return string
     */
    public function getEmailCopyTo($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_COPY_TO,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get email copy method
     *
     * @param int|null $storeId
     * @return string
     */
    public function getEmailCopyMethod($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_COPY_METHOD,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if customer has pending corporate request
     *
     * @param int $customerId
     * @return bool
     */
    public function hasCustomerPendingRequest($customerId)
    {
        try {
            $request = $this->corporateAccountRequestRepository->getByCustomerId($customerId);
            return $request->getStatus() === CorporateAccountRequestInterface::STATUS_PENDING;
        } catch (NoSuchEntityException $e) {
            return false;
        }
    }

    /**
     * Get customer corporate request status
     *
     * @param int $customerId
     * @return string|null
     */
    public function getCustomerRequestStatus($customerId)
    {
        try {
            $request = $this->corporateAccountRequestRepository->getByCustomerId($customerId);
            return $request->getStatus();
        } catch (NoSuchEntityException $e) {
            return null;
        }
    }

    /**
     * Check if customer is corporate
     *
     * @param int $customerId
     * @return bool
     */
    public function isCustomerCorporate($customerId)
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            $corporateGroupId = $this->getCorporateGroupId();

            return $corporateGroupId && $customer->getGroupId() == $corporateGroupId;
        } catch (NoSuchEntityException $e) {
            return false;
        }
    }

    /**
     * Get corporate customer group by code
     *
     * @param string $groupCode
     * @return \Magento\Customer\Api\Data\GroupInterface|null
     */
    public function getCustomerGroupByCode($groupCode)
    {
        try {
            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter('customer_group_code', $groupCode)
                ->create();

            $groups = $this->groupRepository->getList($searchCriteria);
            $items = $groups->getItems();

            return !empty($items) ? reset($items) : null;
        } catch (LocalizedException $e) {
            return null;
        }
    }

    /**
     * Get corporate customer group
     *
     * @return \Magento\Customer\Api\Data\GroupInterface|null
     */
    public function getCorporateCustomerGroup()
    {
        return $this->getCustomerGroupByCode('Corporate Accounts');
    }

    /**
     * Get email template for submission confirmation
     *
     * @param int|null $storeId
     * @return string
     */
    public function getEmailTemplateSubmitted($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_TEMPLATE_SUBMITTED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get email template for approval notification
     *
     * @param int|null $storeId
     * @return string
     */
    public function getEmailTemplateApproved($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_TEMPLATE_APPROVED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get email template for decline notification
     *
     * @param int|null $storeId
     * @return string
     */
    public function getEmailTemplateDeclined($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_TEMPLATE_DECLINED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get email template for admin notification
     *
     * @param int|null $storeId
     * @return string
     */
    public function getEmailTemplateAdminNotification($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_TEMPLATE_ADMIN_NOTIFICATION,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if file upload is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isFileUploadEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_FILE_UPLOAD_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get allowed file extensions for upload
     *
     * @param int|null $storeId
     * @return array
     */
    public function getAllowedFileExtensions($storeId = null)
    {
        $extensions = $this->scopeConfig->getValue(
            self::XML_PATH_FILE_UPLOAD_ALLOWED_EXTENSIONS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );

        return $extensions ? explode(',', str_replace(' ', '', $extensions)) : ['pdf', 'jpg', 'jpeg', 'png'];
    }

    /**
     * Get maximum file size in MB
     *
     * @param int|null $storeId
     * @return int
     */
    public function getMaxFileSize($storeId = null)
    {
        $size = $this->scopeConfig->getValue(
            self::XML_PATH_FILE_UPLOAD_MAX_SIZE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );

        return $size ? (int)$size : 5; // Default 5MB
    }

    /**
     * Get company types
     *
     * @return array
     */
    public function getCompanyTypes()
    {
        return [
            'private_limited' => __('Private Limited Company'),
            'public_limited' => __('Public Limited Company'),
            'partnership' => __('Partnership'),
            'sole_proprietorship' => __('Sole Proprietorship'),
            'llp' => __('Limited Liability Partnership'),
            'other' => __('Other')
        ];
    }

    /**
     * Get status options
     *
     * @return array
     */
    public function getStatusOptions()
    {
        return [
            CorporateAccountRequestInterface::STATUS_PENDING => __('Pending'),
            CorporateAccountRequestInterface::STATUS_APPROVED => __('Approved'),
            CorporateAccountRequestInterface::STATUS_DECLINED => __('Declined')
        ];
    }
}
