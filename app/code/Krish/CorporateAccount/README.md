# Krish Corporate Account Module

## Overview

The Krish_CorporateAccount module enables corporate users to register for business accounts through a dedicated registration flow. The module provides admin approval functionality, customer group management, and email notifications.

## Features

### Frontend Features
- **Corporate Registration Page**: Separate registration page at `corporate/account/register`
- **Native Customer Fields**: Reuses Magento's native customer registration fields
- **Corporate-Specific Fields**:
  - Company Name (required)
  - Company Type (required)
  - GST Number/Tax ID (optional)
  - Business Contact Email (required)
  - Business Contact Phone (required)
  - Document Upload (optional - PDF, JPEG, PNG)
- **File Upload**: Support for business documents with validation
- **Login Restrictions**: Blocks login for pending corporate accounts
- **Registration Link**: Added to customer login page

### Admin Features
- **Admin Grid**: View all corporate account requests at `Admin > Customers > Corporate Account Requests`
- **Request Management**: Approve/Decline requests with admin notes
- **Mass Actions**: Bulk approve/decline operations
- **ACL Permissions**: Granular access control
- **Status Tracking**: Pending, Approved, Declined statuses

### Customer Group Management
- **Auto-Creation**: Automatically creates "Corporate Accounts" customer group
- **Auto-Assignment**: Assigns approved customers to corporate group
- **Group Reversion**: Reverts to general group on decline

### Email Notifications
- **Submission Confirmation**: Sent to customer on request submission
- **Approval Notification**: Sent to customer on approval
- **Decline Notification**: Sent to customer on decline
- **Admin Notification**: Sent to admin on new request submission

## Installation

1. Copy the module to `app/code/Krish/CorporateAccount/`
2. Run setup commands:
   ```bash
   php bin/magento module:enable Krish_CorporateAccount
   php bin/magento setup:upgrade
   php bin/magento setup:di:compile
   php bin/magento cache:flush
   ```

## Configuration

The module automatically:
- Creates the "Corporate Accounts" customer group
- Sets up database tables
- Configures email templates
- Adds admin menu items

## Database Schema

### Table: `krish_corporate_account_request`
- `entity_id` - Primary key
- `customer_id` - Foreign key to customer_entity
- `company_name` - Company name
- `company_type` - Type of company
- `gst_number` - GST/Tax ID (optional)
- `business_email` - Business contact email
- `business_phone` - Business contact phone
- `uploaded_document` - Document file path
- `status` - Request status (pending/approved/declined)
- `approved_by` - Admin user ID
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp
- `processed_at` - Processing timestamp

## File Structure

```
app/code/Krish/CorporateAccount/
├── Api/
│   ├── Data/
│   │   └── CorporateAccountRequestInterface.php
│   └── CorporateAccountRequestRepositoryInterface.php
├── Block/
│   └── Account/
│       └── Register.php
├── Controller/
│   ├── Account/
│   │   ├── Register.php
│   │   └── Save.php
│   └── Adminhtml/
│       └── Account/
│           ├── Index.php
│           ├── Approve.php
│           └── Decline.php
├── Helper/
│   └── Data.php
├── Model/
│   ├── CorporateAccountRequest.php
│   ├── CorporateAccountRequestRepository.php
│   ├── FileUploader.php
│   └── ResourceModel/
├── Observer/
│   └── AssignCustomerGroupObserver.php
├── Plugin/
│   ├── CustomerAuthentication.php
│   └── CustomerSession.php
├── Setup/
│   └── Patch/
│       └── Data/
│           └── CreateCorporateCustomerGroup.php
├── Ui/
│   └── Component/
├── view/
│   ├── adminhtml/
│   └── frontend/
├── etc/
│   ├── module.xml
│   ├── di.xml
│   ├── acl.xml
│   ├── adminhtml/
│   └── frontend/
├── composer.json
├── registration.php
└── README.md
```

## Usage

### For Customers
1. Visit the customer login page
2. Click "Create Corporate Account" button
3. Fill in personal and company information
4. Upload business document (optional)
5. Submit the form
6. Wait for admin approval

### For Administrators
1. Go to Admin > Customers > Corporate Account Requests
2. Review pending requests
3. Click "Approve" or "Decline"
4. Customer receives email notification

## Technical Details

### Routes
- **Frontend**: `corporate/account/register`, `corporate/account/save`
- **Admin**: `corporate/account/index`, `corporate/account/approve`, `corporate/account/decline`

### Events
- `krish_corporate_account_request_submitted`
- `krish_corporate_account_request_approved`
- `krish_corporate_account_request_declined`

### Plugins
- `CustomerAuthentication::beforeAuthenticate` - Blocks login for pending accounts
- `CustomerSession::beforeSetCustomerDataAsLoggedIn` - Session validation

### File Upload
- **Location**: `media/corporate_account_documents/`
- **Allowed Types**: PDF, JPEG, PNG
- **Max Size**: 5MB
- **Validation**: Client-side and server-side

## Compatibility

- **Magento Version**: 2.4.6+
- **PHP Version**: 8.1+
- **Dependencies**: Krish_Base module

## Support

For support and customization, contact Krish Technolabs Pvt Ltd.
- Website: https://www.krishtechnolabs.com/
- Email: <EMAIL>

## License

Open Software License (OSL 3.0)
