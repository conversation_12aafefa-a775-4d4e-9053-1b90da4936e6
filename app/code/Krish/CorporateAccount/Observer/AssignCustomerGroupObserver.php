<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Observer;

use Krish\CorporateAccount\Helper\Data as CorporateHelper;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Assign Customer Group Observer
 */
class AssignCustomerGroupObserver implements ObserverInterface
{
    /**
     * @var CorporateHelper
     */
    private $corporateHelper;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Constructor
     *
     * @param CorporateHelper $corporateHelper
     * @param CustomerRepositoryInterface $customerRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        CorporateHelper $corporateHelper,
        CustomerRepositoryInterface $customerRepository,
        LoggerInterface $logger
    ) {
        $this->corporateHelper = $corporateHelper;
        $this->customerRepository = $customerRepository;
        $this->logger = $logger;
    }

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        try {
            $corporateRequest = $observer->getEvent()->getCorporateRequest();

            if (!$corporateRequest || !$corporateRequest->getCustomerId()) {
                return;
            }

            // Get corporate customer group
            $corporateGroup = $this->corporateHelper->getCorporateCustomerGroup();
            if (!$corporateGroup) {
                $this->logger->warning('Corporate customer group not found');
                return;
            }

            // Update customer group
            $customer = $this->customerRepository->getById($corporateRequest->getCustomerId());
            $customer->setGroupId($corporateGroup->getId());
            $this->customerRepository->save($customer);

            $this->logger->info('Customer assigned to corporate group', [
                'customer_id' => $corporateRequest->getCustomerId(),
                'group_id' => $corporateGroup->getId()
            ]);

        } catch (LocalizedException $e) {
            $this->logger->error('Error assigning customer to corporate group: ' . $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error assigning customer to corporate group: ' . $e->getMessage());
        }
    }
}
