<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Observer;

use Krish\CorporateAccount\Model\EmailService;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Psr\Log\LoggerInterface;

/**
 * Send Admin Notification Observer
 */
class SendAdminNotificationObserver implements ObserverInterface
{
    /**
     * @var EmailService
     */
    private $emailService;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Constructor
     *
     * @param EmailService $emailService
     * @param LoggerInterface $logger
     */
    public function __construct(
        EmailService $emailService,
        LoggerInterface $logger
    ) {
        $this->emailService = $emailService;
        $this->logger = $logger;
    }

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        try {
            $corporateRequest = $observer->getEvent()->getCorporateRequest();

            if (!$corporateRequest || !$corporateRequest->getCustomerId()) {
                return;
            }

            // Send admin notification email using EmailService
            $this->emailService->sendAdminNotificationEmail($corporateRequest);

        } catch (\Exception $e) {
            $this->logger->error('Error in admin notification observer: ' . $e->getMessage());
        }
    }
}
