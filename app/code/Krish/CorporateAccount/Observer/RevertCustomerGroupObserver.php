<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Observer;

use Krish\CorporateAccount\Helper\Data as CorporateHelper;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\GroupManagementInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Revert Customer Group Observer (on decline)
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class RevertCustomerGroupObserver implements ObserverInterface
{
    /**
     * @var CorporateHelper
     */
    private $corporateHelper;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var GroupManagementInterface
     */
    private $groupManagement;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Constructor
     *
     * @param CorporateHelper $corporateHelper
     * @param CustomerRepositoryInterface $customerRepository
     * @param GroupManagementInterface $groupManagement
     * @param LoggerInterface $logger
     */
    public function __construct(
        CorporateHelper $corporateHelper,
        CustomerRepositoryInterface $customerRepository,
        GroupManagementInterface $groupManagement,
        LoggerInterface $logger
    ) {
        $this->corporateHelper = $corporateHelper;
        $this->customerRepository = $customerRepository;
        $this->groupManagement = $groupManagement;
        $this->logger = $logger;
    }

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        try {
            $corporateRequest = $observer->getEvent()->getCorporateRequest();

            if (!$corporateRequest || !$corporateRequest->getCustomerId()) {
                return;
            }

            // Revert customer group to default (General)
            $customer = $this->customerRepository->getById($corporateRequest->getCustomerId());
            $defaultGroup = $this->groupManagement->getDefaultGroup();
            $defaultGroupId = $defaultGroup->getId();

            $customer->setGroupId($defaultGroupId);
            $this->customerRepository->save($customer);

            $this->logger->info('Customer group reverted to default on decline', [
                'customer_id' => $corporateRequest->getCustomerId(),
                'group_id' => $defaultGroupId,
                'group_code' => $defaultGroup->getCode(),
                'request_id' => $corporateRequest->getEntityId()
            ]);

        } catch (LocalizedException $e) {
            $this->logger->error('Error reverting customer group on decline: ' . $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error reverting customer group on decline: ' . $e->getMessage());
        }
    }
}
