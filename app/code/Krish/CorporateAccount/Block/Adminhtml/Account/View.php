<?php
/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_CorporateAccount
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Block\Adminhtml\Account;

use Krish\CorporateAccount\Api\CorporateAccountRequestRepositoryInterface;
use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Krish\CorporateAccount\Helper\Data as CorporateHelper;
use Magento\Backend\Block\Template;
use Magento\Backend\Block\Template\Context;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem;
use Magento\User\Model\UserFactory;

/**
 * Corporate Account Request View Block
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @SuppressWarnings(PHPMD.TooManyFields)
 * @SuppressWarnings(PHPMD.ExcessiveClassComplexity)
 */
class View extends Template
{
    /**
     * @var CorporateAccountRequestRepositoryInterface
     */
    private $corporateAccountRequestRepository;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var CorporateHelper
     */
    private $corporateHelper;

    /**
     * @var UserFactory
     */
    private $userFactory;

    /**
     * @var Filesystem
     */
    protected $_filesystem;

    /**
     * @var CorporateAccountRequestInterface
     */
    private $corporateRequest;

    /**
     * Constructor
     *
     * @param Context $context
     * @param CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param CorporateHelper $corporateHelper
     * @param UserFactory $userFactory
     * @param Filesystem $filesystem
     * @param array $data
     */
    public function __construct(
        Context $context,
        CorporateAccountRequestRepositoryInterface $corporateAccountRequestRepository,
        CustomerRepositoryInterface $customerRepository,
        CorporateHelper $corporateHelper,
        UserFactory $userFactory,
        Filesystem $filesystem,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->corporateAccountRequestRepository = $corporateAccountRequestRepository;
        $this->customerRepository = $customerRepository;
        $this->corporateHelper = $corporateHelper;
        $this->userFactory = $userFactory;
        $this->_filesystem = $filesystem;
    }

    /**
     * Get corporate account request
     *
     * @return CorporateAccountRequestInterface|null
     */
    public function getCorporateRequest()
    {
        if ($this->corporateRequest === null) {
            $id = $this->getRequest()->getParam('id');
            if ($id) {
                try {
                    $this->corporateRequest = $this->corporateAccountRequestRepository->getById($id);
                } catch (NoSuchEntityException $e) {
                    $this->corporateRequest = null;
                }
            }
        }
        return $this->corporateRequest;
    }

    /**
     * Get customer information
     *
     * @return \Magento\Customer\Api\Data\CustomerInterface|null
     */
    public function getCustomer()
    {
        $corporateRequest = $this->getCorporateRequest();
        if ($corporateRequest && $corporateRequest->getCustomerId()) {
            try {
                return $this->customerRepository->getById($corporateRequest->getCustomerId());
            } catch (NoSuchEntityException $e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Get admin user who processed the request
     *
     * @return \Magento\User\Model\User|null
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    public function getProcessedByAdmin()
    {
        $corporateRequest = $this->getCorporateRequest();
        if ($corporateRequest && $corporateRequest->getApprovedBy()) {
            try {
                $user = $this->userFactory->create();
                $user->load($corporateRequest->getApprovedBy());
                return $user->getId() ? $user : null;
            } catch (\Exception $e) {
                // Suppress exception and return null
                return null;
            }
        }
        return null;
    }

    /**
     * Get company type label
     *
     * @return string
     */
    public function getCompanyTypeLabel()
    {
        $corporateRequest = $this->getCorporateRequest();
        if ($corporateRequest) {
            $companyTypes = $this->corporateHelper->getCompanyTypes();
            $companyType = $corporateRequest->getCompanyType();
            return isset($companyTypes[$companyType]) ? $companyTypes[$companyType] : $companyType;
        }
        return '';
    }

    /**
     * Get status label with color
     *
     * @return array
     */
    public function getStatusInfo()
    {
        $corporateRequest = $this->getCorporateRequest();
        if (!$corporateRequest) {
            return ['label' => '', 'class' => ''];
        }

        $status = $corporateRequest->getStatus();
        switch ($status) {
            case CorporateAccountRequestInterface::STATUS_PENDING:
                return ['label' => __('Pending'), 'class' => 'grid-severity-notice'];
            case CorporateAccountRequestInterface::STATUS_APPROVED:
                return ['label' => __('Approved'), 'class' => 'grid-severity-minor'];
            case CorporateAccountRequestInterface::STATUS_DECLINED:
                return ['label' => __('Declined'), 'class' => 'grid-severity-critical'];
            default:
                return ['label' => ucfirst($status), 'class' => ''];
        }
    }

    /**
     * Get document download URL
     *
     * @return string|null
     */
    public function getDocumentUrl()
    {
        $corporateRequest = $this->getCorporateRequest();
        if ($corporateRequest && $corporateRequest->getUploadedDocument()) {
            return $this->getUrl('corporate/account/download', [
                'id' => $corporateRequest->getEntityId()
            ]);
        }
        return null;
    }

    /**
     * Get document filename
     *
     * @return string|null
     */
    public function getDocumentFilename()
    {
        $corporateRequest = $this->getCorporateRequest();
        if ($corporateRequest && $corporateRequest->getUploadedDocument()) {
            return basename($corporateRequest->getUploadedDocument());
        }
        return null;
    }

    /**
     * Get document file extension
     *
     * @return string|null
     */
    public function getDocumentExtension()
    {
        $filename = $this->getDocumentFilename();
        if ($filename) {
            return strtoupper(pathinfo($filename, PATHINFO_EXTENSION));
        }
        return null;
    }

    /**
     * Get document file size
     *
     * @return string|null
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    public function getDocumentFileSize()
    {
        $corporateRequest = $this->getCorporateRequest();
        if ($corporateRequest && $corporateRequest->getUploadedDocument()) {
            try {
                $mediaDirectory = $this->_filesystem->getDirectoryRead(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA);
                $filePath = $corporateRequest->getUploadedDocument();

                if ($mediaDirectory->isFile($filePath)) {
                    $fileSize = $mediaDirectory->stat($filePath)['size'];
                    return $this->formatFileSize($fileSize);
                }
            } catch (\Exception $e) {
                // Suppress exception and return null
                return null;
            }
        }
        return null;
    }

    /**
     * Format file size
     *
     * @param int $bytes
     * @return string
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Get approve URL
     *
     * @return string
     */
    public function getApproveUrl()
    {
        $corporateRequest = $this->getCorporateRequest();
        if ($corporateRequest) {
            return $this->getUrl('corporate/account/approve', [
                'id' => $corporateRequest->getEntityId()
            ]);
        }
        return '';
    }

    /**
     * Get decline URL
     *
     * @return string
     */
    public function getDeclineUrl()
    {
        $corporateRequest = $this->getCorporateRequest();
        if ($corporateRequest) {
            return $this->getUrl('corporate/account/decline', [
                'id' => $corporateRequest->getEntityId()
            ]);
        }
        return '';
    }

    /**
     * Get back URL
     *
     * @return string
     */
    public function getBackUrl()
    {
        return $this->getUrl('corporate/account/index');
    }

    /**
     * Check if request can be approved
     *
     * @return bool
     */
    public function canApprove()
    {
        $corporateRequest = $this->getCorporateRequest();
        if (!$corporateRequest) {
            return false;
        }

        return in_array($corporateRequest->getStatus(), [
            CorporateAccountRequestInterface::STATUS_PENDING,
            CorporateAccountRequestInterface::STATUS_DECLINED
        ]);
    }

    /**
     * Check if request can be declined
     *
     * @return bool
     */
    public function canDecline()
    {
        $corporateRequest = $this->getCorporateRequest();
        if (!$corporateRequest) {
            return false;
        }

        return in_array($corporateRequest->getStatus(), [
            CorporateAccountRequestInterface::STATUS_PENDING,
            CorporateAccountRequestInterface::STATUS_APPROVED
        ]);
    }

    /**
     * Format date for display
     *
     * @param string $date
     * @return string
     */
    public function formatDateForDisplay($date)
    {
        if (!$date) {
            return __('N/A');
        }
        return $this->_localeDate->formatDate($date, \IntlDateFormatter::MEDIUM);
    }
}
