<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Block\Account;

use Krish\CorporateAccount\Helper\Data as CorporateHelper;
use Magento\Customer\Block\Form\Register as CustomerRegister;
use Magento\Customer\Model\Session;
use Magento\Customer\Model\Url;
use Magento\Directory\Helper\Data as DirectoryHelper;
use Magento\Directory\Model\ResourceModel\Country\CollectionFactory as CountryCollectionFactory;
use Magento\Directory\Model\ResourceModel\Region\CollectionFactory as RegionCollectionFactory;
use Magento\Framework\App\Cache\Type\Config;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Json\EncoderInterface;
use Magento\Framework\Module\Manager;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\ScopeInterface;

/**
 * Corporate Account Registration Block
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @SuppressWarnings(PHPMD.TooManyFields)
 */
class Register extends CustomerRegister
{
    /**
     * Configuration paths for password validation
     */
    public const XML_PATH_MINIMUM_PASSWORD_LENGTH = 'customer/password/minimum_password_length';
    public const XML_PATH_REQUIRED_CHARACTER_CLASSES_NUMBER = 'customer/password/required_character_classes_number';

    /**
     * @var CorporateHelper
     */
    private $corporateHelper;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * Constructor
     *
     * @param Context $context
     * @param DirectoryHelper $directoryHelper
     * @param EncoderInterface $jsonEncoder
     * @param Config $configCacheType
     * @param RegionCollectionFactory $regionCollectionFactory
     * @param CountryCollectionFactory $countryCollectionFactory
     * @param Manager $moduleManager
     * @param Session $customerSession
     * @param Url $customerUrl
     * @param CorporateHelper $corporateHelper
     * @param array $data
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        Context $context,
        DirectoryHelper $directoryHelper,
        EncoderInterface $jsonEncoder,
        Config $configCacheType,
        RegionCollectionFactory $regionCollectionFactory,
        CountryCollectionFactory $countryCollectionFactory,
        Manager $moduleManager,
        Session $customerSession,
        Url $customerUrl,
        CorporateHelper $corporateHelper,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $directoryHelper,
            $jsonEncoder,
            $configCacheType,
            $regionCollectionFactory,
            $countryCollectionFactory,
            $moduleManager,
            $customerSession,
            $customerUrl,
            $data
        );
        $this->scopeConfig = $context->getScopeConfig();
        $this->corporateHelper = $corporateHelper;
    }

    /**
     * Get post action URL
     *
     * @return string
     */
    public function getPostActionUrl()
    {
        return $this->getUrl('corporate/account/save');
    }

    /**
     * Get login URL
     *
     * @return string
     */
    public function getLoginUrl()
    {
        return $this->getUrl('customer/account/login');
    }

    /**
     * Get company types
     *
     * @return array
     */
    public function getCompanyTypes()
    {
        return $this->corporateHelper->getCompanyTypes();
    }

    /**
     * Get allowed file extensions
     *
     * @return array
     */
    public function getAllowedFileExtensions()
    {
        return $this->corporateHelper->getAllowedFileExtensions();
    }

    /**
     * Get maximum file size
     *
     * @return int
     */
    public function getMaxFileSize()
    {
        return $this->corporateHelper->getMaxFileSize();
    }

    /**
     * Get allowed file extensions as string
     *
     * @return string
     */
    public function getAllowedFileExtensionsString()
    {
        return implode(', ', $this->getAllowedFileExtensions());
    }

    /**
     * Check if module is enabled
     *
     * @return bool
     */
    public function isEnabled()
    {
        return $this->corporateHelper->isEnabled();
    }

    /**
     * Get form data from session
     *
     * @return \Magento\Framework\DataObject
     */
    public function getFormData()
    {
        $data = $this->getData('form_data');
        if ($data === null) {
            $formData = $this->_customerSession->getCustomerFormData(true);
            $data = new \Magento\Framework\DataObject();
            if ($formData) {
                $data->addData($formData);
                $data->setCustomerData(1);
            }
            if (isset($data['region_id'])) {
                $data['region_id'] = (int)$data['region_id'];
            }
            $this->setData('form_data', $data);
        }
        return $data;
    }

    /**
     * Get back URL
     *
     * @return string
     */
    public function getBackUrl()
    {
        $url = $this->getData('back_url');
        if ($url === null) {
            $url = $this->getUrl('customer/account/login');
        }
        return $url;
    }

    /**
     * Get success URL
     *
     * @return string
     */
    public function getSuccessUrl()
    {
        $url = $this->getData('success_url');
        if ($url === null) {
            $url = $this->getUrl('customer/account/login');
        }
        return $url;
    }

    /**
     * Get error URL
     *
     * @return string
     */
    public function getErrorUrl()
    {
        return $this->getUrl('corporate/account/register');
    }

    /**
     * Check if newsletter subscription is enabled
     *
     * @return bool
     */
    public function isNewsletterEnabled()
    {
        return $this->_moduleManager->isOutputEnabled('Magento_Newsletter');
    }

    /**
     * Get minimum password length
     *
     * @return int
     */
    public function getMinimumPasswordLength()
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_MINIMUM_PASSWORD_LENGTH,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get required character classes number
     *
     * @return int
     */
    public function getRequiredCharacterClassesNumber()
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_REQUIRED_CHARACTER_CLASSES_NUMBER,
            ScopeInterface::SCOPE_STORE
        );
    }
}
