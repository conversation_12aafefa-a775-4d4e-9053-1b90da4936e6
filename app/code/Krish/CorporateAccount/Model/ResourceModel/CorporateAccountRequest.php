<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;

/**
 * Corporate Account Request Resource Model
 */
class CorporateAccountRequest extends AbstractDb
{
    /**
     * Table name
     */
    public const MAIN_TABLE = 'krish_corporate_account_request';

    /**
     * Primary key field name
     */
    public const ID_FIELD_NAME = 'entity_id';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(self::MAIN_TABLE, self::ID_FIELD_NAME);
    }

    /**
     * Load corporate account request by customer ID
     *
     * @param \Krish\CorporateAccount\Model\CorporateAccountRequest $object
     * @param int $customerId
     * @param string|null $field
     * @return $this
     */
    public function loadByCustomerId(
        \Krish\CorporateAccount\Model\CorporateAccountRequest $object,
        $customerId,
        $field = null
    ) {
        $field = $field ?: 'customer_id';
        return $this->load($object, $customerId, $field);
    }

    /**
     * Check if customer has pending request
     *
     * @param int $customerId
     * @return bool
     */
    public function hasPendingRequest($customerId)
    {
        $connection = $this->getConnection();
        $select = $connection->select()
            ->from($this->getMainTable(), 'entity_id')
            ->where('customer_id = ?', $customerId)
            ->where('status = ?', \Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface::STATUS_PENDING)
            ->limit(1);

        return (bool) $connection->fetchOne($select);
    }

    /**
     * Get request status by customer ID
     *
     * @param int $customerId
     * @return string|null
     */
    public function getRequestStatusByCustomerId($customerId)
    {
        $connection = $this->getConnection();
        $select = $connection->select()
            ->from($this->getMainTable(), 'status')
            ->where('customer_id = ?', $customerId)
            ->order('created_at DESC')
            ->limit(1);

        return $connection->fetchOne($select) ?: null;
    }

    /**
     * Update request status
     *
     * @param int $entityId
     * @param string $status
     * @param int|null $adminUserId
     * @return int
     */
    public function updateRequestStatus($entityId, $status, $adminUserId = null)
    {
        $connection = $this->getConnection();
        $data = [
            'status' => $status,
            'processed_at' => new \Zend_Db_Expr('NOW()')
        ];

        if ($adminUserId !== null) {
            $data['approved_by'] = $adminUserId;
        }

        return $connection->update(
            $this->getMainTable(),
            $data,
            ['entity_id = ?' => $entityId]
        );
    }
}
