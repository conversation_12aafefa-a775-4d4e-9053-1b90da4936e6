<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Model\ResourceModel\CorporateAccountRequest;

use <PERSON>h\CorporateAccount\Model\CorporateAccountRequest as Model;
use Krish\CorporateAccount\Model\ResourceModel\CorporateAccountRequest as ResourceModel;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * Corporate Account Request Collection
 */
class Collection extends AbstractCollection
{
    /**
     * ID field name
     *
     * @var string
     */
    protected $_idFieldName = 'entity_id';

    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'krish_corporate_account_request_collection';

    /**
     * Event object
     *
     * @var string
     */
    protected $_eventObject = 'corporate_account_request_collection';

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(Model::class, ResourceModel::class);
    }

    /**
     * Add customer data to collection
     *
     * @return $this
     */
    public function addCustomerData()
    {
        $this->getSelect()->joinLeft(
            ['customer' => $this->getTable('customer_entity')],
            'main_table.customer_id = customer.entity_id',
            [
                'customer_firstname' => 'customer.firstname',
                'customer_lastname' => 'customer.lastname',
                'customer_email' => 'customer.email'
            ]
        );

        return $this;
    }

    /**
     * Add admin user data to collection
     *
     * @return $this
     */
    public function addAdminUserData()
    {
        $this->getSelect()->joinLeft(
            ['admin_user' => $this->getTable('admin_user')],
            'main_table.approved_by = admin_user.user_id',
            [
                'admin_firstname' => 'admin_user.firstname',
                'admin_lastname' => 'admin_user.lastname',
                'admin_username' => 'admin_user.username'
            ]
        );

        return $this;
    }

    /**
     * Filter by status
     *
     * @param string $status
     * @return $this
     */
    public function addStatusFilter($status)
    {
        $this->addFieldToFilter('status', $status);
        return $this;
    }

    /**
     * Filter by customer ID
     *
     * @param int $customerId
     * @return $this
     */
    public function addCustomerFilter($customerId)
    {
        $this->addFieldToFilter('customer_id', $customerId);
        return $this;
    }

    /**
     * Filter pending requests
     *
     * @return $this
     */
    public function addPendingFilter()
    {
        return $this->addStatusFilter(\Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface::STATUS_PENDING);
    }

    /**
     * Filter approved requests
     *
     * @return $this
     */
    public function addApprovedFilter()
    {
        return $this->addStatusFilter(\Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface::STATUS_APPROVED);
    }

    /**
     * Filter declined requests
     *
     * @return $this
     */
    public function addDeclinedFilter()
    {
        return $this->addStatusFilter(\Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface::STATUS_DECLINED);
    }

    /**
     * Add date range filter
     *
     * @param string $from
     * @param string $to
     * @return $this
     */
    public function addDateRangeFilter($from = null, $to = null)
    {
        if ($from) {
            $this->addFieldToFilter('created_at', ['gteq' => $from]);
        }

        if ($to) {
            $this->addFieldToFilter('created_at', ['lteq' => $to]);
        }

        return $this;
    }
}
