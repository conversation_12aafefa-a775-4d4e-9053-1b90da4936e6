<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Model;

use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Krish\CorporateAccount\Helper\Data as CorporateHelper;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Area;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\MailException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Email Service for Corporate Account
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @SuppressWarnings(PHPMD.TooManyFields)
 * @SuppressWarnings(PHPMD.ExcessiveClassComplexity)
 */
class EmailService
{
    /**
     * @var TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var CorporateHelper
     */
    private $corporateHelper;

    /**
     * @var ManagerInterface
     */
    private $eventManager;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Constructor
     *
     * @param TransportBuilder $transportBuilder
     * @param StoreManagerInterface $storeManager
     * @param CustomerRepositoryInterface $customerRepository
     * @param CorporateHelper $corporateHelper
     * @param ManagerInterface $eventManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        TransportBuilder $transportBuilder,
        StoreManagerInterface $storeManager,
        CustomerRepositoryInterface $customerRepository,
        CorporateHelper $corporateHelper,
        ManagerInterface $eventManager,
        LoggerInterface $logger
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->storeManager = $storeManager;
        $this->customerRepository = $customerRepository;
        $this->corporateHelper = $corporateHelper;
        $this->eventManager = $eventManager;
        $this->logger = $logger;
    }

    /**
     * Send submission confirmation email
     *
     * @param CorporateAccountRequestInterface $corporateRequest
     * @return bool
     */
    public function sendSubmissionEmail(CorporateAccountRequestInterface $corporateRequest)
    {
        try {
            // Check if email notifications are enabled
            if (!$this->corporateHelper->isEmailEnabled()) {
                return true; // Skip sending but return success
            }

            $customer = $this->customerRepository->getById($corporateRequest->getCustomerId());
            $templateVars = $this->prepareEmailTemplateVars($corporateRequest, $customer);

            $templateId = $this->corporateHelper->getEmailTemplateSubmitted();

            return $this->sendEmail(
                $templateId,
                $customer->getEmail(),
                $customer->getFirstname() . ' ' . $customer->getLastname(),
                $templateVars,
                Area::AREA_FRONTEND
            );
        } catch (\Exception $e) {
            $this->logger->error('Failed to send submission email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send approval email
     *
     * @param CorporateAccountRequestInterface $corporateRequest
     * @return bool
     */
    public function sendApprovalEmail(CorporateAccountRequestInterface $corporateRequest)
    {
        try {
            // Check if email notifications are enabled
            if (!$this->corporateHelper->isEmailEnabled()) {
                return true; // Skip sending but return success
            }

            $customer = $this->customerRepository->getById($corporateRequest->getCustomerId());
            $templateVars = $this->prepareEmailTemplateVars($corporateRequest, $customer);
            $templateVars['login_url'] = $this->getLoginUrl();
            $templateVars['processed_at'] = $corporateRequest->getProcessedAt();

            $templateId = $this->corporateHelper->getEmailTemplateApproved();

            return $this->sendEmail(
                $templateId,
                $customer->getEmail(),
                $customer->getFirstname() . ' ' . $customer->getLastname(),
                $templateVars,
                Area::AREA_FRONTEND
            );
        } catch (\Exception $e) {
            $this->logger->error('Failed to send approval email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send decline email
     *
     * @param CorporateAccountRequestInterface $corporateRequest
     * @return bool
     */
    public function sendDeclineEmail(CorporateAccountRequestInterface $corporateRequest)
    {
        try {
            // Check if email notifications are enabled
            if (!$this->corporateHelper->isEmailEnabled()) {
                return true; // Skip sending but return success
            }

            $customer = $this->customerRepository->getById($corporateRequest->getCustomerId());
            $templateVars = $this->prepareEmailTemplateVars($corporateRequest, $customer);
            $templateVars['contact_url'] = $this->getContactUrl();
            $templateVars['processed_at'] = $corporateRequest->getProcessedAt();

            $templateId = $this->corporateHelper->getEmailTemplateDeclined();

            return $this->sendEmail(
                $templateId,
                $customer->getEmail(),
                $customer->getFirstname() . ' ' . $customer->getLastname(),
                $templateVars,
                Area::AREA_FRONTEND
            );
        } catch (\Exception $e) {
            $this->logger->error('Failed to send decline email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send admin notification email
     *
     * @param CorporateAccountRequestInterface $corporateRequest
     * @return bool
     */
    public function sendAdminNotificationEmail(CorporateAccountRequestInterface $corporateRequest)
    {
        try {
            // Check if email notifications are enabled
            if (!$this->corporateHelper->isEmailEnabled()) {
                return true; // Skip sending but return success
            }

            $adminEmail = $this->corporateHelper->getAdminEmail() ?: $this->getStoreEmail();
            if (!$adminEmail) {
                $this->logger->warning('No admin email configured for corporate account notifications');
                return false;
            }

            $customer = $this->customerRepository->getById($corporateRequest->getCustomerId());
            $templateVars = $this->prepareEmailTemplateVars($corporateRequest, $customer);
            $templateVars['request_id'] = $corporateRequest->getEntityId();
            $templateVars['admin_url'] = $this->getAdminUrl();
            $templateVars['created_at'] = $corporateRequest->getCreatedAt();

            $templateId = $this->corporateHelper->getEmailTemplateAdminNotification();

            return $this->sendEmail(
                $templateId,
                $adminEmail,
                'Admin',
                $templateVars,
                Area::AREA_FRONTEND
            );
        } catch (\Exception $e) {
            $this->logger->error('Failed to send admin notification email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email using transport builder
     *
     * @param string $templateId
     * @param string $toEmail
     * @param string $toName
     * @param array $templateVars
     * @param string $area
     * @return bool
     */
    private function sendEmail($templateId, $toEmail, $toName, $templateVars, $area = Area::AREA_FRONTEND)
    {
        try {
            $store = $this->storeManager->getStore();
            $senderIdentity = $this->corporateHelper->getEmailSenderIdentity() ?: 'general';

            $this->transportBuilder
                ->setTemplateIdentifier($templateId)
                ->setTemplateOptions([
                    'area' => $area,
                    'store' => $store->getId()
                ])
                ->setTemplateVars($templateVars)
                ->setFromByScope($senderIdentity)
                ->addTo($toEmail, $toName);

            // Add copy recipients if configured
            $copyTo = $this->corporateHelper->getEmailCopyTo();
            if ($copyTo) {
                $copyEmails = array_map('trim', explode(',', $copyTo));
                $copyMethod = $this->corporateHelper->getEmailCopyMethod();

                foreach ($copyEmails as $copyEmail) {
                    if (filter_var($copyEmail, FILTER_VALIDATE_EMAIL)) {
                        if ($copyMethod === 'bcc') {
                            $this->transportBuilder->addBcc($copyEmail);
                        } else {
                            $this->transportBuilder->addCc($copyEmail);
                        }
                    }
                }
            }

            $transport = $this->transportBuilder->getTransport();
            $transport->sendMessage();

            $this->logger->info('Corporate account email sent', [
                'template_id' => $templateId,
                'to_email' => $toEmail,
                'sender_identity' => $senderIdentity,
                'copy_to' => $copyTo,
                'area' => $area
            ]);

            return true;
        } catch (MailException $e) {
            $this->logger->error('Failed to send email: ' . $e->getMessage());
            return false;
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error sending email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Prepare common email template variables
     *
     * @param CorporateAccountRequestInterface $corporateRequest
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     * @return array
     */
    private function prepareEmailTemplateVars(CorporateAccountRequestInterface $corporateRequest, $customer)
    {
        $store = $this->storeManager->getStore();

        return [
            'customer' => $customer,
            'customer_name' => $customer->getFirstname() . ' ' . $customer->getLastname(),
            'company_name' => $corporateRequest->getCompanyName(),
            'company_type' => $this->getCompanyTypeLabel($corporateRequest->getCompanyType()),
            'business_email' => $corporateRequest->getBusinessEmail(),
            'business_phone' => $corporateRequest->getBusinessPhone(),
            'gst_number' => $corporateRequest->getGstNumber() ?: __('Not provided'),
            'store' => $store,
            'store_email' => $this->getStoreEmail(),
            'store_phone' => $this->getStorePhone()
        ];
    }

    /**
     * Get company type label
     *
     * @param string $companyType
     * @return string
     */
    private function getCompanyTypeLabel($companyType)
    {
        $companyTypes = $this->corporateHelper->getCompanyTypes();
        return isset($companyTypes[$companyType]) ? $companyTypes[$companyType] : $companyType;
    }

    /**
     * Get store email
     *
     * @return string
     */
    private function getStoreEmail()
    {
        return $this->storeManager->getStore()->getConfig('trans_email/ident_general/email') ?: '';
    }

    /**
     * Get store phone
     *
     * @return string
     */
    private function getStorePhone()
    {
        return $this->storeManager->getStore()->getConfig('general/store_information/phone') ?: '';
    }

    /**
     * Get login URL
     *
     * @return string
     */
    private function getLoginUrl()
    {
        return $this->storeManager->getStore()->getUrl('customer/account/login');
    }

    /**
     * Get contact URL
     *
     * @return string
     */
    private function getContactUrl()
    {
        return $this->storeManager->getStore()->getUrl('contact');
    }

    /**
     * Get admin URL
     *
     * @return string
     */
    private function getAdminUrl()
    {
        return $this->storeManager->getStore()->getBaseUrl() . 'admin/corporate/account/index';
    }
}
