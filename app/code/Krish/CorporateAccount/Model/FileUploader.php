<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Model;

use Krish\CorporateAccount\Helper\Data as CorporateHelper;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\File\UploaderFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;
use Psr\Log\LoggerInterface;

/**
 * File Uploader for Corporate Account Documents
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class FileUploader
{
    /**
     * Upload directory
     */
    public const UPLOAD_DIR = 'corporate_account_documents';

    /**
     * @var UploaderFactory
     */
    private $uploaderFactory;

    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * @var WriteInterface
     */
    private $mediaDirectory;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var CorporateHelper
     */
    private $corporateHelper;

    /**
     * Constructor
     *
     * @param UploaderFactory $uploaderFactory
     * @param Filesystem $filesystem
     * @param LoggerInterface $logger
     * @param CorporateHelper $corporateHelper
     */
    public function __construct(
        UploaderFactory $uploaderFactory,
        Filesystem $filesystem,
        LoggerInterface $logger,
        CorporateHelper $corporateHelper
    ) {
        $this->uploaderFactory = $uploaderFactory;
        $this->filesystem = $filesystem;
        $this->mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
        $this->logger = $logger;
        $this->corporateHelper = $corporateHelper;
    }

    /**
     * Upload file
     *
     * @param string $fileId
     * @return string|null
     * @throws LocalizedException
     */
    public function uploadFile($fileId)
    {
        try {
            // Check if file upload is enabled
            if (!$this->corporateHelper->isFileUploadEnabled()) {
                throw new LocalizedException(__('File upload is disabled.'));
            }

            $allowedExtensions = $this->corporateHelper->getAllowedFileExtensions();
            $maxFileSize = $this->corporateHelper->getMaxFileSize() * 1024 * 1024; // Convert MB to bytes

            $uploader = $this->uploaderFactory->create(['fileId' => $fileId]);
            $uploader->setAllowedExtensions($allowedExtensions);
            $uploader->setAllowRenameFiles(true);
            $uploader->setFilesDispersion(true);
            $uploader->setAllowCreateFolders(true);

            // Validate file size
            // phpcs:ignore Magento2.Security.Superglobal.SuperglobalUsageError
            if (isset($_FILES[$fileId]['size']) && $_FILES[$fileId]['size'] > $maxFileSize) {
                throw new LocalizedException(
                    __('File size should not exceed %1 MB.', $this->corporateHelper->getMaxFileSize())
                );
            }

            $path = $this->mediaDirectory->getAbsolutePath(self::UPLOAD_DIR);
            $result = $uploader->save($path);

            if (!$result) {
                throw new LocalizedException(__('File upload failed.'));
            }

            return self::UPLOAD_DIR . $result['file'];

        } catch (\Exception $e) {
            $this->logger->error('Corporate Account File Upload Error: ' . $e->getMessage());
            throw new LocalizedException(__('File upload failed: %1', $e->getMessage()));
        }
    }

    /**
     * Get file URL
     *
     * @param string $filePath
     * @return string
     */
    public function getFileUrl($filePath)
    {
        if (!$filePath) {
            return '';
        }

        return $this->mediaDirectory->getAbsolutePath() . $filePath;
    }

    /**
     * Delete file
     *
     * @param string $filePath
     * @return bool
     */
    public function deleteFile($filePath)
    {
        try {
            if ($filePath && $this->mediaDirectory->isFile($filePath)) {
                return $this->mediaDirectory->delete($filePath);
            }
        } catch (\Exception $e) {
            $this->logger->error('Corporate Account File Delete Error: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Check if file exists
     *
     * @param string $filePath
     * @return bool
     */
    public function fileExists($filePath)
    {
        return $filePath && $this->mediaDirectory->isFile($filePath);
    }

    /**
     * Get allowed extensions
     *
     * @return array
     */
    public function getAllowedExtensions()
    {
        return $this->corporateHelper->getAllowedFileExtensions();
    }

    /**
     * Get maximum file size in bytes
     *
     * @return int
     */
    public function getMaxFileSize()
    {
        return $this->corporateHelper->getMaxFileSize() * 1024 * 1024; // Convert MB to bytes
    }

    /**
     * Get maximum file size in MB
     *
     * @return int
     */
    public function getMaxFileSizeMb()
    {
        return $this->corporateHelper->getMaxFileSize();
    }
}
