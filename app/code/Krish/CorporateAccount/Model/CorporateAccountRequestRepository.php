<?php
/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_CorporateAccount
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Model;

use Krish\CorporateAccount\Api\CorporateAccountRequestRepositoryInterface;
use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterfaceFactory;
use Krish\CorporateAccount\Model\ResourceModel\CorporateAccountRequest as ResourceModel;
use Krish\CorporateAccount\Model\ResourceModel\CorporateAccountRequest\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Api\SearchResultsInterfaceFactory;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Corporate Account Request Repository
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @SuppressWarnings(PHPMD.TooManyFields)
 */
class CorporateAccountRequestRepository implements CorporateAccountRequestRepositoryInterface
{
    /**
     * @var ResourceModel
     */
    private $resource;

    /**
     * @var CorporateAccountRequestInterfaceFactory
     */
    private $corporateAccountRequestFactory;

    /**
     * @var CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var SearchResultsInterfaceFactory
     */
    private $searchResultsFactory;

    /**
     * @var CollectionProcessorInterface
     */
    private $collectionProcessor;

    /**
     * Constructor
     *
     * @param ResourceModel $resource
     * @param CorporateAccountRequestInterfaceFactory $corporateAccountRequestFactory
     * @param CollectionFactory $collectionFactory
     * @param SearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceModel $resource,
        CorporateAccountRequestInterfaceFactory $corporateAccountRequestFactory,
        CollectionFactory $collectionFactory,
        SearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->corporateAccountRequestFactory = $corporateAccountRequestFactory;
        $this->collectionFactory = $collectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(CorporateAccountRequestInterface $corporateAccountRequest)
    {
        try {
            $this->resource->save($corporateAccountRequest);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(
                __('Could not save the corporate account request: %1', $exception->getMessage())
            );
        }
        return $corporateAccountRequest;
    }

    /**
     * @inheritDoc
     */
    public function getById($entityId)
    {
        $corporateAccountRequest = $this->corporateAccountRequestFactory->create();
        $this->resource->load($corporateAccountRequest, $entityId);
        if (!$corporateAccountRequest->getEntityId()) {
            throw new NoSuchEntityException(
                __('Corporate account request with id "%1" does not exist.', $entityId)
            );
        }
        return $corporateAccountRequest;
    }

    /**
     * @inheritDoc
     */
    public function getByCustomerId($customerId)
    {
        $corporateAccountRequest = $this->corporateAccountRequestFactory->create();
        $this->resource->loadByCustomerId($corporateAccountRequest, $customerId);
        if (!$corporateAccountRequest->getEntityId()) {
            throw new NoSuchEntityException(
                __('Corporate account request for customer "%1" does not exist.', $customerId)
            );
        }
        return $corporateAccountRequest;
    }

    /**
     * @inheritDoc
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());

        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(CorporateAccountRequestInterface $corporateAccountRequest)
    {
        try {
            $this->resource->delete($corporateAccountRequest);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(
                __('Could not delete the corporate account request: %1', $exception->getMessage())
            );
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($entityId)
    {
        return $this->delete($this->getById($entityId));
    }

    /**
     * @inheritDoc
     */
    public function approve($entityId, $adminUserId = null)
    {
        $corporateAccountRequest = $this->getById($entityId);

        // Allow approval of pending or declined requests
        if (!in_array($corporateAccountRequest->getStatus(), [
            CorporateAccountRequestInterface::STATUS_PENDING,
            CorporateAccountRequestInterface::STATUS_DECLINED
        ])) {
            throw new LocalizedException(
                __('Only pending or declined requests can be approved.')
            );
        }

        $corporateAccountRequest->setStatus(CorporateAccountRequestInterface::STATUS_APPROVED);
        $corporateAccountRequest->setProcessedAt(date('Y-m-d H:i:s'));

        if ($adminUserId) {
            $corporateAccountRequest->setApprovedBy($adminUserId);
        }

        return $this->save($corporateAccountRequest);
    }

    /**
     * @inheritDoc
     */
    public function decline($entityId, $adminUserId = null)
    {
        $corporateAccountRequest = $this->getById($entityId);

        // Allow declining of pending or approved requests
        if (!in_array($corporateAccountRequest->getStatus(), [
            CorporateAccountRequestInterface::STATUS_PENDING,
            CorporateAccountRequestInterface::STATUS_APPROVED
        ])) {
            throw new LocalizedException(
                __('Only pending or approved requests can be declined.')
            );
        }

        $corporateAccountRequest->setStatus(CorporateAccountRequestInterface::STATUS_DECLINED);
        $corporateAccountRequest->setProcessedAt(date('Y-m-d H:i:s'));

        if ($adminUserId) {
            $corporateAccountRequest->setApprovedBy($adminUserId);
        }

        return $this->save($corporateAccountRequest);
    }
}
