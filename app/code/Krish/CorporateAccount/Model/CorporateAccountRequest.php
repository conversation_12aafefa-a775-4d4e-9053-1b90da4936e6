<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_CorporateAccount
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\CorporateAccount\Model;

use Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface;
use Krish\CorporateAccount\Model\ResourceModel\CorporateAccountRequest as ResourceModel;
use Magento\Framework\Model\AbstractModel;

/**
 * Corporate Account Request Model
 */
class CorporateAccountRequest extends AbstractModel implements CorporateAccountRequestInterface
{
    /**
     * Cache tag
     */
    public const CACHE_TAG = 'krish_corporate_account_request';

    /**
     * Cache tag
     *
     * @var string
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'krish_corporate_account_request';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(ResourceModel::class);
    }

    /**
     * @inheritDoc
     */
    public function getEntityId()
    {
        return $this->getData(self::ENTITY_ID);
    }

    /**
     * @inheritDoc
     */
    public function setEntityId($entityId)
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * @inheritDoc
     */
    public function getCustomerId()
    {
        return $this->getData(self::CUSTOMER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setCustomerId($customerId)
    {
        return $this->setData(self::CUSTOMER_ID, $customerId);
    }

    /**
     * @inheritDoc
     */
    public function getCompanyName()
    {
        return $this->getData(self::COMPANY_NAME);
    }

    /**
     * @inheritDoc
     */
    public function setCompanyName($companyName)
    {
        return $this->setData(self::COMPANY_NAME, $companyName);
    }

    /**
     * @inheritDoc
     */
    public function getCompanyType()
    {
        return $this->getData(self::COMPANY_TYPE);
    }

    /**
     * @inheritDoc
     */
    public function setCompanyType($companyType)
    {
        return $this->setData(self::COMPANY_TYPE, $companyType);
    }

    /**
     * @inheritDoc
     */
    public function getGstNumber()
    {
        return $this->getData(self::GST_NUMBER);
    }

    /**
     * @inheritDoc
     */
    public function setGstNumber($gstNumber)
    {
        return $this->setData(self::GST_NUMBER, $gstNumber);
    }

    /**
     * @inheritDoc
     */
    public function getBusinessEmail()
    {
        return $this->getData(self::BUSINESS_EMAIL);
    }

    /**
     * @inheritDoc
     */
    public function setBusinessEmail($businessEmail)
    {
        return $this->setData(self::BUSINESS_EMAIL, $businessEmail);
    }

    /**
     * @inheritDoc
     */
    public function getBusinessPhone()
    {
        return $this->getData(self::BUSINESS_PHONE);
    }

    /**
     * @inheritDoc
     */
    public function setBusinessPhone($businessPhone)
    {
        return $this->setData(self::BUSINESS_PHONE, $businessPhone);
    }

    /**
     * @inheritDoc
     */
    public function getUploadedDocument()
    {
        return $this->getData(self::UPLOADED_DOCUMENT);
    }

    /**
     * @inheritDoc
     */
    public function setUploadedDocument($uploadedDocument)
    {
        return $this->setData(self::UPLOADED_DOCUMENT, $uploadedDocument);
    }

    /**
     * @inheritDoc
     */
    public function getStatus()
    {
        return $this->getData(self::STATUS);
    }

    /**
     * @inheritDoc
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }



    /**
     * @inheritDoc
     */
    public function getApprovedBy()
    {
        return $this->getData(self::APPROVED_BY);
    }

    /**
     * @inheritDoc
     */
    public function setApprovedBy($approvedBy)
    {
        return $this->setData(self::APPROVED_BY, $approvedBy);
    }

    /**
     * @inheritDoc
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @inheritDoc
     */
    public function getUpdatedAt()
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * @inheritDoc
     */
    public function getProcessedAt()
    {
        return $this->getData(self::PROCESSED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setProcessedAt($processedAt)
    {
        return $this->setData(self::PROCESSED_AT, $processedAt);
    }

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities()
    {
        return [self::CACHE_TAG . '_' . $this->getId()];
    }

    /**
     * Check if request is pending
     *
     * @return bool
     */
    public function isPending()
    {
        return $this->getStatus() === self::STATUS_PENDING;
    }

    /**
     * Check if request is approved
     *
     * @return bool
     */
    public function isApproved()
    {
        return $this->getStatus() === self::STATUS_APPROVED;
    }

    /**
     * Check if request is declined
     *
     * @return bool
     */
    public function isDeclined()
    {
        return $this->getStatus() === self::STATUS_DECLINED;
    }
}
