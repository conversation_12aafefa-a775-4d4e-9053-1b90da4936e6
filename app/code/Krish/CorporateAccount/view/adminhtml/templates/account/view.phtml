<?php
/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_CorporateAccount
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

/**
 * @var \Krish\CorporateAccount\Block\Adminhtml\Account\View $block
 * @SuppressWarnings(PHPMD.NPathComplexity)
 * @SuppressWarnings(PHPMD.CyclomaticComplexity)
 */
$corporateRequest = $block->getCorporateRequest();
$customer = $block->getCustomer();
$statusInfo = $block->getStatusInfo();
$processedByAdmin = $block->getProcessedByAdmin();
?>

<?php if ($corporateRequest): ?>
<div class="corporate-account-view">
    <div class="admin__page-nav">
        <button type="button" class="admin__page-nav-link" onclick="setLocation('<?= $escaper->escapeUrl($block->getBackUrl()) ?>')">
            <span class="admin__page-nav-title"><?= $escaper->escapeHtml(__('Back to Corporate Account Requests')) ?></span>
        </button>
    </div>

<div class="page-main-actions">
    <div class="page-actions-buttons">
        <?php if ($block->canApprove()): ?>
        <button type="button"
                class="action-default primary"
                onclick="confirmApprove('<?= $escaper->escapeUrl($block->getApproveUrl()) ?>')">
            <span><?= $escaper->escapeHtml(__('Approve Request')) ?></span>
        </button>
        <?php endif; ?>

        <?php if ($block->canDecline()): ?>
        <button type="button"
                class="action-default"
                onclick="confirmDecline('<?= $escaper->escapeUrl($block->getDeclineUrl()) ?>')">
            <span><?= $escaper->escapeHtml(__('Decline Request')) ?></span>
        </button>
        <?php endif; ?>
    </div>
</div>

<div class="admin__page-section">
    <div class="admin__page-section-title">
        <span class="title"><?= $escaper->escapeHtml(__('Corporate Account Request Details')) ?></span>
    </div>

    <div class="admin__page-section-content">
        <div class="admin__page-section-item order-information request-section">
            <div class="admin__page-section-item-title">
                <span class="title"><?= $escaper->escapeHtml(__('Request Information')) ?></span>
            </div>
            <div class="admin__page-section-item-content">
                <table class="admin__table-secondary">
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Request ID')) ?></th>
                        <td><?= $escaper->escapeHtml($corporateRequest->getEntityId()) ?></td>
                    </tr>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Status')) ?></th>
                        <td>
                            <span class="status-badge status-<?= $escaper->escapeHtmlAttr(strtolower($corporateRequest->getStatus())) ?>">
                                <?= $escaper->escapeHtml($statusInfo['label']) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Submitted Date')) ?></th>
                        <td><?= $escaper->escapeHtml($block->formatDateForDisplay($corporateRequest->getCreatedAt())) ?></td>
                    </tr>
                    <?php if ($corporateRequest->getProcessedAt()): ?>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Processed Date')) ?></th>
                        <td><?= $escaper->escapeHtml($block->formatDateForDisplay($corporateRequest->getProcessedAt())) ?></td>
                    </tr>
                    <?php endif; ?>
                    <?php if ($processedByAdmin): ?>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Processed By')) ?></th>
                        <td><?= $escaper->escapeHtml($processedByAdmin->getFirstname() . ' ' . $processedByAdmin->getLastname()) ?> (<?= $escaper->escapeHtml($processedByAdmin->getUsername()) ?>)</td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>

        <?php if ($customer): ?>
        <div class="admin__page-section-item order-information customer-section">
            <div class="admin__page-section-item-title">
                <span class="title"><?= $escaper->escapeHtml(__('Customer Information')) ?></span>
            </div>
            <div class="admin__page-section-item-content">
                <table class="admin__table-secondary">
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Customer ID')) ?></th>
                        <td>
                            <a href="<?= $escaper->escapeUrl($block->getUrl('customer/index/edit', ['id' => $customer->getId()])) ?>" target="_blank" class="customer-link">
                                <?= $escaper->escapeHtml($customer->getId()) ?>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Name')) ?></th>
                        <td><?= $escaper->escapeHtml($customer->getFirstname() . ' ' . $customer->getLastname()) ?></td>
                    </tr>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Email')) ?></th>
                        <td><?= $escaper->escapeHtml($customer->getEmail()) ?></td>
                    </tr>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Customer Group')) ?></th>
                        <td><?= $escaper->escapeHtml($customer->getGroupId()) ?></td>
                    </tr>
                </table>
            </div>
        </div>
        <?php endif; ?>

        <div class="admin__page-section-item order-information company-section">
            <div class="admin__page-section-item-title">
                <span class="title"><?= $escaper->escapeHtml(__('Company Information')) ?></span>
            </div>
            <div class="admin__page-section-item-content">
                <table class="admin__table-secondary">
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Company Name')) ?></th>
                        <td><?= $escaper->escapeHtml($corporateRequest->getCompanyName()) ?></td>
                    </tr>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Company Type')) ?></th>
                        <td><?= $escaper->escapeHtml($block->getCompanyTypeLabel()) ?></td>
                    </tr>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Business Email')) ?></th>
                        <td><?= $escaper->escapeHtml($corporateRequest->getBusinessEmail()) ?></td>
                    </tr>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Business Phone')) ?></th>
                        <td><?= $escaper->escapeHtml($corporateRequest->getBusinessPhone()) ?></td>
                    </tr>
                    <?php if ($corporateRequest->getGstNumber()): ?>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('GST Number')) ?></th>
                        <td><?= $escaper->escapeHtml($corporateRequest->getGstNumber()) ?></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>

        <div class="admin__page-section-item order-information document-section">
            <div class="admin__page-section-item-title">
                <span class="title"><?= $escaper->escapeHtml(__('Uploaded Document')) ?></span>
            </div>
            <div class="admin__page-section-item-content">
                <table class="admin__table-secondary">
                    <tr>
                        <th><?= $escaper->escapeHtml(__('Document')) ?></th>
                        <td>
                            <?php if ($block->getDocumentUrl()): ?>
                                <div class="admin__field-control">
                                    <a href="<?= $escaper->escapeUrl($block->getDocumentUrl()) ?>" target="_blank" class="document-download">
                                        <span><?= $escaper->escapeHtml($block->getDocumentFilename()) ?></span>
                                    </a>
                                    <?php if ($block->getDocumentExtension()): ?>
                                        <span class="admin__field-note">(<?= $escaper->escapeHtml($block->getDocumentExtension()) ?> file)</span>
                                    <?php endif; ?>
                                </div>
                                <small class="note"><?= $escaper->escapeHtml(__('Click to download the uploaded document')) ?></small>
                            <?php else: ?>
                                <span class="admin__field-note"><?= $escaper->escapeHtml(__('No document uploaded')) ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if ($corporateRequest->getUploadedDocument()): ?>
                    <tr>
                        <th><?= $escaper->escapeHtml(__('File Details')) ?></th>
                        <td>
                            <div class="file-details">
                                <strong><?= $escaper->escapeHtml(__('File Size:')) ?></strong>
                                <?= $escaper->escapeHtml($block->getDocumentFileSize() ?: __('Unknown')) ?>
                                <br/>
                                <strong><?= $escaper->escapeHtml(__('File Path:')) ?></strong>
                                <code><?= $escaper->escapeHtml($corporateRequest->getUploadedDocument()) ?></code>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
function confirmApprove(url) {
    if (confirm('<?= $escaper->escapeJs(__('Are you sure you want to approve this corporate account request?')) ?>')) {
        setLocation(url);
    }
}

function confirmDecline(url) {
    if (confirm('<?= $escaper->escapeJs(__('Are you sure you want to decline this corporate account request?')) ?>')) {
        setLocation(url);
    }
}
</script>

</div> <!-- End corporate-account-view -->

<?php else: ?>
<div class="message message-error error">
    <div><?= $escaper->escapeHtml(__('Corporate account request not found.')) ?></div>
</div>
<?php endif; ?>
