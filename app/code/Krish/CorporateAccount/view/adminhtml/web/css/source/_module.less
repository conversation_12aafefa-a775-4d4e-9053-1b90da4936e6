/**
 * Corporate Account Admin Styles
 *
 * @category   Krish Technolabs Module Development
 * @package    Krish_CorporateAccount
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

// Corporate Account View Page Styles
.corporate-account-view {
    .admin__page-section {
        margin-bottom: 2rem;
        
        .admin__page-section-title {
            .title {
                color: @color-brownie;
                font-size: 1.8rem;
                font-weight: 600;
                margin-bottom: 1rem;
                border-bottom: 2px solid @color-gray90;
                padding-bottom: 0.5rem;
            }
        }
    }

    // Status Badge Styles
    .status-badge {
        display: inline-block;
        padding: 0.4rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        &.status-pending {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            color: #2d3436;
            border: 1px solid #fdcb6e;
        }
        
        &.status-approved {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: #ffffff;
            border: 1px solid #00b894;
        }
        
        &.status-declined {
            background: linear-gradient(135deg, #e17055, #d63031);
            color: #ffffff;
            border: 1px solid #d63031;
        }
    }

    // Action Buttons
    .page-actions-buttons {
        margin-bottom: 2rem;
        
        .action-default {
            margin-right: 1rem;
            padding: 0.8rem 2rem;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
            
            &.primary {
                background: linear-gradient(135deg, #00b894, #00cec9);
                border-color: #00b894;
                color: #ffffff;
                
                &:hover {
                    background: linear-gradient(135deg, #00a085, #00b7b3);
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
                }
            }
            
            &:not(.primary) {
                background: linear-gradient(135deg, #e17055, #d63031);
                border-color: #d63031;
                color: #ffffff;
                
                &:hover {
                    background: linear-gradient(135deg, #d35843, #b92d2d);
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(214, 48, 49, 0.3);
                }
            }
        }
    }

    // Information Cards
    .admin__page-section-item {
        background: #ffffff;
        border: 1px solid @color-gray90;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: box-shadow 0.3s ease;
        
        &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .admin__page-section-item-title {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid @color-gray90;
            border-radius: 8px 8px 0 0;
            padding: 1rem 1.5rem;
            
            .title {
                color: @color-brownie;
                font-size: 1.2rem;
                font-weight: 600;
                margin: 0;
                display: flex;
                align-items: center;
                
                &:before {
                    content: '';
                    width: 4px;
                    height: 20px;
                    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
                    border-radius: 2px;
                    margin-right: 0.8rem;
                }
            }
        }
        
        .admin__page-section-item-content {
            padding: 1.5rem;
            
            .admin__table-secondary {
                width: 100%;
                
                th {
                    background: #f8f9fa;
                    color: @color-brownie;
                    font-weight: 600;
                    padding: 1rem;
                    border-bottom: 1px solid @color-gray90;
                    width: 30%;
                    vertical-align: top;
                }
                
                td {
                    padding: 1rem;
                    border-bottom: 1px solid @color-gray95;
                    vertical-align: top;
                    
                    &:last-child {
                        border-bottom: none;
                    }
                }
                
                tr:last-child {
                    th, td {
                        border-bottom: none;
                    }
                }
            }
        }
    }

    // Document Section Specific Styles
    .document-section {
        .admin__page-section-item-title .title:before {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }
        
        .document-download {
            display: inline-flex;
            align-items: center;
            padding: 0.6rem 1.2rem;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: #ffffff;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
            
            &:hover {
                background: linear-gradient(135deg, #5a9cfc, #0770c7);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
                text-decoration: none;
                color: #ffffff;
            }
            
            &:before {
                content: '📄';
                margin-right: 0.5rem;
                font-size: 1.1rem;
            }
        }
        
        .file-details {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid #fd79a8;
            margin-top: 1rem;
            
            code {
                background: #e9ecef;
                padding: 0.2rem 0.5rem;
                border-radius: 4px;
                font-family: 'Monaco', 'Consolas', monospace;
                font-size: 0.85rem;
                color: #495057;
            }
        }
    }

    // Customer Information Section
    .customer-section {
        .admin__page-section-item-title .title:before {
            background: linear-gradient(135deg, #00cec9, #55a3ff);
        }
        
        .customer-link {
            color: #0984e3;
            text-decoration: none;
            font-weight: 600;
            
            &:hover {
                color: #0770c7;
                text-decoration: underline;
            }
        }
    }

    // Company Information Section
    .company-section {
        .admin__page-section-item-title .title:before {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }
    }

    // Request Information Section
    .request-section {
        .admin__page-section-item-title .title:before {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
        }
    }

    // Back Navigation
    .admin__page-nav {
        margin-bottom: 2rem;
        
        .admin__page-nav-link {
            background: transparent;
            border: 2px solid @color-gray90;
            color: @color-brownie;
            padding: 0.8rem 1.5rem;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
            
            &:hover {
                background: @color-gray95;
                border-color: @color-gray80;
                transform: translateX(-4px);
            }
            
            .admin__page-nav-title:before {
                content: '←';
                margin-right: 0.5rem;
                font-size: 1.2rem;
            }
        }
    }

    // Responsive Design
    @media (max-width: 768px) {
        .admin__page-section-item {
            .admin__page-section-item-content {
                padding: 1rem;
                
                .admin__table-secondary {
                    th, td {
                        padding: 0.8rem;
                        display: block;
                        width: 100%;
                    }
                    
                    th {
                        background: @color-brownie;
                        color: #ffffff;
                        border-radius: 4px 4px 0 0;
                    }
                    
                    td {
                        border-radius: 0 0 4px 4px;
                        margin-bottom: 1rem;
                    }
                }
            }
        }
        
        .page-actions-buttons {
            .action-default {
                display: block;
                width: 100%;
                margin-bottom: 1rem;
                margin-right: 0;
            }
        }
    }
}
