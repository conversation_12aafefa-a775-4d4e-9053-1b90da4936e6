<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_CorporateAccount
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">corporate_account_listing.corporate_account_listing_data_source</item>
        </item>
    </argument>
    
    <settings>
        <buttons>
            <button name="refresh" class="Magento\Ui\Component\Control\Button">
                <url path="*/*/index"/>
                <class>primary</class>
                <label translate="true">Refresh</label>
            </button>
        </buttons>
        <spinner>corporate_account_columns</spinner>
        <deps>
            <dep>corporate_account_listing.corporate_account_listing_data_source</dep>
        </deps>
    </settings>
    
    <dataSource name="corporate_account_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">entity_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Krish_CorporateAccount::corporate_account_view</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="corporate_account_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
            <filterSelect name="status" provider="${ $.parentName }">
                <settings>
                    <captionValue>0</captionValue>
                    <options class="Krish\CorporateAccount\Ui\Component\Listing\Column\Status\Options"/>
                    <caption translate="true">Select...</caption>
                    <label translate="true">Status</label>
                    <dataScope>status</dataScope>
                    <imports>
                        <link name="visible">componentType</link>
                    </imports>
                </settings>
            </filterSelect>
        </filters>
        <massaction name="listing_massaction">
            <action name="approve">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to approve selected requests?</message>
                        <title translate="true">Approve Requests</title>
                    </confirm>
                    <url path="corporate/account/massApprove"/>
                    <type>approve</type>
                    <label translate="true">Approve</label>
                </settings>
            </action>
            <action name="decline">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to decline selected requests?</message>
                        <title translate="true">Decline Requests</title>
                    </confirm>
                    <url path="corporate/account/massDecline"/>
                    <type>decline</type>
                    <label translate="true">Decline</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    
    <columns name="corporate_account_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="corporate/account/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">entity_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">corporate_account_listing.corporate_account_listing.corporate_account_columns.ids</param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">corporate_account_listing.corporate_account_listing.corporate_account_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>entity_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="entity_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="customer_firstname">
            <settings>
                <filter>text</filter>
                <label translate="true">First Name</label>
            </settings>
        </column>
        <column name="customer_lastname">
            <settings>
                <filter>text</filter>
                <label translate="true">Last Name</label>
            </settings>
        </column>
        <column name="customer_email">
            <settings>
                <filter>text</filter>
                <label translate="true">Customer Email</label>
            </settings>
        </column>
        <column name="company_name">
            <settings>
                <filter>text</filter>
                <label translate="true">Company Name</label>
            </settings>
        </column>
        <column name="company_type">
            <settings>
                <filter>text</filter>
                <label translate="true">Company Type</label>
            </settings>
        </column>
        <column name="business_email">
            <settings>
                <filter>text</filter>
                <label translate="true">Business Email</label>
            </settings>
        </column>
        <column name="business_phone">
            <settings>
                <filter>text</filter>
                <label translate="true">Business Phone</label>
            </settings>
        </column>
        <column name="status" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <options class="Krish\CorporateAccount\Ui\Component\Listing\Column\Status\Options"/>
                <filter>select</filter>
                <editor>
                    <editorType>select</editorType>
                </editor>
                <dataType>select</dataType>
                <label translate="true">Status</label>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created At</label>
            </settings>
        </column>
        <actionsColumn name="actions" class="Krish\CorporateAccount\Ui\Component\Listing\Column\Actions">
            <settings>
                <indexField>entity_id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>
