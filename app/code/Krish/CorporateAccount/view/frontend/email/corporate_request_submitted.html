<!--@subject Corporate Account Request Submitted @-->
<!--@vars {
"var customer.name":"Customer Name",
"var customer.email":"Customer Email",
"var company_name":"Company Name",
"var company_type":"Company Type",
"var business_email":"Business Email",
"var business_phone":"Business Phone",
"var store.frontend_name":"Store Name"
} @-->

{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <p class="greeting">{{trans "Dear %customer_name," customer_name=$customer.name}}</p>
            <p>
                {{trans "Thank you for submitting your corporate account request. We have received your application and it is currently under review."}}
            </p>
        </td>
    </tr>
    <tr class="email-summary">
        <td>
            <h3>{{trans "Request Details:"}}</h3>
            <table class="order-details">
                <tr>
                    <td><strong>{{trans "Customer Name:"}}</strong></td>
                    <td>{{var customer.name}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Customer Email:"}}</strong></td>
                    <td>{{var customer.email}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Company Name:"}}</strong></td>
                    <td>{{var company_name}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Company Type:"}}</strong></td>
                    <td>{{var company_type}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Business Email:"}}</strong></td>
                    <td>{{var business_email}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Business Phone:"}}</strong></td>
                    <td>{{var business_phone}}</td>
                </tr>
            </table>
        </td>
    </tr>
    <tr class="email-information">
        <td>
            <p>
                {{trans "Your request will be reviewed by our team within 2-3 business days. You will receive an email notification once your request has been processed."}}
            </p>
            <p>
                {{trans "If you have any questions, please contact us at %store_email or call %store_phone." store_email=$store_email store_phone=$store_phone}}
            </p>
            <p>
                {{trans "Thank you for choosing %store_name." store_name=$store.frontend_name}}
            </p>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
