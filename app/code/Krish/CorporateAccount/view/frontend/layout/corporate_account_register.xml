<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_CorporateAccount
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <title>Corporate Account Registration</title>
    </head>
    <body>
        <referenceContainer name="content">
          <container name="customer.create.page.wrapper" htmlTag="div" htmlClass="customer-forgotpassword-page-wrapper" before="-">
                <block class="Krish\CorporateAccount\Block\Account\Register" name="corporate_account_register" template="Krish_CorporateAccount::account/register.phtml">
                    <container name="form.additional.info" as="form_additional_info"/>
                    <container name="customer.form.register.fields.before" as="form_fields_before"/>
                </block>
            </container>
        </referenceContainer>

         <!-- Move the page title into the new container -->
        <move element="page.main.title" destination="customer.create.page.wrapper" before="-"/>
    </body>
</page>
