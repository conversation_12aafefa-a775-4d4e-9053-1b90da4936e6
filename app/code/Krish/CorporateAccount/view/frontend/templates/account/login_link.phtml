<?php
/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \Magento\Framework\Escaper $escaper
 */
?>

<div class="block block-corporate-register">
    <div class="block-title">
        <strong><?= $escaper->escapeHtml(__('Corporate Account')) ?></strong>
    </div>
    <div class="block-content">
        <p><?= $escaper->escapeHtml(__('Are you a business customer? Register for a corporate account to access special pricing and features.')) ?></p>
        <div class="actions-toolbar">
            <div class="primary">
                <a href="<?= $escaper->escapeUrl($block->getUrl('corporate/account/register')) ?>" 
                   class="action create corporate primary">
                    <span><?= $escaper->escapeHtml(__('Create Corporate Account')) ?></span>
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.block-corporate-register {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.block-corporate-register .block-title {
    margin-bottom: 10px;
}

.block-corporate-register .block-title strong {
    color: #333;
    font-size: 16px;
}

.block-corporate-register .block-content p {
    margin-bottom: 15px;
    color: #666;
}

.block-corporate-register .action.create.corporate {
    background-color: #1979c3;
    border: 1px solid #1979c3;
    color: #fff;
    display: inline-block;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.36;
    padding: 7px 15px;
    text-decoration: none;
    vertical-align: baseline;
    border-radius: 3px;
}

.block-corporate-register .action.create.corporate:hover {
    background-color: #006bb4;
    border-color: #006bb4;
    color: #fff;
    text-decoration: none;
}
</style>
