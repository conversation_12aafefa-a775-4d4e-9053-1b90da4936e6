<?php
/**
 * @var \Krish\CorporateAccount\Block\Account\Register $block
 * @var \Magento\Framework\Escaper $escaper
 */

$formData = $block->getFormData();
?>

<div class="page-title-wrapper">
    <h1 class="page-title">
        <span class="base"><?= $escaper->escapeHtml(__('Corporate Account Registration')) ?></span>
    </h1>
</div>

<?= $block->getChildHtml('form_fields_before') ?>

<form class="form create account form-create-account"
      action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
      method="post"
      id="form-validate"
      enctype="multipart/form-data"
      autocomplete="off">
    <?= /* @noEscape */ $block->getBlockHtml('formkey') ?>
    
    <fieldset class="fieldset create info">
        <legend class="legend"><span><?= $escaper->escapeHtml(__('Personal Information')) ?></span></legend><br>
        
        <input type="hidden" name="success_url" value="<?= $escaper->escapeUrl($block->getSuccessUrl()) ?>">
        <input type="hidden" name="error_url" value="<?= $escaper->escapeUrl($block->getErrorUrl()) ?>">
        
        <?= $block->getLayout()
                ->createBlock(\Magento\Customer\Block\Widget\Name::class)
                ->setObject($formData)
                ->setForceUseCustomerAttributes(true)
                ->toHtml() ?>

        <?php $_dob = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Dob::class) ?>
        <?php if ($_dob->isEnabled()): ?>
            <?= $_dob->setDate($formData->getDob())->toHtml() ?>
        <?php endif ?>

        <?php $_taxvat = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class) ?>
        <?php if ($_taxvat->isEnabled()): ?>
            <?= $_taxvat->setTaxvat($formData->getTaxvat())->toHtml() ?>
        <?php endif ?>

        <?php $_gender = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Gender::class) ?>
        <?php if ($_gender->isEnabled()): ?>
            <?= $_gender->setGender($formData->getGender())->toHtml() ?>
        <?php endif ?>
    </fieldset>

    <fieldset class="fieldset create corporate">
        <legend class="legend"><span><?= $escaper->escapeHtml(__('Company Information')) ?></span></legend><br>
        
        <div class="field company_name required">
            <label for="company_name" class="label"><span><?= $escaper->escapeHtml(__('Company Name')) ?></span></label>
            <div class="control">
                <input type="text" 
                       name="company_name" 
                       id="company_name"
                       value="<?= $escaper->escapeHtmlAttr($formData->getCompanyName()) ?>"
                       title="<?= $escaper->escapeHtmlAttr(__('Company Name')) ?>"
                       class="input-text required-entry"
                       data-validate="{required:true}">
            </div>
        </div>

        <div class="field company_type required">
            <label for="company_type" class="label"><span><?= $escaper->escapeHtml(__('Company Type')) ?></span></label>
            <div class="control">
                <select name="company_type" 
                        id="company_type"
                        title="<?= $escaper->escapeHtmlAttr(__('Company Type')) ?>"
                        class="select required-entry"
                        data-validate="{required:true}">
                    <option value=""><?= $escaper->escapeHtml(__('Please select...')) ?></option>
                    <?php foreach ($block->getCompanyTypes() as $value => $label): ?>
                        <option value="<?= $escaper->escapeHtmlAttr($value) ?>"
                                <?= $formData->getCompanyType() == $value ? 'selected="selected"' : '' ?>>
                            <?= $escaper->escapeHtml($label) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <div class="field gst_number">
            <label for="gst_number" class="label"><span><?= $escaper->escapeHtml(__('GST Number / Tax ID')) ?></span></label>
            <div class="control">
                <input type="text" 
                       name="gst_number" 
                       id="gst_number"
                       value="<?= $escaper->escapeHtmlAttr($formData->getGstNumber()) ?>"
                       title="<?= $escaper->escapeHtmlAttr(__('GST Number / Tax ID')) ?>"
                       class="input-text">
            </div>
        </div>

        <div class="field business_email required">
            <label for="business_email" class="label"><span><?= $escaper->escapeHtml(__('Business Contact Email')) ?></span></label>
            <div class="control">
                <input type="email" 
                       name="business_email" 
                       id="business_email"
                       value="<?= $escaper->escapeHtmlAttr($formData->getBusinessEmail()) ?>"
                       title="<?= $escaper->escapeHtmlAttr(__('Business Contact Email')) ?>"
                       class="input-text required-entry validate-email"
                       data-validate="{required:true, 'validate-email':true}">
            </div>
        </div>

        <div class="field business_phone required">
            <label for="business_phone" class="label"><span><?= $escaper->escapeHtml(__('Business Contact Phone')) ?></span></label>
            <div class="control">
                <input type="tel" 
                       name="business_phone" 
                       id="business_phone"
                       value="<?= $escaper->escapeHtmlAttr($formData->getBusinessPhone()) ?>"
                       title="<?= $escaper->escapeHtmlAttr(__('Business Contact Phone')) ?>"
                       class="input-text required-entry"
                       data-validate="{required:true}">
            </div>
        </div>

        <div class="field corporate_document">
            <label for="corporate_document" class="label">
                <span><?= $escaper->escapeHtml(__('Upload Document (Optional)')) ?></span>
            </label>
            <div class="control">
                <input type="file" 
                       name="corporate_document" 
                       id="corporate_document"
                       title="<?= $escaper->escapeHtmlAttr(__('Upload Document')) ?>"
                       class="input-file"
                       accept=".pdf,.jpg,.jpeg,.png">
                <div class="note">
                    <?= $escaper->escapeHtml(__('Allowed file types: %1', $block->getAllowedFileExtensionsString())) ?><br>
                    <?= $escaper->escapeHtml(__('Maximum file size: %1 MB', $block->getMaxFileSize())) ?>
                </div>
            </div>
        </div>
    </fieldset>

    <fieldset class="fieldset create account" data-hasrequired="<?= $escaper->escapeHtmlAttr(__('* Required Fields')) ?>">
        <legend class="legend"><span><?= $escaper->escapeHtml(__('Sign-in Information')) ?></span></legend><br>
        
        <div class="field email required">
            <label for="email_address" class="label"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label>
            <div class="control">
                <input type="email"
                       name="email"
                       autocomplete="email"
                       id="email_address"
                       value="<?= $escaper->escapeHtmlAttr($formData->getEmail()) ?>"
                       title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                       class="input-text required-entry validate-email"
                       data-validate="{required:true, 'validate-email':true}">
            </div>
        </div>
        
        <div class="field password required">
            <label for="password" class="label"><span><?= $escaper->escapeHtml(__('Password')) ?></span></label>
            <div class="control">
                <input type="password" 
                       name="password" 
                       id="password"
                       title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                       class="input-text required-entry validate-customer-password"
                       data-password-min-length="<?= $escaper->escapeHtmlAttr($block->getMinimumPasswordLength()) ?>"
                       data-password-min-character-sets="<?= $escaper->escapeHtmlAttr($block->getRequiredCharacterClassesNumber()) ?>"
                       data-validate="{required:true, 'validate-customer-password':true}"
                       autocomplete="off">
                <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                    <div id="password-strength-meter" class="password-strength-meter">
                        <?= $escaper->escapeHtml(__('Password Strength')) ?>:
                        <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                            <?= $escaper->escapeHtml(__('No Password')) ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="field confirmation required">
            <label for="password-confirmation" class="label"><span><?= $escaper->escapeHtml(__('Confirm Password')) ?></span></label>
            <div class="control">
                <input type="password" 
                       name="password_confirmation" 
                       id="password-confirmation"
                       title="<?= $escaper->escapeHtmlAttr(__('Confirm Password')) ?>"
                       class="input-text required-entry validate-cpassword"
                       data-validate="{required:true, equalTo:'#password'}"
                       autocomplete="off">
            </div>
        </div>
        
        <?= $block->getChildHtml('form_additional_info') ?>
    </fieldset>

    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit" class="action submit primary" title="<?= $escaper->escapeHtmlAttr(__('Create Corporate Account')) ?>">
                <span><?= $escaper->escapeHtml(__('Create Corporate Account')) ?></span>
            </button>
        </div>
        <div class="secondary">
            <a class="action back" href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                <span><?= $escaper->escapeHtml(__('Back')) ?></span>
            </a>
        </div>
    </div>
</form>

<script type="text/x-magento-init">
{
    "#form-validate": {
        "validation": {},
        "passwordStrengthIndicator": {
            "formSelector": "#form-validate"
        }
    }
}
</script>
