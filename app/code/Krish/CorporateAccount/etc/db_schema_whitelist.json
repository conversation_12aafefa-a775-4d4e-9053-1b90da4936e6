{"krish_corporate_account_request": {"column": {"entity_id": true, "customer_id": true, "company_name": true, "company_type": true, "gst_number": true, "business_email": true, "business_phone": true, "uploaded_document": true, "status": true, "approved_by": true, "created_at": true, "updated_at": true, "processed_at": true}, "constraint": {"PRIMARY": true, "KRISH_CORPORATE_ACCOUNT_REQUEST_CUSTOMER_ID_CUSTOMER_ENTITY_ENTITY_ID": true, "KRISH_CORPORATE_ACCOUNT_REQUEST_APPROVED_BY_ADMIN_USER_USER_ID": true}, "index": {"KRISH_CORPORATE_ACCOUNT_REQUEST_CUSTOMER_ID": true, "KRISH_CORPORATE_ACCOUNT_REQUEST_STATUS": true, "KRISH_CORPORATE_ACCOUNT_REQUEST_CREATED_AT": true}}}