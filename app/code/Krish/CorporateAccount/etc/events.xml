<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_CorporateAccount
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    
    <!-- Observer for corporate account request submission -->
    <event name="krish_corporate_account_request_submitted">
        <observer name="send_submission_email" instance="Krish\CorporateAccount\Observer\SendSubmissionEmailObserver"/>
        <observer name="send_admin_notification" instance="Krish\CorporateAccount\Observer\SendAdminNotificationObserver"/>
        <observer name="assign_customer_group" instance="Krish\CorporateAccount\Observer\AssignCustomerGroupObserver"/>
    </event>
    
    <!-- Observer for corporate account request approval -->
    <event name="krish_corporate_account_request_approved">
        <observer name="send_approval_email" instance="Krish\CorporateAccount\Observer\SendApprovalEmailObserver"/>
        <observer name="update_customer_group" instance="Krish\CorporateAccount\Observer\UpdateCustomerGroupObserver"/>
    </event>
    
    <!-- Observer for corporate account request decline -->
    <event name="krish_corporate_account_request_declined">
        <observer name="send_decline_email" instance="Krish\CorporateAccount\Observer\SendDeclineEmailObserver"/>
        <observer name="revert_customer_group" instance="Krish\CorporateAccount\Observer\RevertCustomerGroupObserver"/>
    </event>
    
</config>
