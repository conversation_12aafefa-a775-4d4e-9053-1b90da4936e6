<?xml version="1.0"?>
<!--
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_CorporateAccount
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="krish_corporate_account_request" resource="default" engine="innodb" comment="Corporate Account Request Table">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" identity="true" comment="Entity ID"/>
        <column xsi:type="int" name="customer_id" unsigned="true" nullable="false" comment="Customer ID"/>
        <column xsi:type="varchar" name="company_name" nullable="false" length="255" comment="Company Name"/>
        <column xsi:type="varchar" name="company_type" nullable="false" length="100" comment="Company Type"/>
        <column xsi:type="varchar" name="gst_number" nullable="true" length="50" comment="GST Number or Tax ID"/>
        <column xsi:type="varchar" name="business_email" nullable="false" length="255" comment="Business Contact Email"/>
        <column xsi:type="varchar" name="business_phone" nullable="false" length="50" comment="Business Contact Phone"/>
        <column xsi:type="text" name="uploaded_document" nullable="true" comment="Uploaded Document Path"/>
        <column xsi:type="varchar" name="status" nullable="false" length="20" default="pending" comment="Request Status"/>
        <column xsi:type="int" name="approved_by" unsigned="true" nullable="true" comment="Admin User ID who approved/declined"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP" comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP" comment="Updated At"/>
        <column xsi:type="timestamp" name="processed_at" nullable="true" comment="Processed At"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>

        <constraint xsi:type="foreign" referenceId="KRISH_CORPORATE_ACCOUNT_REQUEST_CUSTOMER_ID_CUSTOMER_ENTITY_ENTITY_ID"
                    table="krish_corporate_account_request" column="customer_id"
                    referenceTable="customer_entity" referenceColumn="entity_id" onDelete="CASCADE"/>

        <constraint xsi:type="foreign" referenceId="KRISH_CORPORATE_ACCOUNT_REQUEST_APPROVED_BY_ADMIN_USER_USER_ID"
                    table="krish_corporate_account_request" column="approved_by"
                    referenceTable="admin_user" referenceColumn="user_id" onDelete="SET NULL"/>

        <index referenceId="KRISH_CORPORATE_ACCOUNT_REQUEST_CUSTOMER_ID" indexType="btree">
            <column name="customer_id"/>
        </index>

        <index referenceId="KRISH_CORPORATE_ACCOUNT_REQUEST_STATUS" indexType="btree">
            <column name="status"/>
        </index>

        <index referenceId="KRISH_CORPORATE_ACCOUNT_REQUEST_CREATED_AT" indexType="btree">
            <column name="created_at"/>
        </index>
    </table>
</schema>
