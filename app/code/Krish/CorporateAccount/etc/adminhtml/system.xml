<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_CorporateAccount
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="krish" translate="label" sortOrder="200">
            <label>Krish Extensions</label>
        </tab>

        <section id="krish_corporate_account" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Corporate Account</label>
            <tab>krish</tab>
            <resource>Krish_CorporateAccount::config</resource>

            <!-- General Settings Group -->
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1">
                <label>General Settings</label>

                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1">
                    <label>Enable Corporate Account</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable/disable corporate account registration functionality</comment>
                </field>
            </group>

            <!-- Email Settings Group -->
            <group id="email" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Email Settings</label>

                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Email Notifications</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable/disable email notifications for corporate account requests</comment>
                </field>

                <field id="sender_identity" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    <comment>Email identity to be used as sender for corporate account emails</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>

                <field id="admin_email" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Admin Notification Email</label>
                    <comment>Email address to receive admin notifications for new corporate account requests</comment>
                    <validate>validate-email</validate>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>

                <field id="copy_to" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Copy To</label>
                    <comment>Comma-separated email addresses to receive copies of all corporate account emails</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>

                <field id="copy_method" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Copy Method</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Method</source_model>
                    <comment>Method for sending email copies</comment>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="copy_to" separator=",">1</field>
                    </depends>
                </field>
            </group>

            <!-- Email Templates Group -->
            <group id="email_templates" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Email Templates</label>

                <field id="template_submitted" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Request Submitted Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <comment>Email template for customer when corporate account request is submitted</comment>
                </field>

                <field id="template_approved" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Request Approved Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <comment>Email template for customer when corporate account request is approved</comment>
                </field>

                <field id="template_declined" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Request Declined Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <comment>Email template for customer when corporate account request is declined</comment>
                </field>

                <field id="template_admin_notification" translate="label comment" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Admin Notification Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <comment>Email template for admin notification when new corporate account request is submitted</comment>
                </field>
            </group>
        </section>
    </system>
</config>
