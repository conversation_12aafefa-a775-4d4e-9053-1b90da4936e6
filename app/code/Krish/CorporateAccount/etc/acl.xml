<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_CorporateAccount
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Customer::customer">
                    <resource id="Krish_CorporateAccount::corporate_account" title="Corporate Account Requests" sortOrder="100">
                        <resource id="Krish_CorporateAccount::corporate_account_view" title="View" sortOrder="10"/>
                        <resource id="Krish_CorporateAccount::corporate_account_approve" title="Approve" sortOrder="20"/>
                        <resource id="Krish_CorporateAccount::corporate_account_decline" title="Decline" sortOrder="30"/>
                        <resource id="Krish_CorporateAccount::corporate_account_delete" title="Delete" sortOrder="40"/>
                    </resource>
                </resource>
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Krish_CorporateAccount::config" title="Corporate Account Configuration" sortOrder="100"/>
                        </resource>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
