<?xml version="1.0"?>
<!--
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_CorporateAccount
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- Repository Interface Preference -->
    <preference for="Krish\CorporateAccount\Api\CorporateAccountRequestRepositoryInterface"
                type="Krish\CorporateAccount\Model\CorporateAccountRequestRepository"/>

    <!-- Data Interface Preference -->
    <preference for="Krish\CorporateAccount\Api\Data\CorporateAccountRequestInterface"
                type="Krish\CorporateAccount\Model\CorporateAccountRequest"/>

    <!-- Virtual Types for Search Results -->
    <virtualType name="Krish\CorporateAccount\Model\Api\SearchCriteria\CorporateAccountRequestCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="filters" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor</item>
                <item name="sorting" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\SortingProcessor</item>
                <item name="pagination" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\PaginationProcessor</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Inject Collection Processor -->
    <type name="Krish\CorporateAccount\Model\CorporateAccountRequestRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Krish\CorporateAccount\Model\Api\SearchCriteria\CorporateAccountRequestCollectionProcessor</argument>
        </arguments>
    </type>

    <!-- Custom Logger -->
    <virtualType name="Krish\CorporateAccount\Logger\Handler" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/krish_corporate_account.log</argument>
        </arguments>
    </virtualType>

    <virtualType name="Krish\CorporateAccount\Logger\Logger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">Krish\CorporateAccount\Logger\Handler</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- File Uploader -->
    <type name="Krish\CorporateAccount\Model\FileUploader">
        <arguments>
            <argument name="logger" xsi:type="object">Krish\CorporateAccount\Logger\Logger</argument>
        </arguments>
    </type>

    <!-- Customer Authentication Plugin -->
    <type name="Magento\Customer\Model\Authentication">
        <plugin name="krish_corporate_account_authentication"
                type="Krish\CorporateAccount\Plugin\CustomerAuthentication"
                sortOrder="10"/>
    </type>

    <!-- Customer Session Plugin -->
    <type name="Magento\Customer\Model\Session">
        <plugin name="krish_corporate_account_session"
                type="Krish\CorporateAccount\Plugin\CustomerSession"
                sortOrder="10"/>
    </type>

    <!-- Data Provider for Admin Grid -->
    <virtualType name="CorporateAccountGridDataProvider" type="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Krish\CorporateAccount\Model\ResourceModel\CorporateAccountRequest\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">CorporateAccountGridFilterPool</argument>
        </arguments>
    </virtualType>

    <virtualType name="CorporateAccountGridFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="corporate_account_listing_data_source" xsi:type="string">Krish\CorporateAccount\Model\ResourceModel\CorporateAccountRequest\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

</config>
