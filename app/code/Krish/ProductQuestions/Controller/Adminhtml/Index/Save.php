<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Controller\Adminhtml\Index;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterface;

/**
 * Admin question save controller
 */
class Save extends Action
{
    /**
     * Authorization level of a basic admin session
     */
    public const ADMIN_RESOURCE = 'Krish_ProductQuestions::product_questions_manage';

    /**
     * @var QuestionRepositoryInterface
     */
    private $questionRepository;

    /**
     * @param Context $context
     * @param QuestionRepositoryInterface $questionRepository
     */
    public function __construct(
        Context $context,
        QuestionRepositoryInterface $questionRepository
    ) {
        parent::__construct($context);
        $this->questionRepository = $questionRepository;
    }

    /**
     * Execute action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $questionId = (int) $this->getRequest()->getParam('question_id');

        if (!$questionId) {
            $this->messageManager->addErrorMessage(__('Question ID is required.'));
            return $resultRedirect->setPath('*/*/');
        }

        try {
            $question = $this->questionRepository->getById($questionId);
            $data = $this->getRequest()->getPostValue();

            // Update question data
            if (isset($data['answer'])) {
                $question->setAnswer($data['answer']);
            }

            if (isset($data['status'])) {
                $question->setStatus((int) $data['status']);
            }

            if (isset($data['visible_on_frontend'])) {
                $question->setVisibleOnFrontend((bool) $data['visible_on_frontend']);
            }

            // Set answered date if status changed to approved/rejected and answer is provided
            if ($question->getStatus() != QuestionInterface::STATUS_PENDING &&
                $question->getAnswer() &&
                !$question->getAnsweredAt()) {
                $question->setAnsweredAt(date('Y-m-d H:i:s'));
            }

            // Save question (this will trigger events for email notifications)
            $this->questionRepository->save($question);

            $this->messageManager->addSuccessMessage(__('Question has been saved successfully.'));

            // Check if 'Save and Continue' was clicked
            if ($this->getRequest()->getParam('back')) {
                return $resultRedirect->setPath('*/*/edit', ['id' => $questionId]);
            }

            return $resultRedirect->setPath('*/*/');

        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__('Question not found.'));
            return $resultRedirect->setPath('*/*/');
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            return $resultRedirect->setPath('*/*/edit', ['id' => $questionId]);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while saving the question.'));
            return $resultRedirect->setPath('*/*/edit', ['id' => $questionId]);
        }
    }
}
