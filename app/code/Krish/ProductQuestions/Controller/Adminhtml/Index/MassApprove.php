<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Controller\Adminhtml\Index;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\ResultFactory;
use Magento\Ui\Component\MassAction\Filter;
use Krish\ProductQuestions\Model\ResourceModel\Question\CollectionFactory;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterface;
use Krish\ProductQuestions\Helper\Email as EmailHelper;

/**
 * Mass approve questions controller
 */
class MassApprove extends Action
{
    /**
     * Authorization level of a basic admin session
     */
    const ADMIN_RESOURCE = 'Krish_ProductQuestions::product_questions_approve';

    /**
     * @var Filter
     */
    private $filter;

    /**
     * @var CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var QuestionRepositoryInterface
     */
    private $questionRepository;

    /**
     * @var EmailHelper
     */
    private $emailHelper;

    /**
     * @param Context $context
     * @param Filter $filter
     * @param CollectionFactory $collectionFactory
     * @param QuestionRepositoryInterface $questionRepository
     * @param EmailHelper $emailHelper
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory,
        QuestionRepositoryInterface $questionRepository,
        EmailHelper $emailHelper
    ) {
        parent::__construct($context);
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        $this->questionRepository = $questionRepository;
        $this->emailHelper = $emailHelper;
    }

    /**
     * Execute action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        try {
            $collection = $this->filter->getCollection($this->collectionFactory->create());
            $collectionSize = $collection->getSize();
            $approvedCount = 0;

            foreach ($collection as $question) {
                $originalStatus = $question->getStatus();
                
                $question->setStatus(QuestionInterface::STATUS_APPROVED);
                $question->setVisibleOnFrontend(true);
                
                if (!$question->getAnsweredAt()) {
                    $question->setAnsweredAt(date('Y-m-d H:i:s'));
                }
                
                $this->questionRepository->save($question);
                
                // Send email notification if status changed
                if ($originalStatus != QuestionInterface::STATUS_APPROVED) {
                    $this->emailHelper->sendQuestionApprovedEmail($question);
                }
                
                $approvedCount++;
            }

            $this->messageManager->addSuccessMessage(
                __('A total of %1 question(s) have been approved.', $approvedCount)
            );
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(
                __('An error occurred while approving questions: %1', $e->getMessage())
            );
        }

        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        return $resultRedirect->setPath('*/*/');
    }
}
