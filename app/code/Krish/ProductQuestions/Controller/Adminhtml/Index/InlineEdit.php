<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Controller\Adminhtml\Index;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Inline edit controller
 */
class InlineEdit extends Action
{
    /**
     * Authorization level of a basic admin session
     */
    const ADMIN_RESOURCE = 'Krish_ProductQuestions::product_questions_manage';

    /**
     * @param Context $context
     * @param JsonFactory $jsonFactory
     * @param QuestionRepositoryInterface $questionRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        private readonly JsonFactory $jsonFactory,
        private readonly QuestionRepositoryInterface $questionRepository,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Execute action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $resultJson = $this->jsonFactory->create();
        $error = false;
        $messages = [];

        if ($this->getRequest()->getParam('isAjax')) {
            $postItems = $this->getRequest()->getParam('items', []);
            if (!count($postItems)) {
                $messages[] = __('Please correct the data sent.');
                $error = true;
            } else {
                foreach (array_keys($postItems) as $questionId) {
                    try {
                        $question = $this->questionRepository->getById((int)$questionId);
                        $questionData = $postItems[$questionId];
                        $this->setQuestionData($question, $questionData);
                        $this->questionRepository->save($question);
                    } catch (LocalizedException $e) {
                        $messages[] = $this->getErrorWithQuestionId(
                            $question,
                            __($e->getMessage())
                        );
                        $error = true;
                    } catch (\Exception $e) {
                        $this->logger->critical($e);
                        $messages[] = $this->getErrorWithQuestionId(
                            $question,
                            __('Something went wrong while saving the question.')
                        );
                        $error = true;
                    }
                }
            }
        }

        return $resultJson->setData([
            'messages' => $messages,
            'error' => $error
        ]);
    }

    /**
     * Set question data
     *
     * @param \Krish\ProductQuestions\Api\Data\QuestionInterface $question
     * @param array $questionData
     * @return $this
     */
    private function setQuestionData($question, array $questionData)
    {
        if (isset($questionData['status'])) {
            $question->setStatus((int)$questionData['status']);
        }
        if (isset($questionData['visible_on_frontend'])) {
            $question->setVisibleOnFrontend((bool)$questionData['visible_on_frontend']);
        }
        if (isset($questionData['answer'])) {
            $question->setAnswer($questionData['answer']);
        }

        return $this;
    }

    /**
     * Get error with question id
     *
     * @param \Krish\ProductQuestions\Api\Data\QuestionInterface $question
     * @param string $errorText
     * @return string
     */
    private function getErrorWithQuestionId($question, $errorText)
    {
        return '[Question ID: ' . $question->getQuestionId() . '] ' . $errorText;
    }
}
