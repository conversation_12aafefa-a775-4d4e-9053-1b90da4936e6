<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Controller\Adminhtml\Index;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Edit question controller
 */
class Edit extends Action
{
    /**
     * Authorization level of a basic admin session
     */
    const ADMIN_RESOURCE = 'Krish_ProductQuestions::product_questions_manage';

    /**
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param QuestionRepositoryInterface $questionRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        private readonly PageFactory $resultPageFactory,
        private readonly QuestionRepositoryInterface $questionRepository,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Execute action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $id = (int) $this->getRequest()->getParam('id');
        $resultPage = $this->resultPageFactory->create();

        if ($id) {
            try {
                $question = $this->questionRepository->getById($id);
                $resultPage->setActiveMenu('Krish_ProductQuestions::product_questions');
                $resultPage->getConfig()->getTitle()->prepend(__('Edit Question'));
                $resultPage->getConfig()->getTitle()->prepend(__('Review Question #%1', $question->getQuestionId()));
            } catch (NoSuchEntityException $e) {
                $this->logger->critical($e);
                $this->messageManager->addErrorMessage(__('This question no longer exists.'));
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
        } else {
            $resultPage->setActiveMenu('Krish_ProductQuestions::product_questions');
            $resultPage->getConfig()->getTitle()->prepend(__('New Question'));
        }

        return $resultPage;
    }
}
