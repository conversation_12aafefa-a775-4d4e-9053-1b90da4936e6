<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Controller\Question;

use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Kris<PERSON>\ProductQuestions\Api\Data\QuestionInterfaceFactory;
use Krish\ProductQuestions\Helper\Data as DataHelper;
use Krish\ProductQuestions\Helper\Email as EmailHelper;
use Psr\Log\LoggerInterface;

/**
 * Submit question controller
 */
class Submit implements HttpPostActionInterface
{
    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var JsonFactory
     */
    private $jsonFactory;

    /**
     * @var CustomerSession
     */
    private $customerSession;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var QuestionRepositoryInterface
     */
    private $questionRepository;

    /**
     * @var QuestionInterfaceFactory
     */
    private $questionFactory;

    /**
     * @var DataHelper
     */
    private $dataHelper;

    /**
     * @var EmailHelper
     */
    private $emailHelper;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param RequestInterface $request
     * @param JsonFactory $jsonFactory
     * @param CustomerSession $customerSession
     * @param StoreManagerInterface $storeManager
     * @param ProductRepositoryInterface $productRepository
     * @param QuestionRepositoryInterface $questionRepository
     * @param QuestionInterfaceFactory $questionFactory
     * @param DataHelper $dataHelper
     * @param EmailHelper $emailHelper
     * @param LoggerInterface $logger
     */
    public function __construct(
        RequestInterface $request,
        JsonFactory $jsonFactory,
        CustomerSession $customerSession,
        StoreManagerInterface $storeManager,
        ProductRepositoryInterface $productRepository,
        QuestionRepositoryInterface $questionRepository,
        QuestionInterfaceFactory $questionFactory,
        DataHelper $dataHelper,
        EmailHelper $emailHelper,
        LoggerInterface $logger
    ) {
        $this->request = $request;
        $this->jsonFactory = $jsonFactory;
        $this->customerSession = $customerSession;
        $this->storeManager = $storeManager;
        $this->productRepository = $productRepository;
        $this->questionRepository = $questionRepository;
        $this->questionFactory = $questionFactory;
        $this->dataHelper = $dataHelper;
        $this->emailHelper = $emailHelper;
        $this->logger = $logger;
    }

    /**
     * Execute action
     *
     * @return ResponseInterface|ResultInterface
     */
    public function execute()
    {
        $result = $this->jsonFactory->create();

        try {
            // Check if module is enabled
            if (!$this->dataHelper->isEnabled()) {
                throw new LocalizedException(__('Product questions feature is disabled.'));
            }

            // Validate request method
            if (!$this->request->isPost()) {
                throw new LocalizedException(__('Invalid request method.'));
            }

            $params = $this->request->getParams();
            // Get form data
            $productSku = $this->request->getParam('product_sku');
            $question = trim($this->request->getParam('question'));

            // Validate required fields
            if (!$productSku || !$question) {
                throw new LocalizedException(__('Please fill in all required fields.'));
            }

            // Check if customer is logged in (if required)
            if ($this->dataHelper->isLoginRequired() && !$this->customerSession->isLoggedIn()) {
                throw new LocalizedException(__('Please log in to submit a question.'));
            }

            // Validate product exists
            try {
                $product = $this->productRepository->get($productSku);
            } catch (\Exception $e) {
                throw new LocalizedException(__('Product not found.'));
            }

            // Get customer data
            $customer = $this->customerSession->getCustomer();
            $customerEmail = $customer->getEmail() ?: $this->request->getParam('customer_email');
            $customerName = $customer->getName() ?:
                           ($customer->getFirstname() . ' ' . $customer->getLastname()) ?:
                           $this->request->getParam('customer_name');

            if (!$customerEmail || !$customerName) {
                throw new LocalizedException(__('Customer information is required.'));
            }

            // Create question
            $questionModel = $this->questionFactory->create();
            $questionModel->setCustomerEmail($customerEmail);
            $questionModel->setCustomerName(trim($customerName));
            $questionModel->setCustomerId($customer->getId() ?: null);
            $questionModel->setProductSku($productSku);
            $questionModel->setQuestion($question);
            $questionModel->setStoreId($this->storeManager->getStore()->getId());

            // Set status based on auto-approve setting
            if ($this->dataHelper->isAutoApproveEnabled()) {
                $questionModel->setStatus(\Krish\ProductQuestions\Api\Data\QuestionInterface::STATUS_APPROVED);
                $questionModel->setVisibleOnFrontend(true);
            } else {
                $questionModel->setStatus(\Krish\ProductQuestions\Api\Data\QuestionInterface::STATUS_PENDING);
                $questionModel->setVisibleOnFrontend(false);
            }

            // Save question (this will trigger events for email notifications)
            $this->questionRepository->save($questionModel);

            // Prepare success response
            $message = $this->dataHelper->isAutoApproveEnabled()
                ? __('Your question has been submitted and is now visible on the product page.')
                : __('Your question has been submitted and is pending approval.');

            $result->setData([
                'success' => true,
                'message' => $message,
                'question_id' => $questionModel->getQuestionId()
            ]);

        } catch (LocalizedException $e) {
            $result->setData([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error submitting question: ' . $e->getMessage());
            $result->setData([
                'success' => false,
                'message' => __('An error occurred while submitting your question. Please try again.' . $e->getMessage())
            ]);
        }

        return $result;
    }
}
