<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Api\Data;

/**
 * Product Question interface
 */
interface QuestionInterface
{
    /**
     * Constants for keys of data array
     */
    const QUESTION_ID = 'question_id';
    const CUSTOMER_EMAIL = 'customer_email';
    const CUSTOMER_NAME = 'customer_name';
    const CUSTOMER_ID = 'customer_id';
    const PRODUCT_SKU = 'product_sku';
    const QUESTION = 'question';
    const ANSWER = 'answer';
    const STATUS = 'status';
    const VISIBLE_ON_FRONTEND = 'visible_on_frontend';
    const SUBMITTED_AT = 'submitted_at';
    const ANSWERED_AT = 'answered_at';
    const STORE_ID = 'store_id';

    /**
     * Status constants
     */
    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_REJECTED = 2;

    /**
     * Get question ID
     *
     * @return int|null
     */
    public function getQuestionId();

    /**
     * Set question ID
     *
     * @param int $questionId
     * @return $this
     */
    public function setQuestionId($questionId);

    /**
     * Get customer email
     *
     * @return string
     */
    public function getCustomerEmail();

    /**
     * Set customer email
     *
     * @param string $customerEmail
     * @return $this
     */
    public function setCustomerEmail($customerEmail);

    /**
     * Get customer name
     *
     * @return string
     */
    public function getCustomerName();

    /**
     * Set customer name
     *
     * @param string $customerName
     * @return $this
     */
    public function setCustomerName($customerName);

    /**
     * Get customer ID
     *
     * @return int|null
     */
    public function getCustomerId();

    /**
     * Set customer ID
     *
     * @param int|null $customerId
     * @return $this
     */
    public function setCustomerId($customerId);

    /**
     * Get product SKU
     *
     * @return string
     */
    public function getProductSku();

    /**
     * Set product SKU
     *
     * @param string $productSku
     * @return $this
     */
    public function setProductSku($productSku);

    /**
     * Get question
     *
     * @return string
     */
    public function getQuestion();

    /**
     * Set question
     *
     * @param string $question
     * @return $this
     */
    public function setQuestion($question);

    /**
     * Get answer
     *
     * @return string|null
     */
    public function getAnswer();

    /**
     * Set answer
     *
     * @param string|null $answer
     * @return $this
     */
    public function setAnswer($answer);

    /**
     * Get status
     *
     * @return int
     */
    public function getStatus();

    /**
     * Set status
     *
     * @param int $status
     * @return $this
     */
    public function setStatus($status);

    /**
     * Get visible on frontend
     *
     * @return bool
     */
    public function getVisibleOnFrontend();

    /**
     * Set visible on frontend
     *
     * @param bool $visibleOnFrontend
     * @return $this
     */
    public function setVisibleOnFrontend($visibleOnFrontend);

    /**
     * Get submitted at
     *
     * @return string
     */
    public function getSubmittedAt();

    /**
     * Set submitted at
     *
     * @param string $submittedAt
     * @return $this
     */
    public function setSubmittedAt($submittedAt);

    /**
     * Get answered at
     *
     * @return string|null
     */
    public function getAnsweredAt();

    /**
     * Set answered at
     *
     * @param string|null $answeredAt
     * @return $this
     */
    public function setAnsweredAt($answeredAt);

    /**
     * Get store ID
     *
     * @return int
     */
    public function getStoreId();

    /**
     * Set store ID
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId($storeId);
}
