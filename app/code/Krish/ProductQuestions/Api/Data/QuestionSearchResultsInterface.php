<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

/**
 * Interface for question search results
 */
interface QuestionSearchResultsInterface extends SearchResultsInterface
{
    /**
     * Get questions list
     *
     * @return \Krish\ProductQuestions\Api\Data\QuestionInterface[]
     */
    public function getItems();

    /**
     * Set questions list
     *
     * @param \Krish\ProductQuestions\Api\Data\QuestionInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
