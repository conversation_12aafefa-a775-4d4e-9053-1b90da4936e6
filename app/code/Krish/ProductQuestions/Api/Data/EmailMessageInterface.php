<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Api\Data;

/**
 * Email message interface for queue processing
 */
interface EmailMessageInterface
{
    /**
     * Email types
     */
    const TYPE_QUESTION_SUBMITTED_CUSTOMER = 'question_submitted_customer';
    const TYPE_QUESTION_SUBMITTED_ADMIN = 'question_submitted_admin';
    const TYPE_QUESTION_APPROVED = 'question_approved';
    const TYPE_QUESTION_REJECTED = 'question_rejected';

    /**
     * Get email type
     *
     * @return string
     */
    public function getEmailType(): string;

    /**
     * Set email type
     *
     * @param string $emailType
     * @return $this
     */
    public function setEmailType(string $emailType): self;

    /**
     * Get question ID
     *
     * @return int
     */
    public function getQuestionId(): int;

    /**
     * Set question ID
     *
     * @param int $questionId
     * @return $this
     */
    public function setQuestionId(int $questionId): self;

    /**
     * Get recipient email
     *
     * @return string
     */
    public function getRecipientEmail(): string;

    /**
     * Set recipient email
     *
     * @param string $email
     * @return $this
     */
    public function setRecipientEmail(string $email): self;

    /**
     * Get recipient name
     *
     * @return string
     */
    public function getRecipientName(): string;

    /**
     * Set recipient name
     *
     * @param string $name
     * @return $this
     */
    public function setRecipientName(string $name): self;

    /**
     * Get store ID
     *
     * @return int
     */
    public function getStoreId(): int;

    /**
     * Set store ID
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId(int $storeId): self;

    /**
     * Get template variables
     *
     * @return array
     */
    public function getTemplateVars(): array;

    /**
     * Set template variables
     *
     * @param array $templateVars
     * @return $this
     */
    public function setTemplateVars(array $templateVars): self;

    /**
     * Get priority
     *
     * @return int
     */
    public function getPriority(): int;

    /**
     * Set priority
     *
     * @param int $priority
     * @return $this
     */
    public function setPriority(int $priority): self;
}
