<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_ProductQuestions
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="krish_product_question_submitted">
        <observer name="krish_product_question_submitted_email" instance="Krish\ProductQuestions\Observer\QuestionSubmitted" />
    </event>

    <event name="krish_product_question_status_change">
        <observer name="krish_product_question_status_change_email" instance="Krish\ProductQuestions\Observer\QuestionStatusChange" />
    </event>
</config>
