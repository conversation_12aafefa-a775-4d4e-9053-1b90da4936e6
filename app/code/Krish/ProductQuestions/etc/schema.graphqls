type Query {
    productQuestions(
        productSku: String! @doc(description: "Product SKU to get questions for")
        pageSize: Int = 10 @doc(description: "Number of questions per page")
        currentPage: Int = 1 @doc(description: "Current page number")
    ): ProductQuestionsOutput @resolver(class: "Krish\\ProductQuestions\\Model\\Resolver\\ProductQuestions") @doc(description: "Get questions for a specific product")
}

type Mutation {
    submitProductQuestion(input: SubmitProductQuestionInput!): SubmitProductQuestionOutput @resolver(class: "Krish\\ProductQuestions\\Model\\Resolver\\SubmitQuestion") @doc(description: "Submit a question for a product")
}

input SubmitProductQuestionInput {
    product_sku: String! @doc(description: "Product SKU")
    question: String! @doc(description: "Customer question")
    customer_name: String @doc(description: "Customer name (required for guest users)")
    customer_email: String @doc(description: "Customer email (required for guest users)")
}

type SubmitProductQuestionOutput {
    success: Boolean! @doc(description: "Whether the submission was successful")
    message: String! @doc(description: "Success or error message")
    question_id: Int @doc(description: "ID of the submitted question")
}

type ProductQuestionsOutput {
    questions: [ProductQuestion] @doc(description: "List of questions")
    total_count: Int! @doc(description: "Total number of questions")
    page_info: SearchResultPageInfo @doc(description: "Pagination information")
}

type ProductQuestion {
    question_id: Int! @doc(description: "Question ID")
    customer_name: String @doc(description: "Customer name")
    question: String! @doc(description: "Customer question")
    answer: String @doc(description: "Admin answer")
    status: Int! @doc(description: "Question status")
    status_label: String! @doc(description: "Question status label")
    visible_on_frontend: Boolean! @doc(description: "Whether visible on frontend")
    submitted_at: String! @doc(description: "Submission date")
    answered_at: String @doc(description: "Answer date")
}
