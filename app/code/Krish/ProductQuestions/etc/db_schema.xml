<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_ProductQuestions
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="krish_product_question_answer" resource="default" engine="innodb" comment="Krish Product Questions and Answers">
        <column xsi:type="int" name="question_id" unsigned="true" nullable="false" identity="true" comment="Question ID"/>
        <column xsi:type="varchar" name="customer_email" nullable="false" length="255" comment="Customer Email"/>
        <column xsi:type="varchar" name="customer_name" nullable="false" length="255" comment="Customer Name"/>
        <column xsi:type="int" name="customer_id" unsigned="true" nullable="true" comment="Customer ID"/>
        <column xsi:type="varchar" name="product_sku" nullable="false" length="64" comment="Product SKU"/>
        <column xsi:type="text" name="question" nullable="false" comment="Customer Question"/>
        <column xsi:type="text" name="answer" nullable="true" comment="Admin Answer"/>
        <column xsi:type="smallint" name="status" unsigned="true" nullable="false" default="0" comment="Status (0=Pending, 1=Approved, 2=Rejected)"/>
        <column xsi:type="smallint" name="visible_on_frontend" unsigned="true" nullable="false" default="0" comment="Visible on Frontend (0=No, 1=Yes)"/>
        <column xsi:type="timestamp" name="submitted_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP" comment="Submitted Date"/>
        <column xsi:type="timestamp" name="answered_at" on_update="true" nullable="true" comment="Answered Date"/>
        <column xsi:type="smallint" name="store_id" unsigned="true" nullable="false" default="0" comment="Store ID"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="question_id"/>
        </constraint>

        <constraint xsi:type="foreign" referenceId="KRISH_PRODUCT_QUESTION_CUSTOMER_ID_CUSTOMER_ENTITY_ENTITY_ID"
                    table="krish_product_question_answer" column="customer_id"
                    referenceTable="customer_entity" referenceColumn="entity_id" onDelete="SET NULL"/>

        <constraint xsi:type="foreign" referenceId="KRISH_PRODUCT_QUESTION_STORE_ID_STORE_STORE_ID"
                    table="krish_product_question_answer" column="store_id"
                    referenceTable="store" referenceColumn="store_id" onDelete="CASCADE"/>

        <index referenceId="KRISH_PRODUCT_QUESTION_PRODUCT_SKU_STATUS_VISIBLE_ON_FRONTEND_CUSTOMER_EMAIL_SUBMITTED_AT" indexType="btree">
            <column name="product_sku"/>
            <column name="status"/>
            <column name="visible_on_frontend"/>
            <column name="customer_email"/>
            <column name="submitted_at"/>
        </index>
    </table>
</schema>
