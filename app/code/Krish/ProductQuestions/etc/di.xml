<?xml version="1.0"?>
<!--
  ~
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ModuleName
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Service Contracts -->
    <preference for="Krish\ProductQuestions\Api\Data\QuestionInterface" type="Krish\ProductQuestions\Model\Question" />
    <preference for="Krish\ProductQuestions\Api\Data\QuestionSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />
    <preference for="Krish\ProductQuestions\Api\QuestionRepositoryInterface" type="Krish\ProductQuestions\Model\QuestionRepository" />

    <!-- Virtual Types for Search Results -->
    <virtualType name="Krish\ProductQuestions\Model\QuestionSearchResults" type="Magento\Framework\Api\SearchResults">
        <arguments>
            <argument name="dataObjectFactory" xsi:type="object">Krish\ProductQuestions\Api\Data\QuestionInterfaceFactory</argument>
        </arguments>
    </virtualType>

    <!-- Collection Processor -->
    <type name="Krish\ProductQuestions\Model\QuestionRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Krish\ProductQuestions\Model\Api\SearchCriteria\QuestionCollectionProcessor</argument>
        </arguments>
    </type>

    <!-- Collection Processor Virtual Type -->
    <virtualType name="Krish\ProductQuestions\Model\Api\SearchCriteria\QuestionCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="filters" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor</item>
                <item name="sorting" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\SortingProcessor</item>
                <item name="pagination" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\PaginationProcessor</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Logger -->
    <virtualType name="Krish\ProductQuestions\Logger\Handler" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/krish_product_questions.log</argument>
        </arguments>
    </virtualType>

    <virtualType name="Krish\ProductQuestions\Logger\Logger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">Krish\ProductQuestions\Logger\Handler</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Email Helper -->
    <type name="Krish\ProductQuestions\Helper\Email">
        <arguments>
            <argument name="logger" xsi:type="object">Krish\ProductQuestions\Logger\Logger</argument>
        </arguments>
    </type>

    <!-- Observer -->
    <type name="Krish\ProductQuestions\Observer\QuestionStatusChange">
        <arguments>
            <argument name="logger" xsi:type="object">Krish\ProductQuestions\Logger\Logger</argument>
        </arguments>
    </type>

    <!-- Email Message Interface -->
    <preference for="Krish\ProductQuestions\Api\Data\EmailMessageInterface" type="Krish\ProductQuestions\Model\EmailMessage" />

    <!-- Queue Publisher -->
    <type name="Krish\ProductQuestions\Model\Queue\EmailPublisher">
        <arguments>
            <argument name="logger" xsi:type="object">Krish\ProductQuestions\Logger\Logger</argument>
        </arguments>
    </type>

    <!-- Queue Consumer -->
    <type name="Krish\ProductQuestions\Model\Queue\EmailConsumer">
        <arguments>
            <argument name="logger" xsi:type="object">Krish\ProductQuestions\Logger\Logger</argument>
        </arguments>
    </type>

</config>
