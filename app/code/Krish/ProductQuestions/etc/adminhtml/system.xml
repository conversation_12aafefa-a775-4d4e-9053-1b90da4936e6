<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_ProductQuestions
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="krish" translate="label" sortOrder="300">
            <label>Krish Technolabs</label>
        </tab>
        
        <section id="krish_product_questions" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Product Questions</label>
            <tab>krish</tab>
            <resource>Krish_ProductQuestions::config</resource>
            
            <!-- General Settings -->
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Product Questions</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or disable the product questions functionality.</comment>
                </field>
                
                <field id="require_login" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Require Customer Login</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled, only logged-in customers can submit questions.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="auto_approve" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Auto-Approve Questions</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled, questions will be automatically approved and visible on frontend.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="questions_per_page" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Questions Per Page</label>
                    <validate>required-entry validate-digits validate-greater-than-zero</validate>
                    <comment>Number of questions to display per page on frontend.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="allow_guest_questions" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow Guest Questions</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Allow guest customers to submit questions (when login is not required).</comment>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="require_login">0</field>
                    </depends>
                </field>
            </group>
            
            <!-- Email Settings -->
            <group id="email" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Email Settings</label>
                
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Email Notifications</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or disable email notifications for questions.</comment>
                </field>
                
                <field id="sender_identity" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    <comment>Email identity to use for sending notifications.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="admin_email" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Admin Email Address</label>
                    <validate>required-entry validate-email</validate>
                    <comment>Email address to receive admin notifications about new questions.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="copy_to" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Copy To</label>
                    <validate>validate-emails</validate>
                    <comment>Comma-separated list of email addresses to receive copies of notifications.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="copy_method" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Copy Method</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Method</source_model>
                    <comment>Method to use for sending copies (CC or BCC).</comment>
                    <depends>
                        <field id="enabled">1</field>
                        <field id="copy_to" separator=",">.*</field>
                    </depends>
                </field>

                <field id="use_queue" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Use Queue for Email Processing</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable to use message queue for asynchronous email processing (recommended for better performance). Disable for immediate email sending.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>

            <!-- Frontend Display Settings -->
            <group id="frontend" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Frontend Display Settings</label>

                <field id="tab_title" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tab Title</label>
                    <comment>Title to display for the product questions tab.</comment>
                    <depends>
                        <field id="*/general/enabled">1</field>
                    </depends>
                </field>

                <field id="show_customer_name" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Customer Name</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Display customer name with questions on frontend.</comment>
                    <depends>
                        <field id="*/general/enabled">1</field>
                    </depends>
                </field>

                <field id="show_question_date" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Question Date</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Display submission date with questions on frontend.</comment>
                    <depends>
                        <field id="*/general/enabled">1</field>
                    </depends>
                </field>

                <field id="enable_accordion" translate="label comment" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Accordion Display</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Use accordion-style display for questions and answers.</comment>
                    <depends>
                        <field id="*/general/enabled">1</field>
                    </depends>
                </field>
            </group>

            <!-- Email Templates -->
            <group id="email_templates" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Email Templates</label>

                <field id="question_submitted_customer" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Question Submitted - Customer Notification</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <comment>Email template for customer notification when question is submitted.</comment>
                    <depends>
                        <field id="*/email/enabled">1</field>
                    </depends>
                </field>

                <field id="question_submitted_admin" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Question Submitted - Admin Notification</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <comment>Email template for admin notification when question is submitted.</comment>
                    <depends>
                        <field id="*/email/enabled">1</field>
                    </depends>
                </field>

                <field id="question_approved" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Question Approved</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <comment>Email template for customer notification when question is approved.</comment>
                    <depends>
                        <field id="*/email/enabled">1</field>
                    </depends>
                </field>

                <field id="question_rejected" translate="label comment" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Question Rejected</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <comment>Email template for customer notification when question is rejected.</comment>
                    <depends>
                        <field id="*/email/enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
