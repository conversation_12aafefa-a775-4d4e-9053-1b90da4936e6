<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Model\Queue;

use Magento\Framework\MessageQueue\PublisherInterface;
use Krish\ProductQuestions\Api\Data\EmailMessageInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterface;
use Krish\ProductQuestions\Model\EmailMessage;
use Krish\ProductQuestions\Helper\Data as DataHelper;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Email publisher for queue processing
 */
class EmailPublisher
{
    /**
     * Queue topic name
     */
    private const TOPIC_NAME = 'krish.product.questions.email';

    /**
     * @param PublisherInterface $publisher
     * @param DataHelper $dataHelper
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly PublisherInterface $publisher,
        private readonly DataHelper $dataHelper,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Publish question submitted emails to queue
     *
     * @param QuestionInterface $question
     * @return void
     */
    public function publishQuestionSubmittedEmails(QuestionInterface $question): void
    {
        try {
            // Publish customer notification email
            $this->publishCustomerEmail($question, EmailMessageInterface::TYPE_QUESTION_SUBMITTED_CUSTOMER);

            // Publish admin notification email
            $this->publishAdminEmail($question, EmailMessageInterface::TYPE_QUESTION_SUBMITTED_ADMIN);

        } catch (\Exception $e) {
            $this->logger->error('Error publishing question submitted emails to queue: ' . $e->getMessage());
        }
    }

    /**
     * Publish question status change email to queue
     *
     * @param QuestionInterface $question
     * @param string $emailType
     * @return void
     */
    public function publishQuestionStatusChangeEmail(QuestionInterface $question, string $emailType): void
    {
        try {
            $this->publishCustomerEmail($question, $emailType);
        } catch (\Exception $e) {
            $this->logger->error('Error publishing question status change email to queue: ' . $e->getMessage());
        }
    }

    /**
     * Publish customer email to queue
     *
     * @param QuestionInterface $question
     * @param string $emailType
     * @return void
     */
    private function publishCustomerEmail(QuestionInterface $question, string $emailType): void
    {
        $emailMessage = new EmailMessage();
        $emailMessage->setEmailType($emailType)
            ->setQuestionId((int)$question->getQuestionId())
            ->setRecipientEmail($question->getCustomerEmail())
            ->setRecipientName($question->getCustomerName())
            ->setStoreId((int)$this->storeManager->getStore()->getId())
            ->setTemplateVars($this->buildTemplateVars($question))
            ->setPriority(2); // Normal priority for customer emails

        $this->publisher->publish(self::TOPIC_NAME, $emailMessage);
    }

    /**
     * Publish admin email to queue
     *
     * @param QuestionInterface $question
     * @param string $emailType
     * @return void
     */
    private function publishAdminEmail(QuestionInterface $question, string $emailType): void
    {
        $adminEmail = $this->dataHelper->getAdminEmail();
        if (!$adminEmail) {
            return;
        }

        $emailMessage = new EmailMessage();
        $emailMessage->setEmailType($emailType)
            ->setQuestionId($question->getQuestionId())
            ->setRecipientEmail($adminEmail)
            ->setRecipientName('Admin')
            ->setStoreId($this->storeManager->getStore()->getId())
            ->setTemplateVars($this->buildTemplateVars($question))
            ->setPriority(1); // High priority for admin emails

        $this->publisher->publish(
            self::TOPIC_NAME,
            $emailMessage
        );
    }

    /**
     * Build template variables for email
     *
     * @param QuestionInterface $question
     * @return array
     */
    private function buildTemplateVars(QuestionInterface $question): array
    {
        // Only pass serializable data (no complex objects)
        $store = $this->storeManager->getStore();

        return [
            'question_id' => $question->getQuestionId(),
            'customer_name' => $question->getCustomerName(),
            'customer_email' => $question->getCustomerEmail(),
            'product_sku' => $question->getProductSku(),
            'question_text' => $question->getQuestion(),
            'answer' => $question->getAnswer() ?: '',
            'status' => $question->getStatus(),
            'submitted_at' => $question->getSubmittedAt(),
            'store_id' => $store->getId(),
            'store_name' => $store->getName(),
            'store_url' => $store->getBaseUrl()
        ];
    }
}
