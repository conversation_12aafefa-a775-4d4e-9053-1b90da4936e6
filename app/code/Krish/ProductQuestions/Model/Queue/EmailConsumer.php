<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Model\Queue;

use Krish\ProductQuestions\Api\Data\EmailMessageInterface;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Krish\ProductQuestions\Helper\Email as EmailHelper;
use Krish\ProductQuestions\Helper\Data as DataHelper;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Email consumer for queue processing
 */
class EmailConsumer
{
    /**
     * Maximum retry attempts
     */
    private const MAX_RETRY_ATTEMPTS = 3;

    /**
     * @param EmailHelper $emailHelper
     * @param QuestionRepositoryInterface $questionRepository
     * @param DataHelper $dataHelper
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EmailHelper $emailHelper,
        private readonly QuestionRepositoryInterface $questionRepository,
        private readonly DataHelper $dataHelper,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Process email message from queue
     *
     * @param EmailMessageInterface $emailMessage
     * @return void
     */
    public function process(EmailMessageInterface $emailMessage): void
    {
        $startTime = microtime(true);

        try {
            // Validate email message
            $this->validateEmailMessage($emailMessage);

            // Check if email notifications are enabled
            if (!$this->dataHelper->isEmailEnabled()) {
                $this->logger->info('Email notifications are disabled, skipping email processing');
                return;
            }

            // Set store context
            $this->storeManager->setCurrentStore($emailMessage->getStoreId());

            // Get question data with error handling
            $question = $this->getQuestionSafely($emailMessage->getQuestionId());
            if (!$question) {
                return; // Question not found, skip processing
            }

            // Process email based on type
            $success = $this->processEmailByType($emailMessage, $question);

            if ($success) {
                $processingTime = round((microtime(true) - $startTime) * 1000, 2);
                $this->logger->info(sprintf(
                    'Email sent successfully: Type=%s, QuestionId=%d, Recipient=%s, ProcessingTime=%sms',
                    $emailMessage->getEmailType(),
                    $emailMessage->getQuestionId(),
                    $emailMessage->getRecipientEmail(),
                    $processingTime
                ));
            } else {
                throw new \Exception('Email sending failed - no specific error returned');
            }

        } catch (NoSuchEntityException $e) {
            // Question doesn't exist - don't retry
            $this->logger->warning(sprintf(
                'Question not found for email processing: QuestionId=%d, Type=%s - Message will be discarded',
                $emailMessage->getQuestionId(),
                $emailMessage->getEmailType()
            ));
            // Don't re-throw - this will mark message as processed

        } catch (LocalizedException $e) {
            // Magento-specific error - log and retry
            $this->logger->error(sprintf(
                'Localized error processing email queue message: %s, Type=%s, QuestionId=%d',
                $e->getMessage(),
                $emailMessage->getEmailType(),
                $emailMessage->getQuestionId()
            ));
            throw $e; // Re-throw to trigger retry

        } catch (\Exception $e) {
            $this->logger->error(sprintf(
                'Error processing email queue message: %s, Type=%s, QuestionId=%d',
                $e->getMessage(),
                $emailMessage->getEmailType(),
                $emailMessage->getQuestionId()
            ));

            // Re-throw exception to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Process email based on type
     *
     * @param EmailMessageInterface $emailMessage
     * @param \Krish\ProductQuestions\Api\Data\QuestionInterface $question
     * @return bool
     */
    private function processEmailByType(EmailMessageInterface $emailMessage, $question): bool
    {
        switch ($emailMessage->getEmailType()) {
            case EmailMessageInterface::TYPE_QUESTION_SUBMITTED_CUSTOMER:
                return $this->emailHelper->sendQuestionSubmittedCustomerEmail($question);

            case EmailMessageInterface::TYPE_QUESTION_SUBMITTED_ADMIN:
                return $this->emailHelper->sendQuestionSubmittedAdminEmail($question);

            case EmailMessageInterface::TYPE_QUESTION_APPROVED:
                return $this->emailHelper->sendQuestionApprovedEmail($question);

            case EmailMessageInterface::TYPE_QUESTION_REJECTED:
                return $this->emailHelper->sendQuestionRejectedEmail($question);

            default:
                $this->logger->warning('Unknown email type: ' . $emailMessage->getEmailType());
                return false;
        }
    }

    /**
     * Validate email message before processing
     *
     * @param EmailMessageInterface $emailMessage
     * @return void
     * @throws \InvalidArgumentException
     */
    private function validateEmailMessage(EmailMessageInterface $emailMessage): void
    {
        if (!$emailMessage->getEmailType()) {
            throw new \InvalidArgumentException('Email type is required');
        }

        if (!$emailMessage->getQuestionId()) {
            throw new \InvalidArgumentException('Question ID is required');
        }

        if (!$emailMessage->getRecipientEmail()) {
            throw new \InvalidArgumentException('Recipient email is required');
        }

        if (!filter_var($emailMessage->getRecipientEmail(), FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid recipient email format: ' . $emailMessage->getRecipientEmail());
        }
    }

    /**
     * Get question safely with error handling
     *
     * @param int $questionId
     * @return \Krish\ProductQuestions\Api\Data\QuestionInterface|null
     */
    private function getQuestionSafely(int $questionId): ?\Krish\ProductQuestions\Api\Data\QuestionInterface
    {
        try {
            return $this->questionRepository->getById($questionId);
        } catch (NoSuchEntityException $e) {
            $this->logger->warning(sprintf(
                'Question with ID %d not found during queue processing: %s',
                $questionId,
                $e->getMessage()
            ));
            return null;
        } catch (\Exception $e) {
            $this->logger->error(sprintf(
                'Error retrieving question with ID %d: %s',
                $questionId,
                $e->getMessage()
            ));
            throw $e; // Re-throw other exceptions
        }
    }
}
