<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Model\Queue;

use Krish\ProductQuestions\Api\Data\EmailMessageInterface;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Krish\ProductQuestions\Helper\Email as EmailHelper;
use Krish\ProductQuestions\Helper\Data as DataHelper;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Email consumer for queue processing
 */
class EmailConsumer
{
    /**
     * Maximum retry attempts
     */
    private const MAX_RETRY_ATTEMPTS = 3;

    /**
     * @param EmailHelper $emailHelper
     * @param QuestionRepositoryInterface $questionRepository
     * @param DataHelper $dataHelper
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EmailHelper $emailHelper,
        private readonly QuestionRepositoryInterface $questionRepository,
        private readonly DataHelper $dataHelper,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Process email message from queue
     *
     * @param EmailMessageInterface $emailMessage
     * @return void
     */
    public function process(EmailMessageInterface $emailMessage): void
    {
        try {
            // Check if email notifications are enabled
            if (!$this->dataHelper->isEmailEnabled()) {
                $this->logger->info('Email notifications are disabled, skipping email processing');
                return;
            }

            // Set store context
            $this->storeManager->setCurrentStore($emailMessage->getStoreId());

            // Get question data
            $question = $this->questionRepository->getById($emailMessage->getQuestionId());

            // Process email based on type
            $success = $this->processEmailByType($emailMessage, $question);

            if ($success) {
                $this->logger->info(sprintf(
                    'Email sent successfully: Type=%s, QuestionId=%d, Recipient=%s',
                    $emailMessage->getEmailType(),
                    $emailMessage->getQuestionId(),
                    $emailMessage->getRecipientEmail()
                ));
            } else {
                throw new \Exception('Email sending failed');
            }

        } catch (\Exception $e) {
            $this->logger->error(sprintf(
                'Error processing email queue message: %s, Type=%s, QuestionId=%d',
                $e->getMessage(),
                $emailMessage->getEmailType(),
                $emailMessage->getQuestionId()
            ));

            // Re-throw exception to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Process email based on type
     *
     * @param EmailMessageInterface $emailMessage
     * @param \Krish\ProductQuestions\Api\Data\QuestionInterface $question
     * @return bool
     */
    private function processEmailByType(EmailMessageInterface $emailMessage, $question): bool
    {
        switch ($emailMessage->getEmailType()) {
            case EmailMessageInterface::TYPE_QUESTION_SUBMITTED_CUSTOMER:
                return $this->emailHelper->sendQuestionSubmittedCustomerEmail($question);

            case EmailMessageInterface::TYPE_QUESTION_SUBMITTED_ADMIN:
                return $this->emailHelper->sendQuestionSubmittedAdminEmail($question);

            case EmailMessageInterface::TYPE_QUESTION_APPROVED:
                return $this->emailHelper->sendQuestionApprovedEmail($question);

            case EmailMessageInterface::TYPE_QUESTION_REJECTED:
                return $this->emailHelper->sendQuestionRejectedEmail($question);

            default:
                $this->logger->warning('Unknown email type: ' . $emailMessage->getEmailType());
                return false;
        }
    }
}
