<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Model;

use Krish\ProductQuestions\Api\Data\EmailMessageInterface;

/**
 * Email message implementation for queue processing
 */
class EmailMessage implements EmailMessageInterface
{
    /**
     * @param string $emailType
     * @param int $questionId
     * @param string $recipientEmail
     * @param string $recipientName
     * @param int $storeId
     * @param array $templateVars
     * @param int $priority
     */
    public function __construct(
        private string $emailType = '',
        private int $questionId = 0,
        private string $recipientEmail = '',
        private string $recipientName = '',
        private int $storeId = 0,
        private array $templateVars = [],
        private int $priority = 1
    ) {}

    /**
     * @inheritDoc
     */
    public function getEmailType(): string
    {
        return $this->emailType;
    }

    /**
     * @inheritDoc
     */
    public function setEmailType(string $emailType): EmailMessageInterface
    {
        $this->emailType = $emailType;
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getQuestionId(): int
    {
        return $this->questionId;
    }

    /**
     * @inheritDoc
     */
    public function setQuestionId(int $questionId): EmailMessageInterface
    {
        $this->questionId = $questionId;
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getRecipientEmail(): string
    {
        return $this->recipientEmail;
    }

    /**
     * @inheritDoc
     */
    public function setRecipientEmail(string $email): EmailMessageInterface
    {
        $this->recipientEmail = $email;
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getRecipientName(): string
    {
        return $this->recipientName;
    }

    /**
     * @inheritDoc
     */
    public function setRecipientName(string $name): EmailMessageInterface
    {
        $this->recipientName = $name;
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getStoreId(): int
    {
        return $this->storeId;
    }

    /**
     * @inheritDoc
     */
    public function setStoreId(int $storeId): EmailMessageInterface
    {
        $this->storeId = $storeId;
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getTemplateVars(): array
    {
        return $this->templateVars;
    }

    /**
     * @inheritDoc
     */
    public function setTemplateVars(array $templateVars): EmailMessageInterface
    {
        $this->templateVars = $templateVars;
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getPriority(): int
    {
        return $this->priority;
    }

    /**
     * @inheritDoc
     */
    public function setPriority(int $priority): EmailMessageInterface
    {
        $this->priority = $priority;
        return $this;
    }
}
