<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Krish\ProductQuestions\Helper\Data as DataHelper;

/**
 * Product questions GraphQL resolver
 */
class ProductQuestions implements ResolverInterface
{
    /**
     * @var QuestionRepositoryInterface
     */
    private $questionRepository;

    /**
     * @var DataHelper
     */
    private $dataHelper;

    /**
     * @param QuestionRepositoryInterface $questionRepository
     * @param DataHelper $dataHelper
     */
    public function __construct(
        QuestionRepositoryInterface $questionRepository,
        DataHelper $dataHelper
    ) {
        $this->questionRepository = $questionRepository;
        $this->dataHelper = $dataHelper;
    }

    /**
     * Resolve product questions
     *
     * @param Field $field
     * @param \Magento\Framework\GraphQl\Query\Resolver\ContextInterface $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     * @throws GraphQlInputException
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!$this->dataHelper->isEnabled()) {
            throw new GraphQlInputException(__('Product questions feature is disabled.'));
        }

        if (empty($args['productSku'])) {
            throw new GraphQlInputException(__('Product SKU is required.'));
        }

        $productSku = $args['productSku'];
        $pageSize = $args['pageSize'] ?? 10;
        $currentPage = $args['currentPage'] ?? 1;

        // Get approved questions for the product
        $questions = $this->questionRepository->getByProductSku($productSku, true);
        $totalCount = count($questions);

        // Apply pagination
        $offset = ($currentPage - 1) * $pageSize;
        $questionsSlice = array_slice($questions, $offset, $pageSize);

        $questionsData = [];
        foreach ($questionsSlice as $question) {
            $questionsData[] = [
                'question_id' => $question->getQuestionId(),
                'customer_name' => $this->dataHelper->showCustomerName() ? $question->getCustomerName() : null,
                'question' => $question->getQuestion(),
                'answer' => $question->getAnswer(),
                'status' => $question->getStatus(),
                'status_label' => $question->getStatusLabel(),
                'visible_on_frontend' => $question->getVisibleOnFrontend(),
                'submitted_at' => $question->getSubmittedAt(),
                'answered_at' => $question->getAnsweredAt()
            ];
        }

        return [
            'questions' => $questionsData,
            'total_count' => $totalCount,
            'page_info' => [
                'page_size' => $pageSize,
                'current_page' => $currentPage,
                'total_pages' => ceil($totalCount / $pageSize)
            ]
        ];
    }
}
