<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\CustomerGraphQl\Model\Customer\GetCustomer;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use <PERSON><PERSON>\ProductQuestions\Api\QuestionRepositoryInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterfaceFactory;
use Krish\ProductQuestions\Api\Data\QuestionInterface;
use Krish\ProductQuestions\Helper\Data as DataHelper;
use Krish\ProductQuestions\Helper\Email as EmailHelper;

/**
 * Submit question GraphQL resolver
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class SubmitQuestion implements ResolverInterface
{
    /**
     * @var GetCustomer
     */
    private $getCustomer;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var QuestionRepositoryInterface
     */
    private $questionRepository;

    /**
     * @var QuestionInterfaceFactory
     */
    private $questionFactory;

    /**
     * @var DataHelper
     */
    private $dataHelper;

    /**
     * @var EmailHelper
     */
    private $emailHelper;

    /**
     * @param GetCustomer $getCustomer
     * @param StoreManagerInterface $storeManager
     * @param ProductRepositoryInterface $productRepository
     * @param QuestionRepositoryInterface $questionRepository
     * @param QuestionInterfaceFactory $questionFactory
     * @param DataHelper $dataHelper
     * @param EmailHelper $emailHelper
     */
    public function __construct(
        GetCustomer $getCustomer,
        StoreManagerInterface $storeManager,
        ProductRepositoryInterface $productRepository,
        QuestionRepositoryInterface $questionRepository,
        QuestionInterfaceFactory $questionFactory,
        DataHelper $dataHelper,
        EmailHelper $emailHelper
    ) {
        $this->getCustomer = $getCustomer;
        $this->storeManager = $storeManager;
        $this->productRepository = $productRepository;
        $this->questionRepository = $questionRepository;
        $this->questionFactory = $questionFactory;
        $this->dataHelper = $dataHelper;
        $this->emailHelper = $emailHelper;
    }

    /**
     * Resolve submit question
     *
     * @param Field $field
     * @param \Magento\Framework\GraphQl\Query\Resolver\ContextInterface $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     * @throws GraphQlInputException
     * @throws GraphQlAuthorizationException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!$this->dataHelper->isEnabled()) {
            throw new GraphQlInputException(__('Product questions feature is disabled.'));
        }

        $input = $args['input'] ?? [];

        if (empty($input['product_sku']) || empty($input['question'])) {
            throw new GraphQlInputException(__('Product SKU and question are required.'));
        }

        // Check if customer is logged in (if required)
        $currentUserId = $context->getUserId();
        if (!$currentUserId) {
            throw new GraphQlAuthorizationException(__('Please log in to submit a question.'));
        }

        // Validate product exists
        try {
            $product = $this->productRepository->get($input['product_sku']);
        } catch (\Exception $e) {
            throw new GraphQlInputException(__('Product not found.'));
        }

        // Get customer data
        $customerEmail = null;
        $customerName = null;
        $customerId = null;

        if ($currentUserId) {
            $customer = $this->getCustomer->execute($context);
            $customerEmail = $customer->getEmail();
            $customerName = $customer->getFirstname() . ' ' . $customer->getLastname();
            $customerId = $customer->getId();
        } else {
            if (empty($input['customer_email']) || empty($input['customer_name'])) {
                throw new GraphQlInputException(__('Customer email and name are required for guest users.'));
            }
            $customerEmail = $input['customer_email'];
            $customerName = $input['customer_name'];
        }

        try {
            // Create question
            $question = $this->questionFactory->create();
            $question->setCustomerEmail($customerEmail);
            $question->setCustomerName(trim($customerName));
            $question->setCustomerId($customerId);
            $question->setProductSku($input['product_sku']);
            $question->setQuestion($input['question']);
            $question->setStoreId($this->storeManager->getStore()->getId());

            // Set status based on auto-approve setting
            if ($this->dataHelper->isAutoApproveEnabled()) {
                $question->setStatus(QuestionInterface::STATUS_APPROVED);
                $question->setVisibleOnFrontend(true);
            } else {
                $question->setStatus(QuestionInterface::STATUS_PENDING);
                $question->setVisibleOnFrontend(false);
            }

            // Save question
            $this->questionRepository->save($question);

            // Send email notifications
            $this->emailHelper->sendQuestionSubmittedCustomerEmail($question);
            $this->emailHelper->sendQuestionSubmittedAdminEmail($question);

            $message = $this->dataHelper->isAutoApproveEnabled()
                ? __('Your question has been submitted and is now visible on the product page.')
                : __('Your question has been submitted and is pending approval.');

            return [
                'success' => true,
                'message' => $message,
                'question_id' => $question->getQuestionId()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => __('An error occurred while submitting your question. Please try again.'),
                'question_id' => null
            ];
        }
    }
}
