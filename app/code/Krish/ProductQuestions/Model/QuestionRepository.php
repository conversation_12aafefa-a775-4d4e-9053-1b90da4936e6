<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Model;

use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Krish\ProductQuestions\Api\Data\QuestionInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterfaceFactory;
use Krish\ProductQuestions\Api\Data\QuestionSearchResultsInterface;
use <PERSON><PERSON>\ProductQuestions\Api\Data\QuestionSearchResultsInterfaceFactory;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Krish\ProductQuestions\Model\ResourceModel\Question as QuestionResource;
use Krish\ProductQuestions\Model\ResourceModel\Question\CollectionFactory;

/**
 * Question repository implementation
 */
class QuestionRepository implements QuestionRepositoryInterface
{
    /**
     * @var QuestionResource
     */
    private $resource;

    /**
     * @var QuestionInterfaceFactory
     */
    private $questionFactory;

    /**
     * @var CollectionFactory
     */
    private $questionCollectionFactory;

    /**
     * @var QuestionSearchResultsInterfaceFactory
     */
    private $searchResultsFactory;

    /**
     * @var CollectionProcessorInterface
     */
    private $collectionProcessor;

    /**
     * @param QuestionResource $resource
     * @param QuestionInterfaceFactory $questionFactory
     * @param CollectionFactory $questionCollectionFactory
     * @param QuestionSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        QuestionResource $resource,
        QuestionInterfaceFactory $questionFactory,
        CollectionFactory $questionCollectionFactory,
        QuestionSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->questionFactory = $questionFactory;
        $this->questionCollectionFactory = $questionCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * Save question
     *
     * @param QuestionInterface $question
     * @return QuestionInterface
     * @throws CouldNotSaveException
     */
    public function save(QuestionInterface $question)
    {
        try {
            $this->resource->save($question);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(
                __('Could not save the question: %1', $exception->getMessage()),
                $exception
            );
        }
        return $question;
    }

    /**
     * Retrieve question by ID
     *
     * @param int $questionId
     * @return QuestionInterface
     * @throws NoSuchEntityException
     */
    public function getById($questionId)
    {
        $question = $this->questionFactory->create();
        $this->resource->load($question, $questionId);
        if (!$question->getQuestionId()) {
            throw new NoSuchEntityException(__('Question with id "%1" does not exist.', $questionId));
        }
        return $question;
    }

    /**
     * Retrieve questions matching the specified criteria
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return QuestionSearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        $collection = $this->questionCollectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());

        return $searchResults;
    }

    /**
     * Delete question
     *
     * @param QuestionInterface $question
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(QuestionInterface $question)
    {
        try {
            $this->resource->delete($question);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(
                __('Could not delete the question: %1', $exception->getMessage()),
                $exception
            );
        }
        return true;
    }

    /**
     * Delete question by ID
     *
     * @param int $questionId
     * @return bool
     * @throws NoSuchEntityException
     * @throws CouldNotDeleteException
     */
    public function deleteById($questionId)
    {
        return $this->delete($this->getById($questionId));
    }

    /**
     * Get questions by product SKU
     *
     * @param string $productSku
     * @param bool $approvedOnly
     * @return QuestionInterface[]
     */
    public function getByProductSku($productSku, $approvedOnly = true)
    {
        $collection = $this->questionCollectionFactory->create();
        $collection->addProductFilter($productSku);

        if ($approvedOnly) {
            $collection->addApprovedFilter();
            $collection->addVisibleOnFrontendFilter();
        }

        $collection->setOrderBySubmittedDate('DESC');

        return $collection->getData();
    }
}
