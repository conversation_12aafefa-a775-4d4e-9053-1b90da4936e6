<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Model;

use Magento\Framework\Model\AbstractModel;
use Krish\ProductQuestions\Api\Data\QuestionInterface;

/**
 * Question model
 */
class Question extends AbstractModel implements QuestionInterface
{
    /**
     * Cache tag
     */
    const CACHE_TAG = 'krish_product_question';

    /**
     * Cache tag
     *
     * @var string
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'krish_product_question';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(\Krish\ProductQuestions\Model\ResourceModel\Question::class);
    }

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities()
    {
        return [self::CACHE_TAG . '_' . $this->getId()];
    }

    /**
     * Get question ID
     *
     * @return int|null
     */
    public function getQuestionId()
    {
        return $this->getData(self::QUESTION_ID);
    }

    /**
     * Set question ID
     *
     * @param int $questionId
     * @return $this
     */
    public function setQuestionId($questionId)
    {
        return $this->setData(self::QUESTION_ID, $questionId);
    }

    /**
     * Get customer email
     *
     * @return string
     */
    public function getCustomerEmail()
    {
        return $this->getData(self::CUSTOMER_EMAIL);
    }

    /**
     * Set customer email
     *
     * @param string $customerEmail
     * @return $this
     */
    public function setCustomerEmail($customerEmail)
    {
        return $this->setData(self::CUSTOMER_EMAIL, $customerEmail);
    }

    /**
     * Get customer name
     *
     * @return string
     */
    public function getCustomerName()
    {
        return $this->getData(self::CUSTOMER_NAME);
    }

    /**
     * Set customer name
     *
     * @param string $customerName
     * @return $this
     */
    public function setCustomerName($customerName)
    {
        return $this->setData(self::CUSTOMER_NAME, $customerName);
    }

    /**
     * Get customer ID
     *
     * @return int|null
     */
    public function getCustomerId()
    {
        return $this->getData(self::CUSTOMER_ID);
    }

    /**
     * Set customer ID
     *
     * @param int|null $customerId
     * @return $this
     */
    public function setCustomerId($customerId)
    {
        return $this->setData(self::CUSTOMER_ID, $customerId);
    }

    /**
     * Get product SKU
     *
     * @return string
     */
    public function getProductSku()
    {
        return $this->getData(self::PRODUCT_SKU);
    }

    /**
     * Set product SKU
     *
     * @param string $productSku
     * @return $this
     */
    public function setProductSku($productSku)
    {
        return $this->setData(self::PRODUCT_SKU, $productSku);
    }

    /**
     * Get question
     *
     * @return string
     */
    public function getQuestion()
    {
        return $this->getData(self::QUESTION);
    }

    /**
     * Set question
     *
     * @param string $question
     * @return $this
     */
    public function setQuestion($question)
    {
        return $this->setData(self::QUESTION, $question);
    }

    /**
     * Get answer
     *
     * @return string|null
     */
    public function getAnswer()
    {
        return $this->getData(self::ANSWER);
    }

    /**
     * Set answer
     *
     * @param string|null $answer
     * @return $this
     */
    public function setAnswer($answer)
    {
        return $this->setData(self::ANSWER, $answer);
    }

    /**
     * Get status
     *
     * @return int
     */
    public function getStatus()
    {
        return $this->getData(self::STATUS);
    }

    /**
     * Set status
     *
     * @param int $status
     * @return $this
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get visible on frontend
     *
     * @return bool
     */
    public function getVisibleOnFrontend()
    {
        return (bool) $this->getData(self::VISIBLE_ON_FRONTEND);
    }

    /**
     * Set visible on frontend
     *
     * @param bool $visibleOnFrontend
     * @return $this
     */
    public function setVisibleOnFrontend($visibleOnFrontend)
    {
        return $this->setData(self::VISIBLE_ON_FRONTEND, (int) $visibleOnFrontend);
    }

    /**
     * Get submitted at
     *
     * @return string
     */
    public function getSubmittedAt()
    {
        return $this->getData(self::SUBMITTED_AT);
    }

    /**
     * Set submitted at
     *
     * @param string $submittedAt
     * @return $this
     */
    public function setSubmittedAt($submittedAt)
    {
        return $this->setData(self::SUBMITTED_AT, $submittedAt);
    }

    /**
     * Get answered at
     *
     * @return string|null
     */
    public function getAnsweredAt()
    {
        return $this->getData(self::ANSWERED_AT);
    }

    /**
     * Set answered at
     *
     * @param string|null $answeredAt
     * @return $this
     */
    public function setAnsweredAt($answeredAt)
    {
        return $this->setData(self::ANSWERED_AT, $answeredAt);
    }

    /**
     * Get store ID
     *
     * @return int
     */
    public function getStoreId()
    {
        return $this->getData(self::STORE_ID);
    }

    /**
     * Set store ID
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId($storeId)
    {
        return $this->setData(self::STORE_ID, $storeId);
    }

    /**
     * Get status label
     *
     * @return string
     */
    public function getStatusLabel()
    {
        $statuses = [
            self::STATUS_PENDING => __('Pending'),
            self::STATUS_APPROVED => __('Approved'),
            self::STATUS_REJECTED => __('Rejected')
        ];

        return $statuses[$this->getStatus()] ?? __('Unknown');
    }

    /**
     * Check if question is approved
     *
     * @return bool
     */
    public function isApproved()
    {
        return $this->getStatus() == self::STATUS_APPROVED;
    }

    /**
     * Check if question is pending
     *
     * @return bool
     */
    public function isPending()
    {
        return $this->getStatus() == self::STATUS_PENDING;
    }

    /**
     * Check if question is rejected
     *
     * @return bool
     */
    public function isRejected()
    {
        return $this->getStatus() == self::STATUS_REJECTED;
    }
}
