<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_ProductQuestions
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">krish_productquestions_form.krish_productquestions_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Question Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    
    <settings>
        <buttons>
            <button name="save" class="Krish\ProductQuestions\Block\Adminhtml\Question\Edit\SaveButton"/>
            <button name="save_and_continue" class="Krish\ProductQuestions\Block\Adminhtml\Question\Edit\SaveAndContinueButton"/>
            <button name="delete" class="Krish\ProductQuestions\Block\Adminhtml\Question\Edit\DeleteButton"/>
            <button name="back" class="Krish\ProductQuestions\Block\Adminhtml\Question\Edit\BackButton"/>
        </buttons>
        <namespace>krish_productquestions_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>krish_productquestions_form.krish_productquestions_form_data_source</dep>
        </deps>
    </settings>
    
    <dataSource name="krish_productquestions_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="product_questions/index/save"/>
        </settings>
        <dataProvider class="Krish\ProductQuestions\Model\Question\DataProvider" name="krish_productquestions_form_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>question_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    
    <fieldset name="general">
        <settings>
            <label translate="true">Question Details</label>
        </settings>
        
        <field name="question_id" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <visible>false</visible>
                <dataScope>question_id</dataScope>
            </settings>
        </field>
        
        <field name="customer_name" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Customer Name</label>
                <dataScope>customer_name</dataScope>
                <disabled>true</disabled>
            </settings>
        </field>
        
        <field name="customer_email" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-email" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Customer Email</label>
                <dataScope>customer_email</dataScope>
                <disabled>true</disabled>
            </settings>
        </field>
        
        <field name="product_sku" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Product SKU</label>
                <dataScope>product_sku</dataScope>
                <disabled>true</disabled>
            </settings>
        </field>
        
        <field name="question" formElement="textarea">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Customer Question</label>
                <dataScope>question</dataScope>
                <disabled>true</disabled>
            </settings>
        </field>
        
        <field name="submitted_at" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Submitted Date</label>
                <dataScope>submitted_at</dataScope>
                <disabled>true</disabled>
            </settings>
        </field>
    </fieldset>
    
    <fieldset name="response">
        <settings>
            <label translate="true">Admin Response</label>
        </settings>
        
        <field name="answer" formElement="textarea">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Answer</label>
                <dataScope>answer</dataScope>
                <notice translate="true">Provide your answer to the customer's question.</notice>
            </settings>
        </field>
        
        <field name="status" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Status</label>
                <dataScope>status</dataScope>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Krish\ProductQuestions\Model\Source\Status"/>
                        <caption translate="true">-- Please Select --</caption>
                    </settings>
                </select>
            </formElements>
        </field>
        
        <field name="visible_on_frontend" formElement="checkbox">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                    <item name="default" xsi:type="number">0</item>
                </item>
            </argument>
            <settings>
                <dataType>boolean</dataType>
                <label translate="true">Visible on Frontend</label>
                <dataScope>visible_on_frontend</dataScope>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="number">0</map>
                            <map name="true" xsi:type="number">1</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
        
        <field name="answered_at" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">question</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Answered Date</label>
                <dataScope>answered_at</dataScope>
                <disabled>true</disabled>
                <notice translate="true">This will be automatically set when the question is answered.</notice>
            </settings>
        </field>
    </fieldset>
</form>
