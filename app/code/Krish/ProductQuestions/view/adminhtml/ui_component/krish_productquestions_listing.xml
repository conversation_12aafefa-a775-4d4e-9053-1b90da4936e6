<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_ProductQuestions
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">krish_productquestions_listing.krish_productquestions_listing_data_source</item>
        </item>
    </argument>
    
    <settings>
        <buttons>
            <button name="add">
                <url path="*/*/newAction"/>
                <class>primary</class>
                <label translate="true">Add New Question</label>
            </button>
        </buttons>
        <spinner>krish_productquestions_columns</spinner>
        <deps>
            <dep>krish_productquestions_listing.krish_productquestions_listing_data_source</dep>
        </deps>
    </settings>
    
    <dataSource name="krish_productquestions_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">question_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Krish_ProductQuestions::product_questions_view</aclResource>
        <dataProvider class="Krish\ProductQuestions\Ui\DataProvider\Question\ListingDataProvider" name="krish_productquestions_listing_data_source">
            <settings>
                <requestFieldName>question_id</requestFieldName>
                <primaryFieldName>question_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
            <filterSelect name="status" provider="${ $.parentName }">
                <settings>
                    <options class="Krish\ProductQuestions\Model\Source\Status"/>
                    <caption translate="true">Select...</caption>
                    <label translate="true">Status</label>
                    <dataScope>status</dataScope>
                    <imports>
                        <link name="visible">componentType</link>
                    </imports>
                </settings>
            </filterSelect>
        </filters>
        <massaction name="listing_massaction">
            <action name="approve">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to approve selected questions?</message>
                        <title translate="true">Approve Questions</title>
                    </confirm>
                    <url path="product_questions/index/massApprove"/>
                    <type>approve</type>
                    <label translate="true">Approve</label>
                </settings>
            </action>
            <action name="reject">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to reject selected questions?</message>
                        <title translate="true">Reject Questions</title>
                    </confirm>
                    <url path="product_questions/index/massReject"/>
                    <type>reject</type>
                    <label translate="true">Reject</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    
    <columns name="krish_productquestions_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="product_questions/index/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">question_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">krish_productquestions_listing.krish_productquestions_listing.krish_productquestions_columns.ids</param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">krish_productquestions_listing.krish_productquestions_listing.krish_productquestions_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        
        <selectionsColumn name="ids">
            <settings>
                <indexField>question_id</indexField>
            </settings>
        </selectionsColumn>
        
        <column name="question_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        
        <column name="customer_name">
            <settings>
                <filter>text</filter>
                <label translate="true">Customer Name</label>
            </settings>
        </column>
        
        <column name="customer_email">
            <settings>
                <filter>text</filter>
                <label translate="true">Customer Email</label>
            </settings>
        </column>
        
        <column name="product_sku">
            <settings>
                <filter>text</filter>
                <label translate="true">Product SKU</label>
            </settings>
        </column>
        
        <column name="question">
            <settings>
                <filter>text</filter>
                <label translate="true">Question</label>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
            </settings>
        </column>
        
        <column name="status" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <options class="Krish\ProductQuestions\Model\Source\Status"/>
                <filter>select</filter>
                <editor>
                    <editorType>select</editorType>
                </editor>
                <dataType>select</dataType>
                <label translate="true">Status</label>
            </settings>
        </column>
        
        <column name="visible_on_frontend" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <options class="Magento\Config\Model\Config\Source\Yesno"/>
                <filter>select</filter>
                <editor>
                    <editorType>select</editorType>
                </editor>
                <dataType>select</dataType>
                <label translate="true">Visible on Frontend</label>
            </settings>
        </column>
        
        <column name="submitted_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Submitted Date</label>
            </settings>
        </column>
        
        <actionsColumn name="actions" class="Krish\ProductQuestions\Ui\Component\Listing\Column\Actions">
            <settings>
                <indexField>question_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>107</resizeDefaultWidth>
            </settings>
        </actionsColumn>
    </columns>
</listing>
