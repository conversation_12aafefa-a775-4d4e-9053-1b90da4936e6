/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

/* Product Questions Grid Styling */

/* Status Column Styling */
.admin__data-grid-wrap .data-grid .data-grid-cell-content .grid-severity-notice {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    text-align: center;
    min-width: 60px;
    color: white !important;
}

/* Status Colors */
.admin__data-grid-wrap .data-grid .data-grid-cell-content .status-approved {
    background-color: #28a745 !important;
    color: white !important;
}

.admin__data-grid-wrap .data-grid .data-grid-cell-content .status-rejected {
    background-color: #dc3545 !important;
    color: white !important;
}

.admin__data-grid-wrap .data-grid .data-grid-cell-content .status-pending {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

/* Visibility Icons Styling */
.admin__data-grid-wrap .data-grid .data-grid-cell-content .visibility-icon {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
}

.admin__data-grid-wrap .data-grid .data-grid-cell-content .visibility-icon.visible {
    color: #28a745;
}

.admin__data-grid-wrap .data-grid .data-grid-cell-content .visibility-icon.not-visible {
    color: #dc3545;
}

/* Hover Effects */
.admin__data-grid-wrap .data-grid .data-grid-cell-content .grid-severity-notice:hover {
    opacity: 0.8;
    transform: scale(1.05);
    transition: all 0.2s ease-in-out;
}

.admin__data-grid-wrap .data-grid .data-grid-cell-content .visibility-icon:hover {
    transform: scale(1.2);
    transition: all 0.2s ease-in-out;
}

/* Mass Action Styling */
.admin__data-grid-wrap .admin__data-grid-header .admin__data-grid-toolbar .mass-select-wrap .action-select-wrap .action-select .action-menu .action-menu-item._delete {
    color: #dc3545;
}

.admin__data-grid-wrap .admin__data-grid-header .admin__data-grid-toolbar .mass-select-wrap .action-select-wrap .action-select .action-menu .action-menu-item._delete:hover {
    background-color: #dc3545;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin__data-grid-wrap .data-grid .data-grid-cell-content .grid-severity-notice {
        font-size: 10px;
        padding: 2px 4px;
        min-width: 50px;
    }
    
    .admin__data-grid-wrap .data-grid .data-grid-cell-content .visibility-icon {
        font-size: 14px;
        width: 18px;
        height: 18px;
        line-height: 18px;
    }
}
