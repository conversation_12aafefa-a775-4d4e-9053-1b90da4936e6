<!--@subject Your Product Question Has Been Submitted @-->
<!--@vars {
"var question.getQuestion()":"Customer Question",
"var question.getCustomerName()":"Customer Name",
"var question.getProductSku()":"Product SKU",
"var store.frontend_name":"Store Name"
} @-->

{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <p class="greeting">{{trans "Hello %customer_name," customer_name=$question.getCustomerName()}}</p>
            <p>
                {{trans "Thank you for submitting your question about product %product_sku. We have received your question and it is currently being reviewed by our team." product_sku=$question.getProductSku()}}
            </p>
        </td>
    </tr>
    <tr class="email-summary">
        <td>
            <h3>{{trans "Your Question:"}}</h3>
            <div style="background: #f9f9f9; padding: 15px; border-left: 4px solid #1979c3; margin: 15px 0;">
                <p><strong>{{trans "Product:"}} {{var question.getProductSku()}}</strong></p>
                <p><strong>{{trans "Question:"}}</strong></p>
                <p>{{var question.getQuestion()}}</p>
            </div>
        </td>
    </tr>
    <tr class="email-information">
        <td>
            <p>
                {{trans "You will receive an email notification once your question has been reviewed and answered by our team. This typically takes 1-2 business days."}}
            </p>
            <p>
                {{trans "If you have any urgent questions, please feel free to contact our customer service team."}}
            </p>
        </td>
    </tr>
    <tr class="email-summary">
        <td class="action-content">
            <h3>{{trans "Thank you for shopping with %store_name!" store_name=$store.frontend_name}}</h3>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
