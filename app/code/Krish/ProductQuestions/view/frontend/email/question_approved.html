<!--@subject Your Product Question Has Been Answered @-->
<!--@vars {
"var question.getQuestion()":"Customer Question",
"var question.getAnswer()":"Admin Answer",
"var question.getCustomerName()":"Customer Name",
"var question.getProductSku()":"Product SKU",
"var store.frontend_name":"Store Name"
} @-->

{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <p class="greeting">{{trans "Hello %customer_name," customer_name=$question.getCustomerName()}}</p>
            <p>
                {{trans "Great news! Your question about product %product_sku has been answered by our team." product_sku=$question.getProductSku()}}
            </p>
        </td>
    </tr>
    <tr class="email-summary">
        <td>
            <h3>{{trans "Your Question:"}}</h3>
            <div style="background: #f9f9f9; padding: 15px; border-left: 4px solid #1979c3; margin: 15px 0;">
                <p><strong>{{trans "Product:"}} {{var question.getProductSku()}}</strong></p>
                <p><strong>{{trans "Your Question:"}}</strong></p>
                <p>{{var question.getQuestion()}}</p>
            </div>
            
            <h3>{{trans "Our Answer:"}}</h3>
            <div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #4caf50; margin: 15px 0;">
                <p>{{var question.getAnswer()}}</p>
            </div>
        </td>
    </tr>
    <tr class="email-information">
        <td>
            <p>
                {{trans "Your question and answer are now visible on the product page for other customers to see."}}
            </p>
            <p>
                {{trans "If you have any additional questions, please feel free to ask!"}}
            </p>
        </td>
    </tr>
    <tr class="email-summary">
        <td class="action-content">
            <h3>{{trans "Thank you for shopping with %store_name!" store_name=$store.frontend_name}}</h3>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
