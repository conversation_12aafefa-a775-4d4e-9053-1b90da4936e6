<?php
/**
 * @var \Krish\ProductQuestions\Block\Product\Questions $block
 */

$product = $block->getProduct();
$questions = $block->getQuestions();
$questionsCount = $block->getQuestionsCount();
$isLoggedIn = $block->isCustomerLoggedIn();
$isLoginRequired = $block->isLoginRequired();
$showAccordion = $block->isAccordionEnabled();
?>

<div class="product-questions-wrapper" id="product-questions-wrapper">
    <div class="questions-header">
        <h3 class="questions-title">
            <?= $escaper->escapeHtml($block->getTabTitle()) ?>
            <span class="questions-count">(<?= (int) $questionsCount ?>)</span>
        </h3>

        <?php if ($isLoggedIn): ?>
            <button type="button"
                    class="action primary ask-question-btn"
                    id="ask-question-btn"
                    data-mage-init='{"Krish_ProductQuestions/js/product-questions": {}}'>
                <span><?= $escaper->escapeHtml(__('Ask a Question')) ?></span>
            </button>
        <?php else: ?>
            <a href="<?= $escaper->escapeUrl($block->getCustomerLoginUrl()) ?>"
               class="action primary login-to-ask">
                <span><?= $escaper->escapeHtml(__('Login to Ask Question')) ?></span>
            </a>
        <?php endif; ?>
    </div>

    <!-- Question Form Modal -->
    <div id="question-form-modal" class="modal-wrapper" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h4><?= $escaper->escapeHtml(__('Ask a Question')) ?></h4>
                <button type="button" class="modal-close" id="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="question-form" method="post" data-mage-init='{"validation":{}}'>
                    <input type="hidden" name="form_key" value="<?= $escaper->escapeHtmlAttr($block->getFormKey()) ?>"/>
                    <input type="hidden" name="product_sku" value="<?= $escaper->escapeHtmlAttr($product->getSku()) ?>"/>

                    <?php if (!$isLoggedIn): ?>
                        <div class="field required">
                            <label for="customer_name" class="label">
                                <span><?= $escaper->escapeHtml(__('Your Name')) ?></span>
                            </label>
                            <div class="control">
                                <input type="text"
                                       name="customer_name"
                                       id="customer_name"
                                       class="input-text required-entry"
                                       data-validate="{required:true}"/>
                            </div>
                        </div>

                        <div class="field required">
                            <label for="customer_email" class="label">
                                <span><?= $escaper->escapeHtml(__('Your Email')) ?></span>
                            </label>
                            <div class="control">
                                <input type="email"
                                       name="customer_email"
                                       id="customer_email"
                                       class="input-text required-entry validate-email"
                                       data-validate="{required:true, 'validate-email':true}"/>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="field required">
                        <label for="question" class="label">
                            <span><?= $escaper->escapeHtml(__('Your Question')) ?></span>
                        </label>
                        <div class="control">
                            <textarea name="question"
                                      id="question"
                                      class="input-text required-entry"
                                      rows="4"
                                      placeholder="<?= $escaper->escapeHtmlAttr(__('Please enter your question about this product...')) ?>"
                                      data-validate="{required:true}"></textarea>
                        </div>
                    </div>

                    <div class="actions-toolbar">
                        <div class="primary">
                            <button type="submit" class="action submit primary">
                                <span><?= $escaper->escapeHtml(__('Submit Question')) ?></span>
                            </button>
                        </div>
                        <div class="secondary">
                            <button type="button" class="action cancel" id="cancel-question">
                                <span><?= $escaper->escapeHtml(__('Cancel')) ?></span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Questions List -->
    <div class="questions-list-wrapper">
        <?php if ($questionsCount > 0): ?>
            <div class="questions-list" id="questions-list">
                <?php if ($showAccordion): ?>
                    <!-- Accordion Style -->
                    <div class="questions-accordion" data-mage-init='{"accordion":{"collapsible": true, "openedState": "_show", "closedState": "_hide"}}'>
                        <?php foreach ($questions as $index => $question): ?>
                            <div data-role="collapsible" class="question-item">
                                <div data-role="trigger" class="question-header">
                                    <h5 class="question-text">
                                        <?= $escaper->escapeHtml($question['question']) ?>
                                    </h5>
                                    <div class="question-meta">
                                        <?php if ($block->showCustomerName()): ?>
                                            <span class="customer-name">
                                                <?= $escaper->escapeHtml(__('by %1', $question['customer_name'])) ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($block->showQuestionDate()): ?>
                                            <span class="question-date">
                                                <?= $escaper->escapeHtml($block->formatDate($question['submitted_at'])) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div data-role="content" class="question-content">
                                    <?php if ($question['answer']): ?>
                                        <div class="answer">
                                            <strong><?= $escaper->escapeHtml(__('Answer:')) ?></strong>
                                            <p><?= $escaper->escapeHtml($question['answer']) ?></p>
                                            <?php if ($question['answered_at']): ?>
                                                <small class="answer-date">
                                                    <?= $escaper->escapeHtml(__('Answered on %1', $block->formatDate($question['answered_at']))) ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <p class="no-answer"><?= $escaper->escapeHtml(__('This question is pending an answer.')) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <!-- List Style -->
                    <div class="questions-simple-list">
                        <?php foreach ($questions as $question): ?>
                            <div class="question-item">
                                <div class="question-header">
                                    <h5 class="question-text">
                                        <?= $escaper->escapeHtml($question->getQuestion()) ?>
                                    </h5>
                                    <div class="question-meta">
                                        <?php if ($block->showCustomerName()): ?>
                                            <span class="customer-name">
                                                <?= $escaper->escapeHtml(__('by %1', $question->getCustomerName())) ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($block->showQuestionDate()): ?>
                                            <span class="question-date">
                                                <?= $escaper->escapeHtml($block->formatDate($question->getSubmittedAt())) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if ($question->getAnswer()): ?>
                                    <div class="answer">
                                        <strong><?= $escaper->escapeHtml(__('Answer:')) ?></strong>
                                        <p><?= $escaper->escapeHtml($question->getAnswer()) ?></p>
                                        <?php if ($question->getAnsweredAt()): ?>
                                            <small class="answer-date">
                                                <?= $escaper->escapeHtml(__('Answered on %1', $block->formatDate($question->getAnsweredAt()))) ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="no-answer"><?= $escaper->escapeHtml(__('This question is pending an answer.')) ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination will be handled by JavaScript for AJAX loading -->
            <div class="questions-pagination" id="questions-pagination" style="display: none;">
                <button type="button" class="action load-more" id="load-more-questions">
                    <span><?= $escaper->escapeHtml(__('Load More Questions')) ?></span>
                </button>
            </div>
        <?php else: ?>
            <div class="no-questions">
                <p><?= $escaper->escapeHtml(__('No questions have been asked about this product yet.')) ?></p>
                <?php if ($isLoggedIn): ?>
                    <p><?= $escaper->escapeHtml(__('Be the first to ask a question!')) ?></p>
                <?php else: ?>
                    <p><?= $escaper->escapeHtml(__('Login to be the first to ask a question!')) ?></p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<script type="text/x-magento-init">
{
    "#product-questions-wrapper": {
        "Krish_ProductQuestions/js/product-questions": {
            "submitUrl": "<?= $escaper->escapeUrl($block->getSubmitQuestionUrl()) ?>",
            "listUrl": "<?= $escaper->escapeUrl($block->getQuestionsListUrl()) ?>",
            "productSku": "<?= $escaper->escapeJs($product->getSku()) ?>",
            "questionsPerPage": "<?= (int)$block->getQuestionsPerPage() ?>",
            "isAccordion": "<?= $showAccordion ? 'true' : 'false' ?>"
        }
    }
}
</script>
