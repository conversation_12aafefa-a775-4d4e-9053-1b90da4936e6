/**
 * @category   Krish Technolabs Module Development
 * @package    Krish_ProductQuestions
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

define([
    'jquery',
    'mage/url',
    'mage/translate',
    'Magento_Ui/js/modal/alert',
    'Magento_Ui/js/modal/confirm',
    'mage/validation'
], function ($, urlBuilder, $t, alert, confirm) {
    'use strict';

    $.widget('krish.productQuestions', {
        options: {
            submitUrl: '',
            listUrl: '',
            productSku: '',
            questionsPerPage: 10,
            isAccordion: false,
            currentPage: 1,
            totalPages: 1,
            isLoading: false,
            isRefreshing: false
        },

        _create: function () {
            this._bind();
            this._initModal();
        },

        _bind: function () {
            var self = this;

            // Ask question button
            $(document).on('click', '#ask-question-btn', function (e) {
                e.preventDefault();
                self._showModal();
            });

            // Close modal buttons
            $(document).on('click', '#close-modal, #cancel-question', function (e) {
                e.preventDefault();
                self._hideModal();
            });

            // Submit question form
            $(document).on('submit', '#question-form', function (e) {
                e.preventDefault();
                self._submitQuestion($(this));
            });

            // Load more questions
            $(document).on('click', '#load-more-questions', function (e) {
                e.preventDefault();
                self._loadMoreQuestions();
            });

            // Close modal when clicking outside
            $(document).on('click', '#question-form-modal', function (e) {
                if (e.target === this) {
                    self._hideModal();
                }
            });
        },

        _initModal: function () {
            // Initialize form validation
            $('#question-form').validation();
        },

        _showModal: function () {
            $('#question-form-modal').show();
            $('body').addClass('modal-open');
            $('#question').focus();
        },

        _hideModal: function () {
            $('#question-form-modal').hide();
            $('body').removeClass('modal-open');
            this._resetForm();
        },

        _resetForm: function (form) {
            // If form parameter is provided, use it; otherwise use default selector
            var targetForm = form || $('#question-form');
            targetForm[0].reset();
            targetForm.validation('clearError');
        },

        _submitQuestion: function (form) {
            var self = this;

            if (!form.validation('isValid')) {
                return false;
            }

            if (self.options.isLoading) {
                return false;
            }

            self.options.isLoading = true;
            self._showLoader(form);

            var formData = form.serialize();

            $.ajax({
                url: self.options.submitUrl,
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function (response) {
                    // self._hideLoader(form);
                    // self.options.isLoading = false;
                    //
                    // if (response.success) {
                    //     // Reset form first to prevent any validation issues
                    //     self._resetForm(form);
                    //
                    //     // Hide modal after form reset
                    //     self._hideModal();
                    //
                    //     // Show success message
                    //     self._showSuccessMessage(response.message);
                    //
                    //     // Delay the page refresh to allow the success message to be shown
                    //     // and prevent race conditions with error handler
                    //     setTimeout(function() {
                    //         // Set a flag to prevent error handler from firing during reload
                    //         self.options.isRefreshing = true;
                    //         self._refreshQuestionsList();
                    //     }, 2000);
                    // } else {
                    //     self._showErrorMessage(response.message || $t('An error occurred while submitting your question.'));
                    // }
                },
                error: function (xhr, status, error) {
                    self._hideLoader(form);
                    self.options.isLoading = false;

                    // Don't show error if we're refreshing the page or if it's a page unload
                    if (!self.options.isRefreshing &&
                        status !== 'abort' &&
                        xhr.readyState !== 0 &&
                        status !== 'canceled') {
                        self._showErrorMessage($t('An error occurred while submitting your question. Please try again.'));
                    }
                }
            });
        },

        _loadMoreQuestions: function () {
            var self = this;

            if (self.options.isLoading || self.options.currentPage >= self.options.totalPages) {
                return false;
            }

            self.options.isLoading = true;
            self.options.currentPage++;

            var loadMoreBtn = $('#load-more-questions');
            loadMoreBtn.prop('disabled', true).text($t('Loading...'));

            $.ajax({
                url: self.options.listUrl,
                type: 'GET',
                data: {
                    product_sku: self.options.productSku,
                    page: self.options.currentPage
                },
                dataType: 'json',
                success: function (response) {
                    self.options.isLoading = false;
                    loadMoreBtn.prop('disabled', false).text($t('Load More Questions'));

                    if (response.success && response.questions.length > 0) {
                        self._appendQuestions(response.questions);
                        self.options.totalPages = response.total_pages;

                        if (!response.has_more) {
                            $('#questions-pagination').hide();
                        }
                    } else {
                        $('#questions-pagination').hide();
                    }
                },
                error: function () {
                    self.options.isLoading = false;
                    self.options.currentPage--;
                    loadMoreBtn.prop('disabled', false).text($t('Load More Questions'));
                    self._showErrorMessage($t('An error occurred while loading questions.'));
                }
            });
        },

        _appendQuestions: function (questions) {
            var self = this;
            var questionsContainer = self.options.isAccordion ?
                $('.questions-accordion') : $('.questions-simple-list');

            $.each(questions, function (index, question) {
                var questionHtml = self._buildQuestionHtml(question);
                questionsContainer.append(questionHtml);
            });

            // Reinitialize accordion if needed
            if (self.options.isAccordion) {
                questionsContainer.accordion('refresh');
            }
        },

        _buildQuestionHtml: function (question) {
            var self = this;
            var html = '';

            if (self.options.isAccordion) {
                html = '<div data-role="collapsible" class="question-item">' +
                    '<div data-role="trigger" class="question-header">' +
                    '<h5 class="question-text">' + self._escapeHtml(question.question) + '</h5>' +
                    '<div class="question-meta">';

                if (question.customer_name) {
                    html += '<span class="customer-name">' +
                        $t('by %1').replace('%1', self._escapeHtml(question.customer_name)) + '</span>';
                }

                if (question.submitted_at) {
                    html += '<span class="question-date">' +
                        self._formatDate(question.submitted_at) + '</span>';
                }

                html += '</div></div>' +
                    '<div data-role="content" class="question-content">';

                if (question.answer) {
                    html += '<div class="answer">' +
                        '<strong>' + $t('Answer:') + '</strong>' +
                        '<p>' + self._escapeHtml(question.answer) + '</p>';

                    if (question.answered_at) {
                        html += '<small class="answer-date">' +
                            $t('Answered on %1').replace('%1', self._formatDate(question.answered_at)) + '</small>';
                    }

                    html += '</div>';
                } else {
                    html += '<p class="no-answer">' + $t('This question is pending an answer.') + '</p>';
                }

                html += '</div></div>';
            } else {
                html = '<div class="question-item">' +
                    '<div class="question-header">' +
                    '<h5 class="question-text">' + self._escapeHtml(question.question) + '</h5>' +
                    '<div class="question-meta">';

                if (question.customer_name) {
                    html += '<span class="customer-name">' +
                        $t('by %1').replace('%1', self._escapeHtml(question.customer_name)) + '</span>';
                }

                if (question.submitted_at) {
                    html += '<span class="question-date">' +
                        self._formatDate(question.submitted_at) + '</span>';
                }

                html += '</div></div>';

                if (question.answer) {
                    html += '<div class="answer">' +
                        '<strong>' + $t('Answer:') + '</strong>' +
                        '<p>' + self._escapeHtml(question.answer) + '</p>';

                    if (question.answered_at) {
                        html += '<small class="answer-date">' +
                            $t('Answered on %1').replace('%1', self._formatDate(question.answered_at)) + '</small>';
                    }

                    html += '</div>';
                } else {
                    html += '<p class="no-answer">' + $t('This question is pending an answer.') + '</p>';
                }

                html += '</div>';
            }

            return html;
        },

        _refreshQuestionsList: function () {
            // Reload the page to show updated questions list
            // In a more advanced implementation, you could reload just the questions section
            window.location.reload();
        },

        _showLoader: function (form) {
            var submitBtn = form.find('button[type="submit"]');
            submitBtn.prop('disabled', true);
            submitBtn.find('span').text($t('Submitting...'));
        },

        _hideLoader: function (form) {
            var submitBtn = form.find('button[type="submit"]');
            submitBtn.prop('disabled', false);
            submitBtn.find('span').text($t('Submit Question'));
        },

        _showSuccessMessage: function (message) {
            alert({
                title: $t('Success'),
                content: message,
                modalClass: 'success-modal'
            });
        },

        _showErrorMessage: function (message) {
            alert({
                title: $t('Error'),
                content: message,
                modalClass: 'error-modal'
            });
        },

        _escapeHtml: function (text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function (m) {
                return map[m];
            });
        },

        _formatDate: function (dateString) {
            var date = new Date(dateString);
            return date.toLocaleDateString();
        }
    });

    return $.krish.productQuestions;
});
