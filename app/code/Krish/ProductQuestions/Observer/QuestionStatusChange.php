<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterface;
use Krish\ProductQuestions\Api\Data\EmailMessageInterface;
use Krish\ProductQuestions\Helper\Email as EmailHelper;
use Krish\ProductQuestions\Model\Queue\EmailPublisher;
use Krish\ProductQuestions\Helper\Data as DataHelper;
use Psr\Log\LoggerInterface;

/**
 * Question status change observer
 */
class QuestionStatusChange implements ObserverInterface
{
    /**
     * @param EmailPublisher $emailPublisher
     * @param EmailHelper $emailHelper
     * @param DataHelper $dataHelper
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EmailPublisher $emailPublisher,
        private readonly EmailHelper $emailHelper,
        private readonly DataHelper $dataHelper,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        try {
            /** @var QuestionInterface $question */
            $question = $observer->getEvent()->getQuestion();
            $originalStatus = $observer->getEvent()->getOriginalStatus();
            $newStatus = $question->getStatus();

            // Check if email notifications are enabled
            if (!$this->dataHelper->isEmailEnabled()) {
                $this->logger->info('Email notifications are disabled, skipping email processing');
                return;
            }

            // Only send emails if status actually changed
            if ($originalStatus !== $newStatus) {
                $emailType = $this->getEmailTypeForStatus($newStatus);

                if ($emailType) {
                    // Check if queue should be used for email processing
                    if ($this->dataHelper->useQueueForEmails()) {
                        // Try to use queue for email processing
                        try {
                            $this->emailPublisher->publishQuestionStatusChangeEmail($question, $emailType);
                            $this->logger->info(sprintf(
                                'Question status change email queued successfully: QuestionId=%d, Status=%d, EmailType=%s',
                                $question->getQuestionId(),
                                $newStatus,
                                $emailType
                            ));
                        } catch (\Exception $queueException) {
                            // Fallback to direct email sending if queue fails
                            $this->logger->warning('Queue publishing failed, falling back to direct email sending: ' . $queueException->getMessage());

                            $this->sendEmailDirectly($question, $newStatus);

                            $this->logger->info('Fallback email sending completed for question ID: ' . $question->getQuestionId());
                        }
                    } else {
                        // Send emails directly (synchronous)
                        $this->logger->info('Queue disabled, sending email directly for question ID: ' . $question->getQuestionId());
                        $this->sendEmailDirectly($question, $newStatus);
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('Error in QuestionStatusChange observer: ' . $e->getMessage());
        }
    }

    /**
     * Get email type for status
     *
     * @param int $status
     * @return string|null
     */
    private function getEmailTypeForStatus(int $status): ?string
    {
        switch ($status) {
            case QuestionInterface::STATUS_APPROVED:
                return EmailMessageInterface::TYPE_QUESTION_APPROVED;
            case QuestionInterface::STATUS_REJECTED:
                return EmailMessageInterface::TYPE_QUESTION_REJECTED;
            default:
                return null;
        }
    }

    /**
     * Send email directly (fallback method)
     *
     * @param QuestionInterface $question
     * @param int $status
     * @return void
     */
    private function sendEmailDirectly(QuestionInterface $question, int $status): void
    {
        switch ($status) {
            case QuestionInterface::STATUS_APPROVED:
                $this->emailHelper->sendQuestionApprovedEmail($question);
                break;
            case QuestionInterface::STATUS_REJECTED:
                $this->emailHelper->sendQuestionRejectedEmail($question);
                break;
        }
    }
}
