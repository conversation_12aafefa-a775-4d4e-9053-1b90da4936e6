<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterface;
use Krish\ProductQuestions\Helper\Email as EmailHelper;
use Psr\Log\LoggerInterface;

/**
 * Question submitted observer
 */
class QuestionSubmitted implements ObserverInterface
{
    /**
     * @param EmailHelper $emailHelper
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EmailHelper $emailHelper,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        try {
            /** @var QuestionInterface $question */
            $question = $observer->getEvent()->getQuestion();

            // Send customer notification email
            $this->emailHelper->sendQuestionSubmittedCustomerEmail($question);

            // Send admin notification email
            $this->emailHelper->sendQuestionSubmittedAdminEmail($question);

        } catch (\Exception $e) {
            $this->logger->error('Error in QuestionSubmitted observer: ' . $e->getMessage());
        }
    }
}
