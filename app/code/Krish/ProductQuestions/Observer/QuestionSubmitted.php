<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterface;
use Krish\ProductQuestions\Helper\Email as EmailHelper;
use Krish\ProductQuestions\Model\Queue\EmailPublisher;
use Krish\ProductQuestions\Helper\Data as DataHelper;
use Psr\Log\LoggerInterface;

/**
 * Question submitted observer
 */
class QuestionSubmitted implements ObserverInterface
{
    /**
     * @param EmailPublisher $emailPublisher
     * @param EmailHelper $emailHelper
     * @param DataHelper $dataHelper
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EmailPublisher $emailPublisher,
        private readonly EmailHelper $emailHelper,
        private readonly DataHelper $dataHelper,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        try {
            /** @var QuestionInterface $question */
            $question = $observer->getEvent()->getQuestion();

            // Check if email notifications are enabled
            if (!$this->dataHelper->isEmailEnabled()) {
                $this->logger->info('Email notifications are disabled, skipping email processing');
                return;
            }

            // Check if queue should be used for email processing
            if ($this->dataHelper->useQueueForEmails()) {
                // Try to use queue for email processing
                try {
                    $this->emailPublisher->publishQuestionSubmittedEmails($question);
                    $this->logger->info('Question submitted emails queued successfully for question ID: ' . $question->getQuestionId());
                } catch (\Exception $queueException) {
                    // Fallback to direct email sending if queue fails
                    $this->logger->warning('Queue publishing failed, falling back to direct email sending: ' . $queueException->getMessage());

                    $this->sendEmailsDirectly($question);

                    $this->logger->info('Fallback email sending completed for question ID: ' . $question->getQuestionId());
                }
            } else {
                // Send emails directly (synchronous)
                $this->logger->info('Queue disabled, sending emails directly for question ID: ' . $question->getQuestionId());
                $this->sendEmailsDirectly($question);
            }

        } catch (\Exception $e) {
            $this->logger->error('Error in QuestionSubmitted observer: ' . $e->getMessage());
        }
    }

    /**
     * Send emails directly (fallback/direct method)
     *
     * @param QuestionInterface $question
     * @return void
     */
    private function sendEmailsDirectly(QuestionInterface $question): void
    {
        // Send customer notification email
        $this->emailHelper->sendQuestionSubmittedCustomerEmail($question);

        // Send admin notification email
        $this->emailHelper->sendQuestionSubmittedAdminEmail($question);
    }
}
