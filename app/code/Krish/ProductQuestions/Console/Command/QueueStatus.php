<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Console\Cli;

/**
 * Console command to check queue status
 */
class QueueStatus extends Command
{
    /**
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection
    ) {
        parent::__construct();
    }

    /**
     * Configure command
     */
    protected function configure(): void
    {
        $this->setName('krish:product-questions:queue-status')
            ->setDescription('Check Product Questions email queue status');
    }

    /**
     * Execute command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $connection = $this->resourceConnection->getConnection();

            // Check queue table
            $queueTable = $this->resourceConnection->getTableName('queue');
            $queueMessageTable = $this->resourceConnection->getTableName('queue_message');
            $queueMessageStatusTable = $this->resourceConnection->getTableName('queue_message_status');

            $output->writeln('<info>Product Questions Email Queue Status</info>');
            $output->writeln('<info>=====================================</info>');

            // Get queue info
            $queueSelect = $connection->select()
                ->from($queueTable, ['id', 'name'])
                ->where('name = ?', 'krish.product.questions.email.queue');

            $queueInfo = $connection->fetchRow($queueSelect);

            if (!$queueInfo) {
                $output->writeln('<error>Queue not found!</error>');
                return Cli::RETURN_FAILURE;
            }

            $queueId = $queueInfo['id'];
            $output->writeln(sprintf('Queue ID: %d', $queueId));
            $output->writeln(sprintf('Queue Name: %s', $queueInfo['name']));
            $output->writeln('');

            // Get message counts
            $totalMessages = $connection->fetchOne(
                $connection->select()
                    ->from($queueMessageTable, ['COUNT(*)'])
                    ->where('id = ?', $queueId)
            );

            $pendingMessages = $connection->fetchOne(
                $connection->select()
                    ->from(['qm' => $queueMessageTable])
                    ->joinLeft(
                        ['qms' => $queueMessageStatusTable],
                        'qm.id = qms.message_id',
                        []
                    )
                    ->where('qm.id = ?', $queueId)
                    ->where('qms.status IS NULL OR qms.status = ?', 1) // 1 = new/retry
                    ->columns(['COUNT(*)'])
            );

            $processedMessages = $connection->fetchOne(
                $connection->select()
                    ->from(['qm' => $queueMessageTable])
                    ->joinInner(
                        ['qms' => $queueMessageStatusTable],
                        'qm.id = qms.message_id',
                        []
                    )
                    ->where('qm.id = ?', $queueId)
                    ->where('qms.status = ?', 4) // 4 = complete
                    ->columns(['COUNT(*)'])
            );

            $failedMessages = $connection->fetchOne(
                $connection->select()
                    ->from(['qm' => $queueMessageTable])
                    ->joinInner(
                        ['qms' => $queueMessageStatusTable],
                        'qm.id = qms.message_id',
                        []
                    )
                    ->where('qm.id = ?', $queueId)
                    ->where('qms.status = ?', 6) // 6 = error
                    ->columns(['COUNT(*)'])
            );

            $output->writeln('<info>Message Statistics:</info>');
            $output->writeln(sprintf('Total Messages: %d', $totalMessages));
            $output->writeln(sprintf('<comment>Pending Messages: %d</comment>', $pendingMessages));
            $output->writeln(sprintf('<info>Processed Messages: %d</info>', $processedMessages));
            $output->writeln(sprintf('<error>Failed Messages: %d</error>', $failedMessages));

            if ($pendingMessages > 0) {
                $output->writeln('');
                $output->writeln('<comment>There are pending messages in the queue.</comment>');
                $output->writeln('<comment>Make sure the consumer is running:</comment>');
                $output->writeln('<comment>bin/magento queue:consumers:start krish.product.questions.email.consumer</comment>');
            }

            return Cli::RETURN_SUCCESS;

        } catch (\Exception $e) {
            $output->writeln('<error>Error checking queue status: ' . $e->getMessage() . '</error>');
            return Cli::RETURN_FAILURE;
        }
    }
}
