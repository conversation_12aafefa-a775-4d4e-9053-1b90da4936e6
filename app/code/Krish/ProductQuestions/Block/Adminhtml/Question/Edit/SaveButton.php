<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Block\Adminhtml\Question\Edit;

use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;

/**
 * Save button for product question edit page
 */
class SaveButton extends GenericButton implements ButtonProviderInterface
{
    /**
     * Get button data for save button
     *
     * @return array
     */
    public function getButtonData(): array
    {
        return [
            'label' => __('Save Question'),
            'class' => 'save primary',
            'data_attribute' => [
                'mage-init' => [
                    'button' => ['event' => 'save']
                ],
                'form-role' => 'save',
            ],
            'sort_order' => 90,
        ];
    }
}
