<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Block\Adminhtml\Question\Edit;

use Magento\Backend\Block\Widget\Context;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;

/**
 * Generic button for product question edit page
 */
class GenericButton implements ButtonProviderInterface
{
    /**
     * @param Context $context
     * @param QuestionRepositoryInterface $questionRepository
     */
    public function __construct(
        protected readonly Context $context,
        protected readonly QuestionRepositoryInterface $questionRepository
    ) {}

    /**
     * Return question ID
     *
     * @return int|null
     */
    public function getQuestionId(): ?int
    {
        try {
            $questionId = (int)$this->context->getRequest()->getParam('id');
            if ($questionId) {
                $this->questionRepository->getById($questionId);
                return $questionId;
            }
        } catch (NoSuchEntityException $e) {
            return null;
        }
        return null;
    }

    /**
     * Generate url by route and parameters
     *
     * @param string $route
     * @param array $params
     * @return string
     */
    public function getUrl(string $route = '', array $params = []): string
    {
        return $this->context->getUrlBuilder()->getUrl($route, $params);
    }

    /**
     * Get URL for back button
     *
     * @return string
     */
    public function getBackUrl(): string
    {
        return $this->getUrl('*/*/');
    }

    /**
     * Get URL for delete button
     *
     * @return string
     */
    public function getDeleteUrl(): string
    {
        return $this->getUrl('*/*/delete', ['id' => $this->getQuestionId()]);
    }

    /**
     * Get button data (default implementation)
     *
     * @return array
     */
    public function getButtonData(): array
    {
        return [];
    }
}
