<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Block\Product;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Framework\Registry;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\Data\Form\FormKey;
use Krish\ProductQuestions\Api\QuestionRepositoryInterface;
use Krish\ProductQuestions\Helper\Data as DataHelper;

/**
 * Product questions block
 */
class Questions extends Template
{
    /**
     * @var Registry
     */
    private $registry;

    /**
     * @var CustomerSession
     */
    private $customerSession;

    /**
     * @var CurrentCustomer
     */
    private $currentCustomer;

    /**
     * @var QuestionRepositoryInterface
     */
    private $questionRepository;

    /**
     * @var DataHelper
     */
    private $dataHelper;

    /**
     * @var FormKey
     */
    private $formKey;

    /**
     * @var \Magento\Catalog\Model\Product
     */
    private $product;

    /**
     * @var array
     */
    private $questions;

    /**
     * @param Context $context
     * @param Registry $registry
     * @param CustomerSession $customerSession
     * @param CurrentCustomer $currentCustomer
     * @param QuestionRepositoryInterface $questionRepository
     * @param DataHelper $dataHelper
     * @param FormKey $formKey
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        CustomerSession $customerSession,
        CurrentCustomer $currentCustomer,
        QuestionRepositoryInterface $questionRepository,
        DataHelper $dataHelper,
        FormKey $formKey,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->registry = $registry;
        $this->customerSession = $customerSession;
        $this->currentCustomer = $currentCustomer;
        $this->questionRepository = $questionRepository;
        $this->dataHelper = $dataHelper;
        $this->formKey = $formKey;
    }

    /**
     * Get current product
     *
     * @return \Magento\Catalog\Model\Product|null
     */
    public function getProduct()
    {
        if (!$this->product) {
            $this->product = $this->registry->registry('current_product');
        }
        return $this->product;
    }

    /**
     * Check if module is enabled
     *
     * @return bool
     */
    public function isEnabled()
    {
        return $this->dataHelper->isEnabled();
    }

    /**
     * Check if customer is logged in
     *
     * @return bool
     */
    public function isCustomerLoggedIn()
    {
        return $this->customerSession->isLoggedIn();
    }

    /**
     * Check if login is required
     *
     * @return bool
     */
    public function isLoginRequired()
    {
        return $this->dataHelper->isLoginRequired();
    }

    /**
     * Get customer login URL
     *
     * @return string
     */
    public function getCustomerLoginUrl()
    {
        return $this->getUrl('customer/account/login', [
            'referer' => $this->_urlBuilder->getCurrentUrl()
        ]);
    }

    /**
     * Get current customer
     *
     * @return \Magento\Customer\Api\Data\CustomerInterface|null
     */
    public function getCurrentCustomer()
    {
        return $this->currentCustomer->getCustomer();
    }

    /**
     * Get submit question URL
     *
     * @return string
     */
    public function getSubmitQuestionUrl()
    {
        return $this->getUrl('productquestions/question/submit');
    }

    /**
     * Get questions list URL
     *
     * @return string
     */
    public function getQuestionsListUrl()
    {
        return $this->getUrl('productquestions/question/list');
    }

    /**
     * Get questions for current product
     *
     * @return array
     */
    public function getQuestions()
    {
        if ($this->questions === null) {
            $this->questions = [];
            $product = $this->getProduct();

            if ($product) {
                $this->questions = $this->questionRepository->getByProductSku($product->getSku(), true);
            }
        }

        return $this->questions;
    }

    /**
     * Get questions count
     *
     * @return int
     */
    public function getQuestionsCount()
    {
        return count($this->getQuestions());
    }

    /**
     * Get tab title
     *
     * @return string
     */
    public function getTabTitle()
    {
        return $this->dataHelper->getTabTitle();
    }

    /**
     * Check if accordion is enabled
     *
     * @return bool
     */
    public function isAccordionEnabled()
    {
        return $this->dataHelper->isAccordionEnabled();
    }

    /**
     * Check if customer name should be shown
     *
     * @return bool
     */
    public function showCustomerName()
    {
        return $this->dataHelper->showCustomerName();
    }

    /**
     * Check if question date should be shown
     *
     * @return bool
     */
    public function showQuestionDate()
    {
        return $this->dataHelper->showQuestionDate();
    }

    /**
     * Get questions per page
     *
     * @return int
     */
    public function getQuestionsPerPage()
    {
        return $this->dataHelper->getQuestionsPerPage();
    }

    /**
     * Format date
     *
     * @param string|null $date
     * @param int $format
     * @param bool $showTime
     * @param string|null $timezone
     * @return string
     */
    public function formatDate($date = null, $format = \IntlDateFormatter::SHORT, $showTime = false, $timezone = null)
    {
        if ($date === null) {
            return '';
        }

        return $this->_localeDate->formatDateTime(
            new \DateTime($date),
            $format,
            $showTime ? \IntlDateFormatter::SHORT : \IntlDateFormatter::NONE,
            null,
            $timezone
        );
    }

    /**
     * Get form key
     *
     * @return string
     */
    public function getFormKey()
    {
        return $this->formKey->getFormKey();
    }

    /**
     * Check if block should be displayed
     *
     * @return string
     */
    protected function _toHtml(): string
    {
        // Check if module is enabled via admin configuration
        if (!$this->isEnabled()) {
            return '';
        }

        // Check if current product exists
        if (!$this->getProduct()) {
            return '';
        }

        return parent::_toHtml();
    }
}
