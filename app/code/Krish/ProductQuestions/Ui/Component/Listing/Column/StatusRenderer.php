<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Krish\ProductQuestions\Api\Data\QuestionInterface;

/**
 * Status column renderer with color-coded indicators
 */
class StatusRenderer extends Column
{
    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                if (isset($item['status'])) {
                    $item['status'] = $this->getStatusHtml((int)$item['status']);
                }
            }
        }

        return $dataSource;
    }

    /**
     * Get status HTML with color coding
     *
     * @param int $status
     * @return string
     */
    private function getStatusHtml(int $status): string
    {
        $statusLabels = [
            QuestionInterface::STATUS_PENDING => __('Pending'),
            QuestionInterface::STATUS_APPROVED => __('Approved'),
            QuestionInterface::STATUS_REJECTED => __('Rejected')
        ];

        $statusClasses = [
            QuestionInterface::STATUS_PENDING => 'status-pending',
            QuestionInterface::STATUS_APPROVED => 'status-approved',
            QuestionInterface::STATUS_REJECTED => 'status-rejected'
        ];

        $label = $statusLabels[$status] ?? __('Unknown');
        $class = $statusClasses[$status] ?? 'status-unknown';

        return sprintf(
            '<span class="grid-severity-notice %s" title="%s">%s</span>',
            $class,
            $label,
            $label
        );
    }
}
