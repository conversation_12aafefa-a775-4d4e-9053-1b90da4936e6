<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\ProductQuestions\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

/**
 * Visible on Frontend column renderer with icon indicators
 */
class VisibleRenderer extends Column
{
    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                if (isset($item['visible_on_frontend'])) {
                    $item['visible_on_frontend'] = $this->getVisibilityHtml((bool)$item['visible_on_frontend']);
                }
            }
        }

        return $dataSource;
    }

    /**
     * Get visibility HTML with icon indicators
     *
     * @param bool $isVisible
     * @return string
     */
    private function getVisibilityHtml(bool $isVisible): string
    {
        if ($isVisible) {
            return sprintf(
                '<span class="visibility-icon visible" title="%s">✓</span>',
                __('Visible on Frontend')
            );
        } else {
            return sprintf(
                '<span class="visibility-icon not-visible" title="%s">✗</span>',
                __('Not Visible on Frontend')
            );
        }
    }
}
