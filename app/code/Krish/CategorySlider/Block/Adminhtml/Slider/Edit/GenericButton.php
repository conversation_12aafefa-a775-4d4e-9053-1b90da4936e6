<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 Kris<PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Block\Adminhtml\Slider\Edit;

use Magento\Backend\Block\Widget\Context;
use Magento\Framework\Exception\NoSuchEntityException;
use <PERSON><PERSON>\CategorySlider\Api\SliderRepositoryInterface;

/**
 * Generic button for category slider edit page
 */
class GenericButton
{
    /**
     * @var Context
     */
    protected Context $context;

    /**
     * @var SliderRepositoryInterface
     */
    protected SliderRepositoryInterface $sliderRepository;

    /**
     * @param Context $context
     * @param SliderRepositoryInterface $sliderRepository
     */
    public function __construct(
        Context $context,
        SliderRepositoryInterface $sliderRepository
    ) {
        $this->context = $context;
        $this->sliderRepository = $sliderRepository;
    }

    /**
     * Return slider ID
     *
     * @return int|null
     */
    public function getSliderId(): ?int
    {
        try {
            $sliderId = (int)$this->context->getRequest()->getParam('slider_id');
            if ($sliderId) {
                $this->sliderRepository->getById($sliderId);
                return $sliderId;
            }
        } catch (NoSuchEntityException $e) {
            return null;
        }
        return null;
    }

    /**
     * Generate url by route and parameters
     *
     * @param string $route
     * @param array $params
     * @return string
     */
    public function getUrl(string $route = '', array $params = []): string
    {
        return $this->context->getUrlBuilder()->getUrl($route, $params);
    }
}
