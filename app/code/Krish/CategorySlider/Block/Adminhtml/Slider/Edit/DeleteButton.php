<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023 <PERSON><PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Block\Adminhtml\Slider\Edit;

use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;

/**
 * Delete button for category slider edit page
 */
class DeleteButton extends GenericButton implements ButtonProviderInterface
{
    /**
     * Get button data for delete button
     *
     * @return array
     */
    public function getButtonData(): array
    {
        $data = [];
        if ($this->getSliderId()) {
            $data = [
                'label' => __('Delete Slider'),
                'class' => 'delete',
                'on_click' => 'deleteConfirm(\'' . __(
                    'Are you sure you want to delete this slider?'
                ) . '\', \'' . $this->getDeleteUrl() . '\')',
                'sort_order' => 20,
            ];
        }
        return $data;
    }

    /**
     * Get URL for delete button
     *
     * @return string
     */
    public function getDeleteUrl(): string
    {
        return $this->getUrl('*/*/delete', ['slider_id' => $this->getSliderId()]);
    }
}
