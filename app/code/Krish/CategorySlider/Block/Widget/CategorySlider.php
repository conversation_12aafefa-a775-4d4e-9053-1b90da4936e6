<?php

/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\CategorySlider\Block\Widget;

use <PERSON>h\CategorySlider\Api\SliderRepositoryInterface;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Api\Data\CategoryInterface;
use Magento\Catalog\Helper\Output as OutputHelper;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Widget\Block\BlockInterface;
use Psr\Log\LoggerInterface;

/**
 * Category Slider Widget Block
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class CategorySlider extends Template implements BlockInterface
{
    /**
     * Default template path
     *
     * @var string
     */
    protected $_template = "widget/category_slider.phtml";

    /**
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     * @param Context $context Template context
     * @param CategoryRepositoryInterface $categoryRepository Category repository
     * @param StoreManagerInterface $storeManager Store manager
     * @param OutputHelper $outputHelper Output helper
     * @param Filesystem $filesystem Filesystem
     * @param SliderRepositoryInterface $sliderRepository Slider repository
     * @param LoggerInterface $logger Logger interface
     * @param array $data Additional data
     */
    public function __construct(
        Context $context,
        private readonly CategoryRepositoryInterface $categoryRepository,
        private readonly StoreManagerInterface $storeManager,
        private readonly OutputHelper $outputHelper,
        private readonly Filesystem $filesystem,
        private readonly SliderRepositoryInterface $sliderRepository,
        private readonly LoggerInterface $logger,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Get widget unique ID
     *
     * @return string
     */
    public function getWidgetId(): string
    {
        return 'krish-category-slider-' . uniqid();
    }

    /**
     * Get categories for the slider
     *
     * @return array<int, DataObject>
     */
    public function getCategories(): array
    {
        $categories = [];
        $categorySlider = $this->getData('category_slider');

        if (!$categorySlider) {
            return $categories;
        }

        try {
            $categoriesData = $this->sliderRepository->getById((int)$categorySlider);
        } catch (LocalizedException $e) {
            $this->logger->error('Error loading slider: ' . $e->getMessage(), [
                'slider_id' => $categorySlider,
                'exception' => $e
            ]);
            return $categories;
        }

        foreach ($categoriesData->getCategories() as $categoryData) {
            if (empty($categoryData['category_id'])) {
                continue;
            }

            try {
                $categoryId = (int)$categoryData['category_id'][0];
                $category = $this->categoryRepository->get($categoryId);

                // Skip if category is not active or not visible in menu
                if (!$category->getIsActive()) {
                    continue;
                }

                $categoryItem = new DataObject();
                $categoryItem->setCategory($category);
                $categoryItem->setCategoryId($categoryId);
                $categoryItem->setTitle($categoryData['title'] ?? $category->getName());
                $categoryItem->setSubtitle($categoryData['subtitle'] ?? $this->getCategoryDescription($category));
                $categoryItem->setImage($this->getCategoryImageUrl($category, $categoryData['image'] ?? ''));
                $categoryItem->setUrl($category->getUrl());

                $categories[] = $categoryItem;
            } catch (NoSuchEntityException $e) {
                $this->logger->warning('Category not found: ' . $e->getMessage(), [
                    'category_id' => $categoryData['category_id'][0] ?? 'unknown',
                    'exception' => $e
                ]);
                continue;
            } catch (\Exception $e) {
                $this->logger->error('Error processing category: ' . $e->getMessage(), [
                    'category_data' => $categoryData,
                    'exception' => $e
                ]);
                continue;
            }
        }

        return $categories;
    }

    /**
     * Get category description
     *
     * @param CategoryInterface $category Category entity
     * @return string
     */
    private function getCategoryDescription(CategoryInterface $category): string
    {
        try {
            $description = $category->getDescription();
            if (!$description) {
                return '';
            }

            return $this->outputHelper->categoryAttribute($category, $description, 'description');
        } catch (\Exception $e) {
            $this->logger->error('Error getting category description: ' . $e->getMessage(), [
                'category_id' => $category->getId(),
                'exception' => $e
            ]);
            return '';
        }
    }

    /**
     * Get category image URL
     *
     * @param CategoryInterface $category Category entity
     * @param string $image Image path
     * @return string
     */
    private function getCategoryImageUrl(CategoryInterface $category, string $image): string
    {
        if (empty($image)) {
            return $this->getCategoryDefaultImageUrl($category);
        }

        try {
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);

            // Define possible paths to check in order of preference
            $possiblePaths = [
                // Custom module paths
                'krish/category_slider/' . $image,
                'krish/category_slider/tmp/' . $image,
                // Standard Magento catalog paths
                'catalog/category/' . $image,
                'catalog/tmp/category/' . $image,
                // Root media directory
                $image
            ];

            // Check each path and return the first one that exists
            foreach ($possiblePaths as $path) {
                if ($mediaDirectory->isExist($path)) {
                    return $mediaUrl . $path;
                }
            }

            $this->logger->warning('Image not found in any location: ' . $image, [
                'category_id' => $category->getId(),
                'checked_paths' => $possiblePaths
            ]);

            // If no image is found, fall back to the category's own image or a placeholder
            return $this->getCategoryDefaultImageUrl($category);
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Store not found: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            return $this->getCategoryDefaultImageUrl($category);
        } catch (\Exception $e) {
            $this->logger->error('Error getting category image URL: ' . $e->getMessage(), [
                'category_id' => $category->getId(),
                'image' => $image,
                'exception' => $e
            ]);
            return $this->getCategoryDefaultImageUrl($category);
        }
    }

    /**
     * Get default image URL for a category
     *
     * @param CategoryInterface $category Category entity
     * @return string
     */
    private function getCategoryDefaultImageUrl(CategoryInterface $category): string
    {
        try {
            // Try to use the category's own image if available
            $categoryImage = $category->getImageUrl();
            if ($categoryImage) {
                return $categoryImage;
            }

            // Fall back to the placeholder image
            return $this->_assetRepo->getUrl('Magento_Catalog::images/category/placeholder/image.jpg');
        } catch (\Exception $e) {
            $this->logger->error('Error getting default category image: ' . $e->getMessage(), [
                'category_id' => $category->getId(),
                'exception' => $e
            ]);
            // Ultimate fallback
            return '';
        }
    }
}
