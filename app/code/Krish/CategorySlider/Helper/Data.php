<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023 Krish (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace <PERSON>h\CategorySlider\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

/**
 * Category Slider helper
 */
class Data extends AbstractHelper
{
    /**
     * Config paths
     */
    public const XML_PATH_ENABLED = 'krish_category_slider/general/enabled';
    public const XML_PATH_IMAGE_WIDTH = 'krish_category_slider/general/image_width';
    public const XML_PATH_IMAGE_HEIGHT = 'krish_category_slider/general/image_height';

    /**
     * Check if module is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled(?int $storeId = null): bool
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get image width
     *
     * @param int|null $storeId
     * @return int
     */
    public function getImageWidth(?int $storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_IMAGE_WIDTH,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get image height
     *
     * @param int|null $storeId
     * @return int
     */
    public function getImageHeight(?int $storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_IMAGE_HEIGHT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
