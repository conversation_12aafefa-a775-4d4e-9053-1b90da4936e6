<?php
/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

/** @var \Krish\CategorySlider\Block\Widget\CategorySlider $block */
/** @var \Magento\Framework\Escaper $escaper */

$title = $block->getData('title');
$pageLink = $block->getData('page_link');
$pageTitle = $block->getData('page_title');
$customCssClass = $block->getData('custom_css_class');
$widgetId = $block->getWidgetId();
$categories = $block->getCategories();
?>

<?php if (!empty($categories)): ?>
    <div class="block widget block-categories-list grid krish-category-slider <?= $escaper->escapeHtmlAttr($customCssClass) ?>">
        <div class="block-header">
            <?php if ($title): ?>
                <div class="block-title">
                    <strong><?= $escaper->escapeHtml($title) ?></strong>
                </div>
            <?php endif; ?>
            <?php if ($pageTitle && $pageLink): ?>
                <div class="block-link">
                    <a href="<?= $escaper->escapeUrl($pageLink) ?>" class="action view">
                        <span><?= $escaper->escapeHtml($pageTitle) ?></span>
                    </a>
                </div>
            <?php endif; ?>
        </div>
        <div class="block-content">
            <div id="<?= $escaper->escapeHtmlAttr($widgetId) ?>" class="category-items slick-carousel">
                <?php foreach ($categories as $categoryItem): ?>
                    <?php
                    $category = $categoryItem->getCategory();
                    $categoryUrl = $categoryItem->getUrl();
                    $categoryTitle = $categoryItem->getTitle();
                    $categorySubtitle = $categoryItem->getSubtitle();
                    $categoryImage = $categoryItem->getImage();
                    ?>
                    <div class="category-item">
                        <div class="category-item-info">
                            <a href="<?= $escaper->escapeUrl($categoryUrl) ?>" class="category-item-photo">
                                <img src="<?= $escaper->escapeUrl($categoryImage) ?>"
                                     alt="<?= $escaper->escapeHtmlAttr($categoryTitle) ?>"
                                     class="category-image" />
                            </a>
                            <div class="category-item-details">
                                <strong class="category-item-name">
                                    <a href="<?= $escaper->escapeUrl($categoryUrl) ?>" class="category-item-link">
                                        <?= $escaper->escapeHtml($categoryTitle) ?>
                                    </a>
                                </strong>
                                <?php if ($categorySubtitle): ?>
                                    <div class="category-item-subtitle">
                                        <?= $escaper->escapeHtml($categorySubtitle) ?>
                                    </div>
                                <?php endif; ?>
                                <div class="category-item-action">
                                    <a href="<?= $escaper->escapeUrl($categoryUrl) ?>" class="action primary">
                                        <span><?= $escaper->escapeHtml(__('Shop Now')) ?></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <script>
        require(['jquery', 'slick'], function ($) {
            $(document).ready(function () {
                $(".slick-carousel").slick(<?= /* @noEscape */ $block->getSliderConfigJson() ?>);
            });
        });
    </script>
<?php endif; ?>
