<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 Krish (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">category_slider_form.category_slider_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Category Slider Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="back" class="Krish\CategorySlider\Block\Adminhtml\Slider\Edit\BackButton"/>
            <button name="delete" class="Krish\CategorySlider\Block\Adminhtml\Slider\Edit\DeleteButton"/>
            <button name="save" class="Krish\CategorySlider\Block\Adminhtml\Slider\Edit\SaveButton"/>
            <button name="save_and_continue" class="Krish\CategorySlider\Block\Adminhtml\Slider\Edit\SaveAndContinueButton"/>
        </buttons>
        <namespace>category_slider_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>category_slider_form.category_slider_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="category_slider_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="krish_categoryslider/categoryslider/save"/>
        </settings>
        <dataProvider class="Krish\CategorySlider\Ui\DataProvider\CategorySlider\Form\DataProvider" name="category_slider_form_data_source">
            <settings>
                <requestFieldName>slider_id</requestFieldName>
                <primaryFieldName>slider_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="general">
        <settings>
            <label translate="true">General Information</label>
        </settings>
        <field name="slider_id" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">category_slider</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <visible>false</visible>
                <dataScope>slider_id</dataScope>
            </settings>
        </field>
        <field name="title" sortOrder="10" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">category_slider</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Title</label>
                <dataScope>title</dataScope>
            </settings>
        </field>
    </fieldset>
    <fieldset name="slider_items">
        <settings>
            <label translate="true">Slider Items</label>
            <collapsible>true</collapsible>
            <opened>true</opened>
        </settings>
        <container name="slider_items_container" sortOrder="20">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/dynamic-rows/dynamic-rows</item>
                    <item name="template" xsi:type="string">ui/dynamic-rows/templates/default</item>
                    <item name="componentType" xsi:type="string">dynamicRows</item>
                    <item name="addButtonLabel" xsi:type="string" translate="true">Add Item</item>
                    <item name="deleteButtonLabel" xsi:type="string" translate="true">Delete</item>
                    <item name="dndConfig" xsi:type="array">
                        <item name="enabled" xsi:type="boolean">true</item>
                    </item>
                    <item name="dataScope" xsi:type="string">slider_items</item>
                    <item name="columnsHeader" xsi:type="boolean">true</item>
                    <item name="columnsHeaderAfterRender" xsi:type="boolean">true</item>
                    <item name="renderDefaultRecord" xsi:type="boolean">false</item>
                    <item name="additionalClasses" xsi:type="string">admin__field-wide</item>
                    <item name="defaultRecord" xsi:type="boolean">false</item>
                    <item name="recordTemplate" xsi:type="string">record</item>
                    <item name="identificationProperty" xsi:type="string">item_id</item>
                    <item name="identificationDRProperty" xsi:type="string">item_id</item>
                    <item name="map" xsi:type="array">
                        <item name="item_id" xsi:type="string">item_id</item>
                        <item name="category_id" xsi:type="string">category_id</item>
                        <item name="title" xsi:type="string">title</item>
                        <item name="subtitle" xsi:type="string">subtitle</item>
                        <item name="image" xsi:type="string">image</item>
                        <item name="position" xsi:type="string">position</item>
                    </item>
                </item>
            </argument>
            <container name="record" component="Magento_Ui/js/dynamic-rows/record">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="isTemplate" xsi:type="boolean">true</item>
                        <item name="is_collection" xsi:type="boolean">true</item>
                        <item name="componentType" xsi:type="string">container</item>
                    </item>
                </argument>
                <field name="item_id" formElement="input">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="source" xsi:type="string">category_slider</item>
                            <item name="dataScope" xsi:type="string">item_id</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>text</dataType>
                        <visible>false</visible>
                    </settings>
                </field>
                <field name="category_id" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="source" xsi:type="string">category_slider</item>
                            <item name="dataScope" xsi:type="string">category_id</item>
                            <item name="fit" xsi:type="boolean">true</item>
                            <item name="elementTmpl" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            <item name="formElement" xsi:type="string">select</item>
                            <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                        </item>
                    </argument>
                    <settings>
                        <validation>
                            <rule name="required-entry" xsi:type="boolean">true</rule>
                        </validation>
                        <dataType>text</dataType>
                        <label translate="true">Category</label>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Magento\Catalog\Ui\Component\Product\Form\Categories\Options"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <field name="title" formElement="input" sortOrder="20">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="source" xsi:type="string">category_slider</item>
                            <item name="dataScope" xsi:type="string">title</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>text</dataType>
                        <label translate="true">Title</label>
                    </settings>
                </field>
                <field name="subtitle" formElement="input" sortOrder="30">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="source" xsi:type="string">category_slider</item>
                            <item name="dataScope" xsi:type="string">subtitle</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>text</dataType>
                        <label translate="true">Subtitle</label>
                    </settings>
                </field>
                <field name="image" formElement="fileUploader" sortOrder="40">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="source" xsi:type="string">category_slider</item>
                        </item>
                    </argument>
                    <settings>
                        <elementTmpl>ui/form/element/uploader/uploader</elementTmpl>
                        <dataType>string</dataType>
                        <label translate="true">Category Image</label>
                        <visible>true</visible>
                        <required>false</required>
                    </settings>
                    <formElements>
                        <fileUploader>
                            <settings>
                                <required>false</required>
                                <uploaderConfig>
                                    <param xsi:type="url" name="url" path="krish_categoryslider/categoryslider/upload"/>
                                </uploaderConfig>
                                <previewTmpl>Magento_Catalog/image-preview</previewTmpl>
                                <allowedExtensions>jpg jpeg gif png</allowedExtensions>
                                <maxFileSize>2097152</maxFileSize>
                            </settings>
                        </fileUploader>
                    </formElements>
                </field>
                <field name="position" formElement="input" sortOrder="50">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="source" xsi:type="string">category_slider</item>
                            <item name="dataScope" xsi:type="string">position</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>text</dataType>
                        <visible>false</visible>
                        <label translate="true">Position</label>
                    </settings>
                </field>
                <actionDelete>
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="componentType" xsi:type="string">actionDelete</item>
                        </item>
                    </argument>
                </actionDelete>
            </container>
        </container>
    </fieldset>
</form>
