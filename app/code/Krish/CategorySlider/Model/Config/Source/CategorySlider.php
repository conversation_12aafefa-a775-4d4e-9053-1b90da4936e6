<?php

/**
 * @category   Krish
 * @package    Krish_CustomWidget
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\CategorySlider\Model\Config\Source;

use Krish\CategorySlider\Model\ResourceModel\Slider\CollectionFactory;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Category Slider source model for widget configuration
 */
class CategorySlider implements OptionSourceInterface
{
    /**
     * @param CollectionFactory $collectionFactory Collection factory for sliders
     * @param LoggerInterface $logger Logger interface
     */
    public function __construct(
        private readonly CollectionFactory $collectionFactory,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Get options as key value pair
     *
     * @return array<int, array<string, mixed>>
     */
    public function toOptionArray(): array
    {
        try {
            $options = [];
            foreach ($this->toArray() as $value => $label) {
                $options[] = [
                    'value' => $value,
                    'label' => $label
                ];
            }

            return $options;
        } catch (\Exception $e) {
            $this->logger->error('Error generating category slider options: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            return [];
        }
    }

    /**
     * Get options in "key-value" format
     *
     * @return array<int|string, string>
     * @throws LocalizedException
     */
    protected function toArray(): array
    {
        try {
            $options = [];
            $sliders = $this->collectionFactory->create();

            foreach ($sliders as $slider) {
                $options[(int)$slider->getId()] = $slider->getTitle();
            }

            return $options;
        } catch (\Exception $e) {
            $this->logger->error('Error retrieving category sliders: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            throw new LocalizedException(__('Unable to load category sliders.'));
        }
    }
}
