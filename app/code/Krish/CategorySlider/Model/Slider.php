<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023 Krish (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */

declare(strict_types=1);

namespace <PERSON>h\CategorySlider\Model;

use <PERSON><PERSON>\CategorySlider\Api\Data\SliderInterface;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\Context;
use Magento\Framework\Model\ResourceModel\AbstractResource;
use Magento\Framework\Registry;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Log\LoggerInterface;

/**
 * Category Slider model
 */
class Slider extends AbstractModel implements SliderInterface
{
    /**
     * @param Context $context
     * @param Registry $registry
     * @param Json $jsonSerializer
     * @param LoggerInterface $logger
     * @param AbstractResource|null $resource
     * @param AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        private readonly Json $jsonSerializer,
        private readonly LoggerInterface $logger,
        AbstractResource $resource = null,
        AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(ResourceModel\Slider::class);
    }

    /**
     * Get slider ID
     *
     * @return int|null
     */
    public function getSliderId(): ?int
    {
        $sliderId = $this->getData(self::SLIDER_ID);
        return $sliderId !== null ? (int)$sliderId : null;
    }

    /**
     * Set slider ID
     *
     * @param int $sliderId
     * @return SliderInterface
     */
    public function setSliderId(int $sliderId): SliderInterface
    {
        return $this->setData(self::SLIDER_ID, $sliderId);
    }

    /**
     * Get title
     *
     * @return string
     */
    public function getTitle(): string
    {
        return (string)$this->getData(self::TITLE);
    }

    /**
     * Set title
     *
     * @param string $title
     * @return SliderInterface
     */
    public function setTitle(string $title): SliderInterface
    {
        return $this->setData(self::TITLE, $title);
    }

    /**
     * Get categories data
     *
     * @return array<int|string, mixed> Array of category data
     */
    public function getCategories(): array
    {
        try {
            $categories = $this->getData('categories');
            if ($categories === null) {
                return [];
            }

            if (is_string($categories)) {
                try {
                    return $this->jsonSerializer->unserialize($categories);
                } catch (\Exception $e) {
                    $this->logger->error('Error unserializing categories data: ' . $e->getMessage(), [
                        'slider_id' => $this->getId(),
                        'categories_data' => $categories,
                        'exception' => $e
                    ]);
                    return [];
                }
            }

            return is_array($categories) ? $categories : [];
        } catch (\Exception $e) {
            $this->logger->error('Error getting categories: ' . $e->getMessage(), [
                'slider_id' => $this->getId(),
                'exception' => $e
            ]);
            return [];
        }
    }

    /**
     * Set categories data
     *
     * @param array $categories Categories data to set
     * @return $this
     */
    public function setCategories(array $categories): self
    {
        try {
            $this->setData('categories', $this->jsonSerializer->serialize($categories));
        } catch (\Exception $e) {
            $this->logger->error('Error serializing categories data: ' . $e->getMessage(), [
                'slider_id' => $this->getId(),
                'categories' => $categories,
                'exception' => $e
            ]);
            $this->setData('categories', '[]');
        }

        return $this;
    }

    /**
     * Process before save
     *
     * @return $this
     * @throws LocalizedException
     */
    public function beforeSave(): self
    {
        try {
            // If categories is an array, serialize it before saving
            $categories = $this->getData('categories');
            if (is_array($categories)) {
                $this->setCategories($categories);
            }

            return parent::beforeSave();
        } catch (\Exception $e) {
            $this->logger->error('Error in beforeSave: ' . $e->getMessage(), [
                'slider_id' => $this->getId(),
                'exception' => $e
            ]);
            throw new LocalizedException(__('Error saving slider: %1', $e->getMessage()));
        }
    }

    /**
     * Get created at timestamp
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set created at timestamp
     *
     * @param string $createdAt
     * @return SliderInterface
     */
    public function setCreatedAt(string $createdAt): SliderInterface
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get updated at timestamp
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * Set updated at timestamp
     *
     * @param string $updatedAt
     * @return SliderInterface
     */
    public function setUpdatedAt(string $updatedAt): SliderInterface
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }
}
