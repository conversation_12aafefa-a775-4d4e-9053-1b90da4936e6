<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023 Kris<PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */

declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Model;

use <PERSON><PERSON>\CategorySlider\Api\Data\SliderInterface;
use <PERSON><PERSON>\CategorySlider\Api\Data\SliderSearchResultsInterfaceFactory;
use <PERSON><PERSON>\CategorySlider\Api\SliderRepositoryInterface;
use Kris<PERSON>\CategorySlider\Model\ResourceModel\Slider as SliderResource;
use <PERSON><PERSON>\CategorySlider\Model\ResourceModel\Slider\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Log\LoggerInterface;

/**
 * Category Slider repository
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class SliderRepository implements SliderRepositoryInterface
{
    /**
     * @param SliderResource $resource
     * @param SliderFactory $sliderFactory
     * @param CollectionFactory $collectionFactory
     * @param SliderSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     * @param Json $jsonSerializer
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly SliderResource $resource,
        private readonly SliderFactory $sliderFactory,
        private readonly CollectionFactory $collectionFactory,
        private readonly SliderSearchResultsInterfaceFactory $searchResultsFactory,
        private readonly CollectionProcessorInterface $collectionProcessor,
        private readonly Json $jsonSerializer,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Save slider
     *
     * @param SliderInterface $slider
     * @return SliderInterface
     * @throws CouldNotSaveException
     */
    public function save(SliderInterface $slider): SliderInterface
    {
        try {
            $this->resource->save($slider);
            $this->logger->info('Slider saved successfully', ['slider_id' => $slider->getId()]);
            return $slider;
        } catch (\Exception $exception) {
            $this->logger->error('Error saving slider: ' . $exception->getMessage(), [
                'slider_id' => $slider->getId(),
                'exception' => $exception
            ]);
            throw new CouldNotSaveException(__('Could not save slider: %1', $exception->getMessage()));
        }
    }

    /**
     * Get slider by ID
     *
     * @param int $sliderId
     * @return SliderInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $sliderId): SliderInterface
    {
        try {
            $slider = $this->sliderFactory->create();
            $this->resource->load($slider, $sliderId);

            if (!$slider->getId()) {
                $this->logger->warning('Slider not found', ['slider_id' => $sliderId]);
                throw new NoSuchEntityException(__('The category slider with the "%1" ID doesn\'t exist.', $sliderId));
            }

            return $slider;
        } catch (NoSuchEntityException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('Error loading slider: ' . $e->getMessage(), [
                'slider_id' => $sliderId,
                'exception' => $e
            ]);
            $message = __(
                'The category slider with the "%1" ID couldn\'t be loaded: %2',
                $sliderId,
                $e->getMessage()
            );
            throw new NoSuchEntityException($message);
        }
    }

    /**
     * Delete slider
     *
     * @param SliderInterface $slider
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(SliderInterface $slider): bool
    {
        try {
            $this->resource->delete($slider);
            $this->logger->info('Slider deleted successfully', ['slider_id' => $slider->getId()]);
            return true;
        } catch (\Exception $exception) {
            $this->logger->error('Error deleting slider: ' . $exception->getMessage(), [
                'slider_id' => $slider->getId(),
                'exception' => $exception
            ]);
            throw new CouldNotDeleteException(__('Could not delete slider: %1', $exception->getMessage()));
        }
    }

    /**
     * Delete slider by ID
     *
     * @param int $sliderId
     * @return bool
     * @throws CouldNotDeleteException
     * @throws NoSuchEntityException
     */
    public function deleteById(int $sliderId): bool
    {
        try {
            return $this->delete($this->getById($sliderId));
        } catch (NoSuchEntityException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('Error deleting slider by ID: ' . $e->getMessage(), [
                'slider_id' => $sliderId,
                'exception' => $e
            ]);
            $errorMessage = __('Could not delete slider with ID %1: %2', $sliderId, $e->getMessage());
            throw new CouldNotDeleteException($errorMessage);
        }
    }

    /**
     * Get list of sliders
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface
    {
        try {
            $collection = $this->collectionFactory->create();
            $this->collectionProcessor->process($searchCriteria, $collection);

            $searchResults = $this->searchResultsFactory->create();
            $searchResults->setSearchCriteria($searchCriteria);
            $searchResults->setItems($collection->getItems());
            $searchResults->setTotalCount($collection->getSize());

            return $searchResults;
        } catch (\Exception $e) {
            $this->logger->error('Error retrieving slider list: ' . $e->getMessage(), [
                'search_criteria' => $searchCriteria->__toArray(),
                'exception' => $e
            ]);
            throw new LocalizedException(__('Could not retrieve slider list: %1', $e->getMessage()));
        }
    }
}
