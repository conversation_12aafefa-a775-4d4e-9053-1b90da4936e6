<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 <PERSON><PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Model\ResourceModel\Slider;

use <PERSON><PERSON>\CategorySlider\Model\Slider;
use <PERSON><PERSON>\CategorySlider\Model\ResourceModel\Slider as SliderResource;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * Category Slider collection
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'slider_id';

    /**
     * @inheritdoc
     */
    protected function _construct()
    {
        $this->_init(Slider::class, SliderResource::class);
    }
}
