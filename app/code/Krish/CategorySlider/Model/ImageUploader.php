<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023 Krish (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */

declare(strict_types=1);

namespace Krish\CategorySlider\Model;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;
use Magento\Framework\Filesystem\DriverInterface;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\UrlInterface;
use Magento\MediaStorage\Helper\File\Storage\Database;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Category slider image uploader
 */
class ImageUploader
{
    /**
     * @var WriteInterface
     */
    private readonly WriteInterface $mediaDirectory;

    /**
     * @SuppressWarnings(PHPMD.LongVariable)
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     * @param Database $coreFileStorageDatabase Core file storage database
     * @param Filesystem $filesystem Filesystem
     * @param UploaderFactory $uploaderFactory Uploader factory
     * @param StoreManagerInterface $storeManager Store manager
     * @param LoggerInterface $logger Logger interface
     * @param DriverInterface $driver Filesystem driver interface
     * @param File $fileIo File I/O operations
     * @param string $baseTmpPath Base temporary path
     * @param string $basePath Base path
     * @param string[] $allowedExtensions Allowed file extensions
     */
    public function __construct(
        private readonly Database $coreFileStorageDatabase,
        Filesystem $filesystem,
        private readonly UploaderFactory $uploaderFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger,
        private readonly DriverInterface $driver,
        private readonly File $fileIo,
        private string $baseTmpPath = 'krish/category_slider/tmp',
        private string $basePath = 'krish/category_slider',
        private array $allowedExtensions = ['jpg', 'jpeg', 'gif', 'png']
    ) {
        $this->mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
    }

    /**
     * Set base temporary path
     *
     * @param string $baseTmpPath Base temporary path
     * @return void
     */
    public function setBaseTmpPath(string $baseTmpPath): void
    {
        $this->baseTmpPath = $baseTmpPath;
    }

    /**
     * Set base path
     *
     * @param string $basePath Base path
     * @return void
     */
    public function setBasePath(string $basePath): void
    {
        $this->basePath = $basePath;
    }

    /**
     * Set allowed extensions
     *
     * @param string[] $allowedExtensions Allowed file extensions
     * @return void
     */
    public function setAllowedExtensions(array $allowedExtensions): void
    {
        $this->allowedExtensions = $allowedExtensions;
    }

    /**
     * Get base temporary path
     *
     * @return string
     */
    public function getBaseTmpPath(): string
    {
        return $this->baseTmpPath;
    }

    /**
     * Get base path
     *
     * @return string
     */
    public function getBasePath(): string
    {
        return $this->basePath;
    }

    /**
     * Get allowed extensions
     *
     * @return string[]
     */
    public function getAllowedExtensions(): array
    {
        return $this->allowedExtensions;
    }

    /**
     * Get file path
     *
     * @param string $path Path
     * @param string $fileName File name
     * @return string
     */
    public function getFilePath(string $path, string $fileName): string
    {
        return rtrim($path, '/') . '/' . ltrim($fileName, '/');
    }

    /**
     * Save file to temporary directory
     *
     * @param string $fileId File ID
     * @return array<string, mixed>
     * @throws LocalizedException
     */
    public function saveFileToTmpDir(string $fileId): array
    {
        try {
            $this->logger->debug('Starting file upload', [
                'fileId' => $fileId,
                'baseTmpPath' => $this->getBaseTmpPath(),
                'allowedExtensions' => $this->getAllowedExtensions()
            ]);

            $uploader = $this->uploaderFactory->create(['fileId' => $fileId]);
            $uploader->setAllowedExtensions($this->getAllowedExtensions());
            $uploader->setAllowRenameFiles(true);
            $uploader->setFilesDispersion(false);

            // Create directory if it doesn't exist using Magento's file system abstraction
            $baseTmpPath = $this->getBaseTmpPath();
            if (!$this->mediaDirectory->isExist($baseTmpPath)) {
                $this->mediaDirectory->create($baseTmpPath);
                $this->logger->debug('Created tmp directory: ' . $baseTmpPath);
            }

            $absolutePath = $this->mediaDirectory->getAbsolutePath($baseTmpPath);
            $this->logger->debug('Saving file to: ' . $absolutePath);

            $result = $uploader->save($absolutePath);

            if (!$result) {
                throw new LocalizedException(__('File cannot be saved to the destination folder.'));
            }

            $result['tmp_name'] = str_replace('\\', '/', $result['tmp_name']);
            $result['path'] = str_replace('\\', '/', $result['path']);
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            $filePath = $this->getFilePath($this->getBaseTmpPath(), $result['file']);
            $result['url'] = $mediaUrl . $filePath;
            $result['name'] = $result['file'];

            if (isset($result['file'])) {
                try {
                    $relativePath = rtrim($this->getBaseTmpPath(), '/') . '/' . ltrim($result['file'], '/');
                    $this->coreFileStorageDatabase->saveFile($relativePath);
                } catch (\Exception $e) {
                    $this->logger->critical($e);
                    throw new LocalizedException(__('Something went wrong while saving the file.'));
                }
            }

            return $result;
        } catch (\Exception $e) {
            $this->logger->critical($e);
            throw new LocalizedException(
                __('Something went wrong while uploading the file: %1', $e->getMessage())
            );
        }
    }

    /**
     * Save file to temporary directory from file array (without using $_FILES superglobal)
     *
     * @param array $fileArray File array with name, tmp_name, size, type, error
     * @return array<string, mixed>
     * @throws LocalizedException
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.LongVariable)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function saveFileToTmpDirFromArray(array $fileArray): array
    {
        try {
            $this->logger->debug('Starting file upload from array', [
                'fileName' => $fileArray['name'] ?? 'unknown',
                'fileSize' => $fileArray['size'] ?? 0,
                'baseTmpPath' => $this->getBaseTmpPath(),
                'allowedExtensions' => $this->getAllowedExtensions()
            ]);

            // Validate file array structure
            if (empty($fileArray['name']) || empty($fileArray['tmp_name'])) {
                throw new LocalizedException(__('Invalid file data provided.'));
            }

            // Check for upload errors
            if (isset($fileArray['error']) && $fileArray['error'] !== UPLOAD_ERR_OK) {
                throw new LocalizedException(__('File upload error: %1', $fileArray['error']));
            }

            // Validate file extension
            $fileName = $fileArray['name'];
            $fileInfo = $this->fileIo->getPathInfo($fileName);
            $fileExtension = strtolower($fileInfo['extension'] ?? '');
            if (!in_array($fileExtension, $this->getAllowedExtensions(), true)) {
                throw new LocalizedException(
                    __('File type not allowed. Allowed types: %1', implode(', ', $this->getAllowedExtensions()))
                );
            }

            // Create directory if it doesn't exist
            $baseTmpPath = $this->getBaseTmpPath();
            if (!$this->mediaDirectory->isExist($baseTmpPath)) {
                $this->mediaDirectory->create($baseTmpPath);
                $this->logger->debug('Created tmp directory: ' . $baseTmpPath);
            }

            // Generate unique filename to avoid conflicts
            $uniqueFileName = $this->generateUniqueFileName($fileName);
            $destinationPath = $this->getFilePath($baseTmpPath, $uniqueFileName);

            // Read file content from temporary location using Magento's filesystem abstraction
            $tmpFileDriver = $this->driver;
            if (!$tmpFileDriver->isExists($fileArray['tmp_name'])) {
                throw new LocalizedException(__('Uploaded file does not exist in temporary location.'));
            }

            $fileContent = $tmpFileDriver->fileGetContents($fileArray['tmp_name']);
            if ($fileContent === false) {
                throw new LocalizedException(__('Failed to read uploaded file content.'));
            }

            // Write file to destination using Magento's filesystem abstraction
            $this->mediaDirectory->writeFile($destinationPath, $fileContent);

            // Get file size using Magento's filesystem abstraction
            $fileStat = $this->mediaDirectory->stat($destinationPath);
            $fileSize = $fileStat['size'] ?? ($fileArray['size'] ?? 0);

            // Prepare result array
            $absoluteDestinationPath = $this->mediaDirectory->getAbsolutePath($destinationPath);
            $result = [
                'name' => $uniqueFileName,
                'file' => $uniqueFileName,
                'size' => $fileSize,
                'type' => $fileArray['type'] ?? mime_content_type($absoluteDestinationPath),
                'tmp_name' => str_replace('\\', '/', $absoluteDestinationPath),
                'path' => str_replace('\\', '/', $absoluteDestinationPath)
            ];

            // Generate URL for the uploaded file
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            $result['url'] = $mediaUrl . $destinationPath;

            // Save file to database storage
            try {
                $this->coreFileStorageDatabase->saveFile($destinationPath);
            } catch (\Exception $e) {
                $this->logger->critical($e);
                throw new LocalizedException(__('Something went wrong while saving the file.'));
            }

            $this->logger->debug('File uploaded successfully from array', [
                'originalName' => $fileName,
                'savedName' => $uniqueFileName,
                'size' => $result['size']
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->logger->critical($e);
            throw new LocalizedException(
                __('Something went wrong while uploading the file: %1', $e->getMessage())
            );
        }
    }

    /**
     * Generate unique filename to avoid conflicts
     *
     * @param string $fileName Original filename
     * @return string Unique filename
     */
    private function generateUniqueFileName(string $fileName): string
    {
        $fileInfo = $this->fileIo->getPathInfo($fileName);
        $baseName = $fileInfo['filename'] ?? '';
        $extension = $fileInfo['extension'] ?? '';

        // Clean filename
        $baseName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $baseName);

        // Add timestamp for uniqueness
        $uniqueName = $baseName . '_' . time();

        return $extension ? $uniqueName . '.' . $extension : $uniqueName;
    }

    /**
     * Move file from temporary directory to permanent directory
     *
     * @param string $fileName File name
     * @return string
     * @throws LocalizedException
     */
    public function moveFileFromTmp(string $fileName): string
    {
        $baseTmpPath = $this->getBaseTmpPath();
        $basePath = $this->getBasePath();

        $baseFilePath = $this->getFilePath($basePath, $fileName);
        $baseTmpFilePath = $this->getFilePath($baseTmpPath, $fileName);

        try {
            // Check if the source file exists using Magento's file system abstraction
            if (!$this->mediaDirectory->isExist($baseTmpFilePath)) {
                $this->logger->warning('Source file does not exist: ' . $baseTmpFilePath);

                // Try to find the file in alternative locations
                $altLocations = [
                    'catalog/tmp/category/' . $fileName,
                    'catalog/category/' . $fileName,
                    $fileName
                ];

                $foundFile = false;
                foreach ($altLocations as $altPath) {
                    if ($this->mediaDirectory->isExist($altPath)) {
                        $baseTmpFilePath = $altPath;
                        $foundFile = true;
                        $this->logger->debug('Found file in alternative location: ' . $altPath);
                        break;
                    }
                }

                if (!$foundFile) {
                    throw new LocalizedException(
                        __('Source file "%1" does not exist.', $baseTmpFilePath)
                    );
                }
            }

            // Create destination directory if it doesn't exist
            $parentDir = $this->driver->getParentDirectory($baseFilePath);
            $this->mediaDirectory->create($parentDir);

            // Log the paths for debugging
            $this->logger->debug('Moving file from: ' . $baseTmpFilePath);
            $this->logger->debug('Moving file to: ' . $baseFilePath);

            // Copy file using database storage
            $this->coreFileStorageDatabase->copyFile(
                $baseTmpFilePath,
                $baseFilePath
            );

            // Rename file in filesystem
            $this->mediaDirectory->renameFile(
                $baseTmpFilePath,
                $baseFilePath
            );

            $this->logger->debug('File moved successfully: ' . $fileName);
        } catch (\Exception $e) {
            $this->logger->error('Error moving file: ' . $e->getMessage());
            $errorMessage = __('Something went wrong while saving the file: %1', $e->getMessage());
            throw new LocalizedException($errorMessage);
        }

        return $fileName;
    }
}
