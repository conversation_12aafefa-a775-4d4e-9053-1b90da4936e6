#!/bin/bash

# Krish_CategorySlider Module Installation Script
# This script automates the installation process for the Krish_CategorySlider module

# Display header
echo "============================================================"
echo "Krish_CategorySlider Module Installation"
echo "============================================================"

# Check if running from Magento root
if [ ! -f "./app/etc/config.php" ]; then
    echo "Error: This script must be run from the Magento root directory."
    echo "Please navigate to your Magento root directory and try again."
    exit 1
fi

# Check if module directory exists
if [ ! -d "./app/code/Krish/CategorySlider" ]; then
    echo "Error: Module directory not found at ./app/code/Krish/CategorySlider"
    echo "Please ensure the module files are in the correct location."
    exit 1
fi

# Make script executable
chmod +x ./app/code/Krish/CategorySlider/install.sh

echo "Step 1: Enabling the module..."
php bin/magento module:enable Krish_CategorySlider
if [ $? -ne 0 ]; then
    echo "Error: Failed to enable the module."
    exit 1
fi
echo "Module enabled successfully."

echo "Step 2: Running setup upgrade..."
echo "Note: Running with --keep-generated flag to skip OpenSearch validation"
php bin/magento setup:upgrade --keep-generated
if [ $? -ne 0 ]; then
    echo "Warning: Setup upgrade encountered issues, but we'll continue."
    echo "In a production environment, you should resolve these issues before proceeding."
fi
echo "Setup upgrade completed."

echo "Step 3: Compiling dependency injection..."
echo "Note: This may take a few minutes..."
php bin/magento setup:di:compile
if [ $? -ne 0 ]; then
    echo "Warning: DI compilation encountered issues, but we'll continue."
    echo "In a production environment, you should resolve these issues before proceeding."
fi
echo "Dependency injection compilation completed."

echo "Step 4: Deploying static content..."
echo "Note: This may take a few minutes..."
php bin/magento setup:static-content:deploy -f
if [ $? -ne 0 ]; then
    echo "Warning: Static content deployment encountered issues, but we'll continue."
    echo "In a production environment, you should resolve these issues before proceeding."
fi
echo "Static content deployment completed."

echo "Step 5: Cleaning cache..."
php bin/magento cache:clean
if [ $? -ne 0 ]; then
    echo "Error: Failed to clean cache."
    exit 1
fi
echo "Cache cleaned successfully."

echo "Step 6: Flushing cache..."
php bin/magento cache:flush
if [ $? -ne 0 ]; then
    echo "Error: Failed to flush cache."
    exit 1
fi
echo "Cache flushed successfully."

echo "============================================================"
echo "Krish_CategorySlider module has been installed successfully!"
echo "============================================================"
echo "You can now access the module in the admin panel:"
echo "1. Navigate to Content > Category Slider Management"
echo "2. Configure the module at Stores > Configuration > Krish Extensions > Category Slider"
echo "============================================================"
