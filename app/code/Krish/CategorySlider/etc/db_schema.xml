<?xml version="1.0"?>
<!--
/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 Krish (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="krish_category_slider" resource="default" engine="innodb" comment="Category Slider Main Table">
        <column xsi:type="int" name="slider_id" unsigned="true" nullable="false" identity="true" comment="Slider ID"/>
        <column xsi:type="varchar" name="title" nullable="false" length="255" comment="Slider Title"/>
        <column xsi:type="text" name="categories" nullable="true" comment="Categories JSON Data"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP" comment="Creation Time"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP" comment="Update Time"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="slider_id"/>
        </constraint>
    </table>
</schema>
