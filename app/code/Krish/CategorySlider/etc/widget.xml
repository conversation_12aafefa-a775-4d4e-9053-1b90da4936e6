<?xml version="1.0" encoding="UTF-8"?>
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget id="krish_category_slider" class="Krish\CategorySlider\Block\Widget\CategorySlider">
        <label translate="true">Krish Category Slider</label>
        <description translate="true">Displays categories in a carousel slider</description>
        <parameters>
            <parameter name="title" xsi:type="text" visible="true" sort_order="10" required="false">
                <label translate="true">Title</label>
            </parameter>
            <parameter name="category_slider" xsi:type="select" visible="true" required="true" source_model="Krish\CategorySlider\Model\Config\Source\CategorySlider" sort_order="20">
                <label translate="true">Category Slider</label>
            </parameter>
            <parameter name="autoplay" xsi:type="select" visible="true" sort_order="30" required="true">
                <label translate="true">Autoplay</label>
                <options>
                    <option name="yes" value="1">
                        <label translate="true">Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label translate="true">No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="autoplay_timeout" xsi:type="text" visible="true" sort_order="40" required="false">
                <label translate="true">Autoplay Timeout (ms)</label>
                <value>5000</value>
            </parameter>
            <parameter name="show_nav" xsi:type="select" visible="true" sort_order="50" required="true">
                <label translate="true">Show Navigation Arrows</label>
                <options>
                    <option name="yes" value="1">
                        <label translate="true">Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label translate="true">No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="show_dots" xsi:type="select" visible="true" sort_order="60" required="true">
                <label translate="true">Show Dots</label>
                <options>
                    <option name="yes" value="1">
                        <label translate="true">Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label translate="true">No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="custom_css_class" xsi:type="text" visible="true" sort_order="80" required="false">
                <label translate="true">Custom CSS Class</label>
            </parameter>
            <parameter name="page_link" xsi:type="text" visible="true" sort_order="90" required="false">
                <label translate="true">Page Link URL</label>
                <description translate="true">URL to link to a specific page</description>
                <value>#</value>
            </parameter>
            <parameter name="page_title" xsi:type="text" visible="true" sort_order="100" required="false">
                <label translate="true">Page Link Text</label>
                <description translate="true">Text to display for the page link</description>
                <value>View All</value>
            </parameter>
            <parameter name="template" xsi:type="select" visible="true" required="true" sort_order="110">
                <label translate="true">Template</label>
                <options>
                    <option name="default" value="Krish_CategorySlider::widget/category_slider.phtml">
                        <label translate="true">Default Template</label>
                    </option>
                </options>
            </parameter>
        </parameters>
    </widget>
</widgets>
