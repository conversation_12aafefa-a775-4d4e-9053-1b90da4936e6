<?xml version="1.0"?>
<!--
/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 Krish (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="krish" translate="label" sortOrder="200">
            <label>Krish Extensions</label>
        </tab>
        <section id="krish_category_slider" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Category Slider</label>
            <tab>krish</tab>
            <resource>Krish_CategorySlider::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Configuration</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Module</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or disable the Category Slider module</comment>
                </field>
                <field id="image_width" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Image Width</label>
                    <validate>validate-number</validate>
                    <comment>Width of slider images in pixels</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="image_height" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Image Height</label>
                    <validate>validate-number</validate>
                    <comment>Height of slider images in pixels</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
