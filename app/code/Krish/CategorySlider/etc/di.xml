<?xml version="1.0"?>
<!--
/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 <PERSON>h (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Service Contract Preferences -->
    <preference for="Krish\CategorySlider\Api\Data\SliderInterface" type="Kris<PERSON>\CategorySlider\Model\Slider" />
    <preference for="Krish\CategorySlider\Api\SliderRepositoryInterface" type="Krish\CategorySlider\Model\SliderRepository" />
    <preference for="Krish\CategorySlider\Api\Data\SliderSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />

    <!-- Collection Processors for Search Criteria -->
    <virtualType name="Kris<PERSON>\CategorySlider\Model\Api\SearchCriteria\CollectionProcessor\SliderFilterProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor">
        <arguments>
            <argument name="customFilters" xsi:type="array">
                <item name="store_id" xsi:type="object">Magento\Cms\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor\PageStoreFilter</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="Krish\CategorySlider\Model\Api\SearchCriteria\SliderCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="filters" xsi:type="object">Krish\CategorySlider\Model\Api\SearchCriteria\CollectionProcessor\SliderFilterProcessor</item>
                <item name="sorting" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\SortingProcessor</item>
                <item name="pagination" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\PaginationProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Krish\CategorySlider\Model\SliderRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Krish\CategorySlider\Model\Api\SearchCriteria\SliderCollectionProcessor</argument>
        </arguments>
    </type>
</config>
