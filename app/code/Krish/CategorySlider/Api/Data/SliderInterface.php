<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 Kris<PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */

declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Api\Data;

/**
 * Category Slider interface
 */
interface SliderInterface
{
    /**
     * Constants for keys of data array
     */
    public const SLIDER_ID = 'slider_id';
    public const TITLE = 'title';
    public const CREATED_AT = 'created_at';
    public const UPDATED_AT = 'updated_at';

    /**
     * Get slider ID
     *
     * @return int|null
     */
    public function getSliderId(): ?int;

    /**
     * Set slider ID
     *
     * @param int $sliderId
     * @return $this
     */
    public function setSliderId(int $sliderId): self;

    /**
     * Get title
     *
     * @return string
     */
    public function getTitle(): string;

    /**
     * Set title
     *
     * @param string $title
     * @return $this
     */
    public function setTitle(string $title): self;

    /**
     * Get creation time
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string;

    /**
     * Set creation time
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): self;

    /**
     * Get update time
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string;

    /**
     * Set update time
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): self;

    /**
     * Get categories data
     *
     * @return array<int|string, mixed> Array of category data
     */
    public function getCategories(): array;

    /**
     * Set categories data
     *
     * @param array $categories Categories data to set
     * @return $this
     */
    public function setCategories(array $categories): self;
}
