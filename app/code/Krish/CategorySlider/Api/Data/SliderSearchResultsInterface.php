<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 <PERSON><PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace Krish\CategorySlider\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

/**
 * Interface for category slider search results
 */
interface SliderSearchResultsInterface extends SearchResultsInterface
{
    /**
     * Get sliders list
     *
     * @return \Krish\CategorySlider\Api\Data\SliderInterface[]
     */
    public function getItems();

    /**
     * Set sliders list
     *
     * @param \Krish\CategorySlider\Api\Data\SliderInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
