<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 Kris<PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Api;

use <PERSON><PERSON>\CategorySlider\Api\Data\SliderInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Category Slider repository interface
 */
interface SliderRepositoryInterface
{
    /**
     * Save slider
     *
     * @param SliderInterface $slider
     * @return SliderInterface
     * @throws LocalizedException
     */
    public function save(SliderInterface $slider): SliderInterface;

    /**
     * Get slider by ID
     *
     * @param int $sliderId
     * @return SliderInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $sliderId): SliderInterface;

    /**
     * Delete slider
     *
     * @param SliderInterface $slider
     * @return bool
     * @throws LocalizedException
     */
    public function delete(SliderInterface $slider): bool;

    /**
     * Delete slider by ID
     *
     * @param int $sliderId
     * @return bool
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById(int $sliderId): bool;

    /**
     * Get list of sliders
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface;
}
