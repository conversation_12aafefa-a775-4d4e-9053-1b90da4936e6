<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 Kris<PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Controller\Adminhtml\CategorySlider;

use <PERSON><PERSON>\CategorySlider\Api\SliderRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Exception\LocalizedException;

/**
 * Delete controller for category slider
 */
class Delete extends Action implements HttpPostActionInterface
{
    /**
     * Authorization level
     */
    public const ADMIN_RESOURCE = 'Krish_CategorySlider::delete';

    /**
     * @var SliderRepositoryInterface
     */
    private SliderRepositoryInterface $sliderRepository;

    /**
     * @param Context $context
     * @param SliderRepositoryInterface $sliderRepository
     */
    public function __construct(
        Context $context,
        SliderRepositoryInterface $sliderRepository
    ) {
        parent::__construct($context);
        $this->sliderRepository = $sliderRepository;
    }

    /**
     * Delete action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $sliderId = $this->getRequest()->getParam('slider_id');

        if ($sliderId) {
            try {
                $this->sliderRepository->deleteById((int)$sliderId);
                $this->messageManager->addSuccessMessage(__('You deleted the slider.'));
                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('Something went wrong while deleting the slider.'));
            }

            return $resultRedirect->setPath('*/*/edit', ['slider_id' => $sliderId]);
        }

        $this->messageManager->addErrorMessage(__('We can\'t find a slider to delete.'));
        return $resultRedirect->setPath('*/*/');
    }
}
