<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR> <PERSON>
 * @copyright   Copyright (c) 2023 Kris<PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Controller\Adminhtml\CategorySlider;

use <PERSON><PERSON>\CategorySlider\Api\SliderRepositoryInterface;
use <PERSON><PERSON>\CategorySlider\Model\SliderFactory;
use Psr\Log\LoggerInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Registry;
use Magento\Framework\View\Result\PageFactory;

/**
 * Edit controller for category slider
 */
class Edit extends Action implements HttpGetActionInterface
{
    /**
     * Authorization level
     */
    public const ADMIN_RESOURCE = 'Krish_CategorySlider::save';

    /**
     * @var PageFactory
     */
    private PageFactory $resultPageFactory;

    /**
     * @var SliderRepositoryInterface
     */
    private SliderRepositoryInterface $sliderRepository;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var SliderFactory
     */
    private SliderFactory $sliderFactory;

    /**
     * @var Registry
     */
    private Registry $coreRegistry;

    /**
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param SliderRepositoryInterface $sliderRepository
     * @param SliderFactory $sliderFactory
     * @param Registry $coreRegistry
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        SliderRepositoryInterface $sliderRepository,
        SliderFactory $sliderFactory,
        Registry $coreRegistry,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->sliderRepository = $sliderRepository;
        $this->sliderFactory = $sliderFactory;
        $this->coreRegistry = $coreRegistry;
        $this->logger = $logger;
    }

    /**
     * Edit action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $sliderId = $this->getRequest()->getParam('slider_id');
        $model = $this->sliderFactory->create();

        if ($sliderId) {
            try {
                $model = $this->sliderRepository->getById((int)$sliderId);
            } catch (NoSuchEntityException $exception) {
                $this->messageManager->addErrorMessage(__('This slider no longer exists.'));
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
        }

        $this->coreRegistry->register('category_slider', $model);

        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Krish_CategorySlider::category_slider');
        $resultPage->addBreadcrumb(__('Category Slider'), __('Category Slider'));
        $resultPage->addBreadcrumb(
            $sliderId ? __('Edit Slider') : __('New Slider'),
            $sliderId ? __('Edit Slider') : __('New Slider')
        );
        $resultPage->getConfig()->getTitle()->prepend(
            $model->getId() ? $model->getTitle() : __('New Category Slider')
        );

        return $resultPage;
    }
}
