<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023 Krish (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */
declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Controller\Adminhtml\CategorySlider;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\RequestInterface;
use <PERSON><PERSON>\CategorySlider\Model\ImageUploader;
use Psr\Log\LoggerInterface;

/**
 * Upload controller for category slider images
 */
class Upload extends Action implements HttpPostActionInterface
{
    /**
     * Authorization level
     */
    public const ADMIN_RESOURCE = 'Krish_CategorySlider::save';

    /**
     * @param Context $context
     * @param ImageUploader $imageUploader
     * @param RequestInterface $request
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        private readonly ImageUploader $imageUploader,
        private readonly RequestInterface $request,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Upload file controller action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $result = [];

        try {
            $uploadedFile = $this->findUploadedFile();

            if (!$uploadedFile) {
                throw new LocalizedException(__('No file was uploaded.'));
            }

            $result = $this->processFileUpload($uploadedFile);
            $result['cookie'] = $this->getSessionCookieData();

            $this->logger->debug('File uploaded successfully', [
                'file_name' => $result['name'] ?? 'unknown',
                'file_size' => $result['size'] ?? 0
            ]);
        } catch (LocalizedException $e) {
            $result = [
                'error' => $e->getMessage(),
                'errorcode' => $e->getCode()
            ];
            $this->logger->error('Error uploading file: ' . $e->getMessage());
        } catch (\Exception $e) {
            $result = [
                'error' => __('An error occurred while uploading the file.'),
                'errorcode' => $e->getCode()
            ];
            $this->logger->critical('Unexpected error during file upload: ' . $e->getMessage(), [
                'exception' => $e
            ]);
        }

        return $this->resultFactory->create(ResultFactory::TYPE_JSON)->setData($result);
    }

    /**
     * Find uploaded file from request
     *
     * @return array|null
     */
    private function findUploadedFile(): ?array
    {
        $allFiles = $this->request->getFiles()->toArray();
        $allParams = $this->request->getParams();

        $this->logger->debug('Upload request received', [
            'files_count' => count($allFiles),
            'param_name' => $allParams['param_name'] ?? 'not_set'
        ]);

        // Try to find file using param_name first
        $paramName = $this->request->getParam('param_name');
        if ($paramName) {
            $uploadedFile = $this->findFileByParamName($paramName, $allFiles);
            if ($uploadedFile) {
                return $uploadedFile;
            }
        }

        // Fallback to searching all available files
        return $this->findFileInAllFiles($allFiles);
    }

    /**
     * Find file using param_name parameter
     *
     * @param string $paramName
     * @param array $allFiles
     * @return array|null
     */
    private function findFileByParamName(string $paramName, array $allFiles): ?array
    {
        $this->logger->debug('Parsing param_name: ' . $paramName);

        // Parse the nested path like "slider_items[slider_items_container][2][image]"
        if (!preg_match('/^([^[]+)(.*)$/', $paramName, $matches)) {
            return null;
        }

        $rootKey = $matches[1]; // "slider_items"
        $nestedPath = $matches[2]; // "[slider_items_container][2][image]"

        // Start with the root array
        $currentArray = $allFiles[$rootKey] ?? null;
        if (!$currentArray) {
            return null;
        }

        // Parse each nested level
        preg_match_all('/\[([^\]]+)\]/', $nestedPath, $pathMatches);
        $pathKeys = $pathMatches[1];

        // Navigate through the nested structure
        foreach ($pathKeys as $key) {
            if (isset($currentArray[$key])) {
                $currentArray = $currentArray[$key];
            } else {
                return null;
            }
        }

        // Check if we found a valid file
        if ($this->isValidFileArray($currentArray)) {
            $this->logger->debug('Found nested file', [
                'file_name' => $currentArray['name'],
                'file_size' => $currentArray['size'] ?? 0
            ]);
            return $currentArray;
        }

        return null;
    }

    /**
     * Find file in all available files
     *
     * @param array $allFiles
     * @return array|null
     */
    private function findFileInAllFiles(array $allFiles): ?array
    {
        foreach ($allFiles as $key => $file) {
            $this->logger->debug('Checking file parameter: ' . $key, [
                'file_data' => $file,
                'is_array' => is_array($file),
                'has_name' => isset($file['name']),
                'has_tmp_name' => isset($file['tmp_name'])
            ]);

            // Check if it's a direct file array
            if ($this->isValidFileArray($file)) {
                $this->logger->debug('Found file with parameter name: ' . $key, [
                    'file_name' => $file['name'],
                    'file_size' => $file['size'] ?? 0
                ]);
                return $file;
            }

            // Check if it's a nested array (common in dynamic rows)
            if (is_array($file)) {
                $nestedFile = $this->findNestedFile($file, $key);
                if ($nestedFile) {
                    return $nestedFile;
                }
            }
        }

        $this->logger->error('No file was uploaded', [
            'available_files' => array_keys($allFiles),
            'request_params' => array_keys($this->request->getParams())
        ]);

        return null;
    }

    /**
     * Find file in nested array structure
     *
     * @param array $fileArray
     * @param string $parentKey
     * @return array|null
     */
    private function findNestedFile(array $fileArray, string $parentKey): ?array
    {
        foreach ($fileArray as $subKey => $subFile) {
            if ($this->isValidFileArray($subFile)) {
                $this->logger->debug(
                    'Found nested file with parameter name: ' . $parentKey . '[' . $subKey . ']',
                    [
                        'file_name' => $subFile['name'],
                        'file_size' => $subFile['size'] ?? 0
                    ]
                );
                return $subFile;
            }
        }
        return null;
    }

    /**
     * Check if array is a valid file array
     *
     * @param mixed $file
     * @return bool
     */
    private function isValidFileArray($file): bool
    {
        return is_array($file) && !empty($file['name']) && !empty($file['tmp_name']);
    }

    /**
     * Process file upload using ImageUploader
     *
     * @param array $uploadedFile
     * @return array
     * @throws LocalizedException
     */
    private function processFileUpload(array $uploadedFile): array
    {
        return $this->imageUploader->saveFileToTmpDirFromArray($uploadedFile);
    }

    /**
     * Get session cookie data
     *
     * @return array
     */
    private function getSessionCookieData(): array
    {
        return [
            'name' => $this->_getSession()->getName(),
            'value' => $this->_getSession()->getSessionId(),
            'lifetime' => $this->_getSession()->getCookieLifetime(),
            'path' => $this->_getSession()->getCookiePath(),
            'domain' => $this->_getSession()->getCookieDomain(),
        ];
    }
}
