<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR>
 * @copyright   Copyright (c) 2023 <PERSON><PERSON> (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */

declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Controller\Adminhtml\CategorySlider;

use <PERSON><PERSON>\CategorySlider\Api\SliderRepositoryInterface;
use <PERSON><PERSON>\CategorySlider\Model\ImageUploader;
use <PERSON><PERSON>\CategorySlider\Model\SliderFactory;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Log\LoggerInterface;

/**
 * Save controller for category slider
 */
class Save extends Action implements HttpPostActionInterface
{
    /**
     * Authorization level
     */
    public const ADMIN_RESOURCE = 'Krish_CategorySlider::save';

    /**
     * @SuppressWarnings(PHPMD.ShortVariable)
     * @param Context $context
     * @param DataPersistorInterface $dataPersistor
     * @param SliderFactory $sliderFactory
     * @param SliderRepositoryInterface $sliderRepository
     * @param LoggerInterface $logger
     * @param ImageUploader $imageUploader
     * @param Json $jsonSerializer
     */
    public function __construct(
        Context $context,
        private readonly DataPersistorInterface $dataPersistor,
        private readonly SliderFactory $sliderFactory,
        private readonly SliderRepositoryInterface $sliderRepository,
        private readonly LoggerInterface $logger,
        private readonly ImageUploader $imageUploader,
        private readonly Json $jsonSerializer
    ) {
        parent::__construct($context);
    }

    /**
     * Save action
     *
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     *
     * @return ResultInterface
     */
    public function execute(): ResultInterface
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->getRequest()->getPostValue();

        if (!$data) {
            $this->logger->warning('No data provided for slider save action');
            return $resultRedirect->setPath('*/*/');
        }

        try {
            if (isset($data['slider_id']) && empty($data['slider_id'])) {
                $data['slider_id'] = null;
            }

            $model = $this->sliderFactory->create();

            $sliderId = $this->getRequest()->getParam('slider_id');
            if ($sliderId) {
                try {
                    $model = $this->sliderRepository->getById((int)$sliderId);
                    $this->logger->debug('Loaded existing slider', ['slider_id' => $sliderId]);
                } catch (LocalizedException $e) {
                    $this->logger->error('Failed to load slider', [
                        'slider_id' => $sliderId,
                        'error' => $e->getMessage()
                    ]);
                    $this->messageManager->addErrorMessage(__('This slider no longer exists.'));
                    return $resultRedirect->setPath('*/*/');
                }
            }

            $model->setData($data);

            try {
                $this->sliderRepository->save($model);
                $this->logger->debug('Saved slider base data', ['slider_id' => $model->getId()]);

                // Process slider items if present
                $this->processSliderItems($data, $model);

                $this->messageManager->addSuccessMessage(__('You saved the slider.'));
                $this->dataPersistor->clear('category_slider');

                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['slider_id' => $model->getId()]);
                }
                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
                $this->dataPersistor->set('category_slider', $data);
                $this->logger->error('LocalizedException during slider save', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            } catch (\Exception $e) {
                $errorMessage = __('Something went wrong while saving the slider.');
                $this->messageManager->addExceptionMessage($e, $errorMessage);
                $this->dataPersistor->set('category_slider', $data);
                $this->logger->error('Exception during slider save', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Only redirect to edit page if there was an error
            $sliderId = $this->getRequest()->getParam('slider_id');
            return $resultRedirect->setPath('*/*/edit', ['slider_id' => $sliderId]);
        } catch (\Exception $e) {
            $this->logger->critical('Unexpected error in slider save controller', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->messageManager->addErrorMessage(__('An unexpected error occurred. Please try again later.'));
            return $resultRedirect->setPath('*/*/');
        }
    }

    /**
     * Process slider items data
     *
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     *
     * @param array $data
     * @param \Krish\CategorySlider\Model\Slider $model
     * @return void
     */
    private function processSliderItems(array $data, \Krish\CategorySlider\Model\Slider $model): void
    {
        if (!isset($data['slider_items']) || !is_array($data['slider_items'])) {
            $this->logger->debug('No slider items data to process');
            return;
        }

        try {
            $this->logger->debug('Processing slider items', [
                'data' => $this->jsonSerializer->serialize($data['slider_items'])
            ]);

            // Process the data structure based on what's coming from the form
            $categories = [];

            // Check if we have a nested structure
            $hasItems = isset($data['slider_items']['slider_items_container']);
            $isArray = $hasItems && is_array($data['slider_items']['slider_items_container']);
            if ($isArray) {
                foreach ($data['slider_items']['slider_items_container'] as $key => $value) {
                    if (is_numeric($key)) {
                        $categories[] = $value;
                    }
                }
            }

            // Process images in categories
            foreach ($categories as $index => $category) {
                $categories[$index] = $this->processItemImage($category, $index);

                // Ensure each item has an item_id
                if (empty($category['item_id'])) {
                    $categories[$index]['item_id'] = uniqid('item_');
                }
            }

            // Set the categories data on the model
            $model->setCategories($categories);
            $this->sliderRepository->save($model);
            $this->logger->debug('Saved slider with categories', [
                'slider_id' => $model->getId(),
                'categories_count' => count($categories)
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error processing slider items', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new LocalizedException(__('Error processing slider items: %1', $e->getMessage()));
        }
    }

    /**
     * Process item image
     *
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     *
     * @param array $category
     * @param int $index
     * @return array
     */
    private function processItemImage(array $category, int $index): array
    {
        if (empty($category['image']) || !is_array($category['image'])) {
            return $category;
        }

        try {
            if (isset($category['image'][0]['name'])) {
                $imageName = $category['image'][0]['name'];

                // Check if this is a new image that needs to be moved from tmp
                if (isset($category['image'][0]['tmp_name'])) {
                    try {
                        $imageName = $this->imageUploader->moveFileFromTmp($imageName);
                        $this->logger->debug('Moved image from tmp', ['image' => $imageName]);
                    } catch (\Exception $e) {
                        $this->logger->error('Failed to move image', [
                            'error' => $e->getMessage(),
                            'image' => $imageName
                        ]);
                    }
                }

                $category['image'] = $imageName;
            } elseif (isset($category['image']['name'])) {
                $imageName = $category['image']['name'];

                // Check if this is a new image that needs to be moved from tmp
                if (isset($category['image']['tmp_name'])) {
                    try {
                        $imageName = $this->imageUploader->moveFileFromTmp($imageName);
                        $this->logger->debug('Moved image from tmp', ['image' => $imageName]);
                    } catch (\Exception $e) {
                        $this->logger->error('Failed to move image', [
                            'error' => $e->getMessage(),
                            'image' => $imageName
                        ]);
                    }
                }

                $category['image'] = $imageName;
            }
        } catch (\Exception $e) {
            $this->logger->error('Error processing item image', [
                'error' => $e->getMessage(),
                'category' => $category
            ]);
        }

        return $category;
    }
}
