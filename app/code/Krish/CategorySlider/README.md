# Krish_CategorySlider

## Overview

The Category Slider module provides an admin interface to manage category sliders. Each slider can contain multiple items, each with its own category selection, title, subtitle, and image. This module is designed for Magento 2.4.6 and is compatible with PHP 8.1.

## Features

- Admin grid to manage category sliders
- Form to create and edit category sliders
- Dynamic rows for managing slider items
- Category selection dropdown
- Image upload functionality
- Drag and drop reordering of slider items
- Mass delete functionality
- System configuration options

## Requirements

- Magento 2.4.6 or higher
- PHP 8.1 or higher
- MySQL 8.0 or higher

## Installation

### Manual Installation

1. Create the following directory: `app/code/Krish/CategorySlider`
2. Copy all module files into the directory
3. Run the installation script:

```bash
cd [magento_root]
bash app/code/Krish/CategorySlider/install.sh
```

Alternatively, you can run the following commands manually:

```bash
cd [magento_root]
bin/magento module:enable Krish_CategorySlider
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:static-content:deploy -f
bin/magento cache:clean
bin/magento cache:flush
```

### Composer Installation

```bash
composer require krish/module-category-slider
bin/magento module:enable Krish_CategorySlider
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:static-content:deploy -f
bin/magento cache:clean
bin/magento cache:flush
```

## Configuration

1. Navigate to **Stores > Configuration > Krish Extensions > Category Slider**
2. Configure the following options:
   - **Enable Module**: Enable or disable the module
   - **Image Width**: Set the width of slider images in pixels
   - **Image Height**: Set the height of slider images in pixels

## Usage

### Admin

1. Navigate to **Krish Technolabs > Category Slider Management > Category Slider**
2. Click **Add New Category Slider** to create a new slider
3. Fill in the required fields:
   - **Title** (required): Enter a title for the slider
   - **Add slider items** by clicking **Add Item**
   - For each item, select a **Category** (required), and optionally add a **Title**, **Subtitle**, and **Image**
4. Save the slider

### Managing Slider Items

Each slider can have multiple items, each associated with a category. For each item, you can:

1. Select a category from the dropdown (required)
2. Add a custom title (optional)
3. Add a subtitle (optional)
4. Upload an image (optional)
5. Reorder items using drag and drop
6. Delete items using the delete button

### Mass Actions

You can perform mass actions on multiple sliders:

1. Select the sliders you want to modify using the checkboxes
2. Choose an action from the **Actions** dropdown
3. Click **Submit** to apply the action

## Database Schema

The module creates a single database table:

### krish_category_slider

| Column     | Type      | Description       |
|------------|-----------|-------------------|
| slider_id  | int       | Primary Key       |
| title      | varchar   | Slider Title      |
| categories | text      | Categories JSON Data |
| created_at | timestamp | Creation Time     |
| updated_at | timestamp | Last Update Time  |

The `categories` column stores slider items as serialized JSON data, which includes category IDs, titles, subtitles, and image paths. This approach eliminates the need for a separate slider items table and simplifies the database schema.

## Module Structure

```
Krish_CategorySlider/
├── Api/
│   ├── Data/
│   │   ├── SliderInterface.php
│   │   └── SliderSearchResultsInterface.php
│   └── SliderRepositoryInterface.php
├── Block/
│   └── Adminhtml/
│       └── Slider/
│           └── Edit/
│               ├── BackButton.php
│               ├── DeleteButton.php
│               ├── GenericButton.php
│               ├── SaveButton.php
│               └── SaveAndContinueButton.php
├── Controller/
│   └── Adminhtml/
│       └── CategorySlider/
│           ├── Delete.php
│           ├── Edit.php
│           ├── Index.php
│           ├── MassDelete.php
│           ├── NewAction.php
│           ├── Save.php
│           └── Upload.php
├── docs/
│   ├── TECHNICAL.md
│   └── USER_GUIDE.md
├── etc/
│   ├── adminhtml/
│   │   ├── di.xml
│   │   ├── menu.xml
│   │   ├── routes.xml
│   │   └── system.xml
│   ├── acl.xml
│   ├── config.xml
│   ├── db_schema.xml
│   ├── di.xml
│   └── module.xml
├── Helper/
│   └── Data.php
├── i18n/
│   └── en_US.csv
├── Model/
│   ├── ResourceModel/
│   │   ├── Slider/
│   │   │   └── Collection.php
│   │   └── Slider.php
│   ├── ImageUploader.php
│   ├── Slider.php
│   └── SliderRepository.php
├── Ui/
│   ├── Component/
│   │   └── Listing/
│   │       └── Column/
│   │           └── SliderActions.php
│   └── DataProvider/
│       └── CategorySlider/
│           ├── Form/
│           │   └── DataProvider.php
│           └── ListingDataProvider.php
├── view/
│   └── adminhtml/
│       ├── layout/
│       │   ├── krish_categoryslider_categoryslider_edit.xml
│       │   └── krish_categoryslider_categoryslider_index.xml
│       └── ui_component/
│           ├── category_slider_form.xml
│           └── category_slider_listing.xml
├── composer.json
├── install.sh
├── LICENSE.txt
├── README.md
└── registration.php
```

## Recent Changes

### Dependency Injection Fix

The module has been updated to fix a critical dependency injection issue with the `ImageUploader` class. The fix resolves the error:

```
Error: Cannot instantiate interface Magento\Framework\Filesystem\Directory\WriteInterface
```

The solution involves:
- Using Magento's `Filesystem` class to obtain the `WriteInterface` instance
- Properly configuring the dependency injection in `etc/adminhtml/di.xml`
- Using Magento's file system abstraction layer for file operations

### JSON Storage Implementation

The module now uses a simplified database schema with JSON storage for slider items:
- Slider items are stored as serialized JSON in the `categories` column
- This eliminates the need for a separate slider items table
- The `Slider` model handles serialization and deserialization automatically
- This approach improves performance and simplifies the codebase

### Menu Structure Update

The admin menu structure has been updated to use a hierarchical approach:
- Main menu: **Krish Technolabs**
- Submenu: **Category Slider Management**
- Action: **Category Slider**

The ACL structure has been updated to match this menu structure.

## Troubleshooting

### Common Issues

1. **Module not appearing in admin menu**:
   - Check if the module is enabled: `bin/magento module:status Krish_CategorySlider`
   - Clear the cache: `bin/magento cache:clean`
   - Check ACL permissions for your admin user
   - Verify that the Krish_Base module is installed and enabled

2. **Images not uploading**:
   - Check file permissions on the `pub/media/krish/category_slider` directory
   - Ensure the directory exists and is writable
   - Verify that the web server has write permissions to the media directory

3. **Database tables not created**:
   - Run setup upgrade: `bin/magento setup:upgrade`
   - Check for errors in the Magento logs

4. **Error when saving sliders**:
   - Check the Magento logs for specific error messages
   - Ensure that all required dependencies are installed and enabled
   - Verify that the file system permissions are correctly set

## Support

For any issues or questions, <NAME_EMAIL>

## License

[OSL-3.0](https://opensource.org/licenses/OSL-3.0)
