# Krish_CategorySlider Technical Documentation

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Database Schema](#database-schema)
3. [Service Contracts](#service-contracts)
4. [Models and Resource Models](#models-and-resource-models)
5. [Admin UI Components](#admin-ui-components)
6. [Image Handling](#image-handling)
7. [ACL Implementation](#acl-implementation)
8. [Configuration](#configuration)
9. [Extension Points](#extension-points)
10. [Customization Guide](#customization-guide)

## Architecture Overview

The Krish_CategorySlider module follows Magento 2's standard module architecture and best practices. It is designed with the following principles:

- **Service Contracts**: All business logic is exposed through interfaces
- **Dependency Injection**: All dependencies are injected through constructors
- **UI Components**: Admin UI is built using Magento's UI Component framework
- **Declarative Schema**: Database tables are defined using declarative schema
- **ACL**: Access control is implemented for all admin actions

The module consists of the following main components:

- **Data Models**: Represent the slider and slider item entities
- **Resource Models**: Handle database operations
- **Repositories**: Provide CRUD operations for the entities
- **Admin Controllers**: Handle admin actions
- **UI Components**: Define the admin grid and form
- **Helper**: Provides configuration access

## Database Schema

The module creates a single database table:

### krish_category_slider

| Column     | Type      | Description       |
|------------|-----------|-------------------|
| slider_id  | int       | Primary Key       |
| title      | varchar   | Slider Title      |
| categories | text      | Categories JSON Data |
| created_at | timestamp | Creation Time     |
| updated_at | timestamp | Last Update Time  |

The schema is defined in `etc/db_schema.xml` using Magento's declarative schema approach. The `categories` column stores slider items as serialized JSON data, which includes category IDs, titles, subtitles, and image paths.

### JSON Data Structure

The `categories` column stores an array of slider items in JSON format. Each item has the following structure:

```json
[
  {
    "item_id": "unique_id",
    "category_id": ["123"],
    "title": "Custom Title",
    "subtitle": "Custom Subtitle",
    "image": "path/to/image.jpg"
  },
  {
    "item_id": "another_unique_id",
    "category_id": ["456"],
    "title": "Another Title",
    "subtitle": "Another Subtitle",
    "image": "path/to/another_image.jpg"
  }
]
```

This approach eliminates the need for a separate slider items table and simplifies the database schema.

## Service Contracts

The module defines the following service contracts:

### Data Interfaces

- `Api/Data/SliderInterface.php`: Defines the slider entity
- `Api/Data/SliderSearchResultsInterface.php`: Defines the slider search results

### Repository Interfaces

- `Api/SliderRepositoryInterface.php`: Defines CRUD operations for sliders

These interfaces are implemented by the following classes:

- `Model/Slider.php`: Implements `SliderInterface`
- `Model/SliderRepository.php`: Implements `SliderRepositoryInterface`

## Models and Resource Models

### Models

- `Model/Slider.php`: Represents a slider entity with JSON-serialized category data
- `Model/ImageUploader.php`: Handles image upload and storage operations

### Resource Models

- `Model/ResourceModel/Slider.php`: Handles database operations for sliders

### Collections

- `Model/ResourceModel/Slider/Collection.php`: Collection for sliders

## Admin UI Components

The module uses Magento's UI Component framework for the admin interface:

### Grid

- `view/adminhtml/ui_component/category_slider_listing.xml`: Defines the admin grid
- `Ui/DataProvider/CategorySlider/ListingDataProvider.php`: Provides data for the grid
- `Ui/Component/Listing/Column/SliderActions.php`: Defines grid actions

### Form

- `view/adminhtml/ui_component/category_slider_form.xml`: Defines the admin form
- `Ui/DataProvider/CategorySlider/Form/DataProvider.php`: Provides data for the form
- `Block/Adminhtml/Slider/Edit/*.php`: Button classes for the form

## Image Handling

The module uses Magento's image uploader component for handling images:

- `Model/ImageUploader.php`: Handles image upload and storage
- `Controller/Adminhtml/CategorySlider/Upload.php`: Handles image upload requests

Images are stored in the `pub/media/krish/category_slider` directory.

### Dependency Injection Fix

The `ImageUploader` class was updated to resolve a critical dependency injection issue with `WriteInterface`. Instead of directly injecting the interface, which cannot be instantiated by Magento's Object Manager, we now inject the `Filesystem` class and obtain the `WriteInterface` through it:

```php
/**
 * @var WriteInterface
 */
private readonly WriteInterface $mediaDirectory;

/**
 * @param Database $coreFileStorageDatabase Core file storage database
 * @param Filesystem $filesystem Filesystem
 * @param UploaderFactory $uploaderFactory Uploader factory
 * @param StoreManagerInterface $storeManager Store manager
 * @param LoggerInterface $logger Logger interface
 * @param DriverInterface $driver Filesystem driver interface
 * @param string $baseTmpPath Base temporary path
 * @param string $basePath Base path
 * @param string[] $allowedExtensions Allowed file extensions
 */
public function __construct(
    private readonly Database $coreFileStorageDatabase,
    Filesystem $filesystem,
    private readonly UploaderFactory $uploaderFactory,
    private readonly StoreManagerInterface $storeManager,
    private readonly LoggerInterface $logger,
    private readonly DriverInterface $driver,
    private string $baseTmpPath = 'krish/category_slider/tmp',
    private string $basePath = 'krish/category_slider',
    private array $allowedExtensions = ['jpg', 'jpeg', 'gif', 'png']
) {
    $this->mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
}
```

This change resolves the error: `Cannot instantiate interface Magento\Framework\Filesystem\Directory\WriteInterface`.

### Magento 2 File Operations

The module uses Magento's file system abstraction layer instead of standard PHP functions:

#### Checking if a file exists:

```php
// Incorrect approach (standard PHP)
if (file_exists($mediaPath . $path)) {
    // File exists
}

// Correct approach (Magento 2)
if ($mediaDirectory->isExist($path)) {
    // File exists
}
```

#### Getting a parent directory:

```php
// Incorrect approach (standard PHP)
$parentDir = dirname($baseFilePath);

// Correct approach (Magento 2)
$parentDir = $driver->getParentDirectory($baseFilePath);
```

#### Checking for files across multiple locations:

```php
// Define possible paths to check in order of preference
$possiblePaths = [
    'krish/category_slider/' . $image,
    'krish/category_slider/tmp/' . $image,
    'catalog/category/' . $image,
    'catalog/tmp/category/' . $image,
    $image
];

// Check each path and return the first one that exists
foreach ($possiblePaths as $path) {
    if ($mediaDirectory->isExist($path)) {
        return $mediaUrl . $path;
    }
}
```

### DI.xml Configuration

The `etc/adminhtml/di.xml` file was updated to properly configure the `ImageUploader` class:

```xml
<type name="Krish\CategorySlider\Model\ImageUploader">
    <arguments>
        <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem</argument>
        <argument name="baseTmpPath" xsi:type="string">krish/category_slider/tmp</argument>
        <argument name="basePath" xsi:type="string">krish/category_slider</argument>
        <argument name="allowedExtensions" xsi:type="array">
            <item name="jpg" xsi:type="string">jpg</item>
            <item name="jpeg" xsi:type="string">jpeg</item>
            <item name="gif" xsi:type="string">gif</item>
            <item name="png" xsi:type="string">png</item>
        </argument>
    </arguments>
</type>
```

This configuration ensures that the `Filesystem` object is properly injected into the `ImageUploader` class.

## ACL Implementation

The module defines the following ACL resources:

- `Krish_Base::krish_menu`: Top-level menu resource for all Krish modules
- `Krish_CategorySlider::category_slider`: Main resource for the Category Slider Management menu
- `Krish_CategorySlider::category_slider_management`: Resource for the Category Slider submenu
- `Krish_CategorySlider::save`: Permission to save sliders
- `Krish_CategorySlider::delete`: Permission to delete sliders
- `Krish_CategorySlider::config`: Permission to access configuration

These resources are defined in `etc/acl.xml` and used in controllers and UI components.

### Menu and ACL Structure

The module's menu structure has been updated to use a hierarchical approach:

```xml
<menu>
    <add id="Krish_CategorySlider::category_slider"
         title="Category Slider Management"
         module="Krish_CategorySlider"
         sortOrder="110"
         parent="Krish_Base::krish_menu"
         resource="Krish_CategorySlider::category_slider"/>
    <add id="Krish_CategorySlider::category_slider_management"
         title="Category Slider"
         module="Krish_CategorySlider"
         sortOrder="10"
         parent="Krish_CategorySlider::category_slider"
         action="krish_categoryslider/categoryslider/index"
         resource="Krish_CategorySlider::category_slider_management"/>
</menu>
```

The ACL structure mirrors this menu structure:

```xml
<acl>
    <resources>
        <resource id="Magento_Backend::admin">
            <resource id="Krish_Base::krish_menu">
                <resource id="Krish_CategorySlider::category_slider" title="Category Slider Management" sortOrder="110">
                    <resource id="Krish_CategorySlider::category_slider_management" title="Category Slider" sortOrder="10" />
                    <resource id="Krish_CategorySlider::save" title="Save Category Slider" sortOrder="20" />
                    <resource id="Krish_CategorySlider::delete" title="Delete Category Slider" sortOrder="30" />
                    <resource id="Krish_CategorySlider::config" title="Configuration" sortOrder="40" />
                </resource>
            </resource>
        </resource>
    </resources>
</acl>
```

This ensures proper permission management and a consistent user experience in the admin panel.

## Configuration

The module provides the following configuration options:

- `krish_category_slider/general/enabled`: Enable/disable the module
- `krish_category_slider/general/image_width`: Image width in pixels
- `krish_category_slider/general/image_height`: Image height in pixels

These options are defined in `etc/adminhtml/system.xml` and accessed through the `Helper/Data.php` class.

## Extension Points

The module provides the following extension points:

### Events

- `krish_category_slider_save_before`: Dispatched before a slider is saved
- `krish_category_slider_save_after`: Dispatched after a slider is saved
- `krish_category_slider_delete_before`: Dispatched before a slider is deleted
- `krish_category_slider_delete_after`: Dispatched after a slider is deleted

### Plugin Points

- `Krish\CategorySlider\Api\SliderRepositoryInterface`: Repository methods
- `Krish\CategorySlider\Api\SliderItemRepositoryInterface`: Repository methods
- `Krish\CategorySlider\Model\ImageUploader`: Image upload methods

## Customization Guide

### Adding New Fields to Sliders

1. Update the database schema in `etc/db_schema.xml`
2. Add getters and setters to `Api/Data/SliderInterface.php` and `Model/Slider.php`
3. Update the form UI component in `view/adminhtml/ui_component/category_slider_form.xml`
4. Update the grid UI component in `view/adminhtml/ui_component/category_slider_listing.xml` if needed

### Adding New Fields to Slider Items

1. Update the database schema in `etc/db_schema.xml`
2. Add getters and setters to `Api/Data/SliderItemInterface.php` and `Model/SliderItem.php`
3. Update the form UI component in `view/adminhtml/ui_component/category_slider_form.xml`

### Customizing the Admin Grid

1. Edit `view/adminhtml/ui_component/category_slider_listing.xml`
2. Add or modify columns as needed
3. Customize filters, sorting, and actions

### Customizing the Admin Form

1. Edit `view/adminhtml/ui_component/category_slider_form.xml`
2. Add or modify fields as needed
3. Customize validation, dependencies, and UI components

### Adding Custom Functionality

1. Create a new module that depends on Krish_CategorySlider
2. Use plugins to modify behavior
3. Use events to react to slider actions
4. Extend existing classes or implement interfaces

## Module Structure

```
Krish_CategorySlider/
├── Api/
│   ├── Data/
│   │   ├── SliderInterface.php
│   │   └── SliderSearchResultsInterface.php
│   └── SliderRepositoryInterface.php
├── Block/
│   └── Adminhtml/
│       └── Slider/
│           └── Edit/
│               ├── BackButton.php
│               ├── DeleteButton.php
│               ├── GenericButton.php
│               ├── SaveButton.php
│               └── SaveAndContinueButton.php
├── Controller/
│   └── Adminhtml/
│       └── CategorySlider/
│           ├── Delete.php
│           ├── Edit.php
│           ├── Index.php
│           ├── MassDelete.php
│           ├── NewAction.php
│           ├── Save.php
│           └── Upload.php
├── docs/
│   ├── TECHNICAL.md
│   └── USER_GUIDE.md
├── etc/
│   ├── adminhtml/
│   │   ├── di.xml
│   │   ├── menu.xml
│   │   ├── routes.xml
│   │   └── system.xml
│   ├── acl.xml
│   ├── config.xml
│   ├── db_schema.xml
│   ├── di.xml
│   └── module.xml
├── Helper/
│   └── Data.php
├── i18n/
│   └── en_US.csv
├── Model/
│   ├── ResourceModel/
│   │   ├── Slider/
│   │   │   └── Collection.php
│   │   └── Slider.php
│   ├── ImageUploader.php
│   ├── Slider.php
│   └── SliderRepository.php
├── Ui/
│   ├── Component/
│   │   └── Listing/
│   │       └── Column/
│   │           └── SliderActions.php
│   └── DataProvider/
│       └── CategorySlider/
│           ├── Form/
│           │   └── DataProvider.php
│           └── ListingDataProvider.php
├── view/
│   └── adminhtml/
│       ├── layout/
│       │   ├── krish_categoryslider_categoryslider_edit.xml
│       │   └── krish_categoryslider_categoryslider_index.xml
│       └── ui_component/
│           ├── category_slider_form.xml
│           └── category_slider_listing.xml
├── composer.json
├── install.sh
├── LICENSE.txt
├── README.md
└── registration.php
```
