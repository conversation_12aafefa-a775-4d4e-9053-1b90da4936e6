# Krish_CategorySlider User Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Managing Category Sliders](#managing-category-sliders)
   - [Creating a New Slider](#creating-a-new-slider)
   - [Editing an Existing Slider](#editing-an-existing-slider)
   - [Deleting a Slider](#deleting-a-slider)
   - [Mass Actions](#mass-actions)
5. [Managing Slider Items](#managing-slider-items)
   - [Adding Items](#adding-items)
   - [Configuring Items](#configuring-items)
   - [Reordering Items](#reordering-items)
   - [Removing Items](#removing-items)
6. [Image Management](#image-management)
   - [Uploading Images](#uploading-images)
   - [Image Requirements](#image-requirements)
7. [Troubleshooting](#troubleshooting)
8. [Support](#support)

## Introduction

The Krish_CategorySlider module provides an admin interface to manage category sliders for your Magento 2 store. Each slider can contain multiple items, each with its own category selection, title, subtitle, and image.

This user guide will help you understand how to install, configure, and use the module effectively.

## Installation

### Prerequisites

- Magento 2.4.6 or higher
- PHP 8.1 or higher
- MySQL 8.0 or higher

### Installation Steps

1. Extract the module files to `app/code/Krish/CategorySlider` directory
2. Run the installation script:

```bash
cd [magento_root]
bash app/code/Krish/CategorySlider/install.sh
```

Alternatively, you can run the following commands manually:

```bash
cd [magento_root]
bin/magento module:enable Krish_CategorySlider
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:static-content:deploy -f
bin/magento cache:clean
bin/magento cache:flush
```

## Configuration

1. Log in to your Magento admin panel
2. Navigate to **Stores > Configuration > Krish Extensions > Category Slider**
3. Configure the following options:

   - **Enable Module**: Set to "Yes" to enable the module or "No" to disable it
   - **Image Width**: Set the width of slider images in pixels (default: 300)
   - **Image Height**: Set the height of slider images in pixels (default: 300)

4. Click **Save Config** to save your changes

## User Permissions

To grant users access to the Category Slider module, you need to set the appropriate permissions in the Magento admin panel:

1. Navigate to **System > User Roles**
2. Select an existing role or create a new one
3. Click on the **Role Resources** tab
4. Set **Resource Access** to **Custom**
5. In the resource tree, check the following permissions:
   - **Krish Technolabs > Category Slider Management**
   - **Krish Technolabs > Category Slider Management > Category Slider**
   - **Krish Technolabs > Category Slider Management > Save Category Slider**
   - **Krish Technolabs > Category Slider Management > Delete Category Slider**
   - **Krish Technolabs > Category Slider Management > Configuration**
6. Click **Save Role** to apply the permissions

## Managing Category Sliders

### Creating a New Slider

1. Navigate to **Krish Technolabs > Category Slider Management > Category Slider**
2. Click the **Add New Category Slider** button
3. In the **General Information** section, enter a title for the slider
4. In the **Slider Items** section, add one or more items (see [Managing Slider Items](#managing-slider-items))
5. Click **Save Slider** to create the slider

### Editing an Existing Slider

1. Navigate to **Krish Technolabs > Category Slider Management > Category Slider**
2. Find the slider you want to edit in the grid
3. Click the **Edit** link in the Action column
4. Make your changes to the slider
5. Click **Save Slider** to save your changes

### Deleting a Slider

1. Navigate to **Krish Technolabs > Category Slider Management > Category Slider**
2. Find the slider you want to delete in the grid
3. Click the **Delete** link in the Action column
4. Confirm the deletion by clicking **OK** in the confirmation dialog

### Mass Actions

1. Navigate to **Krish Technolabs > Category Slider Management > Category Slider**
2. Select the checkboxes next to the sliders you want to modify
3. Choose an action from the **Actions** dropdown (e.g., Delete)
4. Click **Submit** to apply the action to the selected sliders

## Managing Slider Items

### Adding Items

1. In the slider edit form, scroll to the **Slider Items** section
2. Click the **Add Item** button to add a new item
3. Configure the item (see [Configuring Items](#configuring-items))
4. Repeat steps 2-3 to add more items as needed

### Configuring Items

For each slider item, you can configure the following:

- **Category**: Select a category from the dropdown (required)
- **Title**: Enter a custom title for the item (optional, defaults to category name)
- **Subtitle**: Enter a subtitle for the item (optional, defaults to category description)
- **Image**: Upload an image for the item (optional, defaults to category image if available)

> **Note**: All slider items are stored as JSON data in the database, which simplifies the data structure and improves performance.

### Reordering Items

1. In the slider edit form, scroll to the **Slider Items** section
2. Click and hold the drag handle (≡) next to an item
3. Drag the item to the desired position
4. Release the mouse button to drop the item in its new position

### Removing Items

1. In the slider edit form, scroll to the **Slider Items** section
2. Click the **Delete** button (trash icon) next to the item you want to remove
3. The item will be removed from the list

## Image Management

### Uploading Images

1. In the slider item form, click the **Upload** button in the Image field
2. Select an image from your computer
3. The image will be uploaded and displayed in the form

### Image Requirements

- Supported formats: JPG, JPEG, PNG, GIF
- Maximum file size: 2MB
- Recommended dimensions: Match the configured image width and height in the module settings

## Troubleshooting

### Common Issues

1. **Module not appearing in admin menu**:
   - Check if the module is enabled: `bin/magento module:status Krish_CategorySlider`
   - Clear the cache: `bin/magento cache:clean`
   - Check ACL permissions for your admin user (see [User Permissions](#user-permissions))
   - Verify that the Krish_Base module is installed and enabled

2. **Images not uploading**:
   - Check file permissions on the `pub/media/krish/category_slider` directory
   - Ensure the directory exists and is writable
   - Check that the image meets the format and size requirements
   - Verify that the web server has write permissions to the media directory

3. **Database tables not created**:
   - Run setup upgrade: `bin/magento setup:upgrade`
   - Check for errors in the Magento logs

4. **Changes not appearing after saving**:
   - Clear the cache: `bin/magento cache:clean`
   - Refresh the page

5. **Error when saving sliders**:
   - Check the Magento logs for specific error messages
   - Ensure that all required dependencies are installed and enabled
   - Verify that the file system permissions are correctly set

## Support

For any issues or questions, <NAME_EMAIL>

Please include the following information in your support request:

- Magento version
- PHP version
- Module version
- Detailed description of the issue
- Steps to reproduce the issue
- Screenshots (if applicable)
