<?php

/**
 * @package     Krish_CategorySlider
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023 Krish (https://www.example.com)
 * @license     https://opensource.org/licenses/OSL-3.0 OSL-3.0
 */

declare(strict_types=1);

namespace <PERSON><PERSON>\CategorySlider\Ui\DataProvider\CategorySlider\Form;

use <PERSON><PERSON>\CategorySlider\Model\ResourceModel\Slider\CollectionFactory;
use Krish\CategorySlider\Model\Slider;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Magento\Ui\DataProvider\Modifier\PoolInterface;
use Psr\Log\LoggerInterface;

/**
 * Data provider for category slider form
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class DataProvider extends AbstractDataProvider
{
    /**
     * @var array<int|string, mixed>
     */
    private array $loadedData = [];

    /**
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param StoreManagerInterface $storeManager
     * @param Filesystem $filesystem
     * @param LoggerInterface $logger
     * @param Json $jsonSerializer
     * @param File $file
     * @param array $meta
     * @param array $data
     * @param PoolInterface|null $pool
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        private readonly DataPersistorInterface $dataPersistor,
        private readonly StoreManagerInterface $storeManager,
        private readonly Filesystem $filesystem,
        private readonly LoggerInterface $logger,
        private readonly Json $jsonSerializer,
        private readonly File $file,
        array $meta = [],
        array $data = [],
        ?PoolInterface $pool = null
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->collection = $collectionFactory->create();
        if ($pool !== null) {
            $this->pool = $pool;
        }
    }

    /**
     * Get data
     *
     * @return array<int|string, mixed>
     * @throws LocalizedException
     */
    public function getData(): array
    {
        if (!empty($this->loadedData)) {
            return $this->loadedData;
        }

        try {
            $items = $this->collection->getItems();
            /** @var Slider $slider */
            foreach ($items as $slider) {
                $this->loadedData[$slider->getId()] = $slider->getData();

                // Load categories from JSON data
                $this->processCategoriesData($slider);
            }

            $data = $this->dataPersistor->get('category_slider');
            if (!empty($data)) {
                $slider = $this->collection->getNewEmptyItem();
                $slider->setData($data);
                $this->loadedData[$slider->getId()] = $slider->getData();
                $this->dataPersistor->clear('category_slider');
            }

            return $this->loadedData;
        } catch (\Exception $e) {
            $this->logger->error('Error loading data for category slider form', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new LocalizedException(__('Could not load data for form: %1', $e->getMessage()));
        }
    }

    /**
     * Process categories data for the slider
     *
     * @param Slider $slider
     * @return void
     */
    private function processCategoriesData(Slider $slider): void
    {
        try {
            $categories = $slider->getCategories();
            $categoriesData = [];

            foreach ($categories as $index => $category) {
                $itemData = $category;

                // Format image data for UI component
                if (!empty($itemData['image'])) {
                    // Check if image is already in the correct format
                    if (!is_array($itemData['image'])) {
                        $itemData['image'] = [
                            [
                                'name' => $itemData['image'],
                                'url' => $this->getImageUrl($itemData['image']),
                                'size' => $this->getImageSize($itemData['image']),
                                'type' => $this->getImageMimeType($itemData['image'])
                            ]
                        ];
                    }
                }

                // Ensure each item has a record_id for the dynamic rows component
                if (!isset($itemData['record_id'])) {
                    $itemData['record_id'] = $index;
                }

                $categoriesData[] = $itemData;
            }

            if (!empty($categoriesData)) {
                $this->logger->debug('Categories data processed', [
                    'slider_id' => $slider->getId(),
                    'categories_count' => count($categoriesData)
                ]);
                $this->loadedData[$slider->getId()]['slider_items']['slider_items_container'] = $categoriesData;
            }
        } catch (\Exception $e) {
            $this->logger->error('Error processing categories data', [
                'slider_id' => $slider->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get image URL
     *
     * @param string $image
     * @return string
     * @throws NoSuchEntityException
     * @throws FileSystemException
     */
    private function getImageUrl(string $image): string
    {
        try {
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);

            // Define possible paths to check in order of preference
            $possiblePaths = [
                'krish/category_slider/' . $image,
                'krish/category_slider/tmp/' . $image,
                'catalog/category/' . $image,
                'catalog/tmp/category/' . $image,
                $image
            ];

            // Check each path and return the first one that exists
            foreach ($possiblePaths as $path) {
                if ($mediaDirectory->isExist($path)) {
                    $this->logger->debug('Image found at path: ' . $path);
                    return $mediaUrl . $path;
                }
            }

            $this->logger->warning('Image not found in any expected location', ['image' => $image]);
            return $mediaUrl . 'krish/category_slider/' . $image; // Return the standard path as fallback
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Store not found', ['error' => $e->getMessage()]);
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('Error getting image URL', [
                'image' => $image,
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * Get image size
     *
     * @param string $image
     * @return int
     * @throws FileSystemException
     */
    private function getImageSize(string $image): int
    {
        try {
            $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);

            // Define possible paths to check in order of preference
            $possiblePaths = [
                'krish/category_slider/' . $image,
                'krish/category_slider/tmp/' . $image,
                'catalog/category/' . $image,
                'catalog/tmp/category/' . $image,
                $image
            ];

            // Check each path and return the size of the first one that exists
            foreach ($possiblePaths as $path) {
                if ($mediaDirectory->isExist($path)) {
                    $size = $mediaDirectory->stat($path)['size'];
                    $this->logger->debug('Image found, size calculated', [
                        'path' => $path,
                        'size' => $size
                    ]);
                    return $size;
                }
            }

            $this->logger->warning('Image file not found in any expected location', ['image' => $image]);
        } catch (\Exception $e) {
            $this->logger->error('Error getting image size', [
                'image' => $image,
                'error' => $e->getMessage()
            ]);
        }

        return 1024; // Return a default size
    }

    /**
     * Get image MIME type
     *
     * @param string $image
     * @return string
     */
    private function getImageMimeType(string $image): string
    {
        try {
            $pathInfo = $this->file->getPathInfo($image);
            $extension = strtolower($pathInfo['extension'] ?? '');

            $mimeTypes = [
                'jpg' => 'image/jpeg',
                'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                'webp' => 'image/webp'
            ];

            return $mimeTypes[$extension] ?? 'image/jpeg';
        } catch (\Exception $e) {
            $this->logger->error('Error getting image MIME type: ' . $e->getMessage(), [
                'image' => $image,
                'exception' => $e
            ]);
            return 'image/jpeg';
        }
    }
}
