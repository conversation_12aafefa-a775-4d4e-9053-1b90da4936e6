<?php
declare(strict_types=1);
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\UpcomingProducts\Controller\Notify;

use Exception;
use Krish\UpcomingProducts\Model\Email;
use Krish\UpcomingProducts\Model\ResourceModel\Subscription\CollectionFactory;
use Krish\UpcomingProducts\Model\SubscriptionFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Psr\Log\LoggerInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;

/**
 * Subscribe action
 */
class Subscribe extends Action implements HttpPostActionInterface
{
    /**
     * @var JsonFactory
     */
    protected JsonFactory $resultJsonFactory;

    /**
     * @var SubscriptionFactory
     */
    protected SubscriptionFactory $subscriptionFactory;

    /**
     * @var Email
     */
    protected Email $emailHelper;

    /**
     * @var ProductRepositoryInterface
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var CollectionFactory
     */
    private CollectionFactory $subscriptionCollectionFactory;

    /**
     * @param Context $context
     * @param JsonFactory $resultJsonFactory
     * @param SubscriptionFactory $subscriptionFactory
     * @param Email $emailHelper
     * @param LoggerInterface $logger
     * @param CollectionFactory $subscriptionCollectionFactory
     * @param ProductRepositoryInterface $productRepository
     */
    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        SubscriptionFactory $subscriptionFactory,
        Email $emailHelper,
        LoggerInterface $logger,
        CollectionFactory $subscriptionCollectionFactory,
        ProductRepositoryInterface $productRepository
    ) {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->subscriptionFactory = $subscriptionFactory;
        $this->emailHelper = $emailHelper;
        $this->productRepository = $productRepository;
        $this->logger = $logger;
        $this->subscriptionCollectionFactory = $subscriptionCollectionFactory;
    }

    /**
     * Execute subscription action
     *
     * @return Json
     */
    public function execute(): Json
    {
        $result = $this->resultJsonFactory->create();
        $post = $this->getRequest()->getPostValue();
        $success = false;
        $message = __('Invalid form data.');

        if ($post) {
            try {
                $data = $this->getRequest()->getParams();
                $productSku = $data['product_sku'] ?? '';
                $email = $data['email'] ?? '';

                if (isset($data['check_subscription']) && $data['check_subscription']) {
                    $subscriptionCollection = $this->subscriptionCollectionFactory->create()
                        ->addFieldToFilter('product_sku', $productSku)
                        ->addFieldToFilter('email', $email)
                        ->addFieldToFilter('is_reminder_mail_sent', 0);

                    if ($subscriptionCollection->getSize() > 0) {
                        $message = __('You are already subscribed to this product.');
                    } else {
                        $subscription = $this->subscriptionFactory->create();
                        $subscription->setData([
                            'product_sku' => $post['product_sku'],
                            'email' => $post['email'],
                            'name' => $post['name'],
                            'store_id' => $post['store_id'],
                            'is_subscribe_mail_sent' => 0,
                            'is_reminder_mail_sent' => 0
                        ]);
                        $subscription->save();

                        $success = true;
                        $message = __('You have been subscribed successfully. You will receive an email notification when the product becomes available.');
                    }
                }
            } catch (Exception $e) {
                $this->logger->critical($e);
                $message = __('An error occurred while processing your request.');
            }
        }

        return $result->setData(['success' => $success, 'message' => $message]);
    }
}
