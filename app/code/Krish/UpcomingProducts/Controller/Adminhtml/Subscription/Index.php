<?php
declare(strict_types=1);
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

/**
 * SuppressWarnings: (PHPMD.NumberOfChildren)
 * SuppressWarnings: (PHPMD.CouplingBetweenObjects)
 */
namespace Krish\UpcomingProducts\Controller\Adminhtml\Subscription;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;

/**
 * Index controller for the upcoming product subscriptions
 */
class Index extends Action implements HttpGetActionInterface
{
    /**
     * @var PageFactory
     */
    protected PageFactory $resultPageFactory;

    /**
     * @param Context $context
     * @param PageFactory $resultPageFactory
     */
    public function __construct(
        Context     $context,
        PageFactory $resultPageFactory
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
    }

    /**
     * Index action
     *
     * @return Page
     */
    public function execute(): Page
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->getConfig()->getTitle()->prepend(__('Upcoming Product Subscriptions'));
        return $resultPage;
    }

    /**
     * Check if a user has permissions to access this controller
     *
     * @return bool
     * @SuppressWarnings(PHPMD.CamelCaseMethodName)
     */
    protected function _isAllowed(): bool
    {
        return $this->_authorization->isAllowed('Krish_UpcomingProducts::subscriptions');
    }
}
