<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
declare(strict_types=1);

namespace Krish\UpcomingProducts\Cron;

use Exception;
use Krish\UpcomingProducts\Model\Email;
use Krish\UpcomingProducts\Model\ResourceModel\Subscription\CollectionFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Checks for upcoming products and sends notifications to subscribed customers.
 */
class CheckAndSubscribe
{
    /**
     * Constructor
     *
     * @param CollectionFactory $subscriptionCollectionFactory Subscription collection factory
     * @param Email $emailHelper Email helper
     * @param ProductRepositoryInterface $productRepository Product repository
     * @param State $appState Application state
     * @param StoreManagerInterface $storeManager Store manager
     * @param Emulation $emulation Store emulation
     * @param LoggerInterface $logger Logger
     */
    public function __construct(
        private readonly CollectionFactory          $subscriptionCollectionFactory,
        private readonly Email                      $emailHelper,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly State                      $appState,
        private readonly StoreManagerInterface      $storeManager,
        private readonly Emulation                  $emulation,
        private readonly LoggerInterface            $logger
    )
    {
    }

    /**
     * Execute the cron job to send subscription confirmation emails
     *
     * @return void
     * @throws NoSuchEntityException
     */
    public function execute(): void
    {
        try {
            // Set area code to frontend
            try {
                $this->appState->getAreaCode();
            } catch (Exception $e) {
                $this->appState->setAreaCode(Area::AREA_FRONTEND);
            }

            // First, process subscriptions that need confirmation emails
            $newSubscriptions = $this->subscriptionCollectionFactory->create()
                ->addFieldToFilter('is_subscribe_mail_sent', 0);

            $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();
            $this->emulation->startEnvironmentEmulation($defaultStoreId, Area::AREA_FRONTEND, true);

            foreach ($newSubscriptions as $subscription) {
                try {
                    $product = $this->productRepository->get($subscription->getProductSku());
                } catch (\Exception $e) {
                    $this->logger->error('Subscription notify product not found.'.$e->getMessage());
                    continue;
                }
                try {
                    $this->emailHelper->setVariables([
                        'product' => $product,
                        'customer_name' => $subscription->getName(),
                    ])->sendSubscribeEmail(
                        $subscription->getEmail(),
                        (int)$subscription->getStoreId()
                    );

                    $subscription->setIsSubscribeMailSent(1);
                    $subscription->save();
                } catch (Exception $e) {
                    $this->logger->error('Subscription notify email not sent.'.$e->getMessage());
                    continue;
                }
            }

            $this->emulation->stopEnvironmentEmulation();
        } catch (Exception $e) {
            $this->logger->error('Error in CheckAndSubscribe cron: ' . $e->getMessage());
        }
    }
}
