<?php
declare(strict_types=1);
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\UpcomingProducts\Cron;

use Krish\UpcomingProducts\Model\Config;
use Magento\Catalog\Model\Product\Action as ProductAction;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

/**
 * Manages upcoming products.
 */
class ManageUpcomingProducts
{
    /**
     * Constructor
     *
     * @param CollectionFactory $productCollectionFactory Product collection factory
     * @param ProductAction $productAction Product action
     * @param TimezoneInterface $timezone Timezone interface
     * @param Config $config Module configuration
     */
    public function __construct(
        private readonly CollectionFactory $productCollectionFactory,
        private readonly ProductAction $productAction,
        private readonly TimezoneInterface $timezone,
        private readonly Config $config
    ) {
    }

    /**
     * Execute cron job to update upcoming product status
     *
     * @return void
     */
    public function execute(): void
    {
        if ($this->config->isModuleEnabled() && $this->config->isAutoUpdateEnabled()) {
            $currentDate = $this->timezone->date()->format('Y-m-d H:i:s');
            $collection = $this->productCollectionFactory->create()
                ->addAttributeToSelect(['upcoming_date', 'is_upcoming'])
                ->addAttributeToFilter('is_upcoming', 1);

            foreach ($collection as $product) {
                if ($product->getUpcomingDate() && $product->getUpcomingDate() <= $currentDate) {
                    $this->updateProductUpcomingStatus((int)$product->getId(), 0);
                }
            }
        }
    }

    /**
     * Update product upcoming status
     *
     * @param int $productId
     * @param int $isUpcoming
     * @return void
     */
    private function updateProductUpcomingStatus(int $productId, int $isUpcoming): void
    {
        $this->productAction->updateAttributes(
            [$productId],
            ['is_upcoming' => $isUpcoming],
            0
        );
    }
}
