<?php
declare(strict_types=1);
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\UpcomingProducts\Cron;

use Exception;
use Krish\UpcomingProducts\Model\Email;
use Krish\UpcomingProducts\Model\ResourceModel\Subscription\CollectionFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Exception\MailException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Checks for upcoming products and sends notifications to subscribed customers.
 */
class CheckAndNotify
{
    /**
     * CheckAndNotify constructor.
     *
     * @param CollectionFactory $subscriptionCollectionFactory
     * @param Email $emailHelper
     * @param ProductRepositoryInterface $productRepository
     * @param State $appState
     * @param StoreManagerInterface $storeManager
     * @param Emulation $emulation
     * @param LoggerInterface $logger
     */
    public function __construct(
        private CollectionFactory          $subscriptionCollectionFactory,
        private Email                      $emailHelper,
        private ProductRepositoryInterface $productRepository,
        private State                      $appState,
        private StoreManagerInterface      $storeManager,
        private Emulation                  $emulation,
        private LoggerInterface            $logger
    )
    {
    }

    /**
     * Execute the cron job to check and notify customers about product availability
     *
     * @return void
     * @throws NoSuchEntityException
     * @noinspection PhpUndefinedMethodInspection
     */
    public function execute(): void
    {
        // Set area code to frontend
        try {
            $this->appState->getAreaCode();
        } catch (Exception $e) {
            $this->appState->setAreaCode(Area::AREA_FRONTEND);
        }
            // process subscriptions that need reminder emails
        $subscriptions = $this->subscriptionCollectionFactory->create()
            ->addFieldToFilter('is_subscribe_mail_sent', 1)
            ->addFieldToFilter('is_reminder_mail_sent', 0);

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        $this->emulation->startEnvironmentEmulation($defaultStoreId, Area::AREA_FRONTEND, true);

        foreach ($subscriptions as $subscription) {
            try {
                $product = $this->productRepository->get($subscription->getProductSku());
                $isUpcoming = $product->getIsUpcoming();
            } catch (\Exception $e) {
                $this->logger->error('Subscription product not found.'.$e->getMessage());
                continue;
            }
            if (!$isUpcoming || $isUpcoming == '0') {
                try {
                    $this->emailHelper->setVariables([
                        'product' => $product,
                        'customer_name' => $subscription->getName(),
                    ])->sendReminderEmail(
                        $subscription->getEmail(),
                        (int)$subscription->getStoreId()
                    );
                } catch (Exception $e) {
                    $this->logger->error('Subscription email not sent.'.$e->getMessage());
                    continue;
                }
                $subscription->setIsReminderMailSent(1);
                $subscription->save();
            }
        }
        $this->emulation->stopEnvironmentEmulation();
    }
}
