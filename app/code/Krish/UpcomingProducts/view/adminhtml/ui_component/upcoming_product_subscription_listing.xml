<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_UpcomingProducts
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">upcoming_product_subscription_listing.upcoming_product_subscription_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <buttons>
            <button name="add">
                <url path="*/*/clear_log"/>
                <class>primary</class>
                <label translate="true">Clear Log</label>
            </button>
        </buttons>
        <spinner>upcoming_product_subscription_columns</spinner>
        <deps>
            <dep>upcoming_product_subscription_listing.upcoming_product_subscription_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="upcoming_product_subscription_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Krish_UpcomingProducts::subscriptions</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="upcoming_product_subscription_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <columns name="upcoming_product_subscription_columns">
        <selectionsColumn name="ids">
            <settings>
                <indexField>id</indexField>
            </settings>
        </selectionsColumn>
        <column name="id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="product_sku">
            <settings>
                <filter>text</filter>
                <label translate="true">Product SKU</label>
            </settings>
        </column>
        <column name="email">
            <settings>
                <filter>text</filter>
                <label translate="true">Email</label>
            </settings>
        </column>
        <column name="name">
            <settings>
                <filter>text</filter>
                <label translate="true">Name</label>
            </settings>
        </column>
        <column name="is_subscribe_mail_sent">
            <settings>
                <filter>select</filter>
                <options class="Krish\UpcomingProducts\Model\Source\Status"/>
                <label translate="true">Subscribe Email Sent</label>
            </settings>
        </column>
        <column name="is_reminder_mail_sent">
            <settings>
                <filter>select</filter>
                <options class="Krish\UpcomingProducts\Model\Source\Status"/>
                <label translate="true">Reminder Email Sent</label>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created At</label>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated At</label>
            </settings>
        </column>
    </columns>
</listing>
