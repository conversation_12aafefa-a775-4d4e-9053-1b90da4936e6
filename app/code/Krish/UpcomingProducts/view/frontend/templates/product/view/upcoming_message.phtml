<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

/** @var \Krish\UpcomingProducts\Block\Product\View\UpcomingMessage $block */
/** @var \Magento\Framework\Escaper $escaper */

if ($block->isUpcomingProduct() && $block->isSubscribeFormDisplay()):
    $product = $block->getProduct();
    $sku = $product->getSku();
    ?>
    <div class="upcoming-product-message">
        <span><?= $escaper->escapeHtml(__($block->getUpcomingMessage())) ?></span>
    </div>
    <?php if ($block->isLoggedIn()): ?>
        <div class="actions">
            <?php $isAlreadySubscribed = $block->isCustomerAlreadySubscribed(); ?>
            <button type="button"
                    title="<?= $escaper->escapeHtmlAttr(__($isAlreadySubscribed ? 'Subscribed' : 'Notify Me')) ?>"
                    class="action primary notify-me-logged-in"
                    data-product-sku="<?= $escaper->escapeHtmlAttr($product->getSku()) ?>"
                    data-name="<?= $escaper->escapeHtmlAttr($block->getCustomerName()) ?>"
                    data-email="<?= $escaper->escapeHtmlAttr($block->getCustomerEmail()) ?>"
                    data-store-id="<?= $escaper->escapeHtmlAttr($block->getStoreId()) ?>"
                    <?= $isAlreadySubscribed ? 'disabled="disabled"' : '' ?>>
                <span><?= $escaper->escapeHtml(__($isAlreadySubscribed ? 'Subscribed' : 'Notify Me')) ?></span>
            </button>
        </div>
        <script type="text/javascript">
            require([
                "jquery"
            ], function($) {
                $(".notify-me-logged-in").on('click', function() {
                    var button = $(this);
                    var data = {
                        product_sku: button.data('product-sku'),
                        name: button.data('name'),
                        email: button.data('email'),
                        store_id: button.data('store-id')
                    };

                    $.ajax({
                        url: '<?= $escaper->escapeJs($block->getUrl('upcomingproducts/notify/subscribe')) ?>',
                        data: data,
                        type: 'post',
                        dataType: 'json',
                        showLoader: true,
                        beforeSend: function() {
                            button.prop('disabled', true);
                        },
                        success: function(response) {
                            if (response.success) {
                                $('<div class="message-success success message"><div>' + response.message + '</div></div>').insertAfter('.upcoming-product-message');
                                button.prop('disabled', true).text('<?= $escaper->escapeJs(__('Subscribed')) ?>');
                            } else {
                                $('<div class="message-error error message"><div>' + response.message + '</div></div>').insertAfter('.upcoming-product-message');
                                button.prop('disabled', false);
                            }
                        },
                        error: function() {
                            $('<div class="message-error error message"><div><?= $escaper->escapeJs(__('An error occurred while processing your request.')) ?></div></div>').insertAfter('.upcoming-product-message');
                            button.prop('disabled', false);
                        }
                    });
                });
            });
        </script>
    <?php else: ?>
        <button id="notify-me-button" class="action primary"><?= $escaper->escapeHtml(__($block->getButtonText())) ?></button>
        <div id="notify-me-form-container">
            <form id="notify-me-form" action="<?= $escaper->escapeUrl($block->getUrl('upcomingproducts/notify/subscribe')) ?>" method="post">
                <input type="hidden" name="formKey" value="<?= $escaper->escapeHtmlAttr($block->getFormKey()) ?>">
                <input type="hidden" name="store_id" value="<?= $escaper->escapeHtmlAttr($block->getStoreId()) ?>">
                <input type="hidden" name="product_sku" value="<?= $escaper->escapeHtmlAttr($product->getSku()) ?>">
                <input type="hidden" name="check_subscription" value="1">
                <div class="field name required">
                    <label class="label" for="name"><span><?= $escaper->escapeHtml(__('Name')) ?></span></label>
                    <div class="control">
                        <input type="text" name="name" id="name" class="input-text" data-validate="{required:true}">
                    </div>
                </div>
                <div class="field email required">
                    <label class="label" for="email"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label>
                    <div class="control">
                        <input type="email" name="email" id="email" class="input-text" data-validate="{required:true, 'validate-email':true}">
                    </div>
                </div>
                <div class="actions-toolbar">
                    <div class="primary">
                        <button type="submit" class="action submit primary">
                            <span><?= $escaper->escapeHtml(__('Notify Me')) ?></span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <script type="text/x-magento-init">
            {
                "#notify-me-form": {
                    "validation": {}
                }
            }
        </script>
        <script type="text/javascript">
            require([
                "jquery",
                "Magento_Ui/js/modal/modal",
                "mage/validation"
            ],function($, modal) {

                var options = {
                    type: 'popup',
                    responsive: true,
                    title: $.mage.__('Upcoming Product Subscription'),
                    buttons: []
                };
                var popup = modal(options, $('#notify-me-form-container'));
                $("#notify-me-button").click(function() {
                    popup.openModal();
                });

                // Store the form selector to avoid duplication
                var notifyForm = $('#notify-me-form');

                // Initialize validation with proper options
                notifyForm.validation({
                    errorPlacement: function(error, element) {
                        var parent = element.parent();
                        if (parent.hasClass('control')) {
                            parent.append(error);
                        } else {
                            element.after(error);
                        }
                    }
                });

                notifyForm.on('submit', function(e) {
                    e.preventDefault();
                    var form = $(this);

                    if (form.valid()) {
                        submitForm(form);
                    }
                });

                // Function to handle form submission
                function submitForm(form) {
                    $.ajax({
                        url: form.attr('action'),
                        data: form.serialize(),
                        type: 'post',
                        dataType: 'json',
                        showLoader: true,
                        success: function(response) {
                            if (response.success) {
                                popup.closeModal();
                                $('<div class="message-success success message"><div>' + response.message + '</div></div>').insertAfter('.upcoming-product-message');
                            } else {
                                $('<div class="message-error error message"><div>' + response.message + '</div></div>').insertAfter('#notify-me-form');
                            }
                        }
                    });
                }
            });
        </script>
    <?php endif; ?>
<?php endif; ?>
