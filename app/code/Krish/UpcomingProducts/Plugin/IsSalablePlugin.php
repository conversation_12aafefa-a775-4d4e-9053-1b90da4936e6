<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
declare(strict_types=1);

namespace Krish\UpcomingProducts\Plugin;

use Magento\Catalog\Model\Product;
use Krish\UpcomingProducts\Model\Config;

/**
 * Plugin to prevent upcoming products from being salable
 */
class IsSalablePlugin
{
    /**
     * @param Config $config
     */
    public function __construct(
        private readonly Config $config
    ) {
    }

    /**
     * After plugin for isSalable method to prevent upcoming products from being purchased
     *
     * @param Product $subject
     * @param bool $result
     * @return bool
     */
    public function afterIsSalable(Product $subject, bool $result): bool
    {
        if ($this->config->isModuleEnabled() && $subject->getIsUpcoming()) {
            return false;
        }
        return $result;
    }
}
