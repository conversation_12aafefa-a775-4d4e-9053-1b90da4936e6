<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
declare(strict_types=1);

namespace Krish\UpcomingProducts\Model;

use Exception;
use Magento\Framework\App\Area;
use Magento\Framework\Exception\MailException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Psr\Log\LoggerInterface;

/**
 * Email Model
 */
class Email
{
    /**
     * @var array
     */
    protected array $variables = [];

    /**
     * @param StateInterface $inlineTranslation
     * @param TransportBuilder $transportBuilder
     * @param Config $config
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly StateInterface $inlineTranslation,
        private readonly TransportBuilder $transportBuilder,
        private readonly Config $config,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Send subscription confirmation email
     *
     * @param string $email
     * @param int $storeId
     * @return bool
     */
    public function sendSubscribeEmail(string $email, int $storeId = 0): bool
    {
        $result = false;
        if ($this->config->isSubscriptionEmailEnabled()) {
            $templateId = $this->config->getSubscriptionEmailTemplate();
            $senderIdentity = $this->config->getSubscriptionEmailIdentity();
            if ($templateId && $senderIdentity) {
                $result = $this->send($templateId, $senderIdentity, $email, $storeId);
            } else {
                $this->logger->error('Email template or sender not configured.');
            }
        }
        return $result;
    }

    /**
     * Base method to send emails
     *
     * @param string $templateId
     * @param string $senderIdentity
     * @param string $recipientEmail
     * @param int $storeId
     * @return bool
     */
    public function send(string $templateId, string $senderIdentity, string $recipientEmail, int $storeId = 0): bool
    {
        try {
            $this->inlineTranslation->suspend();
            $transport = $this->transportBuilder
                ->setTemplateIdentifier($templateId)
                ->setTemplateOptions(['area' => Area::AREA_FRONTEND, 'store' => $storeId])
                ->setTemplateVars($this->getVariables())
                ->setFromByScope(
                    $senderIdentity,
                    $storeId
                )->addTo($recipientEmail)
                ->getTransport();

            // Send email
            $transport->sendMessage();
            $this->inlineTranslation->resume();
            return true;
        } catch (Exception $e) {
            $this->logger->error("Failed to send email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get variables
     *
     * @return array
     */
    public function getVariables(): array
    {
        return $this->variables;
    }

    /**
     * Set variables
     *
     * @param array $variables
     * @return $this
     */
    public function setVariables(array $variables): self
    {
        $this->variables = $variables;
        return $this;
    }

    /**
     * Send reminder email
     *
     * @param string $email
     * @param int $storeId
     * @return bool
     */
    public function sendReminderEmail(string $email, int $storeId = 0): bool
    {
        $result = false;
        if ($this->config->isReminderEmailEnabled()) {
            $templateId = $this->config->getReminderEmailTemplate();
            $senderIdentity = $this->config->getReminderEmailIdentity();

            if ($templateId && $senderIdentity) {
                $result = $this->send($templateId, $senderIdentity, $email, $storeId);
            } else {
                $this->logger->error('Reminder email template or sender not configured.');
            }
        }
        return $result;
    }
}
