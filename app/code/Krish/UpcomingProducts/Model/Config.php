<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
declare(strict_types=1);

namespace Krish\UpcomingProducts\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Phrase;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

/**
 * Configuration provider for upcoming products
 */
class Config
{
    /**
     * Configuration paths
     */
    public const XML_PATH_MODULE_ENABLED = 'krish_upcoming_products/general/enabled';
    public const XML_PATH_AUTO_UPDATE_ENABLED = 'krish_upcoming_products/general/auto_update';
    public const XML_PATH_SUBSCRIPTION_ENABLED = 'krish_upcoming_products/subscription/enabled';
    public const XML_PATH_SUBSCRIPTION_BUTTON_TEXT = 'krish_upcoming_products/subscription/button_text';
    public const XML_PATH_SUBSCRIPTION_EMAIL_ENABLED = 'krish_upcoming_products/subscription/subscription_email/enabled';
    public const XML_PATH_SUBSCRIPTION_REMINDER_EMAIL_ENABLED = 'krish_upcoming_products/subscription/reminder_email/enabled';
    public const XML_PATH_SUBSCRIPTION_REMINDER_EMAIL_TEMPLATE = 'krish_upcoming_products/subscription/reminder_email/template';
    public const XML_PATH_SUBSCRIPTION_REMINDER_EMAIL_IDENTITY = 'krish_upcoming_products/subscription/reminder_email/identity';
    public const XML_PATH_SUBSCRIPTION_REMINDER_EMAIL_BCC = 'krish_upcoming_products/subscription/reminder_email/bcc';
    public const XML_PATH_SUBSCRIPTION_EMAIL_TEMPLATE = 'krish_upcoming_products/subscription/subscription_email/template';
    public const XML_PATH_SUBSCRIPTION_EMAIL_IDENTITY = 'krish_upcoming_products/subscription/subscription_email/identity';
    public const XML_PATH_SUBSCRIPTION_EMAIL_BCC = 'krish_upcoming_products/subscription/subscription_email/bcc';

    /**
     * Default email templates
     */
    public const DEFAULT_SUBSCRIPTION_EMAIL_TEMPLATE = 'krish_upcoming_products_subscription_subscription_email_template';
    public const DEFAULT_REMINDER_EMAIL_TEMPLATE = 'krish_upcoming_products_subscription_reminder_email_template';

    /**
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Check if the module is enabled
     *
     * @return bool
     */
    public function isModuleEnabled(): bool
    {
        $enabled = (bool)$this->scopeConfig->getValue(self::XML_PATH_MODULE_ENABLED, ScopeInterface::SCOPE_STORE);
        $this->logger->debug("Module enabled: " . ($enabled ? 'Yes' : 'No'));
        return $enabled;
    }

    /**
     * Check if auto update is enabled
     *
     * @return bool
     */
    public function isAutoUpdateEnabled(): bool
    {
        return (bool)$this->scopeConfig->getValue(self::XML_PATH_AUTO_UPDATE_ENABLED, ScopeInterface::SCOPE_STORE);
    }

    /**
     * Check if a subscription is enabled
     *
     * @return bool
     */
    public function isSubscriptionEnabled(): bool
    {
        return (bool)$this->scopeConfig->getValue(self::XML_PATH_SUBSCRIPTION_ENABLED, ScopeInterface::SCOPE_STORE);
    }

    /**
     * Check if subscription email is enabled
     *
     * @return bool
     */
    public function isSubscriptionEmailEnabled(): bool
    {
        $enabled = (bool)$this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_EMAIL_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
        $this->logger->debug("Subscription email enabled: " . ($enabled ? 'Yes' : 'No'));
        return $enabled;
    }

    /**
     * Check if reminder email is enabled
     *
     * @return bool
     */
    public function isReminderEmailEnabled(): bool
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_REMINDER_EMAIL_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get subscription button text
     *
     * @param int|null $storeId
     * @return Phrase
     */
    public function getSubscriptionButtonText(?int $storeId = null): Phrase
    {
        $configValue = $this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_BUTTON_TEXT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
        return $configValue ? __($configValue) : __('Notify Me');
    }

    /**
     * Get subscription email template
     *
     * @param int|null $storeId
     * @return string
     */
    public function getSubscriptionEmailTemplate(?int $storeId = null): string
    {
        $template = (string)$this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_EMAIL_TEMPLATE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: self::DEFAULT_SUBSCRIPTION_EMAIL_TEMPLATE;
        $this->logger->debug("Subscription email template: {$template}");
        return $template;
    }

    /**
     * Get subscription email identity
     *
     * @param int|null $storeId
     * @return string
     */
    public function getSubscriptionEmailIdentity(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_EMAIL_IDENTITY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: 'general';
    }

    /**
     * Get subscription email BCC recipients
     *
     * @param int|null $storeId
     * @return array
     */
    public function getSubscriptionEmailBcc(?int $storeId = null): array
    {
        $bcc = $this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_EMAIL_BCC,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
        if ($bcc && is_string($bcc)) {
            return explode(',', $bcc);
        }
        return [];
    }

    /**
     * Get reminder email template
     *
     * @param int|null $storeId
     * @return string
     */
    public function getReminderEmailTemplate(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_REMINDER_EMAIL_TEMPLATE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: self::DEFAULT_REMINDER_EMAIL_TEMPLATE;
    }

    /**
     * Get reminder email identity
     *
     * @param int|null $storeId
     * @return string
     */
    public function getReminderEmailIdentity(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_REMINDER_EMAIL_IDENTITY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: 'general';
    }

    /**
     * Get reminder email BCC recipients
     *
     * @param int|null $storeId
     * @return array
     */
    public function getReminderEmailBcc(?int $storeId = null): array
    {
        $bcc = $this->scopeConfig->getValue(
            self::XML_PATH_SUBSCRIPTION_REMINDER_EMAIL_BCC,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
        if ($bcc && is_string($bcc)) {
            return explode(',', $bcc);
        }
        return [];
    }

    /**
     * Get product upcoming message
     *
     * @return Phrase
     */
    public function getProductUpcomingMessage(): Phrase
    {
        return __('This product is scheduled for upcoming release.');
    }

    /**
     * Get email sender information
     *
     * @param string $identity
     * @return array
     */
    public function getEmailSender(string $identity): array
    {
        $path = 'trans_email/ident_' . $identity . '/';
        $sender = [
            'name' => $this->scopeConfig->getValue($path . 'name', ScopeInterface::SCOPE_STORE),
            'email' => $this->scopeConfig->getValue($path . 'email', ScopeInterface::SCOPE_STORE)
        ];

        $this->logger
            ->debug("Email sender for identity '{$identity}': " . print_r($sender, true));

        // Validate sender information
        if (empty($sender['name']) || empty($sender['email'])) {
            $this->logger->error(
                "Invalid sender configuration for identity '{$identity}'. Name or email is missing."
            );
        }

        return $sender;
    }
}
