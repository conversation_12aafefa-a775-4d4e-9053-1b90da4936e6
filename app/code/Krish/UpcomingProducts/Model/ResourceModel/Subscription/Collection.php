<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
declare(strict_types=1);

namespace Krish\UpcomingProducts\Model\ResourceModel\Subscription;

use <PERSON><PERSON>\UpcomingProducts\Model\Subscription as SubscriptionModel;
use Krish\UpcomingProducts\Model\ResourceModel\Subscription as SubscriptionResource;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * Collection for Upcoming Product Subscriptions
 */
class Collection extends AbstractCollection
{
    /**
     * Define resource model and model classes
     *
     * @return void
     * @SuppressWarnings(PHPMD.CamelCaseMethodName)
     */
    protected function _construct(): void
    {
        $this->_init(
            SubscriptionModel::class,
            SubscriptionResource::class
        );
    }
}
