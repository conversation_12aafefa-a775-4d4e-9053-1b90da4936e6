<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
declare(strict_types=1);

namespace Krish\UpcomingProducts\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Status source model for subscription email status
 */
class Status implements OptionSourceInterface
{
    /**
     * Get an option array for email status
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => 0, 'label' => __('Sent')],
            ['value' => 1, 'label' => __('Not Sent')],
        ];
    }
}
