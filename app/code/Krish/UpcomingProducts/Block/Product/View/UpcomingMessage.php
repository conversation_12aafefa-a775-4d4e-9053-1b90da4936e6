<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
declare(strict_types=1);

namespace Krish\UpcomingProducts\Block\Product\View;

use Exception;
use Krish\UpcomingProducts\Model\Config;
use Krish\UpcomingProducts\Service\SubscriptionService;
use Magento\Catalog\Model\Product;
use Magento\Framework\Data\Form\FormKey;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

/**
 * UpcomingMessage Block
 */
class UpcomingMessage extends Template
{

    /**
     * @param Context $context
     * @param Registry $registry
     * @param FormKey $formKey
     * @param Config $config
     * @param SubscriptionService $subscriptionService
     * @param array $data
     */
    public function __construct(
        Template\Context          $context,
        private Registry          $registry,
        private FormKey           $formKey,
        private Config            $config,
        private SubscriptionService $subscriptionService,
        array                     $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Get a form key
     *
     * @return string
     * @throws LocalizedException
     */
    public function getFormKey(): string
    {
        return $this->formKey->getFormKey();
    }

    /**
     * Get store id
     *
     * @return int
     * @throws NoSuchEntityException
     */
    public function getStoreId(): int
    {
        return (int)$this->_storeManager->getStore()->getId();
    }

    /**
     * If a product is upcoming
     *
     * @return bool
     */
    public function isUpcomingProduct(): bool
    {
        if (!$this->config->isModuleEnabled()) {
            return false;
        }
        $product = $this->getProduct();
        return $product && $product->getIsUpcoming();
    }

    /**
     * Get current product
     *
     * @return Product|mixed|null
     */
    public function getProduct()
    {
        return $this->registry->registry('current_product');
    }

    /**
     * Get an upcoming message
     *
     * @return string
     */
    public function getUpcomingMessage(): string
    {
        return (string)$this->config->getProductUpcomingMessage();
    }

    /**
     * Get button text
     *
     * @return string
     */
    public function getButtonText(): string
    {
        return (string)$this->config->getSubscriptionButtonText();
    }

    /**
     * Check if the subscribed form should be displayed
     *
     * @return bool
     */
    public function isSubscribeFormDisplay(): bool
    {
        return $this->config->isSubscriptionEnabled();
    }

    /**
     * Get a customer name
     *
     * @return string
     * @throws LocalizedException
     */
    public function getCustomerName(): string
    {
        return $this->subscriptionService->getCustomerName();
    }

    /**
     * Check if the customer is already subscribed
     *
     * @return bool
     */
    public function isCustomerAlreadySubscribed(): bool
    {
        if (!$this->isLoggedIn()) {
            return false;
        }
        try {
            $productSku = $this->getProduct()->getSku();
            return $this->subscriptionService->isCustomerSubscribed($productSku);
        } catch (Exception $e) {
            $this->_logger->critical($e);
            return false;
        }
    }

    /**
     * Check if a customer is logged in
     *
     * @return bool
     */
    public function isLoggedIn(): bool
    {
        return $this->subscriptionService->isLoggedIn();
    }

    /**
     * Get customer email
     *
     * @return string
     */
    public function getCustomerEmail(): string
    {
        return $this->subscriptionService->getCustomerEmail();
    }
}
