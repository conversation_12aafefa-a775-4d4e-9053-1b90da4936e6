<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_UpcomingProducts
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="krish_upcoming_product_subscriptions" resource="default" engine="innodb"
           comment="Upcoming Product Subscriptions">
        <column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="ID"/>
        <column xsi:type="varchar" name="product_sku" nullable="false" length="64" comment="SKU"/>
        <column xsi:type="varchar" name="email" nullable="false" length="255" comment="Email"/>
        <column xsi:type="varchar" name="name" nullable="false" length="255" comment="Name"/>
        <column xsi:type="smallint" name="store_id" padding="5" unsigned="true" nullable="false" comment="Store ID"/>
        <column xsi:type="smallint" name="is_subscribe_mail_sent" nullable="false" default="0"
                comment="Is Subscribe Email Sent"/>
        <column xsi:type="smallint" name="is_reminder_mail_sent" nullable="false" default="0"
                comment="Is Reminder Email Sent"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="KRISH_UPCOMING_PRODUCT_SUBSCRIPTIONS_PRODUCT_ID_CATALOG_PRODUCT_ENTITY_SKU"
                    table="krish_upcoming_product_subscriptions" column="product_sku"
                    referenceTable="catalog_product_entity" referenceColumn="sku" onDelete="CASCADE"/>
        <constraint xsi:type="foreign" referenceId="KRISH_UPCOMING_PRODUCT_SUBSCRIPTIONS_STORE_ID_STORE_STORE_ID"
                    table="krish_upcoming_product_subscriptions" column="store_id"
                    referenceTable="store" referenceColumn="store_id" onDelete="CASCADE"/>
        <index referenceId="KRISH_UPCOMING_PRODUCT_SUBSCRIPTIONS_PRODUCT_ID" indexType="btree">
            <column name="product_sku"/>
        </index>
        <index referenceId="KRISH_UPCOMING_PRODUCT_SUBSCRIPTIONS_STORE_ID" indexType="btree">
            <column name="store_id"/>
        </index>
    </table>
</schema>
