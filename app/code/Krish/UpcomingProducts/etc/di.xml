<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_UpcomingProducts
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="upcoming_product_subscription_listing_data_source" xsi:type="string">
                    Krish\UpcomingProducts\Model\ResourceModel\Subscription\Grid\Collection
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Krish\UpcomingProducts\Model\ResourceModel\Subscription\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">krish_upcoming_product_subscriptions</argument>
            <argument name="eventPrefix" xsi:type="string">krish_upcoming_product_subscriptions_grid_collection
            </argument>
            <argument name="eventObject" xsi:type="string">upcoming_product_subscriptions_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Krish\UpcomingProducts\Model\ResourceModel\Subscription
            </argument>
        </arguments>
    </type>
    <type name="Magento\Catalog\Model\Product">
        <plugin name="krish_upcomingproducts_is_salable" type="Krish\UpcomingProducts\Plugin\IsSalablePlugin"/>
    </type>

    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="krish_upcomingproducts_test_email" xsi:type="object">
                    Krish\UpcomingProducts\Console\Command\TestEmail
                </item>
                <item name="krish_upcomingproducts_check_product" xsi:type="object">
                    Krish\UpcomingProducts\Console\Command\CheckProduct
                </item>
            </argument>
        </arguments>
    </type>
</config>
