<?xml version="1.0"?>
<!--
  ~
  ~  @category   Krish Technolabs Module Development
  ~  @package    Krish_UpcomingProducts
  ~  <AUTHOR> Technolabs Pvt Ltd.
  ~  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="krish_upcoming_products" translate="label" type="text" sortOrder="10" showInDefault="1"
                 showInWebsite="1" showInStore="1">
            <label>Upcoming Products</label>
            <tab>krish</tab>
            <resource>Krish_UpcomingProducts::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0"
                   showInStore="0">
                <label>General Configuration</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="0"
                       showInStore="0">
                    <frontend_class>on-off-trigger</frontend_class>
                    <label>Enable Module</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="auto_update" translate="label" type="select" sortOrder="20" showInDefault="1"
                       showInWebsite="0" showInStore="0">
                    <frontend_class>on-off-trigger</frontend_class>
                    <label>Auto Update Products</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>
                        <![CDATA[If enabled, the <strong>upcoming flag</strong> will be <em>automatically disabled</em> from the products when the <strong>release date</strong> has passed. This ensures <em>timely updates</em> of product status.]]></comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
            <group id="subscription" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Product Subscription Configuration</label>
                <depends>
                    <field id="krish_upcoming_products/general/enabled">1</field>
                </depends>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <frontend_class>on-off-trigger</frontend_class>
                    <label>Enable Subscription</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="button_text" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Subscribe Button Text</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <group id="subscription_email" translate="label" type="text" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Send Subscribed Email</label>
                    <depends>
                        <field id="krish_upcoming_products/subscription/enabled">1</field>
                    </depends>
                    <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1"
                           showInWebsite="1" showInStore="1">
                        <frontend_class>on-off-trigger</frontend_class>
                        <label>Enable Subscribe Email</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="template" translate="label comment" type="select" sortOrder="20" showInDefault="1"
                           showInWebsite="1" showInStore="1" canRestore="1">
                        <label>Subscribe Email Template</label>
                        <comment>Email template chosen based on theme fallback when "Default" option is selected.
                        </comment>
                        <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                        <depends>
                            <field id="enabled">1</field>
                        </depends>
                    </field>
                    <field id="identity" translate="label" type="select" sortOrder="30" showInDefault="1"
                           showInWebsite="1" showInStore="1" canRestore="1">
                        <label>Subscribe Email Sender</label>
                        <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                        <depends>
                            <field id="enabled">1</field>
                        </depends>
                    </field>
                    <field id="bcc" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1"
                           showInStore="1">
                        <label>Send BCC To</label>
                        <validate>validate-emails</validate>
                        <comment>Comma-separated</comment>
                        <depends>
                            <field id="enabled">1</field>
                        </depends>
                    </field>
                </group>
                <group id="reminder_email" translate="label" type="text" sortOrder="40" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <depends>
                        <field id="krish_upcoming_products/subscription/enabled">1</field>
                    </depends>
                    <label>Send Product Reminder Email</label>
                    <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1"
                           showInWebsite="1" showInStore="1">
                        <frontend_class>on-off-trigger</frontend_class>
                        <label>Enable Reminder Email</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="template" translate="label comment" type="select" sortOrder="20" showInDefault="1"
                           showInWebsite="1" showInStore="1" canRestore="1">
                        <label>Reminder Email Template</label>
                        <comment>Email template chosen based on theme fallback when "Default" option is selected.
                        </comment>
                        <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                        <depends>
                            <field id="enabled">1</field>
                        </depends>
                    </field>
                    <field id="identity" translate="label" type="select" sortOrder="30" showInDefault="1"
                           showInWebsite="1" showInStore="1" canRestore="1">
                        <label>Reminder Email Sender</label>
                        <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                        <depends>
                            <field id="enabled">1</field>
                        </depends>
                    </field>
                    <field id="bcc" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1"
                           showInStore="1">
                        <label>Send BCC To</label>
                        <validate>validate-emails</validate>
                        <comment>Comma-separated</comment>
                        <depends>
                            <field id="enabled">1</field>
                        </depends>
                    </field>
                </group>
            </group>
        </section>
    </system>
</config>
