<?php
declare(strict_types=1);

namespace <PERSON>h\UpcomingProducts\Service;

use Exception;
use <PERSON><PERSON>\UpcomingProducts\Model\ResourceModel\Subscription\CollectionFactory;
use Magento\Customer\Model\Context as CustomerContext;
use Magento\Customer\Model\Session;
use Magento\Customer\Model\SessionFactory as CustomerSession;
use Magento\Framework\App\Http\Context as HttpContext;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * SubscriptionService class
 */
class SubscriptionService
{
    /**
     * @param CustomerSession $customerSession
     * @param HttpContext $httpContext
     * @param CollectionFactory $subscriptionCollectionFactory
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private CustomerSession $customerSession,
        private HttpContext $httpContext,
        private CollectionFactory $subscriptionCollectionFactory,
        private StoreManagerInterface $storeManager,
        private LoggerInterface $logger
    ) {
    }

    /**
     * @return int
     */
    public function getStoreId(): int
    {
        try {
            return (int)$this->storeManager->getStore()->getId();
        } catch (NoSuchEntityException $e) {
            $this->logger->error($e->getMessage());
            return 0;
        }
    }

    /**
     * @return string
     * @throws LocalizedException
     */
    public function getCustomerName(): string
    {
        $customerName = '';
        if ($customer = $this->getCustomerSession()->getCustomer()) {
            $customerName = $customer->getName();
            if ($customerName != ' ') {
                return $customerName;
            }
        }
        return $customerName;
    }

    /**
     * @param string $productSku
     * @return bool
     */
    public function isCustomerSubscribed(string $productSku): bool
    {
        try {
            $customerEmail = $this->getCustomerEmail();

            $collection = $this->subscriptionCollectionFactory->create()
                ->addFieldToFilter('product_sku', $productSku)
                ->addFieldToFilter('email', $customerEmail)
                ->addFieldToFilter('is_reminder_mail_sent', 0);
            return $collection->getSize() > 0;
        } catch (Exception $e) {
            $this->logger->critical($e);
            return false;
        }
    }

    /**
     * @return bool
     */
    public function isLoggedIn(): bool
    {
        return (bool)$this->httpContext->getValue(CustomerContext::CONTEXT_AUTH);
    }

    /**
     * @return string
     */
    public function getCustomerEmail(): string
    {
        $customerEmail = '';
        if ($customer = $this->getCustomerSession()->getCustomer()) {
            $customerEmail = $customer->getEmail();
            if ($customerEmail != '' || $customerEmail != null) {
                return $customerEmail;
            }
        }
        return $customerEmail;
    }

    /**
     * @return Session
     */
    private function getCustomerSession(): Session
    {
        return $this->customerSession->create();
    }
}
