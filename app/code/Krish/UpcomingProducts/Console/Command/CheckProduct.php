<?php
declare(strict_types=1);
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\UpcomingProducts\Console\Command;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Command to check product upcoming status
 */
class CheckProduct extends Command
{
    private const COMMAND_NAME = 'krish:upcomingproducts:check-product';
    private const SKU_OPTION = 'sku';

    /**
     * @param ProductRepositoryInterface $productRepository
     * @param LoggerInterface $logger
     * @param string|null $name
     */
    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly LoggerInterface $logger,
        string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @inheritDoc
     */
    protected function configure(): void
    {
        $this->setName(self::COMMAND_NAME)
            ->setDescription('Check if a product is marked as upcoming')
            ->addOption(
                self::SKU_OPTION,
                's',
                InputOption::VALUE_REQUIRED,
                'Product SKU to check'
            );

        parent::configure();
    }

    /**
     * @inheritDoc
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $sku = $input->getOption(self::SKU_OPTION);

        if (!$sku) {
            $output->writeln('<error>Product SKU is required</error>');
            return Command::FAILURE;
        }

        try {
            $product = $this->productRepository->get($sku);
            $output->writeln("<info>Found product: {$product->getName()}</info>");

            return $this->displayProductUpcomingStatus($product, $output);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            $output->writeln("<error>Error: {$e->getMessage()}</error>");
            return Command::FAILURE;
        }
    }

    /**
     * Display product upcoming status and attributes
     *
     * @param ProductInterface $product
     * @param OutputInterface $output
     * @return int
     */
    private function displayProductUpcomingStatus(ProductInterface $product, OutputInterface $output): int
    {
        $isUpcoming = $product->getCustomAttribute('is_upcoming')
            ? $product->getCustomAttribute('is_upcoming')->getValue()
            : $product->getData('is_upcoming');

        $output->writeln("<info>Is Upcoming attribute value: " . var_export($isUpcoming, true) . "</info>");

        if ($isUpcoming === null) {
            $output->writeln("<comment>The is_upcoming attribute is not set for this product</comment>");
        } elseif ($isUpcoming == '1' || $isUpcoming === true) {
            $output->writeln("<info>This product IS marked as upcoming</info>");
        } else {
            $output->writeln("<info>This product is NOT marked as upcoming</info>");
        }

        $this->displayAllProductAttributes($product, $output);

        return Command::SUCCESS;
    }

    /**
     * Display all product attributes for debugging
     *
     * @param ProductInterface $product
     * @param OutputInterface $output
     * @return void
     */
    private function displayAllProductAttributes(ProductInterface $product, OutputInterface $output): void
    {
        $output->writeln("<info>All product attributes:</info>");
        $attributes = $product->getData();

        foreach ($attributes as $code => $value) {
            if (is_scalar($value)) {
                $output->writeln("  {$code}: " . var_export($value, true));
            }
        }
    }
}
