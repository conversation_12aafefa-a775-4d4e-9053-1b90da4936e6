<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_UpcomingProducts
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\UpcomingProducts\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Krish\UpcomingProducts\Model\Email;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Psr\Log\LoggerInterface;
use Magento\Framework\App\State;
use Magento\Framework\App\Area;

/**
 * Command to test email sending for upcoming products
 */
class TestEmail extends Command
{
    /**
     * @var Email
     */
    private $emailHelper;

    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var State
     */
    private $appState;

    /**
     * @param Email $emailHelper
     * @param ProductRepositoryInterface $productRepository
     * @param LoggerInterface $logger
     * @param State $appState
     * @param string|null $name
     */
    public function __construct(
        Email $emailHelper,
        ProductRepositoryInterface $productRepository,
        LoggerInterface $logger,
        State $appState,
        string $name = null
    ) {
        parent::__construct($name);
        $this->emailHelper = $emailHelper;
        $this->productRepository = $productRepository;
        $this->logger = $logger;
        $this->appState = $appState;
    }

    /**
     * @inheritDoc
     */
    protected function configure()
    {
        $this->setName('krish:upcomingproducts:test-email')
            ->setDescription('Test email sending for upcoming products')
            ->addOption(
                'email',
                'e',
                InputOption::VALUE_REQUIRED,
                'Email address to send test email to'
            )
            ->addOption(
                'sku',
                's',
                InputOption::VALUE_REQUIRED,
                'Product SKU to use in the email'
            )
            ->addOption(
                'type',
                't',
                InputOption::VALUE_REQUIRED,
                'Email type (subscription or reminder)',
                'subscription'
            );

        parent::configure();
    }

    /**
     * @inheritDoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $email = $input->getOption('email');
        $sku = $input->getOption('sku');
        $type = $input->getOption('type');

        if (!$email) {
            $output->writeln('<error>Email address is required</error>');
            return Command::FAILURE;
        }

        if (!$sku) {
            $output->writeln('<error>Product SKU is required</error>');
            return Command::FAILURE;
        }

        try {
            // Set area code to frontend
            try {
                $this->appState->setAreaCode(Area::AREA_FRONTEND);
            } catch (\Exception $e) {
                // Area might already be set
                $this->logger->info("Area is already set: " . $e->getMessage());
            }

            $product = $this->productRepository->get($sku);
            $output->writeln("<info>Found product: {$product->getName()}</info>");

            $this->emailHelper->setVariables([
                'product' => $product,
                'customer_name' => 'Test Customer'
            ]);

            $result = false;
            if ($type === 'subscription') {
                $output->writeln("<info>Sending subscription email...</info>");
                $result = $this->emailHelper->sendSubscribeEmail($email);
            } elseif ($type === 'reminder') {
                $output->writeln("<info>Sending reminder email...</info>");
                $result = $this->emailHelper->sendReminderEmail($email);
            } else {
                $output->writeln("<error>Invalid email type. Use 'subscription' or 'reminder'</error>");
                return Command::FAILURE;
            }

            if ($result) {
                $output->writeln("<info>Email sent successfully!</info>");
                return Command::SUCCESS;
            } else {
                $output->writeln("<error>Failed to send email. Check the logs for details.</error>");
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            $output->writeln("<error>Error: {$e->getMessage()}</error>");
            return Command::FAILURE;
        }
    }
}
