<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\Base\Controller\Adminhtml\Download;

use Magento\Backend\App\Action;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\Xml\Generator as XmlGenerator;

/**
 * Class Sql
 * @package Krish\Base\Controller\Adminhtml\Download
 */
class XmlGenerateAction extends Action implements HttpGetActionInterface
{
    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * @var FileFactory
     */
    private $fileFactory;

    /**
     * @var XmlGenerator
     */
    private $xmlGenerator;

    /**
     * @var string
     */
    protected $fileName = 'unknown';

    /**
     * @var string
     */
    protected $xmlRootNode = 'report';

    /**
     * XmlGenerateAction constructor.
     * @param Action\Context $context
     * @param Filesystem $filesystem
     * @param FileFactory $fileFactory
     * @param XmlGenerator $xmlGenerator
     */
    public function __construct(
        Action\Context $context,
        Filesystem $filesystem,
        FileFactory $fileFactory,
        XmlGenerator $xmlGenerator
    ) {
        parent::__construct($context);
        $this->filesystem = $filesystem;
        $this->fileFactory = $fileFactory;
        $this->xmlGenerator = $xmlGenerator;
    }

    /**
     * @return Redirect|\Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        return $this->executeDownloadAction([]);
    }

    /**
     * @param $data
     * @return Redirect|\Magento\Framework\App\ResponseInterface
     */
    public function executeDownloadAction($data)
    {
        try {
            $content = $this->xmlGenerator
                ->arrayToXml([$this->xmlRootNode => $data])
                ->getDom()
                ->saveXML();

            $tmpDir = $this->filesystem->getDirectoryWrite(DirectoryList::TMP);
            $filePath = $this->fileName . uniqid() . '.xml';
            $tmpDir->writeFile($filePath, $content);

            return $this->fileFactory->create(
                sprintf('%s.%s', $this->fileName . '_' . uniqid(), 'xml'),
                [
                    'type' => 'filename',
                    'value' => $filePath,
                    'rm' => true
                ],
                DirectoryList::TMP
            );
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setRefererUrl();

        return $resultRedirect;
    }
}
