<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\Base\Controller\Adminhtml\Download;

use Magento\Backend\App\Action;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\Xml\Generator as XmlGenerator;
use Krish\Base\Model\IndexReport;

/**
 * Class Indexer
 * @package Krish\Base\Controller\Adminhtml\Download
 */
class Indexer extends XmlGenerateAction implements HttpGetActionInterface
{
    /**
     * @var IndexReport
     */
    private $indexReport;

    /**
     * @var string
     */
    protected $fileName = 'indexer_report';

    /**
     * @var string
     */
    protected $xmlRootNode = 'indexerInfo';

    /**
     * Sql constructor.
     * @param Action\Context $context
     * @param Filesystem $filesystem
     * @param FileFactory $fileFactory
     * @param XmlGenerator $xmlGenerator
     * @param IndexReport $indexReport
     */
    public function __construct(
        Action\Context $context,
        Filesystem $filesystem,
        FileFactory $fileFactory,
        XmlGenerator $xmlGenerator,
        IndexReport $indexReport
    ) {
        parent::__construct($context, $filesystem, $fileFactory, $xmlGenerator);
        $this->indexReport = $indexReport;
    }

    /**
     * @return Redirect|\Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     * @throws \Exception
     */
    public function execute()
    {
        return $this->executeDownloadAction($this->indexReport->generate());
    }
}
