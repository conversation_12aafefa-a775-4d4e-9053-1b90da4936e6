<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\Base\Controller\Adminhtml\Download;

use Magento\Backend\App\Action;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\Xml\Generator as XmlGenerator;
use Krish\Base\Model\MySqlReport;

/**
 * Class Sql
 * @package Krish\Base\Controller\Adminhtml\Download
 */
class Sql extends XmlGenerateAction implements HttpGetActionInterface
{
    /**
     * @var MySqlReport
     */
    private $mySqlReport;

    /**
     * @var string
     */
    protected $fileName = 'sql_report';

    /**
     * @var string
     */
    protected $xmlRootNode = 'sqlInfo';

    /**
     * Sql constructor.
     * @param Action\Context $context
     * @param Filesystem $filesystem
     * @param FileFactory $fileFactory
     * @param XmlGenerator $xmlGenerator
     * @param MySqlReport $mySqlReport
     */
    public function __construct(
        Action\Context $context,
        Filesystem $filesystem,
        FileFactory $fileFactory,
        XmlGenerator $xmlGenerator,
        MySqlReport $mySqlReport
    ) {
        parent::__construct($context, $filesystem, $fileFactory, $xmlGenerator);
        $this->mySqlReport = $mySqlReport;
    }

    /**
     * @return Redirect|\Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        return $this->executeDownloadAction($this->mySqlReport->generate());
    }
}
