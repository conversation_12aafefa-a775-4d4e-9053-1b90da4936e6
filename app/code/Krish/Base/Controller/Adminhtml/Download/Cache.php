<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\Base\Controller\Adminhtml\Download;

use Magento\Backend\App\Action;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\Xml\Generator as XmlGenerator;
use Krish\Base\Model\CacheReport;
use Magento\Framework\App\Action\HttpGetActionInterface;

/**
 * Class Cache
 * @package Krish\Base\Controller\Adminhtml\Download
 */
class Cache extends XmlGenerateAction implements HttpGetActionInterface
{
    /**
     * @var CacheReport
     */
    private $cacheReport;

    /**
     * @var string
     */
    protected $fileName = 'cache_report';

    /**
     * @var string
     */
    protected $xmlRootNode = 'cacheInfo';

    /**
     * Sql constructor.
     * @param Action\Context $context
     * @param Filesystem $filesystem
     * @param FileFactory $fileFactory
     * @param XmlGenerator $xmlGenerator
     * @param CacheReport $cacheReport
     */
    public function __construct(
        Action\Context $context,
        Filesystem $filesystem,
        FileFactory $fileFactory,
        XmlGenerator $xmlGenerator,
        CacheReport $cacheReport
    ) {
        parent::__construct($context, $filesystem, $fileFactory, $xmlGenerator);
        $this->cacheReport = $cacheReport;
    }

    /**
     * @return Redirect|\Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     * @throws \Exception
     */
    public function execute()
    {
        return $this->executeDownloadAction($this->cacheReport->generate());
    }
}
