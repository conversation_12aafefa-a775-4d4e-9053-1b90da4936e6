/*
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

require(["jquery"], function ($) {
    "use strict";

    $(function () {
        // jQuery plugin for on/off switch
        $.fn.onoff = function (options) {
            // Default settings
            var defaults = {
                value: {
                    on: "",
                    off: "",
                },
                class: "krish-config-form-onoff",
                wrap: "container",
                button: "button",
            };

            // Merge default settings with user options
            var settings = $.extend(true, {}, defaults, options);

            // Apply the plugin to each matched element
            return $(this).each(function () {
                var $select = $(this);
                var containerClass = settings.class + "-" + settings.wrap;

                // Check if the element is a valid select with required options
                if (
                    !$select.is("select") ||
                    !$select.children("option[value=0]").length ||
                    !$select.children("option[value=1]").length ||
                    $select.parent("." + containerClass).length
                ) {
                    return $select;
                }

                // Create switch elements
                var $button = $("<div>").attr({
                    class: settings.class + "-" + settings.button,
                });
                var $container = $("<div>").attr({
                    id: $select.attr("id") + "_" + settings.wrap,
                    class: containerClass,
                });

                // Set initial state
                $container.toggleClass("checked", $select.val() !== "0");

                // Wrap select and add button
                $select.hide().wrap($container).before($button);
                $container = $select.parent();

                // Handle select change event
                $select.off("change.onofftrigger").on("change.onofftrigger", function (e) {
                    $container.toggleClass("disabled", $select.is(":disabled"));
                    if ($container.hasClass("disabled")) {
                        return e.preventDefault();
                    }
                    $container.toggleClass("checked", $select.val() !== "0");
                });

                // Handle container click event
                $container.off("click.onofftrigger").on("click.onofftrigger", function (e) {
                    if ($container.hasClass("disabled")) {
                        return e.preventDefault();
                    }
                    if ($select.is(":enabled")) {
                        var value = $select.val() !== "0" ? "0" : "1";
                        $select.val(value);
                        $select[0].dispatchEvent(new Event("change"));
                        $select.trigger("change");
                    }
                });

                return $select;
            });
        };

        // Apply the onoff plugin to elements with class 'on-off-trigger'
        $(".on-off-trigger").onoff();
    });
});
