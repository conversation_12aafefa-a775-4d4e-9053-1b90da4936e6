/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

.admin__menu .item-krish-menu.parent.level-0 {
    > a:before {
        content: '';
        background-image:url('@{baseDir}Krish_Base/images/ktpl_logo.svg');
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        height: 25px;
        margin-bottom: 5px;
    }
    .submenu .submenu-title {
        text-transform: capitalize;
        margin-left: 16px;
        line-height: 8rem;
        margin-bottom: 1rem;
        &:after {
            content: '';
            border-bottom: 4px solid #ffffff;
            height: 50px;
            position: absolute;
            left: 0;
            right: 20px;
            top: 40px;
        }
    }
}
.config-nav-block.krish-tab.krish-tab-container {
    > .admin__page-nav-title {
        > strong {
            margin-left: 25px;
            visibility: hidden;
        }&:before {
             content: '';
             background-image:url('@{baseDir}Krish_Base/images/Krish_logo_full.png');
             position: absolute;
             top: 8px;
             left: 0;
             height: 40px;
             width: 120px;
             background-repeat: no-repeat;
             background-size: contain;
             background-position: center;
         }
    }
}
.krish-card-container {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping of cards on smaller screens */
    justify-content: center; /* Center the cards horizontally */
    gap: 20px; /* Add space between cards */
    padding: 20px;
    .krish-card {
        background-color: #fff;
        border-radius: 12px;
        padding: 15px;
        width: 300px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease-in-out;
        text-align: center;
        overflow: auto;
        &:hover {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(-5px);
        }
        h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }
        p {
            font-size: 18px;
            color: #44a539;
            line-height: 1.6;
        }
        button {
            background: #000000;
            color: #ffffff;
            padding: 10px 20px;
            font-weight: bold;
            &:hover {
                background: #000000;
                color: #ffffff;
            }
        }
    }
}
.krish-info-container {
    &.shadow-box {
        padding: 25px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        width: 350px;
        text-align: center;
    }
    button {
        background: #000000;
        color: #ffffff;
        padding: 10px 20px;
        font-weight: bold;
    }
}

.krish-config-link {
    display: block;
    margin: 2rem 0;
    font-size: 1.8rem;
    cursor: pointer;
    font-weight: 600;
    color: #000000;
    background: #fffbbb;
    text-align: center;
    padding: 5rem 0 8rem 0;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    border: 14px solid #f8f8f8;
    a {
        font-weight: 600;
        color: #000000;
        &:hover {
            border-bottom: none;
        }
    }
}
