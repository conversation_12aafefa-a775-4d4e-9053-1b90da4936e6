/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */


@color-hex: #d5e9dc;

.krish-config-form-onoff-container {
  cursor: pointer;
  position: relative;
  display: inline-block;
  height: 26px;
  width: 60px;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
    border-radius: 30px;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -webkit-box-shadow: inset 1px 1px 6px 0px rgba(170, 157, 137, 0.16);
  box-shadow: inset 1px 1px 6px 0px rgba(170, 157, 137, 0.16);
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border: 1px solid #d2d2d2;
  background: #f8f8f8;
  .disabled {
    background-color: rgba(0, 0, 0, 0.4);
    &.checked {
      border-color: rgba(0, 0, 0, 0.4);
      background-color: rgba(0, 0, 0, 0.4);
    }
    .krish-config-form-onoff-button {
      color: rgba(0, 0, 0, 0.4) !important;
    }
  }
  &.checked {
    background: #52b007;
    border-color: #52b007;
    .krish-config-form-onoff-button {
      right: 4px;
      color: #52b007;
        left: 36px;
    }
  }
  .krish-config-form-onoff-button {
    background: #fff;
    border-radius: 50%;
    box-shadow: 1px 1px 4px 0px rgba(2, 2, 2, 0.21);
    box-sizing: border-box;
    color: #6b625a;
    display: inline-block;
    height: 18px;
    left: 4px;
    line-height: 10px;
    padding: 4px;
    position: absolute;
    text-align: center;
    text-rendering: auto;
    top: 3px;
    transition: all 350ms cubic-bezier(0, 0.89, 0.44, 1);
    -moz-border-radius: 50%;
    -moz-transition: all 350ms cubic-bezier(0, 0.89, 0.44, 1);
    -moz-osx-font-smoothing: grayscale;
    -o-transition: all 350ms cubic-bezier(0, 0.89, 0.44, 1);
    -webkit-border-radius: 50%;
    -webkit-box-shadow: 1px 1px 4px 0px rgba(2, 2, 2, 0.21);
    -webkit-font-smoothing: antialiased;
    -webkit-transition: all 350ms cubic-bezier(0, 0.89, 0.44, 1);
    width: 18px;
  }
  select {
    display: none !important;
  }
}
