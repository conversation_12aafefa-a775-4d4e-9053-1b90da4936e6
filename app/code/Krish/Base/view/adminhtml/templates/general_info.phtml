<?php
/*
 *
 *  @category   <PERSON>h Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */


/** @var \Krish\Base\Block\Adminhtml\System\Config\GeneralInformation $block  */
/** @var \Magento\Framework\Escaper $escaper */
?>
<div class="krish-card-container">
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Magento Version')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getMagentoVersion()) ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Magento Edition')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getMagentoEdition()) ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Magento Mode')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getCurrentMode()) ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Root Path')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getRootPath()) ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Server User')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getServerUser()) ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('PHP Version')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getPhpVersion()) ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('SQL Version')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getSqlVersion()) ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Download SQL Report')) ?></h2>
        <p><?= $block->getDownloadButtonHtml('sql_info', 'krish/download/sql') ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Download Cache Report')) ?></h2>
        <p><?= $block->getDownloadButtonHtml('sql_info', 'krish/download/cache') ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Download Index Report')) ?></h2>
        <p><?= $block->getDownloadButtonHtml('sql_info', 'krish/download/indexer') ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Total Products')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getProductsCount()) ?></p>
    </div>
    <div class="krish-card">
        <h2><?= $escaper->escapeHtml(__('Total Categories')) ?></h2>
        <p><?= $escaper->escapeHtml($block->getCategoriesCount()) ?></p>
    </div>
</div>
