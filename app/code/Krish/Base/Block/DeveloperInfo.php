<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\Base\Block;

use Krish\Base\Block\Adminhtml\System\Config\GeneralInformation;
use Magento\Config\Block\System\Config\Form\Field;
use Magento\Config\Block\System\Config\Form\Fieldset;
use Magento\Framework\Data\Form\Element\AbstractElement;
use Magento\Framework\Phrase;
use Magento\Framework\View\Element\BlockInterface;

/**
 * Class DeveloperInfo
 * @package Krish\Base\Block
 */
class DeveloperInfo extends FieldSet
{
    /**
     * @var Field|null
     */
    private $fieldRenderer;

    /**
     * @param AbstractElement $element
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function render(AbstractElement $element): string
    {
        return $this->_getHeaderHtml($element)
            . $this->getGeneralInformation()
            . $this->_getFooterHtml($element);
    }

    /**
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getGeneralInformation()
    {
        $cacheInfoBlock = $this->getLayout()->createBlock(GeneralInformation::class);
        return $cacheInfoBlock->toHtml();
    }

    /**
     * @param AbstractElement $fieldset
     * @param string $fieldName
     * @param Phrase $label
     * @param string $value
     * @return string
     */
    protected function getFieldHtml(
        AbstractElement $fieldset,
        string $fieldName,
        Phrase $label,
        string $value = ''
    ): string {
        $field = $fieldset->addField($fieldName, 'label', [
            'name'  => 'dummy',
            'label' => $label,
            'after_element_html' => $value,
        ])->setRenderer($this->getFieldRenderer());

        return (string)$field->toHtml();
    }

    /**
     * @return Field|BlockInterface
     */
    private function getFieldRenderer()
    {
        if (empty($this->fieldRenderer)) {
            $this->fieldRenderer = $this->_layout->createBlock(
                Field::class
            );
        }

        return $this->fieldRenderer;
    }
}
