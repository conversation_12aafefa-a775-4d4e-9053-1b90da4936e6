<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\Base\Block\Adminhtml\System\Config;

use Magento\Backend\Block\Widget\Button;
use Magento\Framework\App\DeploymentConfig\Reader;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\State;
use Magento\Framework\View\Element\Template;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\App\ResourceConnection;
use Krish\Base\Model\CronJobs;
use Krish\Base\Model\MagentoVersion;
use Krish\Base\Model\MySqlReport;
use Magento\Framework\View\Element\Template\Context;

/**
 * Class GeneralInformation
 * @package Krish\Base\Block\Adminhtml\System\Config
 */
class GeneralInformation extends Template
{
    /**
     * @var MagentoVersion
     */
    private $magentoVersion;

    /**
     * @var Reader
     */
    private $reader;

    /**
     * @var DirectoryList
     */
    private $directoryList;

    /**
     * @var MySqlReport
     */
    private $mySqlReport;

    /**
     * @var string
     */
    protected $_template = 'Krish_Base::general_info.phtml';

    /**
     * @var CacheInterface
     */
    private CacheInterface $cache;

    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    private $dateCacheLifetime = 2 * 60 * 60;

    /**
     * GeneralInformation constructor.
     * @param MagentoVersion $magentoVersion
     * @param CronJobs $cronJobs
     * @param Reader $reader
     * @param DirectoryList $directoryList
     * @param MySqlReport $mySqlReport
     * @param Context $context
     * @param CacheInterface $cache
     * @param ResourceConnection $resourceConnection
     * @param array $data
     */
    public function __construct(
        MagentoVersion $magentoVersion,
        Reader $reader,
        DirectoryList $directoryList,
        MySqlReport $mySqlReport,
        Template\Context $context,
        CacheInterface $cache,
        ResourceConnection $resourceConnection,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->magentoVersion = $magentoVersion;
        $this->reader = $reader;
        $this->directoryList = $directoryList;
        $this->mySqlReport = $mySqlReport;
        $this->cache = $cache;
        $this->resourceConnection = $resourceConnection;
    }

    /**
     * @return string
     */
    public function getMagentoVersion()
    {
        try {
            return $this->magentoVersion->get();
        } catch (\Exception $e) {
            return __('Unknown');
        }
    }

    /**
     * @return string
     */
    public function getMagentoEdition()
    {
        try {
            return $this->magentoVersion->getEdition();
        } catch (\Exception $e) {
            return __('Unknown');
        }
    }

    /**
     * @return \Magento\Framework\Phrase|string
     */
    public function getCurrentMode()
    {
        try {
            $env = $this->reader->load();
            $mode = $env[State::PARAM_MODE] ?? '';
            return $mode ? ucfirst($mode) : __('Unknown');
        } catch (\Exception $e) {
            return __('Unknown');
        }
    }

    /**
     * @return string
     */
    public function getRootPath()
    {
        try {
            return $this->directoryList->getRoot();
        } catch (\Exception $e) {
            return __('Unknown');
        }
    }

    /**
     * @return \Magento\Framework\Phrase|string
     */
    public function getServerUser()
    {
        try {
            $user = function_exists('get_current_user')
                ? get_current_user()
                : __('Unknown');
        } catch (\Exception $e) {
            $user = __('unknown');
        }

        return $user;
    }

    /**
     * @return string
     */
    public function getPhpVersion()
    {
        try {
            $version = phpversion();
        } catch (\Exception $e) {
            $version = __('unknown');
        }

        return $version;
    }

    /**
     * @return string
     */
    public function getSqlVersion()
    {
        try {
            $version = $this->mySqlReport->getMySqlVersion();
        } catch (\Exception $e) {
            $version = __('unknown');
        }

        return $version;
    }

    /**
     * @param $id
     * @param $action
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getDownloadButtonHtml($id, $action)
    {
        $button = $this->getLayout()->createBlock(Button::class)
            ->setData([
                'id' => $id,
                'label' => __('Download'),
                'onclick' => $this->getButtonClickAction($action)
            ]);

        return $button->toHtml();
    }

    /**
     * @param $action
     * @return string
     */
    private function getButtonClickAction($action): string
    {
        return sprintf(
            "location.href = '%s'",
            $this->_urlBuilder->getUrl($action)
        );
    }

    /**
     * @return int
     */
    public function getProductsCount()
    {
        $cacheKey = 'total_products_count';
        $count = $this->cache->load($cacheKey);

        if ($count === false) {
            $connection = $this->resourceConnection->getConnection();
            $select = $connection->select()->from(
                $this->resourceConnection->getTableName('catalog_product_entity'),
                'entity_id'
            )->distinct();
            $count = $connection->fetchCol($select);
            $count = count($count);
            $this->cache->save($count, $cacheKey, [], $this->dateCacheLifetime);
        }

        return (int)$count;
    }

    /**
     * @return int
     */
    public function getCategoriesCount()
    {
        $cacheKey = 'total_categories_count';
        $count = $this->cache->load($cacheKey);

        if ($count === false) {
            $connection = $this->resourceConnection->getConnection();
            $select = $connection->select()->from(
                $this->resourceConnection->getTableName('catalog_category_entity'),
                'entity_id'
            )->distinct();
            $count = $connection->fetchCol($select);
            $count = count($count);
            $this->cache->save($count, $cacheKey, [], $this->dateCacheLifetime);
        }

        return (int)$count;
    }
}
