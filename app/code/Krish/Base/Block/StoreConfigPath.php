<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\Base\Block;

use Magento\Config\Block\System\Config\Form\Fieldset;
use Magento\Framework\Data\Form\Element\AbstractElement;

/**
 * Class StoreConfigPath
 * @package Krish\Base\Block
 */
class StoreConfigPath extends FieldSet
{
    /**
     * @param AbstractElement $element
     * @return string
     */
    public function render(AbstractElement $element): string
    {
        return $this->_getHeaderHtml($element)
            . $this->getConfigLinkHtml($element)
            . $this->_getFooterHtml($element);
    }

    /**
     * @param AbstractElement $element
     * @return string
     * @sup
     */
    public function getConfigLinkHtml(AbstractElement $element)
    {
        $html = '';
        $groupElement = $element->getData('group');

        if (!$groupElement || empty($groupElement['krish_store_config'])) {
            return $html;
        }

        $configData = $groupElement['krish_store_config'];
        $url = $this->getConfigUrl($configData);

        if (!empty($configData['html_text'])) {
            $html .= '<div class="krish-config-link">';

            if ($url) {
                $html .= '<a href="' . $url . '" class="krish-config-path-link">';
            }

            $html .= $configData['html_text'];

            if ($url) {
                $html .= '</a>';
            }

            $html .= '</div>';
        }

        return $html;
    }

    private function getConfigUrl($configData)
    {
        if (empty($configData['action'])) {
            return '';
        }

        $url = $this->_urlBuilder->getUrl($configData['action']);

        if (!empty($configData['html_id'])) {
            $url .= '#' . $configData['html_id'];
        }

        return $url;
    }
}
