<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\Base\Model;

/**
 * Mysql status report
 */
class MySqlReport
{
    /**
     * @var array
     */
    protected $importantConfig = [
        'Aborted_clients',
        'Aborted_connects',
        'Com_select',
        'Connections',
        'Created_tmp_disk_tables',
        'Created_tmp_files',
        'Created_tmp_tables',
        'Handler_read_rnd_next',
        'Innodb_buffer_pool_read_requests',
        'Innodb_buffer_pool_write_requests',
        'Innodb_log_waits',
        'Innodb_log_write_requests',
        'Innodb_log_writes',
        'Open_files',
        'Open_streams',
        'Open_table_definitions',
        'Open_tables',
        'Opened_files',
        'Opened_table_definitions',
        'Opened_tables',
        'Qcache_lowmem_prunes',
        'Select_full_join',
        'Select_full_range_join',
        'Select_range',
        'Select_range_check',
        'Select_scan',
        'Slow_queries',
        'Slave_running',
        'Sort_range',
        'Sort_rows',
        'Sort_scan',
        'Table_locks_immediate',
        'Table_locks_waited',
        'Threads_cached',
        'Threads_connected',
        'Threads_created',
        'Threads_running'
    ];

    /**
     * @var \Magento\Framework\DB\Adapter\AdapterInterface
     */
    protected $resourceConnection;

    /**
     * MySqlReport constructor.
     * @param \Magento\Framework\Module\ModuleResource $resource
     */
    public function __construct(
        \Magento\Framework\Module\ModuleResource $resource
    ) {
        $this->resourceConnection = $resource->getConnection();
    }

    /**
     * Generate MySQL Status information
     *
     * @return array
     */
    public function generate()
    {
        $data = [];
        $variables = $this->resourceConnection->fetchPairs('SHOW GLOBAL STATUS');
        sleep(10);
        $variablesAfter10Sec = $this->resourceConnection->fetchPairs('SHOW GLOBAL STATUS');

        if (!$variables || !$variablesAfter10Sec) {
            return $data;
        }

        foreach ($this->importantConfig as $name) {
            if (!isset($variables[$name])) {
                continue;
            }

            $value = $variables[$name];
            $data[$name] = [
                'value' => $value,
                'value_after_10_sec' => $this->calculateValueAfter10Sec($name, $value, $variablesAfter10Sec)
            ];
        }

        unset($variables, $variablesAfter10Sec);
        return $data;
    }

    /**
     * Calculate the value after 10 seconds with difference
     *
     * @param string $name
     * @param mixed $originalValue
     * @param array $variablesAfter10Sec
     * @return string
     */
    private function calculateValueAfter10Sec($name, $originalValue, $variablesAfter10Sec)
    {
        if (!isset($variablesAfter10Sec[$name])) {
            return 'n/a';
        }

        $newValue = $variablesAfter10Sec[$name];
        $difference = '';

        if (is_numeric($newValue)) {
            $numericDifference = $newValue - $originalValue;
            if ($numericDifference != 0) {
                $difference = ' (diff: ' . ($numericDifference > 0 ? '+' : '') . $numericDifference . ')';
            }
        }

        return $newValue . $difference;
    }

    /**
     * @return string
     */
    public function getMySqlVersion()
    {
        return $this->resourceConnection->fetchPairs("SHOW variables LIKE 'version'")['version'] ?? '';
    }
}
