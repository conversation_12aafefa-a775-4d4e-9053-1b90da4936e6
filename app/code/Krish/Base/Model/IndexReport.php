<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\Base\Model;

/**
 * Index Status report
 */
class IndexReport
{
    /**
     * @var \Magento\Indexer\Model\Indexer\CollectionFactory
     */
    protected $indexerFactory;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    protected $timeZone;

    /**
     * IndexReport constructor.
     * @param \Magento\Indexer\Model\Indexer\CollectionFactory $indexerFactory
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timeZone
     */
    public function __construct(
        \Magento\Indexer\Model\Indexer\CollectionFactory $indexerFactory,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timeZone
    ) {
        $this->indexerFactory = $indexerFactory;
        $this->timeZone = $timeZone;
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function generate()
    {
        $indexers = $this->indexerFactory->create()->getItems();

        $data = [];

        foreach ($indexers as $indexer) {
            $mode = $indexer->getView()->isEnabled() ? 'Update When Scheduled' : 'Update On Save';
            $latestUpdated = $this->timeZone->formatDateTime(
                new \DateTime($indexer->getLatestUpdated()),
                \IntlDateFormatter::MEDIUM,
                \IntlDateFormatter::MEDIUM
            );
            $data[] = [
                'Index' => (string) $indexer->getTitle(),
                'Status' => $indexer->getStatus(),
                'Update Required' => $indexer->isValid() ? 'No' : 'Yes',
                'Updated At' => $indexer->getLatestUpdated() ? $latestUpdated : 'Never',
                'Mode' => $mode,
                'Description' => (string) $indexer->getDescription()
            ];
        }

        return $data;
    }
}
