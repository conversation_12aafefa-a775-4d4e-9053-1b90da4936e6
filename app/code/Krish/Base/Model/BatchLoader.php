<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\Base\Model;

use Magento\Framework\Data\Collection;

/**
 * Class BatchLoader
 * @package Krish\Base\Utils
 */
class BatchLoader
{
    /**
     * Batch Size
     */
    public const BATCH_SIZE = 500;

    /**
     * @param Collection $collection
     * @param int|null $batchSize
     * @return \Generator
     */
    public function load(Collection $collection, ?int $batchSize = self::BATCH_SIZE): \Generator
    {
        $currentPage = 1;

        $collection->setPageSize($batchSize);
        $collection->setCurPage($currentPage);

        $totalPagesCount = $collection->getLastPageNumber();

        while ($currentPage <= $totalPagesCount) {
            $collection->clear();
            $collection->setCurPage($currentPage);

            foreach ($collection as $item) {
                yield $item;
            }

            $currentPage++;
        }
    }
}
