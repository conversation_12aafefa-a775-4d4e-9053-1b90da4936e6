<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\Base\Model;

/**
 * Class MagentoVersion
 * @package Krish\Base\Model
 */
class MagentoVersion
{
    public const MAGENTO_VERSION = 'krish_magento_version';
    public const MAGENTO_EDITION = 'krish_magento_edition';

    /**
     * @var \Magento\Framework\App\ProductMetadataInterface
     */
    private $productMetadata;

    /**
     * @var \Magento\Framework\App\Cache\Type\Config
     */
    private $cache;

    /**
     * @var string
     */
    private $magentoVersion;

    /**
     * @var string
     */
    private $magentoEdition;

    /**
     * MagentoVersion constructor.
     * @param \Magento\Framework\App\Cache\Type\Config $cache
     * @param \Magento\Framework\App\ProductMetadataInterface $productMetadata
     */
    public function __construct(
        \Magento\Framework\App\Cache\Type\Config $cache,
        \Magento\Framework\App\ProductMetadataInterface $productMetadata
    ) {
        $this->productMetadata = $productMetadata;
        $this->cache = $cache;
    }

    /**
     * @return string
     */
    public function get()
    {
        if (!$this->magentoVersion
            && !($this->magentoVersion = $this->cache->load(self::MAGENTO_VERSION))
        ) {
            $this->magentoVersion = $this->productMetadata->getVersion();
            $this->cache->save($this->magentoVersion, self::MAGENTO_VERSION);
        }

        return $this->magentoVersion;
    }

    /**
     * @return string
     */
    public function getEdition()
    {
        if (!$this->magentoEdition
            && !($this->magentoEdition = $this->cache->load(self::MAGENTO_EDITION))
        ) {
            $this->magentoEdition = $this->productMetadata->getEdition();
            $this->cache->save($this->magentoEdition, self::MAGENTO_EDITION);
        }

        return $this->magentoEdition;
    }
}
