<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_Base
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\Base\Model;

/**
 * Cache Status report
 */
class CacheReport
{
    /**
     * @var \Magento\Framework\App\Cache\TypeList
     */
    protected $typeList;

    /**
     * CacheReport constructor.
     * @param \Magento\Framework\App\Cache\TypeList $typeList
     */
    public function __construct(
        \Magento\Framework\App\Cache\TypeList $typeList
    ) {
        $this->typeList = $typeList;
    }

    /**
     * Generate Cache Status information
     *
     * @return array
     */
    public function generate()
    {
        $invalidatedCacheTypes = $this->typeList->getInvalidated();
        $cacheTypes = $this->typeList->getTypes();

        $data = [];

        foreach ($cacheTypes as $typeName => $type) {
            $status = 'Disabled';
            if (isset($invalidatedCacheTypes[$type->getId()])) {
                $status = 'Invalidated';
            } elseif ($type->getStatus()) {
                $status = 'Enabled';
            }

            $data[] = [
                /** @var \Magento\Framework\DataObject $type */
                'Cache' => $type->getCacheType(),
                'Status' => $status,
                'Type' => $typeName,
                'Associated Tags' => $type->getTags(),
                'Description' => $type->getDescription()
            ];
        }

        return $data;
    }
}
