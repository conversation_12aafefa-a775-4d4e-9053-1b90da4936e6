<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="patch_list" resource="default" comment="List of data/schema patches">
        <column xsi:type="int" name="patch_id" identity="true" comment="Patch Auto Increment" />
        <column xsi:type="varchar" name="patch_name" length="1024" nullable="false" comment="Patch Class Name" />
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="patch_id" />
        </constraint>
    </table>
</schema>
