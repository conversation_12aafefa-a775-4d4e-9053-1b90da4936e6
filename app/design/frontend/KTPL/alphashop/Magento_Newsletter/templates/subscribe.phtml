<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Newsletter\Block\Subscribe $block */
?>
<div class="block newsletter">
    <div class="content">
        <form class="form subscribe"
            novalidate
            action="<?= $block->escapeUrl($block->getFormActionUrl()) ?>"
            method="post"
            data-mage-init='{"validation": {"errorClass": "mage-error"}}'
            id="newsletter-validate-detail">
            <div class="field newsletter">
                <label class="label" for="newsletter">
                    <span >
                        <?= $block->escapeHtml(__('Sign Up for Our Newsletter:')) ?>
                    </span>
                </label>
                <div class="control">
                        <input name="email" type="email" id="newsletter"
                               placeholder="<?= $block->escapeHtml(__('Enter your email address')) ?>"
                               data-mage-init='{"mage/trim-input":{}}'
                               data-validate="{required:true, 'validate-email':true}"
                        />
                        <div class="actions">
                            <button class="action subscribe primary"
                                    title="<?= $block->escapeHtmlAttr(__('Subscribe')) ?>"
                                    type="submit"
                                    aria-label="Subscribe"
                                <?php if ($block->getButtonLockManager()->isDisabled('newsletter_form_submit')): ?>
                                    disabled="disabled"
                                <?php endif; ?>>
                                <span><?= $block->escapeHtml(__('Subscribe')) ?></span>
                            </button>
                        </div>
                </div>
            </div>
        </form>
    </div>
</div>
<script type="text/x-magento-init">
    {
        "*": {
            "Magento_Customer/js/block-submit-on-send": {
                "formId": "newsletter-validate-detail"
            }
        }
    }
</script>
