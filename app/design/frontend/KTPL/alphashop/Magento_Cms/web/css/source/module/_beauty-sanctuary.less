& when (@media-common = true) {
    .beauty-sanctuary-banner {
        position: relative;
        overflow: hidden;
        margin-bottom: @indent__xl;
        
        .content-wrapper {
            .lib-vendor-prefix-display(flex);
            background-color: @gray-100;
            align-items: center;
        }
        
        .image-container {
            width: 100%;
            
            img {
                width: 100%;
                height: auto;
                display: block;
            }
        }
        
        .content-container {
            width: 100%;
            padding: @indent__l @indent__base;
            
            .section-heading {
                margin-bottom: @indent__base;
            }
            
            .description {
                .lib-font-size(14);
                margin-bottom: 10px;
                line-height: 1.6;
            }
            
            .action-link {
                .lib-css(color, @color-black);
                .lib-font-size(16);
                text-decoration: none;
                position: relative;
                padding-right: 20px;
                display: inline-block;
                .lib-css(font-weight, @font-weight__semibold);
                
                &:after {
                    content: '\2192';
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    transition: transform 0.3s ease;
                }
                
                &:hover {
                    text-decoration: none;
                    color: @color-black;
                    
                    &:after {
                        transform: translate(5px, -50%);
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .beauty-sanctuary-banner {
        .content-wrapper {
            flex-direction: column;
            padding: 30px 20px;
        }
        
        .content-container {
            padding: @indent__base 0 @indent__xl;
            
            .description {
                .lib-font-size(14);
                margin-bottom: @indent__base;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .beauty-sanctuary-banner {
        .content-wrapper {
            padding: 25px;
        }
        
        .image-container {
            width: 50%;
            padding-right: @indent__xl;
        }
        
        .content-container {
            width: 50%;
            padding: 0;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .beauty-sanctuary-banner {
        .content-wrapper {
            padding: 50px;
        }
        
        .content-container {
            .description {
                .lib-font-size(18);
                margin-bottom: @indent__l;
            }

            
        }
    }
}