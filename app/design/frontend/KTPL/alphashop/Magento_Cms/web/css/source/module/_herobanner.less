//
//  Home Page Styles
//  _____________________________________________

//
//  Hero Banner
//  _____________________________________________

& when (@media-common = true) {
    // Make the hero banner break out of the container and take full width
    .hero-banner {        
        &__wrapper {
            position: relative;
            margin: 0;
            padding: 0;
            width: 100%;
            
            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.3);
                z-index: 1;
            }
        }
        
        &__image {
            display: block;
            width: 100%;
            height: auto;
            object-fit: cover;
            object-position: center;
            // Set a minimum height for mobile
        }
        
        &__content {
            position: absolute;
            // Adjust position for mobile
            bottom: @indent__l;
            left: 0;
            right: 0;
            z-index: 2;
            text-align: center;
            padding: 0 @indent__base;
            // Add max-width to the content to keep it centered and readable
            max-width: 1280px;
            margin: 0 auto;
        }
        
        &__title {
            color: @color-white;
            margin-bottom: 5px;
            letter-spacing: 1px;
            // Change font family to Adobe Clean
            // Mobile-first font size
            .lib-typography(
                @_font-family: @font-family__secondary,
                @_font-size: 24px,
                @_color: @color-white,
                @_line-height: 1.2
            );
        }
        
        &__subtitle {
            color: @color-white;
            // Reduce margin-bottom to decrease space between subtitle and button
            margin: 0 auto 10px;
            max-width: 90%; // Wider on mobile
            // Mobile-first font size
            .lib-typography(
                @_font-size: 14px,
                @_color: @color-white,
                @_font-weight: @font-weight__regular,
                @_line-height: 1.4
            );
        }
        
        &__action {
            // Reduced margin-top to decrease space between subtitle and button
            margin-top: 10px;
            
            .action.primary {
                .lib-button-primary();
                text-transform: uppercase;
                letter-spacing: 1px;
                // Mobile button styles
                width: 180px; // Slightly smaller on mobile
                padding: 10px 15px; // Smaller padding on mobile
                font-size: 12px; // Smaller font on mobile
                box-sizing: border-box;
                
                &:hover {
                    background: @button-primary__hover__background;
                }
                
                &:focus,
                &:active {
                    background: @button-primary__active__background;
                    border-color: @button-primary__active__border;
                }
            }
        }
    }
    
    // Override max-width for the hero banner when it's inside these containers
    
    
    // Add this to prevent horizontal scrollbar
    html, body {
        overflow-x: hidden;
        max-width: 100%;
    }
}

//
//  Tablet and desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .hero-banner {
        &__wrapper {
        }
        
        &__image {
            height: 100%;
            object-fit: cover;
        }
        
        &__content {
            // Move content further from bottom on larger screens
            bottom: @indent__xl;
        }
        
        &__title {
            // Larger font size on desktop
            .lib-font-size(48);
            // .lib-typography(
            //     @_font-size: 48px,
            //     @_color: @color-white,
            //     @_font-weight: @font-weight__bold,
            //     @_line-height: 1.2
            // );
            margin-bottom: 10px; // More space between title and subtitle on desktop
        }
        
        &__subtitle {
            font-size: @font-size__l;
            max-width: 60%;
            margin-bottom: 15px; // More space between subtitle and button on desktop
        }
        
        &__action {
            margin-top: 15px;
            
            .action.primary {
                // Desktop button styles
                width: 208px;
                padding: 12px 20px;
                font-size: 14px;
                justify-content: center;
            }
        }
    }
}

//
//  Large desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {
    .hero-banner {
        &__subtitle {
            max-width: 50%;
        }
    }
}

//
//  Small mobile devices
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    .hero-banner {
        &__image {
        }
        
        &__content {
            bottom: @indent__m; // Position content closer to bottom on small devices
            padding: 0 @indent__s; // Less padding on small devices
        }
        
        &__title {
            .lib-typography(
                @_font-size: 20px, // Even smaller font on very small devices
                @_color: @color-white,
                @_font-weight: @font-weight__bold,
                @_line-height: 1.2
            );
            margin-bottom: 3px; // Less space on very small devices
        }
        
        &__subtitle {
            .lib-typography(
                @_font-size: 12px, // Even smaller font on very small devices
                @_color: @color-white,
                @_font-weight: @font-weight__regular,
                @_line-height: 1.3
            );
            margin-bottom: 8px; // Less space on very small devices
            max-width: 95%; // Almost full width on very small devices
        }
        
        &__action {
            margin-top: 8px; // Less space on very small devices
            
            .action.primary {
                width: auto; // Even smaller width on very small devices
                padding: 8px 12px; // Less padding on very small devices
                font-size: 11px; // Smaller font on very small devices
            }
        }
    }
}
