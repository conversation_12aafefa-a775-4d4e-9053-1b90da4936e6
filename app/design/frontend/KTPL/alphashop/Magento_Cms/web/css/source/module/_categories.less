& when (@media-common =true) {
    .categories-section {
        .categories-grid {
            .lib-vendor-prefix-display(grid);
        }

        .category-link {
            &:hover {
                text-decoration: none;
            }
        }

        .category-item {
            position: relative;
            transition: background-color 0.3s ease, border-color 0.3s ease;
            padding: 0 15px 0 15px;

            &:before {
                background-color: @gray-100;
                border: 1px solid @gray-100;
                content: '';
                position: absolute;
                top: 30px;
                bottom: 0;
                right: 0;
                left: 30px;
                z-index: -1;
            }
        }

        .category-image-wrapper {
            margin: 0 0 0 0;
        }

        .category-image {
            
            transition: margin-left 0.3s ease;
            margin-bottom: 0;
        }

        .category-name {
            padding: 15px 0 15px 30px;

            .lib-vendor-prefix-display();
            align-items: center;
            .lib-font-size(16);
            .lib-css(font-weight, @font-weight__bold);
            color: @color-black;
        }

        .category-item:hover {

            &:before {
                background-color: @color-white;
                border: 1px solid #EEEEEE;
            }


            .category-image {
                margin-left: 30px;
            }


            .category-name:after {
                transform: translateX(3px);
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__l) {
    .categories-section{
         .categories-grid:not(.slick-initialized) {
            display: flex;
            overflow: hidden;
            
            .category-item{
                flex: 0 0 33%;
                max-width: 100%;
            }
        }
    }
}
.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__l) {
    .categories-section {
        .categories-grid {
            grid-template-columns: repeat(5, minmax(0, 1fr));
            gap: 10px;
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__xl) {
    .categories-section {
        .categories-grid {
            gap: 40px;
        }
    }
}