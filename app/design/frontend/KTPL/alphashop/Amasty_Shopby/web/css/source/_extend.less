//
//  Common
//  _____________________________________________

@amshopby-choice-element-checkbox__active__background: @theme__color__primary;

& when (@media-common =true) {

  #amasty-shopby-product-list {
    padding: 20px 0 0;
   
  }

  .filter-options-item .filter-options-content {
    .items:not(.items-children):not(.am-category-view),
    .am-category-wrapper,
    .amshopby-fromto-wrap {
        margin: 0 0;
    }
  }


  .filter-options-item .filter-options-content .am-shopby-form {
    margin: 0;

  }


}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    #amasty-shopby-product-list {
        border-top: 5px solid @gray-100;
    }
}