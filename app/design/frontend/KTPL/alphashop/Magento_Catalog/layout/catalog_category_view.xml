<?xml version="1.0"?>
<page layout="2columns-left" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <!-- Move category description after title -->
        <move element="category.description" destination="category.view.container" before="category.image"/>
        <!-- Move category image to the end -->
        <move element="category.image" destination="category.view.container" after="category.description"/>
    </body>
</page>