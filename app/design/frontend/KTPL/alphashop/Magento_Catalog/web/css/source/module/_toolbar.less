// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  ---------------------------------------------

@toolbar-mode-icon-font-size: 24px;
@toolbar-element-background: @panel__background-color;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .page-products {
        .columns {
            position: relative;
            z-index: 1;
        }
    }

    .toolbar {
        &:extend(.abs-add-clearfix all);
    }

    .toolbar-amount {
        display: block;
        line-height: normal;
        margin: 0;
        padding: 0;
        vertical-align: middle;

        .products.wrapper ~ .toolbar & {
            display: none;
        }
    }

    .toolbar-products {
        .lib-vendor-prefix-display();
        .lib-vendor-box-align(center);
        margin-bottom: 22px;
        padding: 0 5px 0;
        text-align: center;
        &:extend(.abs-add-clearfix all);

        .pages {
            display: none;

            .products.wrapper ~ & {
                display: block;
            }
        }

        .limiter {
            .control {
                display: inline-block;
            }
        }
    }

    .sorter {
        .lib-vendor-prefix-display();
        .lib-vendor-box-align(center);
        justify-content: end;
        .lib-vendor-prefix-flex-grow(1);
        padding: 4px 0 0;

        .products.wrapper ~ .toolbar & {
            display: none;
        }

        .sorter-action {
            position: relative;
            top: -2px;
        }
    }

    .sorter-options {
        margin: 0 0 0 7px;
        width: auto;
        .lib-font-size(16);
    }

    .sorter-action {
        vertical-align: top;
        .lib-icon-font(
            @icon-arrow-up,
            @_icon-font-size: 28px,
            @_icon-font-line-height: 32px,
            @_icon-font-color: @header-icons-color,
            @_icon-font-color-hover: @header-icons-color-hover,
            @_icon-font-text-hide: true
        );
    }

    .sorter {
        .sort-desc {
            &:before {
                content: @icon-arrow-down;
            }
        }
    }

    .modes {
        display: none;
    }

    .limiter-options {
        margin: 0 5px 0 7px;
        width: auto;
    }

    .limiter-label {
        font-weight: @font-weight__regular;
    }

    .limiter {
        .page-products .toolbar & {
            display: none;
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .page-products {
        .columns {
            padding-top: 0;
            position: relative;
            z-index: 1;
        }
    }

    .toolbar-products{
        padding-left: @indent__base;
        padding-right: @indent__base;
    }

    .toolbar {
        .products.wrapper ~ & .pages {
            float: left;
        }
    }

    .toolbar-amount {
        float: left;
        order: 1;
        .lib-font-size(16);
    }

    .sorter {
        float: right;
        
        order: 3;
        .lib-font-size(16);
    }

    .sorter-label{
        color: #666666;
    }

    .modes {
        gap: 10px;
        order: 2;
        float: left;
        margin-left: @indent__base * 2;

        .products.wrapper ~ .toolbar & {
            display: none;
        }
    }

    .modes-label {
        &:extend(.abs-visually-hidden-desktop all);
    }

    .modes-mode {
        .lib-css(color, @text__color__muted);
        border-right: 0;
        font-weight: @font-weight__regular;
        line-height: 1;
        text-align: center;

        &:not(.active) {
            &:hover {
                .lib-css(color, @text__color__muted);
                // background: darken(@toolbar-element-background, 7%);
            }
        }

        &:last-child {
            // border-left: 1px solid #DDDDDD;
        }

        &.active {
            .lib-css(color, @primary__color__light);
        }

        .lib-icon-font(
            @icon-grid,
            @_icon-font-size: @toolbar-mode-icon-font-size,
            @_icon-font-text-hide: true,
            @_icon-font-color: @text__color__muted,
            @_icon-font-color-hover: @text__color__muted
        );
    }

    .mode-list {
        .lib-icon-font-symbol(@icon-list);
    }

    .toolbar {
        .products.wrapper ~ & .limiter {
            display: block;
            float: right;
        }
    }
}
