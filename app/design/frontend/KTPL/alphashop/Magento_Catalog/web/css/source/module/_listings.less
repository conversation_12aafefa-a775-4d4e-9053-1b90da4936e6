// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  ---------------------------------------------

@product-name-link__color: @gray-200;
@product-name-link__color__active: @gray-200;
@product-name-link__color__hover: @theme__color__primary;
@product-name-link__color__visited: @theme__color__primary;

@product-name-link__text-decoration: none;
@product-name-link__text-decoration__active: @link__hover__text-decoration;
@product-name-link__text-decoration__hover: @link__hover__text-decoration;
@product-name-link__text-decoration__visited: @link__hover__text-decoration;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    // Category view styles
    .catalog-category-view {
        .category-view {
            margin-bottom: @indent__xl;
            
            .category-description {
                margin-bottom: 38px; // Gap between description and image
                
                p {
                    .lib-font-size(16);
                    line-height: 1.6;
                    margin-bottom: @indent__base;
                    
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
            
            .category-image {
                width: 100%;
                
                .image {
                    width: 100%;
                    height: auto;
                    display: block;
                }
            }
        }
    }

    //  Product Lists
    .products {
        margin: 0;
    }

    .product {
        .product-item-name{
            .product-item-link{
                    .lib-font-size(16);
                    .lib-vendor-prefix-display(block);
                    color: @product-name-link__color;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical; 
                    overflow: hidden;
                    height: 45px;
            }
        }
        &-items {
            font-size: 0;
            &:extend(.abs-reset-list all);
        }

        &-item {
            font-size: 1.4rem;
            vertical-align: top;
            position: relative;
            z-index: 1;
                &:before {
                    content: '';
                    background:  @color-white;
                    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.29);
                    position: absolute;
                    left: 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    opacity: 0;
                    visibility: hidden;
                    z-index: -1;
                }

                &:hover {
                    z-index: 2;
                    &:before {
                        opacity: 1;
                        visibility: visible;
                    }
                }

                .products-grid & {
                    display: inline-block;
                    margin-left: 2%;
                    padding: 0;
                    width: calc(~'(100% - 2%) / 2');
                    
                    .product-item-actions{
                        .lib-vendor-prefix-display();
                        .lib-vendor-box-align(center);
                    }
                    .product-item-info{
                        padding: 20px;
                    }

                    .product-item-inner{
                        margin-top: 20px;
                    }

                }

                .product-item-photo-wrapper{
                    position: relative;
                }

            .swatch-wrapper{
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                padding: 5px;
                z-index: 1;
            }

            &:nth-child(2n + 1) {
                margin-left: 0;
            }

            &:extend(.abs-add-box-sizing all);

            

            &-name {
                &:extend(.abs-product-link all);
                -moz-hyphens: auto;
                -ms-hyphens: auto;
                -webkit-hyphens: auto;
                display: block;
                hyphens: auto;
                margin: 10px 0;
                word-wrap: break-word;              
            }

           

                &-photo{
                    .lib-vendor-prefix-display();
                    justify-content: center;
                    background-color: @gray-100;
                    padding: 35px 0;
                }

            

            &-actions {
                font-size: 0;

                > * {
                    font-size: 1.4rem;
                }

                .actions-secondary {
                    display: inline-block;
                    font-size: 1.4rem;
                    vertical-align: middle;
                    white-space: nowrap;
                    > button.action {
                        .lib-button-reset();
                    }

                    > .action {
                        text-align: center;

                        &.tocompare{
                .lib-vendor-prefix-display(none);

                        }

                        &:extend(.abs-actions-addto-gridlist all);
                        &:before {
                            margin: 0;
                        }

                        span {
                            &:extend(.abs-visually-hidden all);
                        }
                    }
                }

                .actions-primary {
                    display: inline-block;
                    vertical-align: middle;
                    .lib-vendor-prefix-flex-grow(1);
                }
            }

            &-description {
                margin: @indent__m 0;
            }

            .product-reviews-summary {
                .lib-vendor-prefix-display(none);
                .rating-summary {
                    margin: 0 4px 0 0;
                }

                .reviews-actions {
                    font-size: @font-size__s;
                    margin-top: 5px;
                }
            }

            .price-box {
                .lib-vendor-prefix-display();
                .lib-vendor-box-align();
                gap: 5px;

                .price {
                    .lib-font-size(16);
                    font-weight: @font-weight__bold;
                    white-space: nowrap;
                }

                .price-label {
                    font-size: @font-size__s;
                    display: none;

                    &:after {
                        content: ':';
                    }
                }
            }

            .special-price,
            .minimal-price {
                .price {
                    .lib-font-size(16);
                    font-weight: @font-weight__bold;
                }

                .price-wrapper {
                    display: inline-block;
                }

                .price-including-tax + .price-excluding-tax {
                    display: block;
                }
            }

            .special-price {
                display: block;
            }

            .old-price {
                .price {
                    font-weight: @font-weight__regular;
                    color: #666666;
                    .lib-font-size(14);
                }
            }

            .regular-price {
                .price-label {
                    display: none;
                }
            }

            .minimal-price {
                .price-container {
                    display: block;
                }
            }

            .minimal-price-link {
                margin-top: 5px;

                .price-label {
                    .lib-css(color, @link__color);
                    .lib-font-size(14);
                }

                .price {
                    font-weight: @font-weight__regular;
                }
            }

            .minimal-price-link,
            .price-excluding-tax,
            .price-including-tax {
                display: block;
                white-space: nowrap;
            }

            .price-from,
            .price-to {
                margin: 0;
            }

            .tocompare {
                .lib-icon-font-symbol(
                @icon-compare-empty
                );
            }

            .tocart {
                white-space: nowrap;
                width: auto;
                justify-content: center;

                .action.primary{
                    padding: 12px;
                    .lib-font-size(12);
                }
            }
        }
    }

    .price-container {
        .price {
            .lib-font-size(14);
        }

        .price-including-tax + .price-excluding-tax,
        .weee {
            margin-top: @indent__xs;
        }

        .price-including-tax + .price-excluding-tax,
        .weee,
        .price-including-tax + .price-excluding-tax .price,
        .weee .price,
        .weee + .price-excluding-tax:before,
        .weee + .price-excluding-tax .price {
            .lib-font-size(11);
        }

        .weee {
            &:before {
                content: '('attr(data-label) ': ';
            }

            &:after {
                content: ')';
            }

            + .price-excluding-tax {
                &:before {
                    content: attr(data-label) ': ';
                }
            }
        }
    }

    .products-list {
        .product {
            &-item {
                display: table;
                width: 100%;

                &-info {
                    display: table-row;
                }

                &-photo {
                    display: table-cell;
                    padding: 0 @indent__l @indent__l 0;
                    vertical-align: top;
                    width: 1%;
                }

                &-details {
                    display: table-cell;
                    vertical-align: top;
                }
            }
        }

        .product-image-wrapper {
            &:extend(.abs-reset-image-wrapper all);
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .products-list .product {
        &-item {
            table-layout: fixed;

            &-photo {
                padding: 0 @indent__s @indent__s 0;
                width: 30%;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .product {
        &-item {
            .products-grid & {
               // margin-bottom: @indent__l;
            }

            &-actions {
                display: block;

                .products-grid & {
                 //   margin: @indent__s 0;
                }

                .actions-primary + .actions-secondary {
                    > * {
                        white-space: normal;
                    }
                }
            }
        }
    }

    .products-grid .product-item {
        width: (100%/3);
    }

    .page-products,
    .page-layout-1column,
    .page-layout-3columns,
    .page-products.page-layout-1column,
    .page-products.page-layout-3columns {
        .products-grid {
            .product-item {
                margin-left: 2%;
                padding: 0;
                width: calc(~'(100% - 4%) / 3');

                &:nth-child(3n + 1) {
                    margin-left: 0;
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m){
    .products-grid{
        .product-item-inner{
            .action.primary{
                padding: 12px;
                .lib-font-size(14);
            }
        }
    }


    
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .product-item .tocart {
        width: 100%;
    }

    .product-item-photo-wrapper{
        .lib-vendor-prefix-display();
        .lib-vendor-prefix-flex-direction();
    }
    .page-products {
        .products-grid {
            .product-item {
                margin-left: 2%;
                padding: 0;
                width: calc(~'(100% - 4%) / 3');

                &:nth-child(3n + 1) {
                    margin-left: 0;
                }
            }

            .product-item-inner{
                .action.primary{
               
                }
            }

            .product-item-actions{
                .lib-vendor-prefix-display();
                .lib-vendor-box-align(center);
                justify-content: center;
                font-size: 0;
                gap: 10px;
            }
        }
    }

    .page-products.page-layout-1column {
        .products-grid {
            .product-item {
                width: (100%/4);
            }
        }
    }

    .page-products.page-layout-3columns {
        .products-grid {
            .product-item {
                width: (100%/2);
            }
        }
    }
}
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .products-grid {
        
        .swatch-attribute-options{
            margin: 0;
        }
        .product-item {
            width: (100%/5);
            margin-bottom: -65px;

            .tocart{
                width: 100%;
            }

            .product-item-info{
               // padding: 20px;

                &:hover{
                    .product-image-photo{
                       // transform: scale(.8)
                    }
                    .swatch-wrapper{
                        .lib-vendor-prefix-display();
                    }
                }
            }

            .swatch-wrapper{
                .lib-vendor-prefix-display(none);
                padding: 15px 0;
                border-bottom: 1px solid @gray-100;
            }

            .product-item-inner{
              //  padding: 0 20px 20px;  
            }

            .product-item-inner {
                position: absolute;
                opacity: 0;
                visibility: hidden;
                left: 0;
                right: 0;
                bottom: 0;
              
            }
            &:hover {
                .product-item-inner {
                    opacity: 1;
                    visibility: visible;
                    transform: translate(0, 0);
                }   
            }

        }
        .product-item-info{
            // margin: -10px;


           &:hover{
            // background: #fff;
            // box-shadow: 3px 4px 4px 0 rgba(0,0,0,0.3);
            // position: relative;
            // z-index: 9;
            
            .product-item-inner{
                display: block;
            }
           }
        }
    
        .product-item-inner{
            // position: absolute;
            // margin: 7px 0 0 -1px; 
            // box-shadow: 3px 6px 4px 0 rgba(0,0,0,0.3);
            // right: 0px;
            // z-index: 2;
            // border-top: none;
            // left: 0;
            // background-color: white;
        }
    
        .product-item-actions {
            // margin: -10px 0 10px;
        }
    }

    .page-layout-1column {
        .products-grid {
            .product-item {
                width: (100%/6);
            }
        }
    }

    .page-layout-3columns {
        .products-grid {
            .product-item {
                width: (100%/4);
            }
        }
    }

    .page-products {
        .products-grid {
            .product-items {
                margin: 0 ;
                padding: 0 0 80px 0 ;
            }

            .product-item {
                margin-left: 2%;
                padding: 0;
                width: calc(~'(100% - 6%) / 4');

                &:nth-child(3n + 1) {
                    margin-left: 2%;
                }

                &:nth-child(4n + 1) {
                    margin-left: 0;
                }

                .product-item-details {
                    padding-bottom: 80px;
                    position: relative;
                }
            }
        }
    }

    .page-products {
        &.page-layout-1column {
            .products-grid {
                .product-item {
                    margin-left: 0;
                    width: (100%/5);
                }
            }
        }

        &.page-layout-3columns {
            .products-grid {
                .product-item {
                    margin-left: 1%;
                    width: 32.667%;

                    &:nth-child(3n) {
                        margin-left: 1%;
                    }

                    &:nth-child(3n + 1) {
                        margin-left: 0;
                    }
                }
            }
        }
    }
}
