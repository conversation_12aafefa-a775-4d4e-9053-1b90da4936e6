// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@import 'module/_collapsible_navigation.less';

//
//  Theme variables
//  _____________________________________________

//  Messages
@message-global-note__background: @color-yellow-light2;
@message-global-note__border-color: @color-yellow-light3;
@message-global-note__color: @text__color;

@message-global-note-link__color: @link__color;
@message-global-note-link__color-hover: @link__hover__color;
@message-global-note-link__color-active: @link__active__color;

@message-global-caution__background: @color-red9;
@message-global-caution__border-color: none;
@message-global-caution__color: @color-white;

@message-global-caution-link__color: @link__color;
@message-global-caution-link__color-hover: @link__hover__color;
@message-global-caution-link__color-active: @link__active__color;

//  Header
@header__background-color: false;
@utility-announcements-wrapper__background-color: @color-black; 
@header-icons-color: @theme__color__primary;
@header-icons-color-hover: @gray-600;
@customer-welcome__z-index: @dropdown-list__z-index + 1;

@addto-color: @theme__color__primary;
@addto-hover-color: @gray-600;

@minicart-icons-color: @header-icons-color;
@minicart-icons-color-hover: @header-icons-color-hover;

@button__shadow: inset 0 2px 1px rgba(0,0,0,.12);

@h1__margin-bottom__desktop: @indent__xl;

//  Footer
@footer__background-color: @color-black;
@footer__color: @color-white;
@footer-bottom-row__background-color: @color-white;
@footer-upper-row__background-color: #141414;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    body {
        .lib-css(background-color, @page__background-color);
    }

    .page-wrapper {
        .lib-vendor-prefix-display(flex);
        .lib-vendor-prefix-flex-direction(column);
        min-height: 100vh; // Stretch content area for sticky footer
    }

    .page-main {
        .lib-vendor-prefix-flex-grow(1);
    }

    //
    //  Header
    //  ---------------------------------------------

    .page-header {
        .lib-css(background-color, @header__background-color);
        border-bottom: 1px solid @border-color__base;
        margin-bottom: @indent__base;
    }
    .utility-announcements-wrapper { 
        .lib-css(background-color, @utility-announcements-wrapper__background-color);
        .lib-vendor-prefix-display();
        .lib-vendor-box-align(center);   
        justify-content: center;
        padding: @indent__xs @indent__s;  
        margin-bottom: 0;
        color: @color-white;
        position: relative;

        .utility-announcements {

        }

        .utility-announcements-slider {
            .lib-list-reset-styles();
            .lib-vendor-prefix-display();
            .lib-vendor-box-align();

            > li {
                margin: 0;
            }
        }

        .actions-toolbar{
            position: absolute;
            right: @indent__s;
            cursor: pointer;
            border-radius: 0;
            color: @color-white;
            top: 50%;
            transform: translateY(-50%);
        }

        .close {
            .lib-button-reset();
            .lib-button(
                @_button-padding: 5px,
                @_button-color: @gray-400,
                @_button-color-hover: @gray-600,
                @_button-color-active: @gray-600,
                @_button-background: false,
                @_button-background-hover: false,
                @_button-background-active: false,
                @_button-border: false,
                @_button-border-hover: false,
                @_button-border-active: false,
                @_button-icon-use: true,
                @_button-font-content: @icon-remove,
                @_button-icon-font-size: 32px,
                @_button-icon-font-line-height: 16px,
                @_button-icon-font-position: before,
                @_button-icon-font-text-hide: true, 
            );
        }
    }

    .header {
        &.content {
            &:extend(.abs-add-clearfix all);
            padding-top: @indent__s;
            padding-bottom: @indent__s;
            .lib-vendor-prefix-display();
            .lib-vendor-box-align(center);
            justify-content: center;
            position: relative;
            z-index: @z-index-2; 
        }
    }
    
    .ammenu-logo,
    .logo {
        margin: 0 0 0 @indent__xl;
        max-width: 50%;
        position: relative;
        z-index: 5;
       
        .lib-vendor-box-align(center);

        img {
            display: block;
            height: auto;
        }

        .page-print & {
            float: none;
        }
    }

    .header-links-wrapper {
        .lib-vendor-prefix-flex-grow(1);
        .lib-vendor-prefix-display();
        justify-content: flex-end;
    }
    
    .header-links-container {
        .lib-vendor-prefix-display();
        .lib-vendor-box-align(center);
        gap: 5px;

    }

    .page-main {
        > .page-title-wrapper {
            .page-title + .action {
                margin-top: @indent__l;
            }
        }
    }

    .action.skip {
        &:not(:focus) {
            &:extend(.abs-visually-hidden all);
        }

        &:focus {
            .lib-css(background, @color-gray94);
            .lib-css(padding, @indent__s);
            box-sizing: border-box;
            left: 0;
            position: absolute;
            text-align: center;
            top: 0;
            width: 100%;
            z-index: 15;
        }
    }

    .action-skip-wrapper {
        height: 0;
        position: relative;
    }

    //
    //  Global notice
    //  ---------------------------------------------

    .message.global {
        p {
            margin: 0;
        }

        &.noscript,
        &.cookie {
            .lib-message(@_message-type: global-note);
            margin: 0;
        }

        &.cookie {
            bottom: 0;
            left: 0;
            position: fixed;
            right: 0;
            z-index: 3;

            .actions {
                margin-top: @indent__s;
            }
        }

        &.demo {
            .lib-message(@_message-type: global-caution);
            margin-bottom: 0;
            text-align: center;
        }
    }

    //
    //  Footer
    //  ---------------------------------------------

    .page-footer {
        .lib-css(background-color, @footer__background-color);
        .lib-css(color, @footer__color);
        margin-top: auto;
    }

    .footer-upper-row {
       margin: 0 auto;
       padding: 15px 0;
       background-color: @footer-upper-row__background-color;

        .upper-content {
            .lib-vendor-prefix-display(flex);
            .lib-vendor-prefix-flex-direction();
            row-gap: 30px;
        }
        &-col-one,
        &-col-second {
            .lib-css(width, 48%);
        }

    }

    .footer-middle-row {
        margin: 0 auto;
        padding: 15px 0;
        .middle-content {
            .lib-vendor-prefix-display(flex);
            .lib-vendor-prefix-flex-direction();
            row-gap: 30px;
        }
    }

    .footer-social-payment-wrapper { 
        
    }

    .footer-services {
        .service-list {
            .lib-list-reset-styles();
            .lib-vendor-prefix-display();
            .lib-vendor-box-align();
            gap: 30px;

        }
        .service-item {
            .lib-vendor-prefix-display(flex);
            .lib-vendor-box-align(center);
            gap: 10px;
            margin-bottom: 0;

            .icon-wrapper {
                .lib-vendor-prefix-flex-shrink(0);
            }
          

            .service-label {
                .lib-font-size(16);
                color: @gray-400;
                white-space: nowrap;
            }
        }
    }
    // Footer Links
    .footer-links-wrapper {
        .lib-clearfix();
        margin-bottom: @indent__base;

        .footer-section {
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: @indent__s;

            [data-role="trigger"] {
                cursor: pointer;
            }

            .title {
                .lib-font-size(18);
                .lib-css(font-weight, @font-weight__bold);
                .lib-css(color, @gray-400);
                padding: @indent__s 0;
                position: relative;
                margin: 0;

                &:after {
                    content: '+';
                    position: absolute;
                    right: @indent__s;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }

            .items {
                .lib-list-reset-styles();
                padding: 0 @indent__s @indent__s;

                .item {
                    .lib-css(margin-bottom, @indent__xs);
                    .lib-font-size(16);
                    

                    a {
                        .lib-link(
                            @_link-color:@gray-600,
                            @_link-text-decoration: @product-name-link__text-decoration,
                            @_link-color-visited: @gray-600,
                            @_link-text-decoration-visited: @product-name-link__text-decoration__visited,
                            @_link-color-hover: @gray-400,
                            @_link-text-decoration-hover: @product-name-link__text-decoration__hover,
                            @_link-color-active: @gray-400,
                            @_link-text-decoration-active: @product-name-link__text-decoration__active
                        );

                      
                    }
                }
            }

            &.active {
                [data-role="trigger"] {
                    .title:after {
                        content: '-';
                    }
                }
            }
        }
    }

    .glamour-trends {
        .lib-css(color, @gray-600);

        &__title {
            .lib-css(font-size, 20px);
            font-family:@font-family__secondary;
            .lib-css(font-weight, @font-weight__regular);
            line-height: normal;
            margin-bottom: @indent__base;
            color: @gray-400;
            text-transform: uppercase;
            margin: 0 0 20px 0;
        }

        &__description {
            .lib-css(font-size, 14px);
            .lib-css(line-height, 20px);
            color: @gray-600;
            margin-bottom: 20px;
            p {
                margin: 0;

            }
         
        }

        &__contact {
            .lib-css(font-size, 14px);
        }

        &__contact-item {
            margin-bottom: @indent__base;
        }

        &__contact-label {
            .lib-font-size(18);
            .lib-css(font-weight, @font-weight__semibold);
            display: block;
            color: @gray-400;
            margin-bottom: 5px;
        }

        &__contact-value {
         
                .lib-link(
                    @_link-color:@gray-600,
                    @_link-text-decoration: @product-name-link__text-decoration,
                    @_link-color-visited: @gray-600,
                    @_link-text-decoration-visited: @product-name-link__text-decoration__visited,
                    @_link-color-hover: @gray-400,
                    @_link-text-decoration-hover: @product-name-link__text-decoration__hover,
                    @_link-color-active: @gray-400,
                    @_link-text-decoration-active: @product-name-link__text-decoration__active
                );

              
           
            transition: color 0.3s ease;

          
        }
    }

    .glamour-trends {
        // ... (keep the existing styles)

        &__social {
            margin-top:45px;
        }

        &__social-title {
            .lib-css(font-size, 18px);
            .lib-css(font-weight, @font-weight__bold);
            .lib-css(color, @gray-400);
            margin: 0 0 20px 0;
        }

        &__social-list {
            .lib-list-reset-styles();
            .lib-vendor-prefix-display(flex);
            gap: @indent__base;
        }

        &__social-item {
     
            margin: 0;
        }

        &__social-link {
            .lib-link(
                @_link-color:@gray-400,
                @_link-text-decoration: none,
                @_link-color-visited: @gray-400,
                @_link-text-decoration-visited: none,
                @_link-color-hover: @gray-600,
                @_link-text-decoration-hover:none,
                @_link-color-active: @gray-600,
                @_link-text-decoration-active: none
            );
        }

        &__social-icon {
            .lib-icon-font(
                @icon-arrow-up,
                @_icon-font-size: 32px,
                @_icon-font-line-height: 37px,
                @_icon-font-color: @gray-400,
                @_icon-font-color-hover: @gray-600,
                @_icon-font-text-hide: true
            );
            .lib-text-hide();
            .lib-vendor-prefix-display();
            .lib-vendor-box-align(center);
            justify-content: center;
            width: 40px;
            height: 40px;
            border: 1px solid @gray-700;
            border-radius: 50%;

        }

        &__social-link--facebook &__social-icon {
            .lib-icon-font-symbol(
                @icon-facebook
            );
        }

        &__social-link--twitter &__social-icon {
            .lib-icon-font-symbol(
                @icon-twitter
            );
        }

        &__social-link--instagram &__social-icon {
            .lib-icon-font-symbol(
                @icon-instagram
            );
        }

        &__social-link--youtube &__social-icon {
            .lib-icon-font-symbol(
                @icon-youtube
            );
        }
    }

    .payment-options {
        margin-top: @indent__xl;

        &__title {
            .lib-css(font-size, 18px);
            .lib-css(font-weight, @font-weight__bold);
            .lib-css(color, @gray-400);
            margin: 0 0 20px 0;
        }

        &__list {
            .lib-list-reset-styles();
            .lib-vendor-prefix-display(flex);
            gap: @indent__s;
        }

        &__item {
            .lib-vendor-prefix-display(flex);
            align-items: center;
            justify-content: center;
            margin-bottom: 0;
        }

        &__icon {
            display: block;
            height: 30px;
            width: auto;
            max-width: 50px;
            object-fit: contain;

            &--cash {
                width: auto;
                max-width: none;
                .lib-css(font-size, 12px);
                .lib-css(font-weight, @font-weight__semibold);
                .lib-css(color, @color-white);
                text-transform: uppercase;
            }
        }
    }

    .footer-bottom-row {
        margin: 0 auto;
        padding: 15px 0;
        background-color: @footer-bottom-row__background-color;
        .bottom-content { 
            .lib-vendor-prefix-display();
            .lib-vendor-prefix-flex-direction();
            .lib-css(justify-content, space-between);
            .lib-font-size(12);
            line-height:normal;
            text-align: center;
            color: @gray-500;
        }
    }
    

    .page-header .panel.wrapper,
    .page-footer {
        .switcher {
            margin-right: 10px;

            .options {
                .lib-dropdown(
                @_dropdown-actions-padding: 0,
                @_dropdown-list-item-padding: 0,
                @_dropdown-toggle-icon-content: @icon-down,
                @_dropdown-toggle-active-icon-content: @icon-up,
                @_icon-font-text-hide: true,
                @_icon-font-size: 22px,
                @_icon-font-line-height: 22px,
                @_dropdown-list-min-width: 160px
                );

                ul.dropdown {
                    a {
                        display: block;
                        padding: 8px;

                        &:hover {
                            text-decoration: none;
                        }
                    }
                }
            }

            li {
                font-size: @font-size__s;
                margin: 0;
            }

            .label {
                &:extend(.abs-visually-hidden all);
            }

            strong {
                font-weight: @font-weight__regular;
            }
        }
    }

    //
    //  Widgets
    //  ---------------------------------------------

    .sidebar {
        .widget.block:not(:last-child),
        .widget:not(:last-child) {
            &:extend(.abs-margin-for-blocks-and-widgets all);
        }
    }

    .widget {
        clear: both;

        .block-title {
            &:extend(.abs-block-title all);
        }
    }

    .page-header,
    .page-footer {
        .widget.block {
            .lib-css(margin, 0 0);
        }
    }

    .no-display {
        &:extend(.abs-no-display all);
    }

    //
    //  Calendar
    //  ---------------------------------------------

    .ui-datepicker {
        td {
            padding: 0;
        }
    }

    .cookie-status-message {
        display: none;
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) { 

    .footer-services {
        .service-list {
            justify-content: center;

        }
        .service-item {
            .lib-vendor-prefix-flex-direction();
            .lib-vendor-prefix-flex-basis(33.33%);
   

            .icon-wrapper {
                height: 30px;
            }
          

            .service-label {
                .lib-font-size(12);
               
            }
        }
    }

}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .footer-links-wrapper {
        .footer-section {
            [data-role="trigger"] {
                .title {
                    padding: @indent__s;
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .page-wrapper {
        > .breadcrumbs,
        > .top-container,
        > .widget {
            box-sizing: border-box;
            width: 100%;
        }
    }



    .navigation ul {
        padding: 0 0;
    }

    .header-links-wrapper {
        position: absolute;
        right: 20px;
        top:12px;
       

    }

    .header {
        &.panel {
            > .header.links {
                float: right;
                font-size: 0;
                .lib-list-inline();
                margin-left: auto;

                > li {
                
                    margin: 0 0 0 0;

                    &.welcome,
                    a {
                        line-height: 1.4;
                    }

                    &.welcome {
                        .lib-visually-hidden();
                        a {
                            .lib-css(padding-left, @indent__xs);
                        }
                    }

                    &.authorization-link {
                        a {
                            .lib-icon-font(
                                @icon-account,
                                @_icon-font-size: 26px,
                                @_icon-font-text-hide: true,
                                @_icon-font-color: @header-icons-color,
                                @_icon-font-color-hover: @header-icons-color-hover
                                );
                                .lib-text-hide();
                             &:before {
                                .lib-vendor-prefix-display(inline-flex);
                                .lib-vendor-box-align(center);
                                justify-content: center;
                                width: 35px;                                
                                height: 35px;                                
                             }

                        }
                    }

                }
            }
        }

        &.content:extend(.abs-add-clearfix-desktop all) {
            padding: @indent__base ;
            .lib-vendor-prefix-display();
            justify-content: center;   
           
        }
    }

    .customer-welcome {
        .lib-dropdown(
            @_toggle-selector: ~'.action.switch',
            @_options-selector: ~'ul',
            @_dropdown-actions-padding: 0,
            @_dropdown-list-item-padding: 0,
            @_dropdown-toggle-icon-content: @icon-account,
            @_dropdown-toggle-active-icon-content: @icon-account,
            @_dropdown-list-border: 1px solid @gray-100,
            @_dropdown-list-shadow: 0px 10px 15px rgba(0, 0, 0, 0.2), 
            @_icon-font-text-hide: true,
            @_icon-font-size: 26px,
            @_icon-font-line-height: 26px,
            @_dropdown-list-pointer-position: right,
            @_dropdown-list-pointer-position-left-right : 53px,
            @_dropdown-list-pointer-border: @gray-100,
            @_dropdown-list-position-right: 50%,
            @_dropdown-list-item-hover: @theme__color__primary,
            @_dropdown-list-z-index: @customer-welcome__z-index   
        );

        ul {
            transform: translateX(calc(50% - 30px));
        }

        li:extend(.switcher li all) {
            a {
                .lib-link(
                @_link-color: @theme__color__primary,
                @_link-text-decoration: none,
                @_link-color-visited:@theme__color__primary,
                @_link-text-decoration-visited: none,
                @_link-color-hover: @theme__color__primary-alt,
                @_link-text-decoration-hover: none,
                @_link-color-active: @theme__color__primary-alt,
                @_link-text-decoration-active: none
                );
                .lib-font-size(14);
                display: block;
                
                padding: 8px;
            }
        }

        .customer-name {
            cursor: pointer;
        }

        .customer-menu {
            display: none;
        }

        .action.switch {
            .lib-button-reset();
            .lib-css(color, @color-black);
        }

        .header.links {
            min-width: 175px;
            z-index: @customer-welcome__z-index;
        }

        &.active {
            .action.switch {
                .lib-icon-font-symbol(
                @_icon-font-content: @icon-account,
                @_icon-font-position: after
                );
            }

            .customer-menu {
                display: block;
            }
        }

        .greet {
            display: none;
        }

        + .authorization-link {
            .lib-visually-hidden();
        }
    }

    .page-header {
        border: 0;
        margin-bottom: 0;

        .panel.wrapper {
            border-bottom: 1px solid @secondary__color;
        }

        .header.panel {
            &:extend(.abs-add-clearfix-desktop all); 

        }

        .switcher {
            display: inline-block;   
        }
    }

    .page-main {
        > .page-title-wrapper {
            .page-title {
                display: inline-block;
            }

            .page-title + .action {
                float: right;
                margin-top: @indent__base;
            }
        }
    }
    .ammenu-logo,
    .logo {
        margin: 0 auto;

        img {
            max-height: inherit;
        }
    }


    .footer-upper-row-col-1,
    .footer-upper-row-col-2 {
         .lib-vendor-prefix-flex-basis(50%);
    }

    
    .footer-upper-row-col-2 {
        max-width: 590px;
       
    }

    .footer-links-wrapper {
        .lib-vendor-prefix-display(flex);
        .lib-vendor-prefix-flex-wrap(wrap);

        .footer-section {
            .lib-vendor-prefix-flex-basis(30%);
            border-bottom: none;

            [data-role="trigger"] {
                cursor: default;

                .title {
                   

                    &:after {
                        content: none;
                    }
                }
            }

            [data-role="content"] {
                display: block !important;
                height: auto !important;
            }

            .items {
                padding: 0;
            }
        }
    }

    .glamour-trends {
        max-width: 305px;

    }


}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__l) {


}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {

    .footer-upper-row {
        padding: 30px 0;
        .upper-content { 
            .lib-vendor-prefix-flex-direction(row);
            .lib-vendor-box-align(center);
            .lib-css(justify-content, space-between);
            gap: 50px;
            position: relative;
            &::before {
                content: '';
                position: absolute;
                width: 30px;
                height: 1px;
                background-color: @gray-600;
                left:50%;
                margin: auto;
                top: 50%;
                transform: translateY(-50%);
                transform: translateX(-50%);

            }
        }
    }

    .footer-middle-row {
        margin: 0 auto;
        padding: 30px 0;
        .middle-content {
            .lib-vendor-prefix-flex-direction(row);
            .lib-css(justify-content, space-between);
            gap: 50px;


        }
    }
    .footer-middle-row-col-left {
        .lib-vendor-prefix-flex-basis(50%);
        position: relative;
        &::before {
            content: '';
            position: absolute;
            width: 1px;
            height: 100%;
            background-color: @gray-600;
            right:30px;
            margin: auto;
            top: 0;

        }
    }

    .footer-middle-row-col-right {
        .lib-vendor-prefix-flex-basis(50%);   
        .lib-vendor-prefix-display();
        color: @gray-600;
        justify-content: space-between;
    }
    .footer-social-payment-wrapper {
        .lib-vendor-prefix-display();
        .lib-vendor-prefix-flex-direction();
        .lib-vendor-prefix-flex-grow(1);
        max-width: 330px;
        padding-left: 30px;
       
    }

    .scroll-to-top {
        position: fixed;
        bottom: 10px;
        right: 10px;
        z-index: 100; 
    }
  
    .backtoTop {
        .lib-button-reset();
        .lib-button(
            @_button-padding: 5px,
            @_button-color: @theme__color__primary-alt,
            @_button-color-hover: @gray-600,
            @_button-color-active: @gray-600,
            @_button-background: @theme__color__primary,
            @_button-background-hover: @theme__color__primary,
            @_button-background-active: @theme__color__primary,
            @_button-border: false,
            @_button-border-hover: false,
            @_button-border-active: false,
            @_button-icon-use: true,
            @_button-font-content: @icon-arrow-short-up,   
            @_button-icon-font-size: 24px,
            @_button-icon-font-line-height: 24px,
            @_button-icon-font-position: before,
            @_button-icon-font-text-hide: true,  
        );
        border-radius: 50px;
        width: 50px;
        height: 50px;
    }

}