<?xml version="1.0"?>
<!--
/**
* Copyright © Magento, Inc. All rights reserved.
* See COPYING.txt for license details.
*/
-->
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="head.additional">
            <block class="Magento\Framework\View\Element\Text" name="theme.fonts">
                <arguments>
                    <argument name="text" xsi:type="string">
                        <![CDATA[<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css2?family=Playfair:ital,opsz,wght@0,5..1200,300..900;1,5..1200,300..900&amp;family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&amp;display=swap" rel="stylesheet">               ]]>
                    </argument>
                </arguments>
            </block>
        </referenceBlock>
 
        <referenceContainer name="header.panel.wrapper">
            <container name="utility.announcements.wrapper" htmlTag="div" htmlClass="utility-announcements-wrapper" before="-">
                <block class="Magento\Cms\Block\Block" name="utility.announcements">
                    <arguments>
                        <argument name="block_id" xsi:type="string">utility_announcements</argument>  
                    </arguments>
                </block>
            </container>
        </referenceContainer>
        <referenceContainer name="header-wrapper">
            <container name="header.links.wrapper" htmlTag="div" htmlClass="header-links-wrapper" after="-">
            <container name="header.links.container" htmlTag="div" htmlClass="header-links-container"  />
            </container>
        </referenceContainer>

         
        
         <move element="skip_to_content" destination="header-wrapper" before="logo"/>
         <move element="minicart" destination="header.links.container" after="-"/>
         <move element="top.search" destination="header.links.container" after="-"/>
         <move element="header.panel" destination="header.links.container" after="top.search"/>
         <move element="navigation.sections" destination="header.container" after="-"/>
 
 
 
                <referenceBlock name="register-link" display="false" />
                
 
 
 
 
        <referenceContainer name="header.panel">
            <block class="Magento\Framework\View\Element\Html\Links" name="header.links">
                <arguments>
                    <argument name="css_class" xsi:type="string">header links</argument>
                </arguments>
            </block>
        </referenceContainer>
 
        <referenceBlock name="logo">
            <arguments>
                <argument name="logo_width" xsi:type="number">264</argument>
            </arguments>
        </referenceBlock>
 
 
 
        <referenceContainer name="footer-container">
            <container name="footer.upper.row" htmlTag="div" htmlClass="footer-upper-row">
                <container name="footer.upper.row.inner" htmlTag="div" htmlClass="footer content upper-content">
                    <container name="footer.upper.row.col.one" htmlTag="div" htmlClass="footer-upper-row-col-1">
                        <block class="Magento\Cms\Block\Block" name="footer.service.section">
                            <arguments>
                                <argument name="block_id" xsi:type="string">footer_service_section</argument>
                            </arguments>
                        </block>
                    </container>
                    <container name="footer.upper.row.col.second" htmlTag="div" htmlClass="footer-upper-row-col-2" /></container>
            </container>
            <container name="footer.middle.row" htmlTag="div" htmlClass="footer-middle-row">
                <container name="footer.middle.row.inner" htmlTag="div" htmlClass="footer content middle-content">
                    <container name="footer.middle.row.col.left" htmlTag="div" htmlClass="footer-middle-row-col-left">
                        <block class="Magento\Cms\Block\Block" name="footer.cms.links">
                            <arguments>
                                <argument name="block_id" xsi:type="string">footer_cms_links</argument>
                            </arguments>
                        </block>
                    </container>
                    <container name="footer.middle.row.col.two" htmlTag="div" htmlClass="footer-middle-row-col-right">
                        <block class="Magento\Cms\Block\Block" name="footer.glamour.trends">
                            <arguments>
                                <argument name="block_id" xsi:type="string">footer_glamour_trends</argument>
                            </arguments>
                        </block>
 
                        <container name="footer.social.and.payment" htmlTag="div" htmlClass="footer-social-payment-wrapper">
                            <block class="Magento\Cms\Block\Block" name="Footer.Social.Media.Info">
                                <arguments>
                                    <argument name="block_id" xsi:type="string">footer_social_media_info</argument>
                                </arguments>
                            </block>
                            <block class="Magento\Cms\Block\Block" name="Footer.Available.Payment.Options.Info">
                                <arguments>
                                    <argument name="block_id" xsi:type="string">footer_available_payment_options_info</argument>
                                </arguments>
                            </block>
                        </container>
                    </container>
                </container>
            </container>
            <container name="footer.bottom.row" htmlTag="div" htmlClass="footer-bottom-row">
                <container name="footer.bottom.row.inner" htmlTag="div" htmlClass="footer content bottom-content">
                    <block class="Magento\Cms\Block\Block" name="Footer.Copyright.Info">
                        <arguments>
                            <argument name="block_id" xsi:type="string">footer_copyright_info</argument>
                        </arguments>
                    </block>
                </container>
            </container>
        </referenceContainer>
        <referenceBlock name="copyright" remove="true" />
        <referenceBlock name="footer_links" remove="true" />
        <referenceBlock name="report.bugs" remove="true" />
 
        <referenceBlock name="advanced-search-link" remove="true"/>


                  <referenceContainer name="before.body.end">
            <block name="scroll.to.top" template="Magento_Theme::scroll-to-top.phtml">
                <arguments>
                    <argument name="position" xsi:type="string">end</argument> <!-- Button position (start/center/end) -->
                    <argument name="visible_offset" xsi:type="number">200</argument> <!-- Offset before showing the button -->
                    <argument name="visible_only_to_top" xsi:type="boolean">true</argument> <!-- Only show when scrolling to top (true/false) -->
                    <argument name="hide_on_inactivity" xsi:type="number">4000</argument> <!-- Hide after x ms -->
                </arguments>
            </block>
        </referenceContainer>
    
    </body>
</page>