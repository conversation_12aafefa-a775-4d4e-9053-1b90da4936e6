<?xml version="1.0"?>
<!--
/**
 * Theme override for <PERSON><PERSON>o_Customer forgot password page
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <!-- Create a new container for the forgot password page content -->
        <referenceContainer name="content">
            <container name="customer.forgotpassword.page.wrapper" htmlTag="div" htmlClass="customer-forgotpassword-page-wrapper" before="-">
                <!-- This container will hold both the page title and forgot password form -->
            </container>
        </referenceContainer>
        
        <!-- Move the page title into the new container -->
        <move element="page.main.title" destination="customer.forgotpassword.page.wrapper" before="-"/>
        
        <!-- Move the forgot password form into the new wrapper -->
        <!-- The correct name based on Magento core is "forgotPassword" -->
        <move element="forgotPassword" destination="customer.forgotpassword.page.wrapper" after="page.main.title"/>
        
        <!-- If SMSProfile module is active, also move its container -->
        <move element="forgot-parent" destination="customer.forgotpassword.page.wrapper" after="page.main.title"/>
    </body>
</page>
