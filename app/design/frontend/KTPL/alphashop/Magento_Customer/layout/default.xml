<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="header.links">
            <block class="Magento\Customer\Block\Account\Customer" name="customer" template="Magento_Customer::account/customer.phtml" before="-"/>
            <block class="Magento\Customer\Block\Account\AuthorizationLink" name="authorization-link-login" template="Magento_Customer::account/link/authorization.phtml">
                <arguments>
                    <argument name="sortOrder" xsi:type="number">10</argument>
                </arguments>
            </block>
        </referenceBlock>
        <move element="header" destination="header.links" before="-"/>
        <move element="register-link" destination="header.links"/>
        <move element="top.links" destination="customer"/>
        <move element="authorization-link" destination="top.links" after="-"/>
    </body>
</page>
