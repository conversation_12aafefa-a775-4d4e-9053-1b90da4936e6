<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Welcome to %store_name" store_name=$store.frontend_name}} @-->
<!--@vars {
"var store.frontend_name":"Store Name",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var customer.email":"Customer Email",
"var customer.name":"Customer Name",
"var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])":"Password Reset URL"
} @-->

{{template config_path="design/email/header_template"}}

<p class="greeting">{{trans "%name," name=$customer.name}}</p>
<p>{{trans "Welcome to %store_name." store_name=$store.frontend_name}}</p>
<p>
    {{trans
        'To sign in to our site, use these credentials during checkout or on the <a href="%customer_url">My Account</a> page:'

        customer_url=$this.getUrl($store,'customer/account/',[_nosid:1])
    |raw}}
</p>
<table class="email-credentials">
    <tr>
        <th>{{trans "Email:"}}</th>
        <td>{{var customer.email}}</td>
    </tr>
    <tr>
        <th>{{trans "Password:"}}</th>
        <td><em>{{trans "Password you set when creating account"}}</em></td>
    </tr>
</table>
<p>
    {{trans
        'Forgot your account password? Click <a href="%reset_url">here</a> to reset it.'

        reset_url="$this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])"
    |raw}}
</p>
<br />
<p>{{trans "When you sign in to your account, you will be able to:"}}</p>
<table class="email-features">
    <tr>
        <td>
            <table>
                <tr>
                    <td>
                        <img src="{{view url='Magento_Customer/images/icn_checkout.png'}}" height="30" width="30" alt="{{trans 'Quick Checkout'}}" />
                    </td>
                    <td>
                        <h3>{{trans "Proceed through checkout faster"}}</h3>
                    </td>
                </tr>
            </table>
        </td>
        <td>
            <table>
                <tr>
                    <td>
                        <img src="{{view url='Magento_Customer/images/icn_status.png'}}" height="30" width="30" alt="{{trans 'Order Status'}}" />
                    </td>
                    <td>
                        <h3>{{trans "Check the status of orders"}}</h3>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td>
            <table>
                <tr>
                    <td>
                        <img src="{{view url='Magento_Customer/images/icn_address.png'}}" height="30" width="30" alt="{{trans 'Manage Addresses'}}" />
                    </td>
                    <td>
                        <h3>{{trans "Store alternative addresses"}}</h3>
                        <p>{{trans "For shipping to multiple family members and friends"}}</p>
                    </td>
                </tr>
            </table>
        </td>
        <td>
            <table>
                <tr>
                    <td>
                        <img src="{{view url='Magento_Customer/images/icn_history.png'}}" height="30" width="30" alt="{{trans 'Order History'}}" />
                    </td>
                    <td>
                        <h3>{{trans "View past orders"}}</h3>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
