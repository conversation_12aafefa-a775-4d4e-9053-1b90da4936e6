// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@autocomplete__background-color: @color-white;
@autocomplete__border: 1px solid @gray-100;
@autocomplete-item__border: 1px solid @gray-100;
@autocomplete-item__hover__color:  @gray-100;
@autocomplete-item-amount__color: @color-gray60;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .open-search  {
        overflow: hidden;
        height: 100vh;
        &::before {
            content: '';
            position: fixed;
            background-color:rgba(0,0,0,.2);
            left: 0;
            top: 0;
            right: 0;
            bottom:0;
            z-index: 10;
        }
    }

  
    .search-block-wrapper {
            .open-search & {
                position: fixed;
                left: 0;
                top: 0;
                right: 0;
                min-height: 300px;
                z-index: 1001;
                background-color: @gray-100;  
                border-top: 5px solid @color-black;

                .search-block-container {
                    background-color: @color-white;
                    padding: @indent__base;
                    position: relative;
            
                }

                

        }
        
    }




    
    .block-search {
        margin-bottom: 0;
        .open-search & {
            max-width: 1100px;
            width: 100%;
            margin: 0 auto;
            
        }

        .actions-toolbar {
            display: none;
            .open-search & {
                display: block;
                position: absolute;
                right: 0;
                top: 0;

            .close {
                .lib-button-reset();
                .lib-button(
                    @_button-padding: 5px,
                    @_button-color: @theme__color__primary,
                    @_button-color-hover: @gray-600,
                    @_button-color-active: @gray-600,
                    @_button-background: false,
                    @_button-background-hover: false,
                    @_button-background-active: false,
                    @_button-border: false,
                    @_button-border-hover: false,
                    @_button-border-active: false,
                    @_button-icon-use: true,
                    @_button-font-content: @icon-remove,
                    @_button-icon-font-size: 44px,
                    @_button-icon-font-line-height: 40px,
                    @_button-icon-font-position: before,
                    @_button-icon-font-text-hide: true, 
                );
            }
            }
        }

        .block {
            &-title {
                display: none;
            }
        }

        .actions {
            display: none;
        }

        .block-content {
            margin-bottom: 0;
        }

        .label {
            .lib-icon-font(
                @_icon-font-content: @icon-search,
                @_icon-font-size: 30px,
                @_icon-font-line-height: 35px,
                @_icon-font-color: @header-icons-color,
                @_icon-font-color-hover: @header-icons-color-hover,
                @_icon-font-color-active: @header-icons-color-hover,
                @_icon-font-text-hide: true
            );
            text-decoration: none;
            display: inline-block;
            width: 35px;
            height: 35px;
            line-height: 35px;
            text-align: center;

            &.active {
                + .control {
                    input {
                        
                    }
                }
            }
            &::before {
                width: 35px;
            }
            .open-search & {
                display: none;
            }
        }

        .action.search {
            display: none;
            .open-search & {
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);

                .lib-button-reset();
                .lib-button(
                    @_button-padding: 5px,
                    @_button-color: @theme__color__primary,
                    @_button-color-hover: @gray-600,
                    @_button-color-active: @gray-600,
                    @_button-background: false,
                    @_button-background-hover: false,
                    @_button-background-active: false,
                    @_button-border: false,
                    @_button-border-hover: false,
                    @_button-border-active: false,
                    @_button-icon-use: true,
                    @_button-font-content: @icon-search,
                    @_button-icon-font-size: 26px,
                    @_button-icon-font-line-height: 26px,
                    @_button-icon-font-position: before,
                    @_button-icon-font-text-hide: true, 
                );

            }
        }

        .control {
            display: none;
            .open-search & {
                display: block;
                position: relative;
                

            }

        }

        .input-text {   
            .lib-form-element-input(
            @_type: input-text,
            @_color: @color-black,
            @_background:@color-white,
            @_border: 1px solid transparent,
            @_border-radius: 0,
            @_width: 100%,
            @_height: 50px,
            @_padding: 10px 10px 10px 10px, 
            @_focus-background: transparent,
            @_focus-border: 1px solid transparent,
            @_focus-color: @gray-600,
            );
        }

        .nested {
            display: none;
        }


    }

    .search-autocomplete {
        &:extend(.abs-add-box-sizing all);
        display: none;
        margin-top: 0;
        overflow: hidden;
        position: absolute;
        z-index: 3;
        box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.2);

        ul {
            .lib-list-reset-styles();

            li {
                .lib-css(border-top, @autocomplete-item__border);
                cursor: pointer;
                margin: 0;
                padding: @indent__s @indent__xl @indent__s @indent__s;
                position: relative;
                text-align: left;
                white-space: normal;

                &:not(:empty) {
                    border-top: 0;
                    .lib-css(border, @autocomplete__border);
                    .lib-css(background, @autocomplete__background-color);
                }

                &:first-child {
                    border-top: none;
                }

                &:hover,
                &.selected {
                    .lib-css(background, @autocomplete-item__hover__color);
                }

                .amount {
                    .lib-css(color, @autocomplete-item-amount__color);
                    position: absolute;
                    right: 7px;
                    top: @indent__xs;
                }
            }
        }
    }

    .form.search.advanced {
        .fields.range {
            .field {
                &:first-child {
                    position: relative;

                    .control {
                        padding-right: 0;

                        &:after {
                            content: ' \2013 ';
                            display: inline-block;
                            position: absolute;
                            right: 0;
                            text-align: center;
                            top: 6px;
                            width: 25px;
                        }
                    }
                }

                &:last-child {
                    position: relative;

                    div.mage-error {
                        left: 0;
                        position: absolute;
                        top: 32px;
                    }
                }

                &.with-addon {
                    .control {
                        padding-right: 0;
                    }
                }
            }
        }

        .group.price {
            .addon {
                .addafter {
                    background: none;
                    border: 0;
                    padding-top: 6px;
                    position: absolute;
                    right: 0;
                    top: 0;
                }
            }
        }
    }

    .search.summary {
        margin-bottom: @indent__s;
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .block-search {
        .block-content {
            margin-bottom: 0;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .block-search {
        margin-top: @indent__xs;
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .block-search {
        
        .control {
            
        }

        .nested {
            display: block;
            padding-top: 5px;
            position: absolute;
        }

        input {
            .lib-input-placeholder(@form-element-input-placeholder__color);
          
        }

/*         .action.search {
            display: inline-block;
            .lib-button-icon(
                @_icon-font-content: @icon-search,
                @_icon-font-text-hide: true,
                @_icon-font-size: 26px,
                @_icon-font-color: @header-icons-color
            );
            .lib-button-reset();
            padding: @indent__xs 0;
            position: absolute;
            right: 10px;
            top: 0;
            z-index: 1;

            &:focus {
                &:before {
                    .lib-css(color, @color-gray20);
                }
            }
        } */
    }

    .search-autocomplete {
        margin-top: 0;
    }
}
