// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Layout variables
//  _____________________________________________

//  Widths
@layout__width: ''; // for the fixed width layout
@layout__max-width: 1860px;
@footer-layout__max-width: 1390px;
@layout-indent__width: 20px;
@layout__width-xs-indent: 15px;


//
//  Typography
//  ---------------------------------------------

@icons__font-name: 'glamour-trends-icons';
@icons__font-path: "@{baseDir}fonts/glamour-trends-icons/glamour-trends"; 

//  Fonts
@font-family-name__base: 'Roboto Condensed';
@font-family__base: @font-family-name__base, @font-family__sans-serif;
@font-family-name__secondary: 'Playfair';
@font-family__secondary: @font-family-name__secondary, @font-family__sans-serif; 



//
//  Color nesting
//  ---------------------------------------------

@theme__color__primary: @color-black;
@theme__color__primary-alt: @color-white;
@theme__color__secondary: @color-orange-red1;

@primary__color: @color-black;


//
//  Links
//  ---------------------------------------------

@link__color: @theme__color__primary;
@link__text-decoration: none;

@link__visited__color: @theme__color__primary;
@link__visited__text-decoration: none;

@link__hover__color: @gray-600;
@link__hover__text-decoration: underline;

@link__active__color: @gray-600;
@link__active__text-decoration: underline;


@secondary__color: @color-gray91;
@gray-100: #F4F4F4;
@gray-200: #4F4E52;
@gray-300: '';
@gray-400: #cccccc;
@gray-500: #4E4E4E;
@gray-600: #666666;
@gray-700: #333333;
@gray-800: #141414;
@gray-900: '';


@page__background-color: @color-white;
@panel__background-color: darken(@page__background-color, 6%);

@active__color: @theme__color__secondary;
@error__color: @color-red10;


//
//  Sections variables
//  _____________________________________________

//
//  Tabs
//  ---------------------------------------------
@tab-content__border-top-status: true;


//
//  Sidebar
//  ---------------------------------------------

@sidebar__background-color: @color-white-smoke; // Used in cart sidebar, Checkout sidebar, Tier Prices, My account navigation, Rating block background



//
//  Buttons
//  ---------------------------------------------

//  Font style
@button__font-family: @font-family__base;
@button__font-size: 18px;
@button__font-weight: @font-weight__bold;
@button__line-height: @font-size__base + 2;
@button__margin: 0;
@button__padding: 18px 28px; // is set up to false when buttons don't depend on side paddings or have fixed width



//  Display settings
@button__display: inline-flex;
@button__cursor: pointer;
@button__border-radius: 3px;


//  Default = secondary button
@button__color: @primary__color;
@button__background: @color-white;
@button__border: 1px solid @theme__color__primary;

@button__hover__color: @color-white;
@button__hover__background: @theme__color__primary;
@button__hover__border: @button__border;

@button__active__color: @color-white;
@button__active__background:@theme__color__primary;
@button__active__border: @button__border; 



@button-primary__background: @theme__color__primary;
@button-primary__border: 1px solid @theme__color__primary;
@button-primary__color: @color-white;


@button-primary__hover__background: @theme__color__primary-alt;
@button-primary__hover__border: 1px solid @theme__color__primary;
@button-primary__hover__color: @theme__color__primary;

@button-primary__active__background: @theme__color__primary-alt;
@button-primary__active__border: @button-primary__hover__border; 
@button-primary__active__color: @theme__color__primary; 


//
//   Variables for Glamour Trends Theme
//  ---------------------------------------------
@icon-help: "\e623"; 
@icon-gift-registry: "\e628"; 
@icon-present: "\e629";
@icon-account: "\e627";
@icon-arrow-up-thin: "\e633";
@icon-arrow-right-thin: "\e624";
@icon-arrow-left-thin: "\e625";
@icon-arrow-down-thin: "\e626";
@icon-wishlist-full: "\e600";
@icon-wishlist-empty: "\e601";
@icon-warning: "\e602";
@icon-update: "\e603";
@icon-trash: "\e604";
@icon-star: "\e605";
@icon-settings: "\e606";
@icon-pointer-down: "\e607";
@icon-next: "\e608";
@icon-menu: "\e609";
@icon-location: "\e60a";
@icon-list: "\e60b";
@icon-info: "\e60c";
@icon-grid: "\e60d";
@icon-comment-reflected: "\e60e";
@icon-collapse: "\e60f";
@icon-checkmark: "\e610";
@icon-cart: "\e611";
@icon-calendar: "\e612";
@icon-arrow-up: "\e613";
@icon-arrow-down: "\e614";
@icon-search: "\e615";
@icon-remove: "\e616";
@icon-prev: "\e617";
@icon-pointer-up: "\e618";
@icon-pointer-right: "\e619";
@icon-pointer-left: "\e61a";
@icon-flag: "\e61b";
@icon-expand: "\e61c";
@icon-envelope: "\e61d";
@icon-compare-full: "\e61e";
@icon-compare-empty: "\e61f";
@icon-comment: "\e620";
@icon-up: "\e621";
@icon-down: "\e622";
@icon-twitter: "\e900";
@icon-youtube: "\e901";
@icon-facebook: "\e902";
@icon-instagram: "\e903";
@icon-arrow-short-left: "\e904";
@icon-arrow-short-right: "\e905";
@icon-arrow-short-down: "\e906";
@icon-arrow-short-up: "\e907";
@icon-live-chat: "\e908";
@icon-offer: "\e909";
@icon-gift-cart: "\e90a";
@icon-pin-location: "\e90b";
@icon-phone-call: "\e90c";
@icon-edit: "\e90c";


//
//  Navigation variables
//  _____________________________________________

@navigation__background: @color-white;



//
//  Desktop navigation
//  ---------------------------------------------

@navigation-desktop__background:  @gray-100;
@navigation-desktop__border: 1px solid @gray-400;
@navigation-desktop__font-size: '';



@menu-brand-items-wrapper-bg: #ff6600;    


//
//  Amasty Module Variables
//  _____________________________________________

@ammenu__indent__xxl: ''; 