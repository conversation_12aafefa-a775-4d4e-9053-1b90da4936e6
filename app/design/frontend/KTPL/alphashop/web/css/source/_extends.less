// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Styles Extends
//  _____________________________________________

//
//  List default styles reset
//  ---------------------------------------------

/**
 * @codingStandardsIgnoreStart
 */
& when (@media-common = true) {
    .abs-reset-list {
        .lib-list-reset-styles();
        > li {
            margin: 0;
        }
    }
}

//
//  Link as a button
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-action-link-button {
        .lib-button();
        .lib-link-as-button();
        .lib-css(border-radius, @button__border-radius);
    }
}

//
//  Product options list
//  ---------------------------------------------

@abs-product-options-list: {
    dt {
        clear: left;
        float: left;
        margin: 0 @indent__s @indent__xs 0;

        &:after {
            content: ': ';
        }
    }

    dd {
        display: inline-block;
        float: left;
        margin: 0 0 @indent__xs;
        word-break: break-all;
    }
};

& when (@media-common = true) {
    .abs-product-options-list {
        @abs-product-options-list();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-product-options-list-desktop {
        @abs-product-options-list();
    }
}

//
//  Button reset width, floats, margins
//  ---------------------------------------------

@abs-button-responsive: {
    .lib-button-responsive();
};

& when (@media-common = true) {
    .abs-button-responsive {
        @abs-button-responsive();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-button-desktop {
        width: auto;
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .abs-button-responsive-smaller {
        @abs-button-responsive();
    }
}

//
//  Blocks in 2 columns
//  ---------------------------------------------

@abs-blocks-2columns: {
    width: 48.8%;

    &:nth-child(odd) {
        clear: left;
        float: left;
    }

    &:nth-child(even) {
        float: right;
    }
};

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-blocks-2columns {
        @abs-blocks-2columns();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .abs-blocks-2columns-s {
        @abs-blocks-2columns();
    }
}

//
//  Reset image alignment in container
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-reset-image-wrapper {
        height: auto;
        padding: 0 !important;

        .product-image-photo {
            position: static;
        }
    }
}

//
//  Adaptive images
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-adaptive-images {
        display: block;
        height: auto;
        max-width: 100%;
    }

    .abs-adaptive-images-centered {
        display: block;
        height: auto;
        margin: 0 auto;
        max-width: 100%;
    }
}

//
//  Title for login blocks
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-login-block-title {
        strong {
            font-weight: @font-weight__heavier;
        }

        .lib-font-size(18);
        border-bottom: 1px solid @secondary__color;
        margin-bottom: 15px;
        padding-bottom: 12px;
    }
}

//
//  Abstract block title
//  ---------------------------------------------

@abs-block-title: {
    margin-bottom: 15px;

    > strong {
        .lib-heading(h3);
    }
};

& when (@media-common = true) {
    .abs-block-title {
        @abs-block-title();
    }
}

//
//  Account blocks
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-account-blocks {
        .block-title {
            @abs-block-title();

            > .action {
                margin-left: 15px;
            }
        }

        .box-title {
            display: inline-block;
            margin: 0 0 @indent__xs;

            > span {
                .lib-heading(h4);
            }

            > .action {
                font-weight: @font-weight__regular;
                margin-left: @indent__s;
            }
        }

        .block-content {
            p:last-child {
                margin-bottom: 0;
            }

            .box {
                margin-bottom: @indent__base;
            }
        }
    }
}

//
//  Simple Dropdown
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-dropdown-simple {
        .lib-dropdown(
            @_dropdown-list-item-padding: 5px 5px 5px 23px,
            @_dropdown-list-min-width: 200px,
            @_icon-font-margin: 0 0 0 5px,
            @_icon-font-vertical-align: middle
        );
    }
}

//
//  Input quantity
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-input-qty {
        text-align: center;
        width: 47px;
    }
}

//
//  Margin for blocks & widgets
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-margin-for-blocks-and-widgets {
        margin-bottom: @indent__xl;
    }
}

//
//  Remove button for blocks
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-remove-button-for-blocks {
        .lib-icon-font(
            @icon-remove,
            @_icon-font-size: 26px,
            @_icon-font-line-height: 15px,
            @_icon-font-text-hide: true,
            @_icon-font-color: @color-gray19,
            @_icon-font-color-hover: @color-gray19,
            @_icon-font-color-active: @color-gray19
        );
    }
}

//
//  Product link
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-product-link {
        font-weight: @font-weight__regular;

        > a {
            .lib-link(
                @_link-color: @product-name-link__color,
                @_link-text-decoration: @product-name-link__text-decoration,
                @_link-color-visited: @product-name-link__color__visited,
                @_link-text-decoration-visited: @product-name-link__text-decoration__visited,
                @_link-color-hover: @product-name-link__color__hover,
                @_link-text-decoration-hover: @product-name-link__text-decoration__hover,
                @_link-color-active: @product-name-link__color__active,
                @_link-text-decoration-active: @product-name-link__text-decoration__active
            );
        }
    }
}

//
//  Reset left margin
//  ---------------------------------------------

@abs-reset-left-margin: {
    margin-left: 0;
};

& when (@media-common = true) {
    .abs-reset-left-margin {
        @abs-reset-left-margin();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-reset-left-margin-desktop {
        @abs-reset-left-margin();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .abs-reset-left-margin-desktop-s {
        @abs-reset-left-margin();
    }
}

//
//  Button as a link
//  ---------------------------------------------

@abs-action-button-as-link: {
    .lib-button-as-link(@_margin: false);
    border-radius: 0;
    font-size: inherit;
    font-weight: @font-weight__regular;

    &:active,
    &:not(:focus) {
        box-shadow: none;
    }
};

& when (@media-common = true) {
    .abs-action-button-as-link {
        @abs-action-button-as-link();
    }
}

//
//  Action with icon remove with text
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-action-remove {
        @abs-action-button-as-link();
        left: @indent__s;
        margin-left: 70%;
        position: absolute;
        top: 31px;
        width: auto;
    }
}

//
//  Action with icon remove with text for desktop
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-action-remove-desktop when not (@form-field-type-label-inline__width = false) and not (@form-field-type-label-inline__width = '') {
        margin-left: @form-field-type-label-inline__width + 50%;
        top: 6px;
    }
}

//
//  Add Recipient
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-add-fields {
        .fieldset {
            margin-bottom: 50px;

            .field {
                &:not(.choice) {
                    .control {
                        width: 70%;
                    }
                }
            }

            .actions-toolbar {
                &:not(:first-child) {
                    &:extend(.abs-add-clearfix all);

                    > .secondary {
                        .action {
                            &.add {
                                margin-top: @indent__l;
                            }
                        }

                        float: left;
                    }
                }
            }

            .fields {
                .actions-toolbar {
                    margin: 0;
                }
            }
        }

        .message {
            &.notice {
                margin: @indent__l 0 0;
            }
        }

        .additional {
            margin-top: 55px;
            position: relative;
        }

        .action {
            &.remove {
                &:extend(.abs-action-remove all);
            }
        }
    }
}

//
//  Add Recipient for desktop
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-add-fields-desktop {
        .fieldset {
            .field {
                &:not(.choice) {
                    .control {
                        width: 50%;
                    }
                }
            }

            .additional {
                .action {
                    &.remove {
                        &:extend(.abs-action-remove-desktop all);
                    }
                }
            }
        }
    }
}

//
//  Margin for forms
//  ---------------------------------------------

@abs-margin-for-forms-desktop: {
    .lib-css(margin-left, @form-field-type-label-inline__width);
};

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-margin-for-forms-desktop {
        @abs-margin-for-forms-desktop();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .abs-margin-for-forms-desktop-s {
        @abs-margin-for-forms-desktop();
    }
}

//
//  Visibility hidden / show visibility hidden
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-hidden {
        .lib-visibility-hidden();
    }
}

//
//  Visually hidden / show visually hidden
//  ---------------------------------------------

@abs-visually-hidden: {
    .lib-visually-hidden();
};

& when (@media-common = true) {
    .abs-visually-hidden {
        @abs-visually-hidden();
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .abs-visually-hidden-mobile {
        @abs-visually-hidden();
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .abs-visually-hidden-mobile-m {
        @abs-visually-hidden();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .abs-visually-hidden-desktop-s {
        @abs-visually-hidden();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-visually-hidden-desktop {
        @abs-visually-hidden();
    }
}

//
//  Visually hidden reset
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-visually-hidden-reset {
        .lib-visually-hidden-reset();
    }
}

//
//  Clearfix
//  ---------------------------------------------

@abs-add-clearfix: {
    .lib-clearfix();
};

& when (@media-common = true) {
    .abs-add-clearfix {
        @abs-add-clearfix();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-add-clearfix-desktop {
        @abs-add-clearfix();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .abs-add-clearfix-desktop-s {
        @abs-add-clearfix();
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .abs-add-clearfix-mobile {
        @abs-add-clearfix();
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .abs-add-clearfix-mobile-m {
        @abs-add-clearfix();
    }
}

//
//  Box-sizing
//  ---------------------------------------------

@abs-add-box-sizing: {
    box-sizing: border-box;
};

& when (@media-common = true) {
    .abs-add-box-sizing {
        @abs-add-box-sizing();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-add-box-sizing-desktop {
        @abs-add-box-sizing();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .abs-add-box-sizing-desktop-s {
        @abs-add-box-sizing();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-add-box-sizing-desktop-m {
        @abs-add-box-sizing();
    }
}

//
//  Revert field type
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-revert-field-type-desktop {
        .fieldset {
            > .field,
            .fields > .field {
                .lib-form-field-type-revert(@_type: block);

                &:not(:first-child):last-of-type {
                    margin-bottom: 0;
                }
            }
        }
    }
}

//
//  Settings icons
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-navigation-icon {
        .lib-icon-font(
            @_icon-font-content: @icon-down,
            @_icon-font-size: 34px,
            @_icon-font-line-height: 1.2,
            @_icon-font-position: after,
            @_icon-font-display: block
        );

        &:after {
            position: absolute;
            right: @indent__xs;
            top: 0;
        }
    }
}

//
//  Split button
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-split-button {
        .lib-dropdown-split(
            @_options-selector : ~'.items',
            @_dropdown-split-button-border-radius-fix: true
        );
        vertical-align: middle;
    }
}

//
//  Action addto
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-action-addto-product {
        &:extend(.abs-action-link-button all);
        .lib-button-s();
    }

    .abs-actions-addto-gridlist {
        .lib-icon-font(
            @_icon-font-content: '',
            @_icon-font-size: 64px,
            @_icon-font-color: @addto-color,
            @_icon-font-color-hover: @addto-hover-color,
            @_icon-font-text-hide: true,
            @_icon-font-vertical-align: middle,
            @_icon-font-line-height: 60px
        );
    }
}

//
//  Large button
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-button-l {
        .lib-button-l();
    }
}

//
//  Button revert secondary color
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-revert-secondary-color {
        .lib-button-revert-secondary-color();
    }
}

//
//  Button revert secondary size
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-revert-secondary-size {
        .lib-button-revert-secondary-size();
    }
}

//
//  Box-tocart block
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-box-tocart {
        margin: @indent__s 0;
    }
}

//
//  Excl/Incl tax
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-adjustment-incl-excl-tax {
        .price-including-tax,
        .price-excluding-tax,
        .weee {
            .lib-font-size(14);
            display: inline-block;
            white-space: nowrap;
        }

        .price-including-tax + .price-excluding-tax {
            display: inline-block;
            .lib-font-size(11);

            &:before {
                content: '('attr(data-label)': ';
            }

            &:after {
                content: ')';
            }
        }
    }
}

//
//  Cart tax total
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-tax-total {
        cursor: pointer;
        padding-right: 12px;
        position: relative;
        .lib-icon-font(
            @icon-down,
            @_icon-font-size: 26px,
            @_icon-font-line-height: 10px,
            @_icon-font-margin: 3px 0 0 0,
            @_icon-font-position: after
        );

        &:after {
            position: absolute;
            right: -@indent__s;
            top: 3px;
        }

        &-expanded {
            .lib-icon-font-symbol(
                @_icon-font-content: @icon-up,
                @_icon-font-position: after
            );
        }
    }

    .abs-tax-total-expanded {
        .lib-icon-font-symbol(
            @_icon-font-content: @icon-up,
            @_icon-font-position: after
        );
    }
}

//
//  Checkout shipping methods title
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-methods-shipping-title {
        .lib-font-size(14);
        font-weight: @font-weight__bold;
        margin: 0 0 15px;
    }
}

//
//  Checkout order review
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .abs-checkout-order-review {
        tbody tr {
            &:not(:last-child) {
                border-bottom: @border-width__base solid @border-color__base;
            }

            &:extend(.abs-add-clearfix-mobile all);

            .col {
                &.item {
                    &:before {
                        display: none;
                    }
                }

                &.qty,
                &.price,
                &.subtotal {
                    box-sizing: border-box;
                    float: left;
                    text-align: center;
                    white-space: nowrap;
                    width: 33%;

                    &[data-th]:before {
                        content: attr(data-th) ':';
                        display: block;
                        font-weight: @font-weight__bold;
                        padding-bottom: @indent__s;
                    }
                }
            }

            .product-item-name {
                margin: 0;
            }
        }
    }
}

//
//  Account pages: title
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-account-title {
        > strong,
        > span {
            .lib-font-size(22);
            .lib-css(font-weight, @font-weight__light);
        }

        .lib-css(border-bottom, 1px solid @border-color__base);
        .lib-css(margin-bottom, @indent__m);
        .lib-css(padding-bottom, @indent__s);
    }
}

//
//  Ratings: vertical alignment
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-rating-summary {
        .rating {
            &-summary {
                display: table-row;
            }

            &-label {
                display: table-cell;
                padding-bottom: @indent__xs;
                padding-right: @indent__m;
                padding-top: 1px;
                vertical-align: top;
            }

            &-result {
                display: table-cell;
                vertical-align: top;
            }
        }
    }
}

//
//  Add colon
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-colon {
        &:after {
            content: ': ';
        }
    }
}

//
//  Icon - create add
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-icon-add {
        .lib-icon-font(
            @_icon-font-content: @icon-expand,
            @_icon-font-size: 10px,
            @_icon-font-line-height: 10px,
            @_icon-font-vertical-align: middle
        );
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .abs-icon-add-mobile {
        .lib-icon-font(
            @_icon-font-content: @icon-expand,
            @_icon-font-size: 10px,
            @_icon-font-line-height: 10px,
            @_icon-font-vertical-align: middle,
            @_icon-font-margin: 0 5px 0 0,
            @_icon-font-display: block
        );
    }
}

//
//  Dropdown items - create new
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-dropdown-items-new {
        .items .item:last-child {
            &:hover {
                .lib-css(background, @dropdown-list-item__hover);
            }
        }

        .action.new {
            &:extend(.abs-icon-add all);
            &:before {
                margin-left: -17px;
                margin-right: @indent__xs;
            }
        }
    }
}

//
//  Abstract toggle title block
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .abs-toggling-title-mobile {
        border-bottom: @border-width__base solid @border-color__base;
        border-top: @border-width__base solid @border-color__base;
        cursor: pointer;
        margin-bottom: 0;
        padding: @indent__s @indent__xl @indent__s @layout__width-xs-indent;
        position: relative;

        .lib-icon-font(
            @_icon-font-content: @icon-down,
            @_icon-font-size: 28px,
            @_icon-font-text-hide: false,
            @_icon-font-position: after,
            @_icon-font-display: block
        );

        &:after {
            position: absolute;
            right: @indent__s;
            top: 0;
        }

        &.active {
            .lib-icon-font-symbol(
                @_icon-font-content: @icon-up,
                @_icon-font-position: after
            );
        }
    }
}

//
//  Abstract no display
//  ---------------------------------------------

@abs-no-display: {
    display: none;
};

& when (@media-common = true) {
    .abs-no-display {
        @abs-no-display();
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .abs-no-display-s {
        @abs-no-display();
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-no-display-desktop {
        @abs-no-display();
    }
}

//
//  Status
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-status {
        display: inline-block;
        margin-bottom: @indent__base;
    }
}

//
//  Pager toolbar for non-catalog pages mobile
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .abs-pager-toolbar-mobile {
        .toolbar-amount,
        .limiter,
        .pages {
            float: none;
            margin-bottom: @indent__m;
        }
    }
}

//
//  Pager toolbar for non-catalog pages mobile
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .abs-pager-toolbar-mobile-s {
        .toolbar-amount,
        .limiter,
        .pages {
            margin-bottom: @indent__m;
        }
    }
}

//
//  Pager toolbar for non-catalog pages desktop
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-pager-toolbar {
        margin-bottom: @indent__base;
        position: relative;
        &:extend(.abs-add-clearfix-desktop all);

        .limiter {
            float: right;
            position: relative;
            z-index: 1;
        }

        .toolbar-amount {
            float: left;
            line-height: normal;
            padding: 7px 0 0;
            position: relative;
            z-index: 1;
        }

        .pages {
            position: absolute;
            width: 100%;
            z-index: 0;
        }
    }
}

//
//  Items counter in blocks
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-block-items-counter {
        .lib-css(color, @primary__color__lighter);
        .lib-font-size(12px);
        white-space: nowrap;
    }
}

//
//  Shopping cart items
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-shopping-cart-items {
        .action {
            &.continue {
                border-radius: @button__border-radius;
                font-weight: @font-weight__bold;
                .lib-link-as-button();
                .lib-button(
                    @_button-padding: 7px 15px 7px 0,
                    @_button-icon-use: true,
                    @_button-font-content: @icon-prev,
                    @_button-icon-font-size: 32px,
                    @_button-icon-font-line-height: 16px,
                    @_button-icon-font-position: before
                );

                &:active {
                    .lib-css(box-shadow, @button__shadow);
                }
            }

            &.update {
                .lib-button-icon(
                    @icon-update,
                    @_icon-font-size: 32px,
                    @_icon-font-line-height: 16px
                );
                padding-left: @indent__xs;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .abs-shopping-cart-items-mobile {
        .actions {
            text-align: center;
        }

        .action {
            &.update,
            &.continue,
            &.clear {
                margin: 0 auto @indent__s;
            }

            &.update,
            &.clear {
                display: block;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .abs-shopping-cart-items-desktop {
        float: left;
        position: relative;
        width: 73%;

        .actions {
            text-align: right;
        }

        .action {
            &.clear,
            &.update {
                margin-left: @indent__s;
            }

            &.continue {
                float: left;
            }
        }
    }
}

//
//  Form Field Date
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-field-date {
        .control {
            &:extend(.abs-add-box-sizing all);
            position: relative;
        }

        input {
            &:extend(.abs-field-date-input);
        }
    }
}

//
//  Form Field Date Input
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-field-date-input {
        margin-right: @indent__s;
        width: calc(~'100% -' @icon-calendar__font-size + @indent__s);
    }
}

//
//  Form Field Tooltip
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-field-tooltip {
        &:extend(.abs-add-box-sizing all);
        position: relative;

        input {
            margin-right: @indent__s;
            width: calc(~'100% -' @checkout-tooltip-icon__font-size + @indent__s + @indent__xs);
        }
    }
}

//
//  Checkout Tooltip Content (position: top)
//  ---------------------------------------------

@abs-checkout-tooltip-content-position-top: {
    .lib-css(right, @checkout-tooltip-content-mobile__right);
    .lib-css(top, @checkout-tooltip-content-mobile__top);
    left: auto;

    &:before,
    &:after {
        .lib-arrow(
            @_position: top,
            @_size: @checkout-tooltip-icon-arrow__font-size,
            @_color: @checkout-tooltip-content__background-color
        );
        .lib-css(margin-top, @checkout-tooltip-icon-arrow__left);
        right: @indent__s;
        left: auto;
        top: 0;
    }

    &:before {
        .lib-css(border-bottom-color, @checkout-tooltip-content__border-color);
    }

    &:after {
        .lib-css(border-bottom-color, @checkout-tooltip-content__background-color);
        top: 1px;
    }
};

& when (@media-common = true) {
    .abs-checkout-tooltip-content-position-top {
        @abs-checkout-tooltip-content-position-top();
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .abs-checkout-tooltip-content-position-top-mobile {
        @abs-checkout-tooltip-content-position-top();
    }
}

//
//  Checkout title
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-checkout-title {
        .lib-css(border-bottom, @checkout-step-title__border);
        .lib-css(padding-bottom, @checkout-step-title__padding);
        .lib-typography(
            @_font-size: @checkout-step-title__font-size,
            @_font-weight: @checkout-step-title__font-weight,
            @_font-family: false,
            @_font-style: false,
            @_line-height: false
        );
    }
}

//
//  Mini Cart and checkout sidebar totals
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-sidebar-totals {
        .mark {
            font-weight: @font-weight__regular;
            padding-left: 4px;

            strong {
                font-weight: @font-weight__regular;
            }
        }

        .amount {
            padding-right: 4px;
            text-align: right;
            white-space: nowrap;

            strong {
                font-weight: @font-weight__regular;
            }
        }

        .grand {
            .mark,
            .amount {
                padding-top: @indent__base;
            }

            .amount {
                padding-right: 4px;
                text-align: right;

                strong {
                    font-weight: @font-weight__bold;
                }
            }
        }

        .msrp {
            margin-bottom: @indent__s;
        }

        .totals-tax {
            &-summary {
                .mark,
                .amount {
                    .lib-css(border-top, @border-width__base solid @border-color__base);
                    .lib-css(border-bottom, @border-width__base solid @border-color__base);
                    cursor: pointer;
                }

                .amount .price {
                    .lib-icon-font(
                        @icon-down,
                        @_icon-font-size: 30px,
                        @_icon-font-text-hide: true,
                        @_icon-font-position: after,
                        @_icon-font-display: block
                    );
                    padding-right: @indent__m;
                    position: relative;

                    &:after {
                        position: absolute;
                        right: -5px;
                        top: -12px;
                    }
                }

                &.expanded {
                    .mark,
                    .amount {
                        border-bottom: 0;
                    }

                    .amount .price {
                        .lib-icon-font-symbol(
                            @_icon-font-content: @icon-up,
                            @_icon-font-position: after
                        );
                    }
                }
            }

            &-details {
                .lib-css(border-bottom, @border-width__base solid @border-color__base);
                display: none;

                &.shown {
                    display: table-row;
                }
            }
        }

        .table-caption {
            &:extend(.abs-no-display all);
        }
    }
}

//
//  Shopping cart and payment discount codes block
//  ---------------------------------------------

& when (@media-common = true) {
    .abs-discount-block {
        > .title {
            border-top: @border-width__base solid @border-color__base;
            cursor: pointer;
            font-weight: @font-weight__semibold;
            .lib-icon-font(
                @_icon-font-content: @icon-down,
                @_icon-font-size: 30px,
                @_icon-font-position: after,
                @_icon-font-display: block
            );
            margin-bottom: 0;
            overflow: hidden;
            padding: 7px @indent__l 7px @indent__xs;
            position: relative;

            &:after {
                position: absolute;
                right: -5px;
                top: -5px;
            }

            strong {
                .column.main & {
                    .lib-font-size(18);
                    font-weight: @font-weight__regular;
                }
            }
        }

        > .content {
            display: none;
        }

        &.active {
            > .title {
                .lib-icon-font-symbol(
                    @_icon-font-content: @icon-up,
                    @_icon-font-position: after
                );
            }

            > .content {
                display: block;
            }
        }
    }
}
//@codingStandardsIgnoreEnd
