// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //  Using buttons mixins
    button,
    a.action.primary {
        .lib-css(border-radius, @button__border-radius); 
       
    }
    .action,
    button {
        justify-content: center;
        .lib-vendor-box-align(center);
    }

    button {
        &:active {
            .lib-css(box-shadow, @button__shadow);  
        }
    }

    a.action.primary {
        .lib-link-as-button();
    }

    .action.primary,
    .action-primary {
        .lib-button-primary();
        text-transform: uppercase;
    }
}
