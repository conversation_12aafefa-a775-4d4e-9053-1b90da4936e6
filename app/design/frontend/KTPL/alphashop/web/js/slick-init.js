define([
    'jquery',
    'slick'
], function ($) {
    'use strict';

    return function (config, element) {
        console.log("called")
        var $element = $(element);
        var $categoriesGrid = $element.find('.slick-slider-mobile');
        
        function initSlider() {
            if (window.innerWidth < 768) {
                if (!$categoriesGrid.hasClass('slick-initialized')) {
                    $categoriesGrid.slick({
                        dots: true,
                        arrows: false,
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        mobileFirst: true,
                        responsive: [
                            {
                                breakpoint: 480,
                                settings: {
                                    slidesToShow: 2,
                                    slidesToScroll: 2
                                }
                            }
                        ]
                    });
                }
            } else {
                if ($categoriesGrid.hasClass('slick-initialized')) {
                    $categoriesGrid.slick('unslick');
                }
            }
        }

        initSlider();
        $(window).on('resize', initSlider);
    };
});
