<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="block-title">
    <div class="block-title-content">
        <div class="title">
            <span class="text" translate="'My Cart'"></span>
            
        </div>
        <if args="getCartParam('summary_count')">
            <div class="items-total">
                <span class="count" if="maxItemsToDisplay < getCartLineItemsCount()" text="maxItemsToDisplay"></span>
                <translate args="'of'" if="maxItemsToDisplay < getCartLineItemsCount()"></translate>
                <span class="count" text="getCartParam('summary_count').toLocaleString(window.LOCALE)"></span>
                    <!-- ko if: (getCartParam('summary_count') > 1) -->
                        <span translate="'Items in Cart'"></span>
                    <!--/ko-->
                    <!-- ko if: (getCartParam('summary_count') <= 1) -->
                        <span translate="'Item in Cart'"></span> 
                    <!--/ko-->
            </div>
        </if>
    </div>
    <button type="button"
        id="btn-minicart-close"
        class="action close"
        data-action="close"
        data-bind="
            attr: {
                title: $t('Close')
            },
            click: closeMinicart()
        ">
        <span translate="'Close'"></span>
    </button>
</div>

<div class="block-content">
    <div class="block-content-wrapper">
        <div class="block-content-container">



            <if args="getCartParam('summary_count')">
                <strong class="subtitle" translate="'Recently added item(s)'"></strong>
                <div data-action="scroll" class="minicart-items-wrapper">
                    <ol id="mini-cart" class="minicart-items" data-bind="foreach: { data: getCartItems(), as: 'item' }">
                        <each args="$parent.getRegion($parent.getItemRenderer(item.product_type))"
                            render="{name: getTemplate(), data: item, afterRender: function() {$parents[1].initSidebar()}}"></each>
                    </ol>
                </div>
            </if>

            <ifnot args="getCartParam('summary_count')">
                <strong class="subtitle empty"
                        translate="'You have no items in your shopping cart.'"></strong>
                <if args="getCartParam('cart_empty_message')">
                    <p class="minicart empty text" text="getCartParam('cart_empty_message')"></p>
                    <div class="actions">
                        <div class="secondary">
                            <a class="action viewcart" data-bind="attr: {href: shoppingCartUrl}">
                                <span translate="'View and Edit Cart'"></span>
                            </a>
                        </div>
                    </div>
                </if>
            </ifnot>



            <div id="minicart-widgets" class="minicart-widgets" if="regionHasElements('promotion')">
                <each args="getRegion('promotion')" render=""></each>
            </div>
        </div>
        <if args="getCartParam('summary_count')">
        <div class="block-footer">
            <div class="block-footer-wrapper">
        
                    <div class="subtotal-action-container">
                        <each args="getRegion('subtotalContainer')" render=""></each>
                        <each args="getRegion('extraInfo')" render=""></each>
                
                        <div class="actions" if="getCartParam('summary_count')">
                            <div class="secondary">
                                <a class="action viewcart" data-bind="attr: {href: shoppingCartUrl}">
                                    <span translate="'View and Edit Cart'"></span>
                                </a>
                            </div>
                        </div>
                    </div>
        
                    <div class="actions" if="getCartParam('possible_onepage_checkout')">
                        <div class="primary">
                            <button
                                    id="top-cart-btn-checkout"
                                    type="button"
                                    class="action primary checkout"
                                    data-action="close"
                                    data-bind="
                                        attr: {
                                            title: $t('Proceed to Checkout')
                                        },
                                        click: closeMinicart()
                                    "
                                    translate="'Proceed to Checkout'">
                            </button>
                            <div data-bind="html: getCartParamUnsanitizedHtml('extra_actions')"></div>
                        </div>
                    </div>
                    
                    <each args="getRegion('sign-in-popup')" render=""></each>
                </div>
            </div>
        </if>
    </div>
</div>
