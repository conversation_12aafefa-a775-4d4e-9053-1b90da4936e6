//
//  Common
//  _____________________________________________

& when (@media-common =true) {
  .amscroll-load-button {
    .lib-button() !important;
    .lib-vendor-prefix-display() !important;
    margin: 0 auto !important;
    border-radius:3px !important;
    min-width: 220px;
    text-transform: uppercase;
    &:not(.focus-visible) {
      border-color: @theme__color__primary !important; // override set by Js property
    }
  }

  

 
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
      
}