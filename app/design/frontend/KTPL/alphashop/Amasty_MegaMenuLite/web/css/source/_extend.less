//
//  Common
//  _____________________________________________

& when (@media-common =true) {

  .ammenu-header-container .header.content .ammenu-logo {
    margin: 0 auto;
  }
  .ammenu-menu-wrapper .ammenu-link {
    justify-content: flex-start;
  }

  .ammenu-menu-wrapper {
    margin-bottom: 0;
  }
  
  .ammenu-nav-sections {
    .lib-css(background, @navigation__background); 
  }

  .ammenu-menu-wrapper .ammenu-main-container { 
    position: relative;
  }


  .ammenu-nav-sections.-topmenu .ammenu-items.-root > .ammenu-item {
    margin: 0 25px;
  }

  .ammenu-nav-sections.-topmenu .ammenu-items.-root > .ammenu-item > .ammenu-link {
    .lib-font-size(18);
    color: @color-white;    
   
  }
  .ammenu-nav-sections.-topmenu .ammenu-items.-root > .ammenu-item > .ammenu-link:focus {
    box-shadow: none;
  }
  .ammenu-nav-sections.-topmenu.-sticky .ammenu-items.-root > .ammenu-item > .ammenu-link,
  .ammenu-nav-sections.-topmenu .ammenu-items.-root > .ammenu-item > .ammenu-link {
    padding: 14.5px 0;
    .lib-css(font-weight, @font-weight__regular) !important; 

  }
  .ammenu-category-tree .ammenu-title.-parent { 
    .lib-font-size(16);
    margin: 0 0 5px 0;
    .lib-css(font-weight, @font-weight__bold);
    color: @gray-700 !important;

  }
  .ammenu-category-tree .ammenu-list {
    .lib-font-size(12);
    color: @gray-700 !important;
  }

  .ammenu-menu-wrapper .ammenu-link.-simple {
    .lib-font-size(14);
    padding: 0 0 5px 0;
      .lib-link(
        @_link-color: rgba(0, 0, 0, 0.7),
        @_link-text-decoration: none,
        @_link-color-visited: rgba(0, 0, 0, 0.7),
        @_link-text-decoration-visited: none,
        @_link-color-hover: @theme__color__primary,
        @_link-text-decoration-hover: underline,
        @_link-color-active: @theme__color__primary,
        @_link-text-decoration-active: underline) !important;
      
  }

  .ammenu-category-tree .ammenu-item {}









 
  .ammenu-tabs-list {
    margin: 0 0 20px 0;
    padding: 0;
    .ammenu-button {
      height: 60px;
      border-radius: 0;
      background-color: @gray-100;
      color:  @theme__color__primary;
      &.-active  {
        background-color: @theme__color__primary !important; 
        color: @color-white !important;
      }
    }
  }

  .ammenu-nav-sections.-sidebar .ammenu-items.-root > .ammenu-item:first-child {
    display: none;
  }

  .ammenu-button.-hamburger {
    margin: 0;
    min-width: 20px;
    flex-shrink: 0;
  }

  .ammenu-nav-sections.-sidebar {
    width: 85vw;
  }

  .ammenu-menu-title {
    display: none;
  }

  .ammenu-nav-sections.-sidebar .ammenu-items.-root > .ammenu-item > .ammenu-link > .ammenu-text-block {
    .lib-font-size(14);
    .lib-css(font-weight, @font-weight__regular);
  }

  .ammenu-nav-sections.-sidebar .ammenu-items.-root > .ammenu-item > .ammenu-link {
    border-bottom-color: @gray-100 !important;

    svg {
      width: 12px;
      height: 12px;
    }
  }

  ._ammenu-link.-second, .ammenu-nav-sections.-mobile.-accordion .ammenu-items.-root > .ammenu-item .ammenu-items .ammenu-link {
    font-weight: 500;
    .lib-font-size(14);
  }

  .ammenu-nav-sections.-mobile.-accordion .ammenu-items.-root > .ammenu-item .ammenu-items {
    padding: 0 0 0 20px;

    .ammenu-items {
      padding-left: 35px;
      padding-right: 20px;

      .ammenu-link {
        font-weight: 400;
        min-height: 28px;
        margin: 0;
        padding: 0;
        .lib-link(
          @_link-color: rgba(0, 0, 0, 0.7),
          @_link-text-decoration: none,
          @_link-color-visited: rgba(0, 0, 0, 0.7),
          @_link-text-decoration-visited: none,
          @_link-color-hover: @theme__color__primary,
          @_link-text-decoration-hover: underline,
          @_link-color-active: @theme__color__primary,
          @_link-text-decoration-active: underline) !important;
      }
      .ammenu-item {
        &:last-child {
          .ammenu-link {
            color: @theme__color__primary !important; 
            .ammenu-text {
              margin-left: -10px;
              .lib-icon-font(
                @icon-next,
                @_icon-font-position:before,
                @_icon-font-size: 24px,
                @_icon-font-line-height: 32px,
            );
      
            }
            
          }
        }
      }

    }

  }

  

  ._ammenu-link.-second, .ammenu-nav-sections.-mobile.-accordion .ammenu-items.-root > .ammenu-item .ammenu-items .ammenu-link, .ammenu-nav-sections.-mobile.-accordion .ammenu-items.-root > .ammenu-item .ammenu-items .ammenu-link {

    svg {
      width: 12px;
      height: 12px;
    }
  }

  .ammenu-menu-wrapper .ammenu-icon-block.-toggle {
    width: 40px;

  }

  .ammenu-category-tree .ammenu-list > .ammenu-item:last-child .ammenu-link {
      color: @theme__color__primary !important; 
      display: inline;
      font-weight: 500;

      .ammenu-text-block {

        display: inline-flex
      }

      .ammenu-text {
        .lib-icon-font(
          @icon-next,
          @_icon-font-position:after,
          @_icon-font-size: 24px,
          @_icon-font-line-height: 32px, 
      );

      }


  }

  .ammenu-menu-greetings.-logged {
    background-color: green;
  } 


  .menu-left-category-wrapper {
    padding: 0 20px 20px 0; 

  }
  .menu-category-wrapper {

  }

  .all-category-wrapper {
    margin-top: 10px;

    .block-category-link {
       text-align: right;

       a {
        .lib-button();
        .lib-link-as-button();
        .lib-css(border-radius, @button__border-radius);
          margin-top: 0;
       }
    }
  }


  .ammenu-nav-sections.-topmenu .ammenu-submenu-wrapper {
    border-radius: 0;
    background-color: @gray-100 !important;
    padding: 20px;

  }

}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {



   .ammenu-header-container {
    .header.content {
      .ammenu-logo {
        margin-left: 15px;
      }


    }
  } 

}


//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {

  .ammenu-menu-wrapper .ammenu-main-container {
    position: relative;
  }

  .ammenu-main-container {
    .lib-css(background, @navigation-desktop__background);  
  }

  .ammenu-nav-sections.-topmenu:not(.-hamburger) .ammenu-main-container .ammenu-items.-root {
      box-sizing: border-box;
      margin-left: auto;
      margin-right: auto;
      max-width: @layout__max-width;
      padding-left: @layout-indent__width;
      padding-right: @layout-indent__width; 
      width: auto;
      position: static;
      justify-content: center;
  }

  .ammenu-nav-sections.-topmenu .ammenu-items.-root {
    > .ammenu-item {


      > .ammenu-link  {

        position: relative;

        &:after {
          display: block;
          width: 2px;
          height: 28px;
          background-color: rgba(255, 255, 255, 0.3);
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          bottom: 0;
          content: "";
         
        }

        .ammenu-text {
          position: relative;

          &::after {
            content: "";
            position: absolute;
            left: 50%;
            bottom: -5px;
            background-color: @theme__color__primary;
            height: 0;
            width: 0;
            opacity: 0;
            transition: .3s;
            transition-timing-function: cubic-bezier(.58,.3,.005,1);
          }
        }

        &:hover {
          .ammenu-text {
  
            &::after {
              width: 100%;
              opacity: 1;
              height: 2px;
              left: 0;
            }
          }

        }


      }


    }
  }

  .ammenu-category-tree .ammenu-item {
    .ammenu-icon-block.-toggle {
      margin-left: 0;
      width: auto;
    }
    svg {
      width: 10px;
      height: 10px;
    }
  } 

}


& when (@media-common =true) {

  .ammenu-submenu-block.-root {
    [data-content-type='row'][data-appearance='contained'] {
      .cms-home.cms-index-index & {
        padding: 0 !important; 
      }
    }
  }

.custom-megamenu-wrapper {
  
  padding: 0;
}

  .ammenu-submenu-block.-root {
    max-width: 1340px;
    width: 100%;
    margin: 0 auto;
    padding: 0;
  }

  .brands-spotlight {
    margin: 0;
    max-width: 300px;
    color: @theme__color__primary;
    
    &-title {
       .lib-font-size(15);
       margin: 0 0 20px 0;
       .lib-css(font-weight, @font-weight__bold);
       color: @theme__color__primary;

    }
    
    &-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        background: @color-white;
        overflow: hidden;
        padding: 0 0;
        text-align: center;

    }
    
    &-item {
        padding: 20px;
        text-align: center;
        border: 1px solid @gray-100;
        transition: background-color 0.3s;
        
        a {
          .lib-font-size(13);
            color: @theme__color__primary;
            text-decoration: none;

            &:hover {
                text-decoration: none;
            } 
            
            
        }
    }
    
    &-footer {
        margin-top: 20px;
        
        .action {
            display: inline-flex;
            align-items: center; 
            .lib-css(font-weight, @font-weight__bold); 

            &:hover {
              text-decoration: none;
            }
            
            &:after {
                content: @icon-arrow-right-thin;
                font-family: @icons__font-name;
                margin-left: 10px;
                font-size: 12px;
            }
        }
    }
  }
}